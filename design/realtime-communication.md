# Real-time Communication Design for Internal Chat System

## WebSocket Architecture Overview

The real-time communication system extends the existing Socket.IO infrastructure to support internal domain chat functionality while maintaining compatibility with the current agent and social media chat systems.

## Socket.IO Integration

### Namespace Structure

```typescript
// Extend existing socket structure
const CHAT_NAMESPACES = {
  DOMAIN_CHAT: (domain_uuid: string) => `${domain_uuid}_internal_chat`,
  ROOM_CHAT: (domain_uuid: string, room_uuid: string) => `${domain_uuid}_room_${room_uuid}`,
  USER_PRESENCE: (domain_uuid: string) => `${domain_uuid}_presence`,
  NOTIFICATIONS: (domain_uuid: string) => `${domain_uuid}_notifications`
}
```

### Connection Management

```typescript
// src/redux-store/slices/internal-chat/socketActions.ts

export const connectInternalChat = (domain_uuid: string, user_uuid: string): AppThunk =>
  async (dispatch, getState) => {
    const host = process.env.NEXT_PUBLIC_SOCKET_IO_URL ?? 'http://localhost:3000'
    
    const socket = io(host, {
      query: {
        domain: domain_uuid,
        user: user_uuid,
        type: 'internal_chat'
      }
    })

    // Connection events
    socket.on('connect', () => {
      dispatch(setChatConnected(true))
      console.log('Internal chat connected')
      
      // Join domain-wide chat namespace
      socket.emit('join_domain_chat', { domain_uuid, user_uuid })
      
      // Update user presence to online
      socket.emit('update_presence', { 
        status: 'online',
        user_uuid,
        domain_uuid 
      })
    })

    socket.on('disconnect', () => {
      dispatch(setChatConnected(false))
      console.log('Internal chat disconnected')
    })

    // Set up event listeners
    setupChatEventListeners(socket, dispatch, domain_uuid)
    
    dispatch(setChatSocket(socket))
  }

const setupChatEventListeners = (socket: Socket, dispatch: AppDispatch, domain_uuid: string) => {
  // Message events
  socket.on(`${domain_uuid}_new_message`, (data: ChatMessage) => {
    dispatch(receiveMessage(data))
    dispatch(updateUnreadCount({ roomId: data.room_uuid, increment: 1 }))
    
    // Show notification if not in active room
    const state = getState()
    if (state.internalChatReducer.activeRoomId !== data.room_uuid) {
      dispatch(showNotification({
        type: 'message',
        title: data.sender_name,
        message: data.message_content,
        roomId: data.room_uuid
      }))
    }
  })

  socket.on(`${domain_uuid}_message_updated`, (data: ChatMessage) => {
    dispatch(updateMessage(data))
  })

  socket.on(`${domain_uuid}_message_deleted`, (data: { message_uuid: string, room_uuid: string }) => {
    dispatch(deleteMessage(data))
  })

  // Typing indicators
  socket.on(`${domain_uuid}_user_typing`, (data: { room_uuid: string, user_uuid: string, user_name: string }) => {
    dispatch(addTypingUser(data))
    
    // Auto-remove typing indicator after 3 seconds
    setTimeout(() => {
      dispatch(removeTypingUser({ room_uuid: data.room_uuid, user_uuid: data.user_uuid }))
    }, 3000)
  })

  socket.on(`${domain_uuid}_user_stopped_typing`, (data: { room_uuid: string, user_uuid: string }) => {
    dispatch(removeTypingUser(data))
  })

  // Presence events
  socket.on(`${domain_uuid}_user_presence_changed`, (data: UserPresence) => {
    dispatch(updateUserPresence(data))
  })

  socket.on(`${domain_uuid}_user_joined_domain`, (data: { user_uuid: string, user_name: string }) => {
    dispatch(addOnlineUser(data.user_uuid))
    dispatch(updateUserPresence({ 
      user_uuid: data.user_uuid, 
      status: 'online', 
      last_seen: new Date().toISOString() 
    }))
  })

  socket.on(`${domain_uuid}_user_left_domain`, (data: { user_uuid: string }) => {
    dispatch(removeOnlineUser(data.user_uuid))
    dispatch(updateUserPresence({ 
      user_uuid: data.user_uuid, 
      status: 'offline', 
      last_seen: new Date().toISOString() 
    }))
  })

  // Room events
  socket.on(`${domain_uuid}_room_created`, (data: ChatRoom) => {
    dispatch(addRoom(data))
  })

  socket.on(`${domain_uuid}_room_updated`, (data: ChatRoom) => {
    dispatch(updateRoom(data))
  })

  socket.on(`${domain_uuid}_participant_added`, (data: { room_uuid: string, participant: ChatParticipant }) => {
    dispatch(addRoomParticipant(data))
  })

  socket.on(`${domain_uuid}_participant_removed`, (data: { room_uuid: string, user_uuid: string }) => {
    dispatch(removeRoomParticipant(data))
  })

  // Error handling
  socket.on('error', (error: { error_code: string, error_message: string, context: any }) => {
    console.error('Chat socket error:', error)
    dispatch(setChatError(error))
  })
}
```

### Message Broadcasting System

```typescript
// Server-side message broadcasting (server.js extension)

class InternalChatManager {
  private io: Server
  private connectedUsers: Map<string, { socket: Socket, user_uuid: string, domain_uuid: string }> = new Map()
  private roomParticipants: Map<string, Set<string>> = new Map() // room_uuid -> user_uuids

  constructor(io: Server) {
    this.io = io
    this.setupEventHandlers()
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket: Socket) => {
      socket.on('join_domain_chat', this.handleJoinDomainChat.bind(this, socket))
      socket.on('join_room', this.handleJoinRoom.bind(this, socket))
      socket.on('leave_room', this.handleLeaveRoom.bind(this, socket))
      socket.on('send_message', this.handleSendMessage.bind(this, socket))
      socket.on('typing_start', this.handleTypingStart.bind(this, socket))
      socket.on('typing_stop', this.handleTypingStop.bind(this, socket))
      socket.on('update_presence', this.handleUpdatePresence.bind(this, socket))
      socket.on('disconnect', this.handleDisconnect.bind(this, socket))
    })
  }

  private async handleJoinDomainChat(socket: Socket, data: { domain_uuid: string, user_uuid: string }) {
    const { domain_uuid, user_uuid } = data
    
    // Store connection
    this.connectedUsers.set(socket.id, { socket, user_uuid, domain_uuid })
    
    // Join domain namespace
    socket.join(`${domain_uuid}_internal_chat`)
    
    // Notify others that user joined
    socket.to(`${domain_uuid}_internal_chat`).emit(`${domain_uuid}_user_joined_domain`, {
      user_uuid,
      user_name: await this.getUserName(user_uuid)
    })
    
    // Send current online users to new connection
    const onlineUsers = await this.getOnlineUsersInDomain(domain_uuid)
    socket.emit(`${domain_uuid}_online_users`, onlineUsers)
  }

  private async handleJoinRoom(socket: Socket, data: { room_uuid: string }) {
    const { room_uuid } = data
    const connection = this.connectedUsers.get(socket.id)
    
    if (!connection) return
    
    // Verify user has access to room
    const hasAccess = await this.verifyRoomAccess(connection.user_uuid, room_uuid)
    if (!hasAccess) {
      socket.emit('error', {
        error_code: 'ROOM_ACCESS_DENIED',
        error_message: 'You do not have access to this room',
        context: { room_uuid }
      })
      return
    }
    
    // Join room namespace
    socket.join(`${connection.domain_uuid}_room_${room_uuid}`)
    
    // Track room participants
    if (!this.roomParticipants.has(room_uuid)) {
      this.roomParticipants.set(room_uuid, new Set())
    }
    this.roomParticipants.get(room_uuid)!.add(connection.user_uuid)
    
    // Confirm room joined
    socket.emit('room_joined', {
      room_uuid,
      participant_count: this.roomParticipants.get(room_uuid)!.size
    })
  }

  private async handleSendMessage(socket: Socket, data: {
    room_uuid: string
    message_content: string
    message_type: string
    parent_message_uuid?: string
    message_metadata?: any
  }) {
    const connection = this.connectedUsers.get(socket.id)
    if (!connection) return

    try {
      // Save message to database
      const message = await this.saveMessage({
        ...data,
        sender_user_uuid: connection.user_uuid,
        domain_uuid: connection.domain_uuid
      })

      // Broadcast to room participants
      this.io.to(`${connection.domain_uuid}_room_${data.room_uuid}`)
        .emit(`${connection.domain_uuid}_new_message`, message)

      // Send push notifications to offline users
      await this.sendPushNotifications(data.room_uuid, message)

    } catch (error) {
      socket.emit('error', {
        error_code: 'MESSAGE_SEND_FAILED',
        error_message: 'Failed to send message',
        context: { error: error.message }
      })
    }
  }

  private handleTypingStart(socket: Socket, data: { room_uuid: string }) {
    const connection = this.connectedUsers.get(socket.id)
    if (!connection) return

    socket.to(`${connection.domain_uuid}_room_${data.room_uuid}`)
      .emit(`${connection.domain_uuid}_user_typing`, {
        room_uuid: data.room_uuid,
        user_uuid: connection.user_uuid,
        user_name: connection.user_name
      })
  }

  private handleTypingStop(socket: Socket, data: { room_uuid: string }) {
    const connection = this.connectedUsers.get(socket.id)
    if (!connection) return

    socket.to(`${connection.domain_uuid}_room_${data.room_uuid}`)
      .emit(`${connection.domain_uuid}_user_stopped_typing`, {
        room_uuid: data.room_uuid,
        user_uuid: connection.user_uuid
      })
  }

  private async handleUpdatePresence(socket: Socket, data: {
    status: 'online' | 'away' | 'busy' | 'offline'
    current_activity?: string
  }) {
    const connection = this.connectedUsers.get(socket.id)
    if (!connection) return

    // Update presence in database
    await this.updateUserPresence(connection.user_uuid, data)

    // Broadcast presence change to domain
    socket.to(`${connection.domain_uuid}_internal_chat`)
      .emit(`${connection.domain_uuid}_user_presence_changed`, {
        user_uuid: connection.user_uuid,
        status: data.status,
        last_seen: new Date().toISOString(),
        current_activity: data.current_activity
      })
  }

  private handleDisconnect(socket: Socket) {
    const connection = this.connectedUsers.get(socket.id)
    if (!connection) return

    // Remove from connected users
    this.connectedUsers.delete(socket.id)

    // Remove from room participants
    this.roomParticipants.forEach((participants, roomId) => {
      participants.delete(connection.user_uuid)
    })

    // Update presence to offline
    this.updateUserPresence(connection.user_uuid, { status: 'offline' })

    // Notify domain that user left
    socket.to(`${connection.domain_uuid}_internal_chat`)
      .emit(`${connection.domain_uuid}_user_left_domain`, {
        user_uuid: connection.user_uuid
      })
  }

  // Helper methods
  private async verifyRoomAccess(user_uuid: string, room_uuid: string): Promise<boolean> {
    // Check if user is participant in room
    const participant = await prisma.v_chat_room_participants.findFirst({
      where: {
        room_uuid,
        user_uuid
      }
    })
    return !!participant
  }

  private async saveMessage(messageData: any): Promise<ChatMessage> {
    const message = await prisma.v_chat_messages.create({
      data: {
        room_uuid: messageData.room_uuid,
        sender_user_uuid: messageData.sender_user_uuid,
        message_content: messageData.message_content,
        message_type: messageData.message_type,
        parent_message_uuid: messageData.parent_message_uuid,
        message_metadata: messageData.message_metadata || {}
      },
      include: {
        sender: {
          select: {
            user_uuid: true,
            username: true,
            // Add avatar field when available
          }
        }
      }
    })

    return {
      message_uuid: message.message_uuid,
      room_uuid: message.room_uuid,
      sender_user_uuid: message.sender_user_uuid,
      sender_name: message.sender.username,
      message_content: message.message_content,
      message_type: message.message_type,
      message_metadata: message.message_metadata,
      is_edited: message.is_edited,
      is_deleted: message.is_deleted,
      reactions: [],
      attachments: [],
      insert_date: message.insert_date.toISOString()
    }
  }

  private async updateUserPresence(user_uuid: string, presenceData: any) {
    await prisma.v_chat_user_presence.upsert({
      where: { user_uuid },
      update: {
        status: presenceData.status,
        current_activity: presenceData.current_activity,
        last_seen: new Date(),
        update_date: new Date()
      },
      create: {
        user_uuid,
        domain_uuid: presenceData.domain_uuid,
        status: presenceData.status,
        current_activity: presenceData.current_activity,
        last_seen: new Date()
      }
    })
  }

  private async sendPushNotifications(room_uuid: string, message: ChatMessage) {
    // Get offline participants
    const participants = await prisma.v_chat_room_participants.findMany({
      where: { room_uuid },
      include: { user: true }
    })

    const offlineParticipants = participants.filter(p => 
      !this.isUserOnline(p.user_uuid) && 
      p.user_uuid !== message.sender_user_uuid
    )

    // Create notifications
    for (const participant of offlineParticipants) {
      await prisma.v_chat_notifications.create({
        data: {
          user_uuid: participant.user_uuid,
          room_uuid,
          message_uuid: message.message_uuid,
          notification_type: 'message'
        }
      })
    }
  }

  private isUserOnline(user_uuid: string): boolean {
    return Array.from(this.connectedUsers.values())
      .some(conn => conn.user_uuid === user_uuid)
  }
}
```

## Connection Resilience & Error Handling

### Auto-Reconnection Logic

```typescript
// src/hooks/internal-chat/useChatConnection.ts

export const useChatConnection = (domainId: string, userId: string) => {
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected')
  const [reconnectAttempts, setReconnectAttempts] = useState(0)
  const maxReconnectAttempts = 5
  const reconnectDelay = 1000 // Start with 1 second

  const connect = useCallback(() => {
    setConnectionStatus('connecting')
    dispatch(connectInternalChat(domainId, userId))
  }, [domainId, userId, dispatch])

  const handleReconnect = useCallback(() => {
    if (reconnectAttempts < maxReconnectAttempts) {
      const delay = reconnectDelay * Math.pow(2, reconnectAttempts) // Exponential backoff
      
      setTimeout(() => {
        setReconnectAttempts(prev => prev + 1)
        connect()
      }, delay)
    } else {
      setConnectionStatus('error')
      // Show user notification about connection failure
    }
  }, [reconnectAttempts, connect])

  useEffect(() => {
    const socket = getState().internalChatReducer.socket
    
    if (socket) {
      socket.on('connect', () => {
        setConnectionStatus('connected')
        setReconnectAttempts(0)
      })

      socket.on('disconnect', () => {
        setConnectionStatus('disconnected')
        handleReconnect()
      })

      socket.on('connect_error', () => {
        setConnectionStatus('error')
        handleReconnect()
      })
    }

    return () => {
      if (socket) {
        socket.off('connect')
        socket.off('disconnect')
        socket.off('connect_error')
      }
    }
  }, [handleReconnect])

  return { connectionStatus, connect, reconnectAttempts }
}
```

### Message Delivery Guarantees

```typescript
// Message acknowledgment system
export const sendMessageWithAck = (messageData: any): AppThunk =>
  async (dispatch, getState) => {
    const socket = getState().internalChatReducer.socket
    const tempId = generateTempId()
    
    // Add message with pending status
    dispatch(addPendingMessage({
      ...messageData,
      temp_id: tempId,
      status: 'sending'
    }))

    return new Promise((resolve, reject) => {
      socket.emit('send_message', messageData, (response: any) => {
        if (response.success) {
          dispatch(confirmMessage({ temp_id: tempId, message: response.message }))
          resolve(response.message)
        } else {
          dispatch(failMessage({ temp_id: tempId, error: response.error }))
          reject(response.error)
        }
      })

      // Timeout after 10 seconds
      setTimeout(() => {
        dispatch(failMessage({ temp_id: tempId, error: 'Message timeout' }))
        reject(new Error('Message timeout'))
      }, 10000)
    })
  }
```

## Performance Optimizations

### Message Batching
```typescript
// Batch multiple typing events to reduce network traffic
const useTypingIndicator = (roomId: string) => {
  const [isTyping, setIsTyping] = useState(false)
  const typingTimeoutRef = useRef<NodeJS.Timeout>()
  const lastTypingEmitRef = useRef<number>(0)
  const TYPING_EMIT_INTERVAL = 1000 // 1 second

  const startTyping = useCallback(() => {
    const now = Date.now()
    
    if (!isTyping || now - lastTypingEmitRef.current > TYPING_EMIT_INTERVAL) {
      socket.emit('typing_start', { room_uuid: roomId })
      lastTypingEmitRef.current = now
      setIsTyping(true)
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }

    // Set new timeout to stop typing
    typingTimeoutRef.current = setTimeout(() => {
      socket.emit('typing_stop', { room_uuid: roomId })
      setIsTyping(false)
    }, 3000)
  }, [roomId, isTyping])

  return { startTyping, isTyping }
}
```

### Connection Pooling
```typescript
// Reuse existing socket connection for multiple chat features
class ChatSocketManager {
  private static instance: ChatSocketManager
  private socket: Socket | null = null
  private eventHandlers: Map<string, Function[]> = new Map()

  static getInstance(): ChatSocketManager {
    if (!ChatSocketManager.instance) {
      ChatSocketManager.instance = new ChatSocketManager()
    }
    return ChatSocketManager.instance
  }

  connect(domainId: string, userId: string) {
    if (this.socket?.connected) {
      return this.socket
    }

    this.socket = io(process.env.NEXT_PUBLIC_SOCKET_IO_URL!, {
      query: { domain: domainId, user: userId, type: 'internal_chat' }
    })

    return this.socket
  }

  addEventHandler(event: string, handler: Function) {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, [])
    }
    this.eventHandlers.get(event)!.push(handler)
    
    if (this.socket) {
      this.socket.on(event, handler)
    }
  }

  removeEventHandler(event: string, handler: Function) {
    const handlers = this.eventHandlers.get(event)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
        if (this.socket) {
          this.socket.off(event, handler)
        }
      }
    }
  }
}
```
