# Group Chat Testing Guide

## ✅ **Fixed Issues**
- ✅ Fixed import paths: `@/lib/auth` → `@/libs/auth`
- ✅ Fixed import paths: `@/lib/prisma` → `@/libs/db/prisma`
- ✅ Fixed TypeScript null assertion issues
- ✅ All compilation errors resolved

## 🧪 **Testing Steps**

### **1. Test Group Creation**

#### **Step 1: Access Group Creation**
1. Open the chat interface
2. Look for the **"Group"** button in the ChatRoomList header (next to "Messages")
3. Click the **"Group"** button

#### **Step 2: Create a Group**
1. Enter a group name (e.g., "Test Group")
2. In the search box, type a username to find users
3. Click **"Add"** next to users you want to include
4. Selected users should appear in the "Selected Members" section
5. Click **"Create Group"**

#### **Expected Results:**
- ✅ Group should be created successfully
- ✅ You should be redirected to the new group chat
- ✅ Group should appear in the chat room list
- ✅ Group should be filtered under "Group" tab in room list

### **2. Test Group Member Management**

#### **Step 1: Access Member List**
1. Open a group chat
2. Look for the **group icon** in the chat header (should show member count)
3. Click the **group icon**

#### **Step 2: View Members**
1. Member list dropdown should appear
2. Should show all group members with:
   - ✅ User avatars
   - ✅ Usernames
   - ✅ Role badges (Owner, Admin, Member)
   - ✅ Online status indicators

#### **Step 3: Test Member Actions (if you're admin/owner)**
1. Try removing a member (if you have permissions)
2. Try leaving the group using "Leave Group" button

#### **Expected Results:**
- ✅ Member list displays correctly
- ✅ Role-based permissions work
- ✅ Member removal works (with confirmation)
- ✅ Leave group functionality works

### **3. Test Group Chat Interface**

#### **Step 1: Send Messages**
1. Send messages in the group chat
2. Check if sender names appear above messages
3. Verify message grouping works correctly

#### **Step 2: Check Group Display**
1. Verify group name appears in chat header
2. Check member count display (e.g., "3 members • 2 online")
3. Confirm group icon is used instead of user avatar

#### **Expected Results:**
- ✅ Messages show sender names in groups
- ✅ Group header displays correctly
- ✅ Member count updates properly
- ✅ Group-specific UI elements work

### **4. Test API Endpoints**

#### **Test Member Management APIs:**

```bash
# Get group members
GET /api/internal-chat/rooms/{roomId}/members

# Add members to group
POST /api/internal-chat/rooms/{roomId}/members
{
  "user_uuids": ["user-uuid-1", "user-uuid-2"],
  "role": "member",
  "send_notification": true
}

# Remove member from group
DELETE /api/internal-chat/rooms/{roomId}/members/{userId}

# Update member role
PUT /api/internal-chat/rooms/{roomId}/members/{userId}
{
  "new_role": "admin"
}
```

#### **Expected API Responses:**
- ✅ All endpoints return proper JSON responses
- ✅ Permission validation works correctly
- ✅ Error handling provides clear messages
- ✅ Database transactions maintain consistency

## 🐛 **Common Issues to Check**

### **1. Import Path Issues**
- ❌ If you see "Module not found" errors
- ✅ **Fixed**: All import paths corrected

### **2. TypeScript Errors**
- ❌ If you see null/undefined errors
- ✅ **Fixed**: Added proper null assertions

### **3. Database Issues**
- ❌ If group creation fails
- ✅ Check database connection
- ✅ Verify user permissions
- ✅ Check domain_uuid consistency

### **4. UI Issues**
- ❌ If buttons don't appear
- ✅ Check component imports
- ✅ Verify CSS classes
- ✅ Check responsive design

## 📊 **Success Criteria**

### **Functionality:**
- ✅ Users can create groups with names
- ✅ Users can search and add members
- ✅ Groups appear in chat room list
- ✅ Group filtering works in room list
- ✅ Member management works correctly
- ✅ Permission system functions properly

### **UI/UX:**
- ✅ Group creation is intuitive
- ✅ Member list is accessible
- ✅ Group chats display sender names
- ✅ Interface is responsive
- ✅ Dark mode support works

### **Technical:**
- ✅ No compilation errors
- ✅ APIs respond correctly
- ✅ Database operations are atomic
- ✅ Error handling is robust

## 🚀 **Next Steps After Testing**

### **If Everything Works:**
1. **Document any edge cases found**
2. **Consider additional features:**
   - Group name editing
   - Group avatar upload
   - Advanced permissions
   - Group notifications settings

### **If Issues Found:**
1. **Check browser console for errors**
2. **Verify API responses in Network tab**
3. **Check server logs for backend errors**
4. **Test with different user roles**

## 📝 **Test Checklist**

- [ ] Group creation dialog opens
- [ ] User search works in group creation
- [ ] Group is created successfully
- [ ] Group appears in room list
- [ ] Group filter works in room list
- [ ] Group member list opens from header
- [ ] Member roles display correctly
- [ ] Member removal works (with permissions)
- [ ] Leave group functionality works
- [ ] Messages show sender names in groups
- [ ] Group header displays member count
- [ ] APIs return correct responses
- [ ] Permission validation works
- [ ] Error handling is user-friendly

## 🎯 **Performance Notes**

- Group member list loads quickly
- User search is responsive (300ms debounce)
- Group creation is fast (single API call)
- Member management is efficient
- UI remains responsive during operations

The simplified group chat implementation should now be fully functional and ready for testing!
