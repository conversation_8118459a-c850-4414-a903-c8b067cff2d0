# Discord-Inspired Message Storage Optimization

## Storage Comparison: Before vs After

### Original Design (Per Message Record)
```sql
CREATE TABLE v_chat_messages (
    message_uuid UUID PRIMARY KEY,              -- 36 bytes (string) or 16 bytes (binary)
    room_uuid UUID NOT NULL,                    -- 36 bytes (string) or 16 bytes (binary)  
    sender_user_uuid UUID NOT NULL,             -- 36 bytes (string) or 16 bytes (binary)
    parent_message_uuid UUID,                   -- 36 bytes (string) or 16 bytes (binary)
    message_content TEXT,                       -- Variable
    message_type VARCHAR(50),                   -- 50 bytes
    message_metadata JSONB,                     -- Variable (often 100+ bytes)
    is_edited BOOLEAN,                          -- 1 byte
    is_deleted BOOLEAN,                         -- 1 byte
    edited_date TIMESTAMPTZ,                    -- 8 bytes
    insert_date TIMESTAMPTZ,                    -- 8 bytes
    insert_user UUID,                           -- 36 bytes (string) or 16 bytes (binary)
    update_date TIMESTAMPTZ,                    -- 8 bytes
    update_user UUID                            -- 36 bytes (string) or 16 bytes (binary)
);
-- Total: ~200+ bytes + content + metadata per message
```

### Discord-Inspired Design (Per Message Record)
```sql
CREATE TABLE v_chat_messages (
    message_id BIGSERIAL PRIMARY KEY,          -- 8 bytes
    room_uuid UUID NOT NULL,                   -- 16 bytes (binary)
    author_uuid UUID NOT NULL,                 -- 16 bytes (binary)
    content TEXT,                              -- Variable
    message_type SMALLINT DEFAULT 0,           -- 2 bytes
    reply_to BIGINT,                           -- 8 bytes (nullable)
    edited_at BIGINT,                          -- 8 bytes (nullable)
    created_at BIGINT NOT NULL,                -- 8 bytes
    flags INTEGER DEFAULT 0                    -- 4 bytes
);
-- Total: ~70 bytes + content per message
```

**Storage Reduction: 65% smaller per message!**

## Message Type Optimization

### Discord's Message Type Approach
```typescript
// Instead of storing strings, use integers
enum MessageType {
  DEFAULT = 0,                    // Regular text message
  RECIPIENT_ADD = 1,              // User added to group
  RECIPIENT_REMOVE = 2,           // User removed from group
  CALL = 3,                       // Call started
  CHANNEL_NAME_CHANGE = 4,        // Channel name changed
  CHANNEL_ICON_CHANGE = 5,        // Channel icon changed
  CHANNEL_PINNED_MESSAGE = 6,     // Message pinned
  USER_JOIN = 7,                  // User joined server
  USER_PREMIUM_GUILD_SUBSCRIPTION = 8, // User boosted server
  // ... etc
}

// Usage in your system
const MESSAGE_TYPES = {
  TEXT: 0,
  IMAGE: 1,
  FILE: 2,
  SYSTEM_USER_JOINED: 3,
  SYSTEM_USER_LEFT: 4,
  SYSTEM_CALL_STARTED: 5,
  SYSTEM_CALL_ENDED: 6
} as const;
```

## Flags System (Discord's Bitfield Approach)

### Instead of Multiple Boolean Columns
```typescript
// Discord uses bitfields for message flags
enum MessageFlags {
  CROSSPOSTED = 1 << 0,           // Message has been crossposted
  IS_CROSSPOST = 1 << 1,          // Message is a crosspost
  SUPPRESS_EMBEDS = 1 << 2,       // Do not include embeds
  SOURCE_MESSAGE_DELETED = 1 << 3, // Source message deleted
  URGENT = 1 << 4,                // Message is urgent
  HAS_THREAD = 1 << 5,            // Message has thread
  EPHEMERAL = 1 << 6,             // Message is ephemeral
  LOADING = 1 << 7,               // Message is loading
  FAILED_TO_MENTION = 1 << 8,     // Failed to mention some roles
  SUPPRESS_NOTIFICATIONS = 1 << 12, // Do not trigger notifications
}

// Usage in your system
const MESSAGE_FLAGS = {
  DELETED: 1 << 0,        // Message is deleted
  EDITED: 1 << 1,         // Message has been edited
  PINNED: 1 << 2,         // Message is pinned
  SYSTEM: 1 << 3,         // System message
  SILENT: 1 << 4,         // Don't send notifications
  URGENT: 1 << 5,         // High priority message
} as const;

// Check if message is deleted
const isDeleted = (flags: number) => (flags & MESSAGE_FLAGS.DELETED) !== 0;

// Mark message as deleted
const markDeleted = (flags: number) => flags | MESSAGE_FLAGS.DELETED;

// Remove deleted flag
const unmarkDeleted = (flags: number) => flags & ~MESSAGE_FLAGS.DELETED;
```

## Prisma Schema Update

```prisma
// Updated Prisma model for Discord-inspired design
model v_chat_messages {
  message_id   BigInt   @id @default(autoincrement())
  room_uuid    String   @db.Uuid
  author_uuid  String   @db.Uuid
  content      String?  @db.Text
  message_type Int      @default(0) @db.SmallInt
  reply_to     BigInt?
  edited_at    BigInt?
  created_at   BigInt   @default(dbgenerated("EXTRACT(EPOCH FROM NOW())::BIGINT"))
  flags        Int      @default(0)
  
  // Relations
  room         v_chat_rooms @relation(fields: [room_uuid], references: [room_uuid], onDelete: Cascade)
  author       v_users      @relation(fields: [author_uuid], references: [user_uuid])
  reply_parent v_chat_messages? @relation("MessageReplies", fields: [reply_to], references: [message_id])
  replies      v_chat_messages[] @relation("MessageReplies")
  
  // Attachments (one-to-many)
  attachments  v_chat_message_attachments[]
  
  @@index([room_uuid, created_at(sort: Desc)])
  @@index([author_uuid])
  @@map("v_chat_messages")
}
```

## TypeScript Types for Your System

```typescript
// Discord-inspired message types
export interface ChatMessage {
  message_id: string;           // BigInt as string for JavaScript
  room_uuid: string;
  author_uuid: string;
  author_name?: string;         // Populated from join/cache
  author_avatar?: string;       // Populated from join/cache
  content: string | null;
  message_type: MessageType;
  reply_to?: string;
  edited_at?: number;           // Unix timestamp
  created_at: number;           // Unix timestamp
  flags: number;
  
  // Computed properties
  is_deleted?: boolean;         // Computed from flags
  is_edited?: boolean;          // Computed from edited_at
  is_pinned?: boolean;          // Computed from flags
  is_system?: boolean;          // Computed from flags
  
  // Relations (populated when needed)
  attachments?: MessageAttachment[];
  reactions?: MessageReaction[];
  reply_parent?: ChatMessage;
}

export enum MessageType {
  TEXT = 0,
  IMAGE = 1,
  FILE = 2,
  SYSTEM_USER_JOINED = 3,
  SYSTEM_USER_LEFT = 4,
  SYSTEM_CALL_STARTED = 5,
  SYSTEM_CALL_ENDED = 6,
}

export const MESSAGE_FLAGS = {
  DELETED: 1 << 0,
  EDITED: 1 << 1,
  PINNED: 1 << 2,
  SYSTEM: 1 << 3,
  SILENT: 1 << 4,
  URGENT: 1 << 5,
} as const;

// Helper functions
export const MessageUtils = {
  isDeleted: (flags: number) => (flags & MESSAGE_FLAGS.DELETED) !== 0,
  isEdited: (message: ChatMessage) => message.edited_at !== null,
  isPinned: (flags: number) => (flags & MESSAGE_FLAGS.PINNED) !== 0,
  isSystem: (flags: number) => (flags & MESSAGE_FLAGS.SYSTEM) !== 0,
  
  markDeleted: (flags: number) => flags | MESSAGE_FLAGS.DELETED,
  markPinned: (flags: number) => flags | MESSAGE_FLAGS.PINNED,
  
  formatTimestamp: (timestamp: number) => new Date(timestamp * 1000),
  
  getMessageTypeLabel: (type: MessageType) => {
    switch (type) {
      case MessageType.TEXT: return 'Text';
      case MessageType.IMAGE: return 'Image';
      case MessageType.FILE: return 'File';
      case MessageType.SYSTEM_USER_JOINED: return 'User Joined';
      case MessageType.SYSTEM_USER_LEFT: return 'User Left';
      default: return 'Unknown';
    }
  }
};
```

## API Usage Examples

```typescript
// Creating a message (Discord-inspired)
export const createMessage = async (messageData: {
  room_uuid: string;
  author_uuid: string;
  content: string;
  message_type?: MessageType;
  reply_to?: string;
}) => {
  const message = await prisma.v_chat_messages.create({
    data: {
      room_uuid: messageData.room_uuid,
      author_uuid: messageData.author_uuid,
      content: messageData.content,
      message_type: messageData.message_type || MessageType.TEXT,
      reply_to: messageData.reply_to ? BigInt(messageData.reply_to) : null,
      created_at: Math.floor(Date.now() / 1000), // Unix timestamp
      flags: 0
    }
  });
  
  return {
    ...message,
    message_id: message.message_id.toString(), // Convert BigInt to string
    reply_to: message.reply_to?.toString(),
    is_deleted: MessageUtils.isDeleted(message.flags),
    is_edited: MessageUtils.isEdited(message),
    is_pinned: MessageUtils.isPinned(message.flags),
    is_system: MessageUtils.isSystem(message.flags)
  };
};

// Soft delete a message (Discord style)
export const deleteMessage = async (messageId: string) => {
  await prisma.v_chat_messages.update({
    where: { message_id: BigInt(messageId) },
    data: {
      flags: { 
        set: MESSAGE_FLAGS.DELETED 
      }
    }
  });
};

// Edit a message
export const editMessage = async (messageId: string, newContent: string) => {
  await prisma.v_chat_messages.update({
    where: { message_id: BigInt(messageId) },
    data: {
      content: newContent,
      edited_at: Math.floor(Date.now() / 1000),
      flags: {
        set: MESSAGE_FLAGS.EDITED
      }
    }
  });
};
```

## Storage Savings Calculation

### For Your Expected Volume (5,000 messages/day)

**Original Design:**
- 5,000 messages × 200 bytes = 1,000 KB/day
- Monthly: ~30 MB
- Yearly: ~365 MB

**Discord-Inspired Design:**
- 5,000 messages × 70 bytes = 350 KB/day  
- Monthly: ~10.5 MB
- Yearly: ~128 MB

**Savings: 65% reduction in storage space**

## Benefits of Discord's Approach

1. **Smaller Storage**: 65% less space per message
2. **Faster Queries**: Smaller records = faster scans
3. **Better Caching**: More messages fit in memory
4. **Simpler Indexing**: Fewer columns to index
5. **Bitfield Flexibility**: Easy to add new flags without schema changes
6. **Integer Performance**: Integers are faster than UUIDs for joins

This Discord-inspired approach will make your chat system much more efficient while maintaining all the functionality you need!
