# Security & Permissions Design for Internal Chat System

## Authentication & Authorization Framework

### Domain-Based Security Model

The internal chat system implements a multi-layered security approach that ensures:
- **Domain Isolation**: Users can only access chat rooms within their domain
- **Role-Based Access Control**: Different permission levels for room management
- **Session-Based Authentication**: Integration with existing NextAuth system
- **Real-time Authorization**: WebSocket connection validation

### Authentication Flow

```typescript
// src/middleware/chat-auth.ts

export interface ChatAuthContext {
  user_uuid: string
  domain_uuid: string
  roles: string[]
  permissions: string[]
  session_token: string
}

export const authenticateChatUser = async (
  sessionToken: string
): Promise<ChatAuthContext | null> => {
  try {
    // Validate session using existing NextAuth system
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return null
    }

    // Get user details from database
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      include: {
        v_user_groups: {
          include: {
            v_group: {
              include: {
                v_group_permissions: {
                  include: {
                    v_permission: true
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!user || user.user_enabled !== 'true') {
      return null
    }

    // Extract roles and permissions
    const roles = user.v_user_groups.map(ug => ug.v_group.group_name)
    const permissions = user.v_user_groups
      .flatMap(ug => ug.v_group.v_group_permissions)
      .map(gp => gp.v_permission.permission_name)

    return {
      user_uuid: user.user_uuid,
      domain_uuid: user.domain_uuid!,
      roles,
      permissions,
      session_token: sessionToken
    }
  } catch (error) {
    console.error('Chat authentication error:', error)
    return null
  }
}

// WebSocket authentication middleware
export const authenticateSocketConnection = async (
  socket: Socket,
  next: (err?: Error) => void
) => {
  try {
    const token = socket.handshake.auth.token || socket.handshake.query.token
    const authContext = await authenticateChatUser(token)

    if (!authContext) {
      return next(new Error('Authentication failed'))
    }

    // Attach auth context to socket
    socket.data.auth = authContext
    next()
  } catch (error) {
    next(new Error('Authentication error'))
  }
}
```

### Permission System

```typescript
// src/types/apps/internal-chat/permissions.ts

export enum ChatPermissions {
  // Room permissions
  CREATE_ROOM = 'chat:create_room',
  DELETE_ROOM = 'chat:delete_room',
  MANAGE_ROOM = 'chat:manage_room',
  
  // Message permissions
  SEND_MESSAGE = 'chat:send_message',
  DELETE_ANY_MESSAGE = 'chat:delete_any_message',
  EDIT_ANY_MESSAGE = 'chat:edit_any_message',
  
  // Participant permissions
  ADD_PARTICIPANTS = 'chat:add_participants',
  REMOVE_PARTICIPANTS = 'chat:remove_participants',
  MANAGE_ROLES = 'chat:manage_roles',
  
  // File permissions
  UPLOAD_FILES = 'chat:upload_files',
  UPLOAD_LARGE_FILES = 'chat:upload_large_files',
  
  // Admin permissions
  VIEW_ALL_ROOMS = 'chat:view_all_rooms',
  MODERATE_CONTENT = 'chat:moderate_content',
  ACCESS_AUDIT_LOGS = 'chat:access_audit_logs'
}

export enum RoomRoles {
  OWNER = 'owner',
  ADMIN = 'admin',
  MODERATOR = 'moderator',
  MEMBER = 'member'
}

// Permission matrix for room roles
export const ROOM_ROLE_PERMISSIONS: Record<RoomRoles, ChatPermissions[]> = {
  [RoomRoles.OWNER]: [
    ChatPermissions.MANAGE_ROOM,
    ChatPermissions.DELETE_ROOM,
    ChatPermissions.ADD_PARTICIPANTS,
    ChatPermissions.REMOVE_PARTICIPANTS,
    ChatPermissions.MANAGE_ROLES,
    ChatPermissions.SEND_MESSAGE,
    ChatPermissions.DELETE_ANY_MESSAGE,
    ChatPermissions.EDIT_ANY_MESSAGE,
    ChatPermissions.UPLOAD_FILES,
    ChatPermissions.MODERATE_CONTENT
  ],
  [RoomRoles.ADMIN]: [
    ChatPermissions.MANAGE_ROOM,
    ChatPermissions.ADD_PARTICIPANTS,
    ChatPermissions.REMOVE_PARTICIPANTS,
    ChatPermissions.SEND_MESSAGE,
    ChatPermissions.DELETE_ANY_MESSAGE,
    ChatPermissions.UPLOAD_FILES,
    ChatPermissions.MODERATE_CONTENT
  ],
  [RoomRoles.MODERATOR]: [
    ChatPermissions.SEND_MESSAGE,
    ChatPermissions.DELETE_ANY_MESSAGE,
    ChatPermissions.UPLOAD_FILES,
    ChatPermissions.MODERATE_CONTENT
  ],
  [RoomRoles.MEMBER]: [
    ChatPermissions.SEND_MESSAGE,
    ChatPermissions.UPLOAD_FILES
  ]
}
```

### Authorization Middleware

```typescript
// src/middleware/chat-authorization.ts

export class ChatAuthorizationService {
  static async canAccessRoom(
    user_uuid: string,
    room_uuid: string
  ): Promise<boolean> {
    const participant = await prisma.v_chat_room_participants.findFirst({
      where: {
        user_uuid,
        room_uuid
      },
      include: {
        v_chat_rooms: true
      }
    })

    return !!participant && participant.v_chat_rooms.is_active
  }

  static async canPerformAction(
    user_uuid: string,
    room_uuid: string,
    action: ChatPermissions
  ): Promise<boolean> {
    // Get user's role in the room
    const participant = await prisma.v_chat_room_participants.findFirst({
      where: { user_uuid, room_uuid }
    })

    if (!participant) return false

    // Check if role has permission
    const rolePermissions = ROOM_ROLE_PERMISSIONS[participant.participant_role as RoomRoles]
    if (rolePermissions.includes(action)) return true

    // Check global permissions
    const user = await prisma.v_users.findUnique({
      where: { user_uuid },
      include: {
        v_user_groups: {
          include: {
            v_group: {
              include: {
                v_group_permissions: {
                  include: {
                    v_permission: true
                  }
                }
              }
            }
          }
        }
      }
    })

    const globalPermissions = user?.v_user_groups
      .flatMap(ug => ug.v_group.v_group_permissions)
      .map(gp => gp.v_permission.permission_name) || []

    return globalPermissions.includes(action)
  }

  static async canCreateRoom(
    user_uuid: string,
    room_type: string
  ): Promise<boolean> {
    // Check global permission
    const hasGlobalPermission = await this.hasGlobalPermission(
      user_uuid,
      ChatPermissions.CREATE_ROOM
    )

    if (hasGlobalPermission) return true

    // Department rooms might have special rules
    if (room_type === 'department') {
      return await this.canCreateDepartmentRoom(user_uuid)
    }

    return false
  }

  private static async hasGlobalPermission(
    user_uuid: string,
    permission: ChatPermissions
  ): Promise<boolean> {
    const user = await prisma.v_users.findUnique({
      where: { user_uuid },
      include: {
        v_user_groups: {
          include: {
            v_group: {
              include: {
                v_group_permissions: {
                  include: {
                    v_permission: true
                  }
                }
              }
            }
          }
        }
      }
    })

    const permissions = user?.v_user_groups
      .flatMap(ug => ug.v_group.v_group_permissions)
      .map(gp => gp.v_permission.permission_name) || []

    return permissions.includes(permission)
  }

  private static async canCreateDepartmentRoom(user_uuid: string): Promise<boolean> {
    // Check if user is department manager or has admin role
    const employee = await prisma.v_employee.findFirst({
      where: { user_uuid },
      include: {
        v_department: true
      }
    })

    // Add logic to check if user is department head/manager
    return !!employee?.v_department
  }
}

// Express middleware for API endpoints
export const requireChatPermission = (permission: ChatPermissions) => {
  return async (req: NextRequest, res: NextResponse, next: NextFunction) => {
    try {
      const session = await getSession()
      if (!session?.user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      const hasPermission = await ChatAuthorizationService.hasGlobalPermission(
        session.user.id,
        permission
      )

      if (!hasPermission) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      next()
    } catch (error) {
      return NextResponse.json({ error: 'Authorization error' }, { status: 500 })
    }
  }
}

// Room-specific authorization middleware
export const requireRoomAccess = (action: ChatPermissions) => {
  return async (req: NextRequest, res: NextResponse, next: NextFunction) => {
    try {
      const session = await getSession()
      const room_uuid = req.nextUrl.pathname.split('/').pop()

      if (!session?.user || !room_uuid) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      const canPerform = await ChatAuthorizationService.canPerformAction(
        session.user.id,
        room_uuid,
        action
      )

      if (!canPerform) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      next()
    } catch (error) {
      return NextResponse.json({ error: 'Authorization error' }, { status: 500 })
    }
  }
}
```

### Content Security & Validation

```typescript
// src/utils/chat-security.ts

export class ChatSecurityService {
  // Message content validation
  static validateMessageContent(content: string, message_type: string): {
    isValid: boolean
    sanitizedContent?: string
    errors?: string[]
  } {
    const errors: string[] = []
    let sanitizedContent = content

    // Length validation
    if (content.length > 4000) {
      errors.push('Message too long (max 4000 characters)')
    }

    // HTML sanitization for text messages
    if (message_type === 'text') {
      sanitizedContent = this.sanitizeHtml(content)
    }

    // Profanity filter (optional)
    if (this.containsProfanity(sanitizedContent)) {
      errors.push('Message contains inappropriate content')
    }

    // Spam detection
    if (this.isSpam(sanitizedContent)) {
      errors.push('Message detected as spam')
    }

    return {
      isValid: errors.length === 0,
      sanitizedContent: errors.length === 0 ? sanitizedContent : undefined,
      errors: errors.length > 0 ? errors : undefined
    }
  }

  // File upload validation
  static validateFileUpload(file: File, user_uuid: string): {
    isValid: boolean
    errors?: string[]
  } {
    const errors: string[] = []
    const maxSize = 50 * 1024 * 1024 // 50MB
    const allowedTypes = [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'application/pdf', 'text/plain',
      'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ]

    // Size validation
    if (file.size > maxSize) {
      errors.push('File too large (max 50MB)')
    }

    // Type validation
    if (!allowedTypes.includes(file.type)) {
      errors.push('File type not allowed')
    }

    // Filename validation
    if (!/^[a-zA-Z0-9._-]+$/.test(file.name)) {
      errors.push('Invalid filename characters')
    }

    return {
      isValid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined
    }
  }

  // Rate limiting
  static async checkRateLimit(
    user_uuid: string,
    action: 'message' | 'file_upload' | 'room_create',
    window_minutes: number = 1
  ): Promise<{ allowed: boolean; remaining: number }> {
    const limits = {
      message: 60,
      file_upload: 10,
      room_create: 5
    }

    const key = `rate_limit:${action}:${user_uuid}`
    const windowStart = new Date(Date.now() - window_minutes * 60 * 1000)

    // Count recent actions (implement with Redis or database)
    const recentActions = await this.countRecentActions(user_uuid, action, windowStart)
    const limit = limits[action]
    const remaining = Math.max(0, limit - recentActions)

    return {
      allowed: recentActions < limit,
      remaining
    }
  }

  // XSS prevention
  private static sanitizeHtml(content: string): string {
    // Use a library like DOMPurify or implement basic sanitization
    return content
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;')
  }

  // Basic profanity filter
  private static containsProfanity(content: string): boolean {
    // Implement profanity detection logic
    const profanityWords = ['badword1', 'badword2'] // Replace with actual list
    const lowerContent = content.toLowerCase()
    return profanityWords.some(word => lowerContent.includes(word))
  }

  // Spam detection
  private static isSpam(content: string): boolean {
    // Basic spam detection rules
    const spamPatterns = [
      /(.)\1{10,}/, // Repeated characters
      /https?:\/\/[^\s]+/gi, // Multiple URLs
      /\b(buy|sale|discount|offer)\b.*\b(now|today|urgent)\b/i // Promotional spam
    ]

    return spamPatterns.some(pattern => pattern.test(content))
  }

  private static async countRecentActions(
    user_uuid: string,
    action: string,
    since: Date
  ): Promise<number> {
    // Implement with your preferred storage (Redis, database, etc.)
    // This is a placeholder implementation
    return 0
  }
}
```

### Audit Logging

```typescript
// src/utils/chat-audit.ts

export enum AuditEventType {
  ROOM_CREATED = 'room_created',
  ROOM_DELETED = 'room_deleted',
  PARTICIPANT_ADDED = 'participant_added',
  PARTICIPANT_REMOVED = 'participant_removed',
  MESSAGE_SENT = 'message_sent',
  MESSAGE_EDITED = 'message_edited',
  MESSAGE_DELETED = 'message_deleted',
  FILE_UPLOADED = 'file_uploaded',
  PERMISSION_CHANGED = 'permission_changed'
}

export interface AuditEvent {
  event_type: AuditEventType
  user_uuid: string
  domain_uuid: string
  room_uuid?: string
  target_user_uuid?: string
  metadata: Record<string, any>
  ip_address?: string
  user_agent?: string
  timestamp: Date
}

export class ChatAuditService {
  static async logEvent(event: AuditEvent): Promise<void> {
    try {
      await prisma.v_chat_audit_logs.create({
        data: {
          event_type: event.event_type,
          user_uuid: event.user_uuid,
          domain_uuid: event.domain_uuid,
          room_uuid: event.room_uuid,
          target_user_uuid: event.target_user_uuid,
          metadata: event.metadata,
          ip_address: event.ip_address,
          user_agent: event.user_agent,
          insert_date: event.timestamp
        }
      })
    } catch (error) {
      console.error('Failed to log audit event:', error)
    }
  }

  static async getAuditLogs(
    domain_uuid: string,
    filters: {
      user_uuid?: string
      room_uuid?: string
      event_type?: AuditEventType
      start_date?: Date
      end_date?: Date
    },
    pagination: { page: number; limit: number }
  ): Promise<{ logs: any[]; total: number }> {
    const where: any = { domain_uuid }

    if (filters.user_uuid) where.user_uuid = filters.user_uuid
    if (filters.room_uuid) where.room_uuid = filters.room_uuid
    if (filters.event_type) where.event_type = filters.event_type
    if (filters.start_date || filters.end_date) {
      where.insert_date = {}
      if (filters.start_date) where.insert_date.gte = filters.start_date
      if (filters.end_date) where.insert_date.lte = filters.end_date
    }

    const [logs, total] = await Promise.all([
      prisma.v_chat_audit_logs.findMany({
        where,
        include: {
          user: { select: { username: true } },
          target_user: { select: { username: true } },
          room: { select: { room_name: true } }
        },
        orderBy: { insert_date: 'desc' },
        skip: (pagination.page - 1) * pagination.limit,
        take: pagination.limit
      }),
      prisma.v_chat_audit_logs.count({ where })
    ])

    return { logs, total }
  }
}
```

### Data Privacy & Compliance

```typescript
// src/utils/chat-privacy.ts

export class ChatPrivacyService {
  // GDPR compliance - user data export
  static async exportUserData(user_uuid: string): Promise<{
    rooms: any[]
    messages: any[]
    files: any[]
  }> {
    const [rooms, messages, files] = await Promise.all([
      // Export rooms where user is participant
      prisma.v_chat_room_participants.findMany({
        where: { user_uuid },
        include: { v_chat_rooms: true }
      }),
      
      // Export user's messages
      prisma.v_chat_messages.findMany({
        where: { sender_user_uuid: user_uuid },
        include: { v_chat_message_attachments: true }
      }),
      
      // Export uploaded files
      prisma.v_chat_message_attachments.findMany({
        where: {
          v_chat_messages: {
            sender_user_uuid: user_uuid
          }
        }
      })
    ])

    return { rooms, messages, files }
  }

  // GDPR compliance - user data deletion
  static async deleteUserData(user_uuid: string): Promise<void> {
    await prisma.$transaction(async (tx) => {
      // Anonymize messages instead of deleting to preserve conversation flow
      await tx.v_chat_messages.updateMany({
        where: { sender_user_uuid: user_uuid },
        data: {
          message_content: '[Message deleted - user account removed]',
          sender_user_uuid: 'anonymous',
          is_deleted: true
        }
      })

      // Remove from room participants
      await tx.v_chat_room_participants.deleteMany({
        where: { user_uuid }
      })

      // Delete presence data
      await tx.v_chat_user_presence.deleteMany({
        where: { user_uuid }
      })

      // Delete notifications
      await tx.v_chat_notifications.deleteMany({
        where: { user_uuid }
      })
    })
  }

  // Message retention policy
  static async cleanupOldMessages(retention_days: number = 365): Promise<void> {
    const cutoffDate = new Date(Date.now() - retention_days * 24 * 60 * 60 * 1000)

    await prisma.v_chat_messages.deleteMany({
      where: {
        insert_date: { lt: cutoffDate },
        is_deleted: true
      }
    })
  }
}
