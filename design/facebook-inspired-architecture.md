# Facebook-Inspired Chat Architecture for AgentDeskLite

## Scale Comparison

### Facebook Scale
- **60+ billion messages/day**
- **2+ billion active users**
- **Thousands of database servers**

### Your Expected Scale
- **~5,000 messages/day** (100 users × 50 messages)
- **100-1000 users per domain**
- **Single database server**

## Facebook's Core Strategies Applied to Your System

### 1. Message Storage Optimization

#### Facebook's Approach: Minimal Message Records
```sql
-- Facebook stores minimal data per message
CREATE TABLE messages_optimized (
    message_id BIGINT PRIMARY KEY,           -- 8 bytes
    conversation_id BIGINT NOT NULL,         -- 8 bytes  
    sender_id BIGINT NOT NULL,               -- 8 bytes
    message_text TEXT,                       -- Variable
    timestamp BIGINT NOT NULL,               -- 8 bytes (Unix timestamp)
    message_type TINYINT DEFAULT 0           -- 1 byte (0=text, 1=image, etc.)
);
-- Total: ~33 bytes + message content per record
```

#### Our Optimized Version
```sql
-- Optimized for your scale
CREATE TABLE v_chat_messages_optimized (
    message_id BIGSERIAL PRIMARY KEY,                    -- 8 bytes
    room_id UUID NOT NULL,                               -- 16 bytes
    sender_id UUID NOT NULL,                             -- 16 bytes
    content TEXT,                                        -- Variable
    created_at BIGINT NOT NULL,                          -- 8 bytes (Unix timestamp)
    msg_type SMALLINT DEFAULT 0,                         -- 2 bytes
    reply_to BIGINT,                                     -- 8 bytes (nullable)
    
    -- Indexes
    INDEX idx_room_time (room_id, created_at DESC),
    INDEX idx_sender (sender_id)
);
-- ~58 bytes + content per message (vs 200+ bytes in original design)
```

### 2. Conversation Metadata (Facebook's Secret Sauce)

#### Separate Conversation State from Messages
```sql
-- Facebook stores conversation metadata separately
CREATE TABLE v_chat_conversations (
    conversation_id UUID PRIMARY KEY,
    room_uuid UUID NOT NULL,
    participant_count SMALLINT DEFAULT 2,
    last_message_id BIGINT,
    last_message_preview TEXT(100),          -- First 100 chars
    last_activity BIGINT NOT NULL,
    unread_count JSONB,                      -- {user_id: count, user_id: count}
    is_archived BOOLEAN DEFAULT false,
    
    INDEX idx_room_activity (room_uuid, last_activity DESC)
);
```

### 3. Message Batching (WhatsApp Style)

#### Batch Insert Messages
```typescript
// Instead of inserting one message at a time
class MessageBatcher {
  private batch: MessageData[] = []
  private readonly BATCH_SIZE = 100
  private readonly FLUSH_INTERVAL = 1000 // 1 second

  constructor() {
    setInterval(() => this.flush(), this.FLUSH_INTERVAL)
  }

  addMessage(message: MessageData) {
    this.batch.push(message)
    
    if (this.batch.length >= this.BATCH_SIZE) {
      this.flush()
    }
  }

  private async flush() {
    if (this.batch.length === 0) return

    const messages = [...this.batch]
    this.batch = []

    try {
      // Single batch insert instead of 100 individual inserts
      await prisma.v_chat_messages_optimized.createMany({
        data: messages
      })

      // Update conversation metadata
      await this.updateConversationMetadata(messages)
      
      // Broadcast to real-time listeners
      this.broadcastMessages(messages)
    } catch (error) {
      console.error('Batch insert failed:', error)
      // Re-queue failed messages
      this.batch.unshift(...messages)
    }
  }
}
```

### 4. Smart Caching Strategy (Discord Inspired)

#### Three-Tier Caching
```typescript
class ChatCacheManager {
  private redis: Redis
  private memoryCache: LRU<string, any>

  constructor() {
    this.redis = new Redis(process.env.REDIS_URL)
    this.memoryCache = new LRU({ max: 1000 }) // 1000 conversations in memory
  }

  // Tier 1: Memory cache (fastest)
  async getRecentMessages(roomId: string, limit: number = 50): Promise<Message[]> {
    const cacheKey = `room:${roomId}:recent`
    
    // Check memory first
    let messages = this.memoryCache.get(cacheKey)
    if (messages) return messages

    // Check Redis second
    messages = await this.redis.get(cacheKey)
    if (messages) {
      const parsed = JSON.parse(messages)
      this.memoryCache.set(cacheKey, parsed)
      return parsed
    }

    // Database last resort
    messages = await prisma.v_chat_messages_optimized.findMany({
      where: { room_id: roomId },
      orderBy: { created_at: 'desc' },
      take: limit
    })

    // Cache for future requests
    await this.redis.setex(cacheKey, 300, JSON.stringify(messages)) // 5 min
    this.memoryCache.set(cacheKey, messages)
    
    return messages
  }

  // Invalidate cache when new message arrives
  async invalidateRoomCache(roomId: string) {
    const cacheKey = `room:${roomId}:recent`
    this.memoryCache.delete(cacheKey)
    await this.redis.del(cacheKey)
  }
}
```

### 5. Message History Limits (Discord Style)

#### Implement Message Limits per Room
```typescript
// Limit message history to prevent infinite growth
const MESSAGE_LIMITS = {
  direct: 10000,      // Direct messages: 10k messages
  group: 5000,        // Group chats: 5k messages  
  department: 20000,  // Department: 20k messages
  broadcast: 1000     // Broadcast: 1k messages
}

class MessageLimitManager {
  async enforceMessageLimit(roomId: string, roomType: string) {
    const limit = MESSAGE_LIMITS[roomType] || 5000
    
    // Count current messages
    const messageCount = await prisma.v_chat_messages_optimized.count({
      where: { room_id: roomId }
    })

    if (messageCount > limit) {
      // Archive oldest messages
      const excessCount = messageCount - limit
      const oldestMessages = await prisma.v_chat_messages_optimized.findMany({
        where: { room_id: roomId },
        orderBy: { created_at: 'asc' },
        take: excessCount,
        select: { message_id: true }
      })

      // Move to archive
      await this.archiveMessages(oldestMessages.map(m => m.message_id))
      
      // Delete from main table
      await prisma.v_chat_messages_optimized.deleteMany({
        where: {
          message_id: { in: oldestMessages.map(m => m.message_id) }
        }
      })
    }
  }
}
```

### 6. Lazy Loading Strategy

#### Load Messages on Demand
```typescript
// Frontend: Load messages progressively
const useMessageHistory = (roomId: string) => {
  const [messages, setMessages] = useState<Message[]>([])
  const [hasMore, setHasMore] = useState(true)
  const [loading, setLoading] = useState(false)

  // Load initial messages (most recent 50)
  useEffect(() => {
    loadRecentMessages()
  }, [roomId])

  const loadRecentMessages = async () => {
    const recent = await api.get(`/chat/rooms/${roomId}/messages?limit=50`)
    setMessages(recent.data)
    setHasMore(recent.data.length === 50)
  }

  // Load older messages when user scrolls up
  const loadMoreMessages = async () => {
    if (loading || !hasMore) return
    
    setLoading(true)
    const oldestMessageId = messages[messages.length - 1]?.message_id
    
    const older = await api.get(
      `/chat/rooms/${roomId}/messages?before=${oldestMessageId}&limit=50`
    )
    
    setMessages(prev => [...prev, ...older.data])
    setHasMore(older.data.length === 50)
    setLoading(false)
  }

  return { messages, loadMoreMessages, hasMore, loading }
}
```

## Storage Size Comparison

### Original Design (Per Message)
```
- message_uuid: 36 bytes (UUID string)
- room_uuid: 36 bytes  
- sender_user_uuid: 36 bytes
- message_content: Variable
- message_type: 50 bytes (VARCHAR)
- message_metadata: Variable (JSONB)
- timestamps: 16 bytes
- Total: ~174 bytes + content + metadata
```

### Optimized Design (Per Message)
```
- message_id: 8 bytes (BIGSERIAL)
- room_id: 16 bytes (UUID binary)
- sender_id: 16 bytes (UUID binary)
- content: Variable
- created_at: 8 bytes (Unix timestamp)
- msg_type: 2 bytes (SMALLINT)
- reply_to: 8 bytes (nullable)
- Total: ~58 bytes + content
```

**Storage Reduction: 67% smaller per message**

## Database Growth Projection

### With Original Design
- 5,000 messages/day × 174 bytes = 870 KB/day
- Monthly: ~26 MB
- Yearly: ~318 MB (just message records)

### With Optimized Design  
- 5,000 messages/day × 58 bytes = 290 KB/day
- Monthly: ~8.7 MB
- Yearly: ~106 MB (67% reduction)

## Implementation Strategy

### Phase 1: Core Optimization
1. Implement optimized message table
2. Add conversation metadata table
3. Set up message batching
4. Implement basic caching

### Phase 2: Advanced Features
1. Add message history limits
2. Implement lazy loading
3. Set up archiving system
4. Add performance monitoring

### Phase 3: Scale Preparation
1. Implement sharding (if needed)
2. Add advanced caching layers
3. Set up monitoring and alerts
4. Optimize for your specific usage patterns

This Facebook-inspired approach will handle your current scale efficiently while providing room for growth to 10x or even 100x your current expected volume.
