# Frontend Component Architecture for Internal Chat System

## Component Hierarchy

```
InternalChatApp
├── ChatLayout
│   ├── ChatSidebar
│   │   ├── UserProfile
│   │   ├── RoomList
│   │   │   ├── RoomListHeader
│   │   │   ├── RoomSearch
│   │   │   ├── RoomItem
│   │   │   └── CreateRoomButton
│   │   ├── DirectMessagesList
│   │   │   ├── UserSearch
│   │   │   └── UserItem
│   │   └── OnlineUsersList
│   │       └── OnlineUserItem
│   ├── ChatMain
│   │   ├── ChatHeader
│   │   │   ├── RoomInfo
│   │   │   ├── ParticipantsList
│   │   │   └── RoomActions
│   │   ├── MessageList
│   │   │   ├── MessageGroup
│   │   │   │   ├── MessageItem
│   │   │   │   │   ├── MessageContent
│   │   │   │   │   ├── MessageAttachment
│   │   │   │   │   ├── MessageReactions
│   │   │   │   │   └── MessageActions
│   │   │   │   └── MessageThread
│   │   │   ├── TypingIndicator
│   │   │   └── MessageSeparator
│   │   ├── MessageInput
│   │   │   ├── TextEditor
│   │   │   ├── AttachmentButton
│   │   │   ├── EmojiPicker
│   │   │   └── SendButton
│   │   └── MessageThread (Modal/Sidebar)
│   └── ChatNotifications
│       ├── NotificationToast
│       └── NotificationBadge
```

## Core Components Design

### 1. InternalChatApp (Main Container)

```typescript
// src/views/apps/internal-chat/InternalChatApp.tsx
interface InternalChatAppProps {
  token: sessionToken
}

const InternalChatApp: React.FC<InternalChatAppProps> = ({ token }) => {
  // Initialize WebSocket connection
  // Set up global chat state
  // Handle authentication
  // Manage presence status
}
```

### 2. ChatLayout (Layout Manager)

```typescript
// src/views/apps/internal-chat/ChatLayout.tsx
interface ChatLayoutProps {
  isMobile: boolean
  sidebarOpen: boolean
  setSidebarOpen: (open: boolean) => void
}

const ChatLayout: React.FC<ChatLayoutProps> = ({
  isMobile,
  sidebarOpen,
  setSidebarOpen
}) => {
  // Responsive layout management
  // Sidebar toggle functionality
  // Main chat area rendering
}
```

### 3. ChatSidebar (Navigation & Room List)

```typescript
// src/views/apps/internal-chat/ChatSidebar.tsx
interface ChatSidebarProps {
  currentUser: ProfileUserType
  rooms: ChatRoom[]
  activeRoomId: string | null
  onRoomSelect: (roomId: string) => void
  onCreateRoom: () => void
}

const ChatSidebar: React.FC<ChatSidebarProps> = ({
  currentUser,
  rooms,
  activeRoomId,
  onRoomSelect,
  onCreateRoom
}) => {
  // Room list management
  // User search functionality
  // Online users display
  // Room creation interface
}
```

### 4. MessageList (Message Display)

```typescript
// src/views/apps/internal-chat/MessageList.tsx
interface MessageListProps {
  roomId: string
  messages: ChatMessage[]
  currentUser: ProfileUserType
  onLoadMore: () => void
  onMessageReact: (messageId: string, emoji: string) => void
  onMessageReply: (messageId: string) => void
  onMessageEdit: (messageId: string, content: string) => void
  onMessageDelete: (messageId: string) => void
}

const MessageList: React.FC<MessageListProps> = ({
  roomId,
  messages,
  currentUser,
  onLoadMore,
  onMessageReact,
  onMessageReply,
  onMessageEdit,
  onMessageDelete
}) => {
  // Virtual scrolling for performance
  // Message grouping by sender/time
  // Infinite scroll for message history
  // Real-time message updates
}
```

### 5. MessageInput (Message Composition)

```typescript
// src/views/apps/internal-chat/MessageInput.tsx
interface MessageInputProps {
  roomId: string
  replyToMessage?: ChatMessage
  onSendMessage: (content: string, type: MessageType, metadata?: any) => void
  onCancelReply: () => void
  onFileUpload: (files: File[]) => void
}

const MessageInput: React.FC<MessageInputProps> = ({
  roomId,
  replyToMessage,
  onSendMessage,
  onCancelReply,
  onFileUpload
}) => {
  // Rich text editing
  // File drag & drop
  // Emoji picker integration
  // Typing indicators
  // Message formatting (bold, italic, code)
}
```

## State Management Architecture

### Redux Store Structure

```typescript
// src/redux-store/slices/internal-chat/internalChatSlice.ts

interface InternalChatState {
  // Connection state
  isConnected: boolean
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error'
  
  // Current user
  currentUser: ProfileUserType | null
  
  // Rooms
  rooms: Record<string, ChatRoom>
  activeRoomId: string | null
  roomsLoading: boolean
  
  // Messages
  messages: Record<string, ChatMessage[]> // roomId -> messages
  messagesLoading: Record<string, boolean>
  hasMoreMessages: Record<string, boolean>
  
  // Users & Presence
  domainUsers: Record<string, DomainUser>
  userPresence: Record<string, UserPresence>
  onlineUsers: string[]
  
  // UI State
  sidebarOpen: boolean
  selectedThread: string | null
  typingUsers: Record<string, string[]> // roomId -> userIds
  
  // Notifications
  unreadCounts: Record<string, number> // roomId -> count
  notifications: ChatNotification[]
  
  // File uploads
  uploadProgress: Record<string, number>
}
```

### Action Creators

```typescript
// Connection actions
export const connectToChat = createAsyncThunk(...)
export const disconnectFromChat = createAsyncThunk(...)

// Room actions
export const fetchRooms = createAsyncThunk(...)
export const createRoom = createAsyncThunk(...)
export const joinRoom = createAsyncThunk(...)
export const leaveRoom = createAsyncThunk(...)
export const updateRoom = createAsyncThunk(...)

// Message actions
export const fetchMessages = createAsyncThunk(...)
export const sendMessage = createAsyncThunk(...)
export const editMessage = createAsyncThunk(...)
export const deleteMessage = createAsyncThunk(...)
export const reactToMessage = createAsyncThunk(...)

// Real-time actions (from WebSocket)
export const receiveMessage = createAction<ChatMessage>('chat/receiveMessage')
export const userTyping = createAction<{roomId: string, userId: string}>('chat/userTyping')
export const userStoppedTyping = createAction<{roomId: string, userId: string}>('chat/userStoppedTyping')
export const userPresenceChanged = createAction<UserPresence>('chat/userPresenceChanged')
```

## Type Definitions

```typescript
// src/types/apps/internal-chat/chatTypes.ts

export interface ChatRoom {
  room_uuid: string
  domain_uuid: string
  room_name: string
  room_description?: string
  room_type: 'direct' | 'group' | 'department' | 'broadcast'
  room_avatar?: string
  created_by_user_uuid: string
  is_active: boolean
  is_archived: boolean
  participants: ChatParticipant[]
  last_message?: ChatMessage
  unread_count: number
  insert_date: string
  update_date: string
}

export interface ChatParticipant {
  participant_uuid: string
  user_uuid: string
  user_name: string
  user_avatar?: string
  participant_role: 'owner' | 'admin' | 'moderator' | 'member'
  joined_date: string
  last_read_message_uuid?: string
  is_muted: boolean
}

export interface ChatMessage {
  message_uuid: string
  room_uuid: string
  sender_user_uuid: string
  sender_name: string
  sender_avatar?: string
  parent_message_uuid?: string
  message_content: string
  message_type: 'text' | 'file' | 'image' | 'video' | 'audio' | 'system' | 'call'
  message_metadata: Record<string, any>
  is_edited: boolean
  is_deleted: boolean
  reactions: MessageReaction[]
  attachments: MessageAttachment[]
  insert_date: string
  edited_date?: string
}

export interface MessageReaction {
  reaction_uuid: string
  user_uuid: string
  user_name: string
  reaction_emoji: string
  insert_date: string
}

export interface MessageAttachment {
  attachment_uuid: string
  file_name: string
  file_path: string
  file_size: number
  file_type: string
  mime_type: string
  thumbnail_path?: string
}

export interface UserPresence {
  user_uuid: string
  status: 'online' | 'away' | 'busy' | 'offline'
  last_seen: string
  current_activity?: string
}

export interface DomainUser {
  user_uuid: string
  user_name: string
  user_email: string
  user_avatar?: string
  department?: string
  role?: string
}

export interface ChatNotification {
  notification_uuid: string
  room_uuid: string
  room_name: string
  message_uuid: string
  notification_type: 'message' | 'mention' | 'reply' | 'room_invite'
  is_read: boolean
  insert_date: string
}
```

## Custom Hooks

```typescript
// src/hooks/internal-chat/useChatConnection.ts
export const useChatConnection = (domainId: string, userId: string) => {
  // WebSocket connection management
  // Auto-reconnection logic
  // Connection status tracking
}

// src/hooks/internal-chat/useChatRoom.ts
export const useChatRoom = (roomId: string) => {
  // Room-specific state management
  // Message loading and pagination
  // Typing indicators
  // Presence tracking
}

// src/hooks/internal-chat/useMessageInput.ts
export const useMessageInput = (roomId: string) => {
  // Message composition state
  // File upload handling
  // Emoji picker integration
  // Draft message persistence
}

// src/hooks/internal-chat/useNotifications.ts
export const useNotifications = () => {
  // Browser notification permissions
  // Push notification handling
  // Notification sound management
  // Badge count updates
}
```

## Responsive Design Considerations

### Mobile Layout
- **Collapsible sidebar**: Overlay on mobile, persistent on desktop
- **Touch-friendly interactions**: Larger touch targets, swipe gestures
- **Optimized message list**: Virtual scrolling, lazy loading
- **Mobile-first input**: Optimized keyboard handling

### Desktop Layout
- **Multi-panel layout**: Sidebar + main chat + optional thread panel
- **Keyboard shortcuts**: Quick navigation, message actions
- **Drag & drop**: File uploads, message organization
- **Context menus**: Right-click actions for messages and rooms

## Performance Optimizations

### Message List Virtualization
```typescript
// Use react-window for large message lists
import { FixedSizeList as List } from 'react-window'

const VirtualizedMessageList = ({ messages, height }) => (
  <List
    height={height}
    itemCount={messages.length}
    itemSize={80}
    itemData={messages}
  >
    {MessageRow}
  </List>
)
```

### Image Lazy Loading
```typescript
// Lazy load message images and attachments
const LazyImage = ({ src, alt, ...props }) => {
  const [isLoaded, setIsLoaded] = useState(false)
  const [isInView, setIsInView] = useState(false)
  const imgRef = useRef()
  
  useEffect(() => {
    const observer = new IntersectionObserver(...)
    // Implement intersection observer logic
  }, [])
  
  return isInView ? <img src={src} alt={alt} {...props} /> : <div>Loading...</div>
}
```

### Message Caching
```typescript
// Cache messages in localStorage for offline access
const useMessageCache = (roomId: string) => {
  const cacheKey = `chat_messages_${roomId}`
  
  const getCachedMessages = () => {
    const cached = localStorage.getItem(cacheKey)
    return cached ? JSON.parse(cached) : []
  }
  
  const setCachedMessages = (messages: ChatMessage[]) => {
    localStorage.setItem(cacheKey, JSON.stringify(messages))
  }
  
  return { getCachedMessages, setCachedMessages }
}
```

## Integration Points

### Agent System Integration
- **Status synchronization**: Chat availability based on agent status
- **Queue integration**: Chat notifications during calls
- **Presence updates**: Automatic status updates from agent system

### Notification System Integration
- **Browser notifications**: Desktop notifications for new messages
- **Email notifications**: Digest emails for missed messages
- **Mobile push**: Integration with mobile app notifications

### File Storage Integration
- **Secure uploads**: Integration with existing file storage system
- **Thumbnail generation**: Automatic thumbnails for images/videos
- **Virus scanning**: File security validation
