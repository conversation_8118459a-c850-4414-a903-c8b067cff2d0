# ZALO Integration Enhancement Plan

## Current State Analysis

### Existing ZALO Integration
The current ZALO integration has two implementations:
1. **Legacy**: `/api/social/zalo/webhook` - Basic webhook handling
2. **New**: `/api/zalo-oa/webhook` - Integrated with internal chat system

### Issues Identified
1. **Dual Implementation**: Two webhook endpoints causing confusion
2. **Inconsistent Patterns**: Not fully aligned with Telegram integration approach
3. **Missing Features**: Incomplete real-time integration and configuration management
4. **OAuth Management**: Basic OAuth handling without dynamic configuration per domain

## Enhancement Strategy

### 1. Consolidate to Single Implementation
- **Keep**: `/api/internal-chat/zalo/webhook` (new unified endpoint)
- **Deprecate**: `/api/social/zalo/webhook` and `/api/zalo-oa/webhook`
- **Migrate**: Existing data and functionality to new unified system

### 2. Align with Telegram Pattern
Follow the successful Telegram integration architecture:
- Dynamic configuration management per domain
- Unified message processing service
- Consistent real-time broadcasting
- Standardized error handling

### 3. Enhanced Database Schema
Improve existing ZALO tables to match Telegram patterns:

```sql
-- Enhanced ZALO OA Configuration Table (New)
CREATE TABLE v_zalo_oa_config (
    config_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain_uuid UUID NOT NULL UNIQUE,
    app_id VARCHAR(255) NOT NULL,
    app_secret VARCHAR(500) NOT NULL,
    oa_id VARCHAR(255) NOT NULL,
    access_token VARCHAR(1000) NOT NULL,
    refresh_token VARCHAR(1000),
    webhook_url VARCHAR(500),
    webhook_secret VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    token_expires_at TIMESTAMPTZ,
    allowed_events JSONB DEFAULT '["user_send_text", "user_send_image", "user_send_file"]',
    oa_settings JSONB DEFAULT '{}',
    insert_date TIMESTAMPTZ DEFAULT NOW(),
    insert_user UUID,
    update_date TIMESTAMPTZ DEFAULT NOW(),
    update_user UUID,

    CONSTRAINT unique_domain_zalo_oa UNIQUE (domain_uuid)
);

-- Enhanced ZALO Contacts Table (Update existing)
-- Add missing fields to match Telegram pattern
ALTER TABLE v_zalo_oa_contacts 
ADD COLUMN IF NOT EXISTS username VARCHAR(255),
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS contact_metadata JSONB DEFAULT '{}';

-- Enhanced ZALO Message Mapping (Update existing)
-- Add missing fields for better tracking
ALTER TABLE v_zalo_message_mapping
ADD COLUMN IF NOT EXISTS zalo_event_type VARCHAR(100),
ADD COLUMN IF NOT EXISTS message_metadata JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS retry_count INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_retry_at TIMESTAMPTZ;
```

## Implementation Plan

### Phase 1: Configuration Management Enhancement

#### 1.1 Create ZALO Configuration API
```typescript
// /api/internal-chat/zalo/oa-config
export class ZaloOaConfigAPI {
  async GET() // Get current configuration
  async POST() // Create/update configuration
  async PUT() // Update settings
  async DELETE() // Remove configuration
}
```

#### 1.2 Dynamic Token Management
```typescript
export class ZaloTokenService {
  static async getAccessToken(domain_uuid: string): Promise<string>
  static async refreshToken(domain_uuid: string): Promise<void>
  static async validateToken(domain_uuid: string): Promise<boolean>
}
```

### Phase 2: Unified Webhook Implementation

#### 2.1 New Webhook Endpoint
```typescript
// /api/internal-chat/zalo/webhook
export async function POST(request: NextRequest) {
  // 1. Validate webhook signature
  // 2. Log webhook event
  // 3. Process message using unified service
  // 4. Broadcast via Socket.IO
  // 5. Return success response
}
```

#### 2.2 Enhanced Message Service
```typescript
export class ZaloMessageService {
  static async processInboundMessage(domain_uuid: string, webhookEvent: ZaloWebhookEvent)
  static async processOutboundMessage(domain_uuid: string, room_uuid: string, content: string)
  static async findOrCreateContact(domain_uuid: string, zaloUser: ZaloUser)
  static async findOrCreateRoom(domain_uuid: string, contact_uuid: string)
  static async sendToZaloApi(domain_uuid: string, oa_id: string, message: string)
}
```

### Phase 3: Real-time Integration Enhancement

#### 3.1 Socket.IO Integration
Align with Telegram's broadcasting pattern:
```typescript
// Emit events using main chat system's broadcasting
(global as any).socketBroadcast.broadcastMessage(
  room_uuid,
  messageWithUnreadCount,
  domain_uuid
)
```

#### 3.2 Notification System
- Desktop notifications for new ZALO messages
- Unread count management
- Agent assignment notifications
- Delivery status updates

### Phase 4: Migration Strategy

#### 4.1 Data Migration
```sql
-- Migrate existing v_zalo_oauth to new v_zalo_oa_config
INSERT INTO v_zalo_oa_config (
    domain_uuid, app_id, app_secret, access_token, 
    token_expires_at, is_active
)
SELECT 
    domain_uuid, app_id, app_secret, access_token,
    expires_at, true
FROM v_zalo_oauth
WHERE access_token IS NOT NULL;
```

#### 4.2 Endpoint Migration
1. **Phase 1**: Deploy new endpoint alongside existing ones
2. **Phase 2**: Update webhook URLs in ZALO OA settings
3. **Phase 3**: Monitor traffic and validate functionality
4. **Phase 4**: Deprecate old endpoints

## Enhanced Features

### 1. Advanced Configuration
- **Multi-OA Support**: Support multiple ZALO OAs per domain
- **Webhook Validation**: Signature verification for security
- **Token Auto-refresh**: Automatic token renewal
- **Health Monitoring**: Connection status tracking

### 2. Improved Message Handling
- **Rich Media Support**: Images, files, stickers
- **Message Templates**: Quick replies and structured messages
- **Delivery Tracking**: Read receipts and delivery confirmations
- **Error Recovery**: Retry mechanisms for failed messages

### 3. Agent Experience
- **Unified Interface**: ZALO conversations in main chat interface
- **Contact Information**: Rich user profiles from ZALO
- **Message History**: Complete conversation history
- **File Sharing**: Support for media attachments

### 4. Analytics and Monitoring
- **Message Metrics**: Volume, response times, success rates
- **Agent Performance**: Response times, resolution rates
- **System Health**: API status, webhook reliability
- **Error Tracking**: Failed messages, retry statistics

## Security Enhancements

### 1. Webhook Security
- **Signature Verification**: Validate ZALO webhook signatures
- **Rate Limiting**: Prevent webhook abuse
- **IP Whitelisting**: Restrict webhook sources
- **Payload Validation**: Strict input validation

### 2. Token Security
- **Encryption**: Encrypt access tokens in database
- **Rotation**: Regular token rotation
- **Scope Limitation**: Minimal required permissions
- **Audit Logging**: Track token usage

### 3. Domain Isolation
- **Strict Validation**: Prevent cross-domain access
- **Data Segregation**: Complete domain isolation
- **Access Control**: Role-based permissions
- **Audit Trail**: Complete operation logging

## Testing Strategy

### 1. Migration Testing
- **Data Integrity**: Verify migration accuracy
- **Functionality**: Test all features post-migration
- **Performance**: Ensure no performance degradation
- **Rollback**: Test rollback procedures

### 2. Integration Testing
- **End-to-End**: Complete message flow testing
- **Real-time**: Socket.IO broadcasting validation
- **Error Handling**: Failure scenario testing
- **Load Testing**: High-volume message handling

### 3. User Acceptance Testing
- **Agent Interface**: Test from agent perspective
- **Customer Experience**: Validate customer-facing functionality
- **Admin Features**: Test configuration and monitoring
- **Mobile Compatibility**: Test on mobile devices

## Deployment Plan

### 1. Pre-deployment
- **Database Migration**: Run schema updates
- **Configuration**: Set up environment variables
- **Testing**: Complete integration testing
- **Documentation**: Update API documentation

### 2. Deployment
- **Blue-Green**: Deploy to staging environment first
- **Gradual Rollout**: Phase deployment by domain
- **Monitoring**: Real-time monitoring during rollout
- **Rollback Plan**: Ready rollback procedures

### 3. Post-deployment
- **Monitoring**: 24/7 monitoring for first week
- **Support**: Dedicated support for issues
- **Optimization**: Performance tuning based on usage
- **Documentation**: Update user guides

## Success Metrics

### 1. Technical Metrics
- **Message Delivery**: >99% success rate
- **Response Time**: <500ms webhook processing
- **Uptime**: >99.9% availability
- **Error Rate**: <0.1% error rate

### 2. User Experience Metrics
- **Agent Satisfaction**: Survey feedback >4.5/5
- **Response Time**: Improved agent response times
- **Feature Adoption**: >80% feature utilization
- **Support Tickets**: <5% increase in support requests

### 3. Business Metrics
- **Customer Satisfaction**: Improved CSAT scores
- **Operational Efficiency**: Reduced manual work
- **Cost Reduction**: Lower operational costs
- **Scalability**: Support for 10x message volume
