# Internal Domain Chat System - Final Design Summary

## Overview

This document summarizes the complete design for an internal domain chat system that integrates seamlessly with the existing AgentDeskLite architecture. The design is inspired by <PERSON><PERSON>'s efficient approach while maintaining compatibility with your current system patterns.

## Key Design Decisions

### 1. Discord-Inspired Database Optimization
- **65% storage reduction**: From ~200 bytes to ~70 bytes per message
- **Minimal schema**: Essential data only, no redundant metadata
- **Integer types**: SMALLINT for message types, INTEGER for flags
- **Unix timestamps**: BIGINT instead of TIMESTAMPTZ for better performance
- **Sequential IDs**: BIGSERIAL for messages, UUID only for compatibility

### 2. Smart Notification Strategy
- **Selective notifications**: Only for offline users, mentions, and direct messages
- **Real-time awareness**: Skip notifications for active users in rooms
- **Reuse existing infrastructure**: Leverage current Socket.IO patterns

### 3. Hybrid UI Approach
- **Chat popup**: 350×500px floating window for quick conversations
- **Full-page interface**: Complete chat experience at `/apps/internal-chat`
- **Seamless transitions**: Easy switching between popup and full-page modes
- **Browser notifications**: Desktop alerts when users are away

### 4. Integration with Existing System
- **Agent status sync**: Chat presence reflects agent availability
- **Department rooms**: Auto-created based on call center queues
- **Socket.IO extension**: Reuse existing real-time infrastructure
- **Prisma compatibility**: Works with current ORM patterns
- **NextAuth integration**: Uses existing authentication system

## Database Schema (Final)

### Core Tables
1. **v_chat_rooms**: Room management with types (direct, group, department, broadcast)
2. **v_chat_room_participants**: User membership with roles (owner, admin, moderator, member)
3. **v_chat_messages**: Optimized message storage (Discord-inspired)
4. **v_chat_message_attachments**: File attachments with metadata
5. **v_chat_message_reactions**: Emoji reactions (simplified)
6. **v_chat_user_presence**: Online status tracking
7. **v_chat_notifications**: Smart notification system with auto-cleanup
8. **v_chat_room_unread_counts**: Denormalized counters for performance

### Storage Optimization
```sql
-- Optimized message table (Discord-inspired)
CREATE TABLE v_chat_messages (
    message_id BIGSERIAL PRIMARY KEY,           -- 8 bytes
    room_uuid UUID NOT NULL,                    -- 16 bytes
    author_uuid UUID NOT NULL,                  -- 16 bytes
    content TEXT,                               -- Variable
    message_type SMALLINT DEFAULT 0,            -- 2 bytes
    reply_to BIGINT,                            -- 8 bytes
    edited_at BIGINT,                           -- 8 bytes
    created_at BIGINT NOT NULL,                 -- 8 bytes
    flags INTEGER DEFAULT 0                     -- 4 bytes
);
-- Total: ~70 bytes + content (vs ~200 bytes in original design)
```

## API Architecture

### REST Endpoints
- **Room Management**: CRUD operations for chat rooms
- **Message Operations**: Send, edit, delete, react to messages
- **File Handling**: Secure upload/download with validation
- **User Search**: Find users for room invitations
- **Presence Management**: Online status updates

### WebSocket Events
- **Real-time messaging**: Instant message delivery
- **Typing indicators**: Show when users are typing
- **Presence updates**: Online/offline status changes
- **Room notifications**: User joins/leaves, room updates

## Frontend Architecture

### Component Structure
```
InternalChatApp
├── ChatLayout (responsive layout manager)
├── ChatSidebar (room list, online users)
├── ChatMain (message list, input, header)
├── ChatPopup (floating window interface)
└── ChatNotifications (browser alerts)
```

### State Management
- **Redux integration**: Extends existing store patterns
- **Real-time sync**: WebSocket events update state
- **Optimistic updates**: Immediate UI feedback
- **Caching strategy**: Recent messages in memory

## Security & Permissions

### Domain Isolation
- **Complete separation**: Users only access their domain's chats
- **Role-based access**: Granular permissions for room management
- **Content validation**: XSS prevention and spam filtering
- **Audit logging**: Complete action tracking for compliance

### File Security
- **Type validation**: Allowed file types and size limits
- **Virus scanning**: Security validation for uploads
- **Access control**: Only room participants can download files
- **Secure storage**: Files stored outside web root

## Performance Characteristics

### Expected Scale (100 users, 50 messages/day each)
- **Daily volume**: 5,000 messages
- **Database growth**: 350 KB/day, 128 MB/year
- **Concurrent users**: 50-100 simultaneous connections
- **Message delivery**: <100ms latency target

### Optimization Features
- **Message batching**: Reduce database load
- **Smart caching**: Recent messages in memory
- **Lazy loading**: Load message history on demand
- **Connection pooling**: Efficient WebSocket management

## Integration Points

### Agent System
- **Status synchronization**: Chat presence reflects agent status
- **Queue integration**: Department rooms based on call queues
- **Call integration**: Chat rooms for active calls

### Notification System
- **Browser notifications**: Desktop alerts for new messages
- **Email digests**: Summary of missed messages
- **Smart filtering**: Reduce notification noise

## Implementation Benefits

### For Your Current System
1. **Seamless integration**: Works with existing patterns and infrastructure
2. **Minimal disruption**: Extends current architecture without major changes
3. **Performance optimized**: 65% storage reduction, faster queries
4. **Scalable design**: Can handle 10x growth without architectural changes
5. **User-friendly**: Familiar UI patterns, non-intrusive popup interface

### Technical Advantages
1. **Discord-proven approach**: Battle-tested at massive scale
2. **Simple maintenance**: Minimal schema, clear data flow
3. **Future-ready**: Easy to add features like message limits, archiving
4. **Cost-effective**: Efficient storage and bandwidth usage

## Next Steps

The design is complete and ready for implementation. The system will provide:
- **Real-time internal communication** for domain users
- **Seamless integration** with existing agent workflows
- **Efficient performance** optimized for your scale
- **Room for growth** to handle future expansion
- **Professional user experience** with popup and full-page interfaces

This design balances functionality, performance, and maintainability while staying true to your existing system architecture and patterns.
