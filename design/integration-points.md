# Integration Points Design for Internal Chat System

## Agent System Integration

### Agent Status Synchronization

The internal chat system integrates with the existing agent management system to provide contextual availability and status information.

```typescript
// src/integrations/agent-chat-sync.ts

export class AgentChatIntegration {
  static async syncAgentStatusToChat(agent_uuid: string, agent_status: AgentStatusTypes): Promise<void> {
    // Map agent status to chat presence
    const chatStatus = this.mapAgentStatusToChatPresence(agent_status)
    
    // Update chat presence
    await prisma.v_chat_user_presence.upsert({
      where: { user_uuid: agent_uuid },
      update: {
        status: chatStatus,
        current_activity: `Agent: ${agent_status}`,
        update_date: new Date()
      },
      create: {
        user_uuid: agent_uuid,
        domain_uuid: await this.getUserDomain(agent_uuid),
        status: chatStatus,
        current_activity: `Agent: ${agent_status}`
      }
    })

    // Broadcast status change to connected chat users
    const socketManager = ChatSocketManager.getInstance()
    const domain_uuid = await this.getUserDomain(agent_uuid)
    
    socketManager.broadcastToDomain(domain_uuid, 'user_presence_changed', {
      user_uuid: agent_uuid,
      status: chatStatus,
      current_activity: `Agent: ${agent_status}`,
      last_seen: new Date().toISOString()
    })
  }

  private static mapAgentStatusToChatPresence(agent_status: AgentStatusTypes): 'online' | 'away' | 'busy' | 'offline' {
    const statusMap: Record<AgentStatusTypes, 'online' | 'away' | 'busy' | 'offline'> = {
      'Available': 'online',
      'Available (On Demand)': 'online',
      'On Break': 'away',
      'Do Not Disturb': 'busy',
      'Logged Out': 'offline',
      '': 'offline'
    }
    
    return statusMap[agent_status] || 'offline'
  }

  // Auto-create department rooms based on agent queues
  static async createDepartmentRoomsFromQueues(): Promise<void> {
    const queues = await prisma.v_call_center_queues.findMany({
      where: { queue_enabled: 'true' },
      include: {
        v_call_center_tiers: {
          include: {
            v_call_center_agents: true
          }
        }
      }
    })

    for (const queue of queues) {
      // Check if department room already exists
      const existingRoom = await prisma.v_chat_rooms.findFirst({
        where: {
          domain_uuid: queue.domain_uuid,
          room_type: 'department',
          room_name: `Queue: ${queue.queue_name}`
        }
      })

      if (!existingRoom) {
        // Create department room for queue
        const room = await prisma.v_chat_rooms.create({
          data: {
            domain_uuid: queue.domain_uuid!,
            room_name: `Queue: ${queue.queue_name}`,
            room_description: `Department chat for ${queue.queue_name} queue agents`,
            room_type: 'department',
            created_by_user_uuid: 'system', // System-created room
            room_settings: {
              auto_add_queue_agents: true,
              queue_uuid: queue.call_center_queue_uuid
            }
          }
        })

        // Add all queue agents as participants
        const agents = queue.v_call_center_tiers.map(tier => tier.v_call_center_agents)
        for (const agent of agents) {
          if (agent?.user_uuid) {
            await prisma.v_chat_room_participants.create({
              data: {
                room_uuid: room.room_uuid,
                user_uuid: agent.user_uuid,
                participant_role: 'member'
              }
            })
          }
        }
      }
    }
  }

  // Handle agent queue assignments
  static async onAgentQueueAssignment(agent_uuid: string, queue_uuid: string): Promise<void> {
    // Find department room for this queue
    const room = await prisma.v_chat_rooms.findFirst({
      where: {
        room_type: 'department',
        room_settings: {
          path: ['queue_uuid'],
          equals: queue_uuid
        }
      }
    })

    if (room) {
      // Add agent to department room
      await prisma.v_chat_room_participants.upsert({
        where: {
          room_uuid_user_uuid: {
            room_uuid: room.room_uuid,
            user_uuid: agent_uuid
          }
        },
        update: {},
        create: {
          room_uuid: room.room_uuid,
          user_uuid: agent_uuid,
          participant_role: 'member'
        }
      })

      // Notify room about new participant
      const socketManager = ChatSocketManager.getInstance()
      const user = await prisma.v_users.findUnique({
        where: { user_uuid: agent_uuid },
        select: { username: true }
      })

      socketManager.broadcastToRoom(room.room_uuid, 'participant_added', {
        room_uuid: room.room_uuid,
        user_uuid: agent_uuid,
        user_name: user?.username,
        role: 'member'
      })
    }
  }
}
```

### Call Integration

```typescript
// src/integrations/call-chat-integration.ts

export class CallChatIntegration {
  // Create chat room for conference calls
  static async createCallRoom(call_uuid: string, participants: string[]): Promise<string> {
    const room = await prisma.v_chat_rooms.create({
      data: {
        domain_uuid: await this.getCallDomain(call_uuid),
        room_name: `Call: ${call_uuid.substring(0, 8)}`,
        room_description: 'Chat room for active call',
        room_type: 'group',
        created_by_user_uuid: participants[0],
        room_settings: {
          call_uuid,
          auto_archive_on_call_end: true,
          temporary_room: true
        }
      }
    })

    // Add all call participants
    for (const participant_uuid of participants) {
      await prisma.v_chat_room_participants.create({
        data: {
          room_uuid: room.room_uuid,
          user_uuid: participant_uuid,
          participant_role: 'member'
        }
      })
    }

    return room.room_uuid
  }

  // Send call status updates to chat
  static async sendCallStatusMessage(
    room_uuid: string,
    status: 'started' | 'ended' | 'participant_joined' | 'participant_left',
    metadata: any
  ): Promise<void> {
    const systemMessage = await prisma.v_chat_messages.create({
      data: {
        room_uuid,
        sender_user_uuid: 'system',
        message_content: this.getCallStatusMessage(status, metadata),
        message_type: 'system',
        message_metadata: { call_status: status, ...metadata }
      }
    })

    // Broadcast to room participants
    const socketManager = ChatSocketManager.getInstance()
    socketManager.broadcastToRoom(room_uuid, 'new_message', {
      ...systemMessage,
      sender_name: 'System',
      insert_date: systemMessage.insert_date.toISOString()
    })
  }

  private static getCallStatusMessage(status: string, metadata: any): string {
    switch (status) {
      case 'started':
        return '📞 Call started'
      case 'ended':
        return `📞 Call ended (Duration: ${metadata.duration})`
      case 'participant_joined':
        return `📞 ${metadata.participant_name} joined the call`
      case 'participant_left':
        return `📞 ${metadata.participant_name} left the call`
      default:
        return '📞 Call status updated'
    }
  }
}
```

## Notification System Integration

### Push Notifications

```typescript
// src/integrations/notification-integration.ts

export class ChatNotificationIntegration {
  // Browser push notifications
  static async sendBrowserNotification(
    user_uuid: string,
    notification: {
      title: string
      body: string
      icon?: string
      room_uuid: string
      message_uuid: string
    }
  ): Promise<void> {
    // Get user's notification preferences
    const preferences = await this.getUserNotificationPreferences(user_uuid)
    
    if (!preferences.browser_notifications) return

    // Check if user is currently active in the room
    const isActiveInRoom = await this.isUserActiveInRoom(user_uuid, notification.room_uuid)
    if (isActiveInRoom) return // Don't notify if user is actively viewing the room

    // Send browser notification via WebSocket
    const socketManager = ChatSocketManager.getInstance()
    socketManager.sendToUser(user_uuid, 'browser_notification', {
      title: notification.title,
      body: notification.body,
      icon: notification.icon || '/icons/chat-notification.png',
      data: {
        room_uuid: notification.room_uuid,
        message_uuid: notification.message_uuid,
        url: `/apps/internal-chat?room=${notification.room_uuid}`
      }
    })
  }

  // Email digest notifications
  static async scheduleEmailDigest(user_uuid: string): Promise<void> {
    const preferences = await this.getUserNotificationPreferences(user_uuid)
    
    if (!preferences.email_digest) return

    // Get unread messages from last 24 hours
    const unreadMessages = await prisma.v_chat_notifications.findMany({
      where: {
        user_uuid,
        is_read: false,
        insert_date: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
        }
      },
      include: {
        v_chat_rooms: true,
        v_chat_messages: {
          include: {
            sender: { select: { username: true } }
          }
        }
      }
    })

    if (unreadMessages.length === 0) return

    // Group by room
    const messagesByRoom = unreadMessages.reduce((acc, notification) => {
      const roomId = notification.room_uuid
      if (!acc[roomId]) {
        acc[roomId] = {
          room: notification.v_chat_rooms,
          messages: []
        }
      }
      acc[roomId].messages.push(notification.v_chat_messages)
      return acc
    }, {} as Record<string, any>)

    // Send email digest
    await this.sendEmailDigest(user_uuid, messagesByRoom)
  }

  // Mobile push notifications (if mobile app exists)
  static async sendMobilePushNotification(
    user_uuid: string,
    notification: {
      title: string
      body: string
      room_uuid: string
      message_uuid: string
    }
  ): Promise<void> {
    // Get user's mobile device tokens
    const deviceTokens = await this.getUserDeviceTokens(user_uuid)
    
    if (deviceTokens.length === 0) return

    // Send via Firebase Cloud Messaging or similar service
    for (const token of deviceTokens) {
      await this.sendFCMNotification(token, {
        title: notification.title,
        body: notification.body,
        data: {
          type: 'chat_message',
          room_uuid: notification.room_uuid,
          message_uuid: notification.message_uuid
        }
      })
    }
  }

  private static async getUserNotificationPreferences(user_uuid: string) {
    // Get from user settings or default preferences
    return {
      browser_notifications: true,
      email_digest: true,
      mobile_push: true,
      mention_only: false
    }
  }

  private static async isUserActiveInRoom(user_uuid: string, room_uuid: string): Promise<boolean> {
    // Check if user's socket is currently in the room
    const socketManager = ChatSocketManager.getInstance()
    return socketManager.isUserInRoom(user_uuid, room_uuid)
  }
}
```

## File Storage Integration

### Secure File Handling

```typescript
// src/integrations/file-storage-integration.ts

export class FileStorageIntegration {
  private static readonly UPLOAD_PATH = process.env.CHAT_UPLOAD_PATH || '/uploads/chat'
  private static readonly MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB
  private static readonly ALLOWED_TYPES = [
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'application/pdf', 'text/plain',
    'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ]

  static async uploadFile(
    file: File,
    user_uuid: string,
    room_uuid: string
  ): Promise<{
    attachment_uuid: string
    file_path: string
    thumbnail_path?: string
  }> {
    // Validate file
    const validation = ChatSecurityService.validateFileUpload(file, user_uuid)
    if (!validation.isValid) {
      throw new Error(validation.errors?.join(', '))
    }

    // Generate unique filename
    const fileExtension = path.extname(file.name)
    const uniqueFilename = `${uuidv4()}${fileExtension}`
    const filePath = path.join(this.UPLOAD_PATH, room_uuid, uniqueFilename)

    // Ensure directory exists
    await fs.ensureDir(path.dirname(filePath))

    // Save file
    const buffer = await file.arrayBuffer()
    await fs.writeFile(filePath, Buffer.from(buffer))

    // Generate thumbnail for images
    let thumbnailPath: string | undefined
    if (file.type.startsWith('image/')) {
      thumbnailPath = await this.generateThumbnail(filePath, file.type)
    }

    // Scan for malware (if antivirus service is available)
    const isSafe = await this.scanFile(filePath)
    if (!isSafe) {
      await fs.remove(filePath)
      if (thumbnailPath) await fs.remove(thumbnailPath)
      throw new Error('File failed security scan')
    }

    // Create attachment record
    const attachment = await prisma.v_chat_message_attachments.create({
      data: {
        file_name: file.name,
        file_path: filePath,
        file_size: file.size,
        file_type: fileExtension,
        mime_type: file.type,
        thumbnail_path: thumbnailPath,
        insert_user: user_uuid
      }
    })

    return {
      attachment_uuid: attachment.attachment_uuid,
      file_path: filePath,
      thumbnail_path: thumbnailPath
    }
  }

  static async downloadFile(
    attachment_uuid: string,
    user_uuid: string
  ): Promise<{ filePath: string; fileName: string; mimeType: string }> {
    // Get attachment info
    const attachment = await prisma.v_chat_message_attachments.findUnique({
      where: { attachment_uuid },
      include: {
        v_chat_messages: {
          include: {
            v_chat_rooms: {
              include: {
                v_chat_room_participants: {
                  where: { user_uuid }
                }
              }
            }
          }
        }
      }
    })

    if (!attachment) {
      throw new Error('File not found')
    }

    // Check if user has access to the room
    const hasAccess = attachment.v_chat_messages.v_chat_rooms.v_chat_room_participants.length > 0
    if (!hasAccess) {
      throw new Error('Access denied')
    }

    // Check if file exists
    const fileExists = await fs.pathExists(attachment.file_path)
    if (!fileExists) {
      throw new Error('File not found on disk')
    }

    return {
      filePath: attachment.file_path,
      fileName: attachment.file_name,
      mimeType: attachment.mime_type
    }
  }

  private static async generateThumbnail(filePath: string, mimeType: string): Promise<string> {
    if (!mimeType.startsWith('image/')) return ''

    const thumbnailPath = filePath.replace(/(\.[^.]+)$/, '_thumb$1')
    
    try {
      // Use sharp or similar library for image processing
      const sharp = require('sharp')
      await sharp(filePath)
        .resize(200, 200, { fit: 'inside', withoutEnlargement: true })
        .jpeg({ quality: 80 })
        .toFile(thumbnailPath)
      
      return thumbnailPath
    } catch (error) {
      console.error('Thumbnail generation failed:', error)
      return ''
    }
  }

  private static async scanFile(filePath: string): Promise<boolean> {
    // Implement virus scanning if antivirus service is available
    // For now, return true (safe)
    return true
  }

  // Cleanup old files based on retention policy
  static async cleanupOldFiles(retention_days: number = 365): Promise<void> {
    const cutoffDate = new Date(Date.now() - retention_days * 24 * 60 * 60 * 1000)

    const oldAttachments = await prisma.v_chat_message_attachments.findMany({
      where: {
        insert_date: { lt: cutoffDate },
        v_chat_messages: {
          is_deleted: true
        }
      }
    })

    for (const attachment of oldAttachments) {
      try {
        // Delete file from disk
        await fs.remove(attachment.file_path)
        if (attachment.thumbnail_path) {
          await fs.remove(attachment.thumbnail_path)
        }

        // Delete database record
        await prisma.v_chat_message_attachments.delete({
          where: { attachment_uuid: attachment.attachment_uuid }
        })
      } catch (error) {
        console.error(`Failed to cleanup file ${attachment.file_path}:`, error)
      }
    }
  }
}
```

## Search Integration

### Full-Text Search

```typescript
// src/integrations/search-integration.ts

export class ChatSearchIntegration {
  // Search messages across accessible rooms
  static async searchMessages(
    user_uuid: string,
    query: string,
    filters: {
      room_uuid?: string
      message_type?: string
      date_from?: Date
      date_to?: Date
    } = {},
    pagination: { page: number; limit: number } = { page: 1, limit: 20 }
  ): Promise<{
    messages: any[]
    total: number
    rooms: any[]
  }> {
    // Get rooms user has access to
    const accessibleRooms = await prisma.v_chat_room_participants.findMany({
      where: { user_uuid },
      select: { room_uuid: true }
    })

    const roomIds = accessibleRooms.map(r => r.room_uuid)

    // Build search query
    const whereClause: any = {
      room_uuid: { in: roomIds },
      is_deleted: false,
      OR: [
        { message_content: { contains: query, mode: 'insensitive' } },
        { sender: { username: { contains: query, mode: 'insensitive' } } }
      ]
    }

    // Apply filters
    if (filters.room_uuid) {
      whereClause.room_uuid = filters.room_uuid
    }
    if (filters.message_type) {
      whereClause.message_type = filters.message_type
    }
    if (filters.date_from || filters.date_to) {
      whereClause.insert_date = {}
      if (filters.date_from) whereClause.insert_date.gte = filters.date_from
      if (filters.date_to) whereClause.insert_date.lte = filters.date_to
    }

    // Execute search
    const [messages, total] = await Promise.all([
      prisma.v_chat_messages.findMany({
        where: whereClause,
        include: {
          sender: { select: { username: true } },
          v_chat_rooms: { select: { room_name: true, room_type: true } },
          v_chat_message_attachments: true
        },
        orderBy: { insert_date: 'desc' },
        skip: (pagination.page - 1) * pagination.limit,
        take: pagination.limit
      }),
      prisma.v_chat_messages.count({ where: whereClause })
    ])

    // Get unique rooms from results
    const uniqueRooms = Array.from(
      new Map(messages.map(m => [m.room_uuid, m.v_chat_rooms])).values()
    )

    return {
      messages: messages.map(m => ({
        message_uuid: m.message_uuid,
        room_uuid: m.room_uuid,
        room_name: m.v_chat_rooms.room_name,
        sender_name: m.sender.username,
        message_content: this.highlightSearchTerm(m.message_content, query),
        message_type: m.message_type,
        insert_date: m.insert_date.toISOString(),
        attachments: m.v_chat_message_attachments
      })),
      total,
      rooms: uniqueRooms
    }
  }

  private static highlightSearchTerm(content: string, query: string): string {
    if (!query || !content) return content
    
    const regex = new RegExp(`(${query})`, 'gi')
    return content.replace(regex, '<mark>$1</mark>')
  }

  // Search users for adding to rooms
  static async searchUsers(
    domain_uuid: string,
    query: string,
    exclude_room_uuid?: string
  ): Promise<any[]> {
    const whereClause: any = {
      domain_uuid,
      user_enabled: 'true',
      OR: [
        { username: { contains: query, mode: 'insensitive' } },
        { user_email: { contains: query, mode: 'insensitive' } }
      ]
    }

    // Exclude users already in the room
    if (exclude_room_uuid) {
      const existingParticipants = await prisma.v_chat_room_participants.findMany({
        where: { room_uuid: exclude_room_uuid },
        select: { user_uuid: true }
      })
      
      const excludeIds = existingParticipants.map(p => p.user_uuid)
      if (excludeIds.length > 0) {
        whereClause.user_uuid = { notIn: excludeIds }
      }
    }

    const users = await prisma.v_users.findMany({
      where: whereClause,
      select: {
        user_uuid: true,
        username: true,
        user_email: true,
        v_employee: {
          select: {
            first_name: true,
            last_name: true,
            avatar: true,
            v_department: {
              select: { department_name: true }
            }
          }
        }
      },
      take: 20
    })

    return users.map(user => ({
      user_uuid: user.user_uuid,
      username: user.username,
      email: user.user_email,
      full_name: user.v_employee ? 
        `${user.v_employee.first_name} ${user.v_employee.last_name}`.trim() : 
        user.username,
      avatar: user.v_employee?.avatar,
      department: user.v_employee?.v_department?.department_name
    }))
  }
}
