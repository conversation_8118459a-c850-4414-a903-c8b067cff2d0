# Facebook Messenger Integration Plan

## Overview
This document outlines the integration plan for Facebook Messenger with the internal chat system, following the successful Telegram integration pattern.

## Architecture Components

### 1. Webhook Endpoint Structure
```
/api/internal-chat/facebook/webhook
├── GET  - Webhook verification (Facebook requirement)
└── POST - Process incoming messages and events
```

### 2. Configuration Management
```
/api/internal-chat/facebook/page-config
├── GET    - Retrieve Facebook page configuration
├── POST   - Create/update page configuration
├── PUT    - Update configuration settings
└── DELETE - Remove configuration
```

### 3. Message Processing Service
```typescript
export class FacebookMessageService {
  static async processInboundMessage(domain_uuid: string, webhookEvent: FacebookWebhookEvent)
  static async processOutboundMessage(domain_uuid: string, room_uuid: string, content: string)
  static async findOrCreateContact(domain_uuid: string, facebookUser: FacebookUser)
  static async findOrCreateRoom(domain_uuid: string, contact_uuid: string)
}
```

## Facebook Webhook Requirements

### 1. Webhook Verification Process
Facebook requires a GET endpoint that:
- Validates the `hub.verify_token` parameter
- Returns the `hub.challenge` parameter if verification succeeds
- Returns 403 if verification fails

### 2. Webhook Event Processing
Facebook sends POST requests with:
- `X-Hub-Signature-256` header for payload verification
- JSON payload containing messaging events
- Various event types: messages, postbacks, delivery confirmations

### 3. Required Facebook App Setup
1. **Facebook App Creation**
   - Create app in Facebook Developer Console
   - Add Messenger product
   - Configure webhook URL and verify token

2. **Page Access Token**
   - Generate page access token for sending messages
   - Store securely in database per domain

3. **Webhook Subscription**
   - Subscribe to messaging events
   - Configure webhook URL: `https://yourdomain.com/api/internal-chat/facebook/webhook?domain={domain_uuid}`

## Integration Flow

### Inbound Message Flow
1. **Facebook → Webhook**: User sends message via Messenger
2. **Webhook Verification**: Validate signature and payload
3. **Event Logging**: Store webhook event for audit
4. **Contact Management**: Find or create Facebook contact
5. **Room Management**: Find or create internal chat room bridge
6. **Message Creation**: Create internal chat message
7. **Message Mapping**: Track Facebook ↔ Internal message relationship
8. **Real-time Broadcast**: Emit message via Socket.IO
9. **Agent Notification**: Notify assigned agents

### Outbound Message Flow
1. **Agent → Internal Chat**: Agent sends message in internal system
2. **Room Bridge Lookup**: Find Facebook room bridge
3. **Message Creation**: Create internal message
4. **Facebook API Call**: Send message via Graph API
5. **Message Mapping**: Track delivery status
6. **Status Update**: Update delivery status in real-time

## Security Considerations

### 1. Webhook Security
- Verify `X-Hub-Signature-256` header using app secret
- Validate payload integrity before processing
- Rate limiting on webhook endpoint

### 2. Token Management
- Encrypt page access tokens in database
- Implement token refresh mechanism
- Secure storage of app secrets

### 3. Domain Isolation
- Strict domain_uuid validation
- Prevent cross-domain data access
- Audit logging for all operations

## Error Handling

### 1. Facebook API Errors
- Rate limiting (HTTP 429)
- Invalid tokens (HTTP 401)
- User blocked bot (HTTP 403)
- Message delivery failures

### 2. Webhook Processing Errors
- Invalid payload format
- Missing required fields
- Database connection issues
- Socket.IO broadcast failures

### 3. Recovery Mechanisms
- Retry logic for transient failures
- Dead letter queue for failed messages
- Manual retry interface for administrators

## Real-time Integration

### 1. Socket.IO Events
```typescript
// Inbound message from Facebook
socket.emit(`${domain_uuid}_new_message`, {
  room_uuid,
  message_id,
  content,
  author_type: 'external',
  platform: 'facebook'
})

// Delivery status updates
socket.emit(`${domain_uuid}_message_status`, {
  message_id,
  delivery_status: 'delivered',
  platform: 'facebook'
})
```

### 2. Notification System
- Desktop notifications for new messages
- Unread count updates
- Agent assignment notifications
- Conversation status changes

## Configuration Interface

### 1. Admin Panel Features
- Facebook app configuration form
- Page access token management
- Webhook URL generation
- Connection status monitoring
- Message delivery statistics

### 2. Agent Interface Features
- Facebook conversation list
- Message history with Facebook users
- Send/receive messages seamlessly
- File attachment support (images, documents)
- Quick replies and templates

## Testing Strategy

### 1. Unit Tests
- Webhook verification logic
- Message processing functions
- Contact and room management
- Error handling scenarios

### 2. Integration Tests
- End-to-end message flow
- Facebook API integration
- Socket.IO broadcasting
- Database operations

### 3. Manual Testing
- Facebook Messenger app testing
- Webhook endpoint validation
- Real-time message delivery
- Error scenario handling

## Deployment Considerations

### 1. Environment Variables
```env
FACEBOOK_APP_SECRET=your_app_secret
FACEBOOK_WEBHOOK_VERIFY_TOKEN=your_verify_token
FACEBOOK_GRAPH_API_URL=https://graph.facebook.com/v18.0
```

### 2. Database Migration
- Run Facebook schema migration
- Update Prisma schema
- Generate new Prisma client

### 3. Monitoring
- Webhook endpoint health checks
- Facebook API rate limit monitoring
- Message delivery success rates
- Error rate tracking

## Future Enhancements

### 1. Advanced Features
- Facebook template messages
- Quick reply buttons
- Persistent menu configuration
- Customer service handover protocol

### 2. Analytics
- Conversation metrics
- Response time tracking
- Customer satisfaction surveys
- Agent performance analytics

### 3. Automation
- Chatbot integration
- Auto-assignment rules
- Canned responses
- Business hours management
