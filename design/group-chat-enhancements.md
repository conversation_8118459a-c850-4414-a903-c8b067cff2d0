# Group Chat Enhancement Plan
## Facebook Messenger-Inspired Features

### 1. Database Schema Enhancements

#### Current Schema Analysis
Your existing schema already supports group chats well:
- ✅ `v_chat_rooms` with `room_type` including 'group'
- ✅ `v_chat_room_participants` with roles (owner, admin, moderator, member)
- ✅ `v_chat_messages` with proper message handling
- ✅ `v_chat_unread_counts` for notification management

#### Minor Enhancements Needed
```sql
-- Add group-specific settings to room_settings JSONB
-- Examples of settings to store:
{
  "allow_members_to_add_others": true,
  "allow_members_to_change_name": false,
  "require_admin_approval_for_new_members": false,
  "mute_notifications_for_all": false,
  "auto_archive_after_days": null,
  "group_description_editable_by": "admins_only", // "anyone" | "admins_only" | "owner_only"
  "member_permissions": {
    "can_send_messages": true,
    "can_send_media": true,
    "can_add_members": true,
    "can_remove_members": false
  }
}

-- Add group-specific notification settings to participant notification_settings
{
  "mentions": true,
  "all_messages": true,
  "member_additions": true,
  "member_removals": true,
  "role_changes": true,
  "group_info_changes": true
}
```

### 2. API Enhancements

#### A. Group Member Management APIs

**POST /api/internal-chat/rooms/[roomId]/members**
```typescript
interface AddMembersRequest {
  user_uuids: string[]
  role?: 'member' | 'moderator' | 'admin'
  send_notification?: boolean
}
```

**DELETE /api/internal-chat/rooms/[roomId]/members/[userId]**
- Remove member from group
- Only admins/owners can remove others
- Members can remove themselves (leave group)

**PUT /api/internal-chat/rooms/[roomId]/members/[userId]/role**
```typescript
interface UpdateMemberRoleRequest {
  new_role: 'member' | 'moderator' | 'admin' | 'owner'
}
```

#### B. Group Settings APIs

**PUT /api/internal-chat/rooms/[roomId]/settings**
```typescript
interface UpdateGroupSettingsRequest {
  room_name?: string
  room_description?: string
  room_avatar?: string
  room_settings?: {
    allow_members_to_add_others?: boolean
    require_admin_approval_for_new_members?: boolean
    // ... other settings
  }
}
```

**GET /api/internal-chat/rooms/[roomId]/members**
- Get all group members with their roles and status
- Include online status and last seen

#### C. Enhanced Notification APIs

**POST /api/internal-chat/rooms/[roomId]/notifications/settings**
```typescript
interface GroupNotificationSettings {
  mentions: boolean
  all_messages: boolean
  member_changes: boolean
  group_info_changes: boolean
}
```

### 3. Frontend Component Enhancements

#### A. Enhanced Group Creation Flow

**Multi-Step Group Creation Modal:**
1. **Step 1**: Group name and description
2. **Step 2**: Add members (with search and selection)
3. **Step 3**: Set initial permissions and settings
4. **Step 4**: Confirmation and creation

**Key Features:**
- Multi-user selection with checkboxes
- Real-time user search
- Role assignment during creation
- Group avatar upload
- Permission presets (Public, Private, Restricted)

#### B. Group Member Management UI

**Group Info Panel/Modal:**
- Member list with avatars and roles
- Online status indicators
- Add/remove member buttons (permission-based)
- Role management dropdown
- Group settings section

**Member List Component:**
```typescript
interface GroupMemberListProps {
  roomId: string
  members: GroupMember[]
  currentUserRole: ParticipantRole
  onAddMember: () => void
  onRemoveMember: (userId: string) => void
  onChangeRole: (userId: string, newRole: ParticipantRole) => void
}
```

#### C. Enhanced Message Display

**Group Message Enhancements:**
- Show sender avatar in group messages
- Display sender name above message
- Group typing indicators: "John, Sarah and 2 others are typing..."
- Message reactions with user attribution
- Reply-to functionality with user mentions

#### D. Group Notification Enhancements

**Smart Notification System:**
- Mention notifications (@username)
- Group activity notifications (member added/removed)
- Role change notifications
- Group settings change notifications
- Customizable notification preferences per group

### 4. Real-time Features

#### A. Enhanced WebSocket Events

**Group Member Events:**
```typescript
// Member added to group
{
  type: 'GROUP_MEMBER_ADDED',
  room_uuid: string,
  added_user: UserProfile,
  added_by: UserProfile,
  timestamp: number
}

// Member removed from group
{
  type: 'GROUP_MEMBER_REMOVED',
  room_uuid: string,
  removed_user: UserProfile,
  removed_by: UserProfile,
  timestamp: number
}

// Member role changed
{
  type: 'GROUP_MEMBER_ROLE_CHANGED',
  room_uuid: string,
  user: UserProfile,
  old_role: ParticipantRole,
  new_role: ParticipantRole,
  changed_by: UserProfile,
  timestamp: number
}
```

**Group Typing Indicators:**
```typescript
{
  type: 'GROUP_TYPING_START',
  room_uuid: string,
  user: UserProfile,
  timestamp: number
}

{
  type: 'GROUP_TYPING_STOP',
  room_uuid: string,
  user: UserProfile,
  timestamp: number
}
```

#### B. Enhanced Presence System

**Group Presence Tracking:**
- Track which members are currently viewing the group
- Show "last seen" for offline members
- Display active/away status in member list
- Real-time member count updates

### 5. Permission System

#### A. Role-Based Permissions

**Owner Permissions:**
- All admin permissions
- Transfer ownership
- Delete group
- Change group type
- Manage admin roles

**Admin Permissions:**
- Add/remove members
- Change group settings
- Promote/demote moderators
- Pin/unpin messages
- Manage group info

**Moderator Permissions:**
- Add members (if allowed by settings)
- Remove messages
- Mute members temporarily
- Pin messages

**Member Permissions:**
- Send messages
- Add members (if allowed by settings)
- Leave group
- Change notification settings

#### B. Configurable Group Settings

**Group Permission Presets:**

1. **Open Group:**
   - Anyone can add members
   - Anyone can change group info
   - All members have equal permissions

2. **Moderated Group:**
   - Only admins can add members
   - Only admins can change group info
   - Members can send messages only

3. **Restricted Group:**
   - Only owner can add members
   - Only owner can change settings
   - Admin approval required for all changes

### 6. UI/UX Improvements

#### A. Facebook Messenger-Inspired Design Elements

**Group Chat Header:**
- Group avatar and name
- Member count indicator
- Online member count
- Group info button
- Call/video call buttons (if applicable)

**Message Layout:**
- Sender avatars for group messages
- Sender name display
- Message clustering by sender
- Timestamp grouping
- Read receipts (if enabled)

**Group Member Indicators:**
- Online status dots
- Typing indicators with names
- "Seen by" indicators for messages
- Member role badges

#### B. Mobile-Responsive Design

**Mobile Group Management:**
- Swipe actions for member management
- Bottom sheet for group settings
- Optimized member list for small screens
- Touch-friendly member selection

**Tablet/Desktop Enhancements:**
- Side panel for group info
- Hover states for member actions
- Keyboard shortcuts for group actions
- Multi-column layout for large groups

### 7. Implementation Priority

#### Phase 1 (Core Group Features):
1. ✅ Database schema review (already good)
2. 🔄 Group member management APIs
3. 🔄 Enhanced group creation UI
4. 🔄 Basic group member list

#### Phase 2 (Advanced Features):
1. Group settings management
2. Role-based permissions
3. Group notifications system
4. Real-time member updates

#### Phase 3 (Polish & Optimization):
1. Advanced UI enhancements
2. Mobile optimization
3. Performance improvements
4. Analytics and insights

### 8. Technical Considerations

#### A. Performance Optimization
- Lazy loading for large member lists
- Efficient WebSocket event handling
- Optimized database queries for group operations
- Caching for frequently accessed group data

#### B. Security Considerations
- Proper permission validation on all group operations
- Rate limiting for member additions
- Audit logging for group changes
- Privacy controls for group visibility

#### C. Scalability Considerations
- Efficient handling of large groups (100+ members)
- Optimized notification delivery
- Database indexing for group queries
- WebSocket connection management for groups

## 9. Facebook Messenger Key Behavioral Patterns

### A. Group Creation Flow
**Facebook's Approach:**
1. **Instant Group Creation**: Groups are created immediately when you select multiple users
2. **Progressive Enhancement**: Start with basic group, add features as needed
3. **Smart Defaults**: Sensible permission defaults based on group size
4. **Member Invitation**: Members are notified but can join/leave freely

**Implementation for Your System:**
```typescript
// Create group immediately when first member is selected
const handleCreateInstantGroup = async (selectedUsers: string[]) => {
  if (selectedUsers.length >= 2) {
    // Create group with default settings
    const group = await createGroup({
      room_name: generateGroupName(selectedUsers), // "You, John, Sarah"
      room_type: 'group',
      participant_uuids: selectedUsers,
      room_settings: getDefaultGroupSettings()
    })

    // Navigate to group immediately
    navigateToRoom(group.room_uuid)
  }
}
```

### B. Member Management Patterns
**Facebook's Behavior:**
- **Anyone can add**: By default, any member can add others
- **Admin controls**: Admins can restrict who can add members
- **Leave anytime**: Members can always leave groups
- **Remove permissions**: Only admins can remove others (except self)

**Key UI Patterns:**
- **Add button**: Always visible in group info
- **Member list**: Shows roles and online status
- **Quick actions**: Swipe/right-click for member actions
- **Confirmation dialogs**: For destructive actions only

### C. Notification Intelligence
**Facebook's Smart Notifications:**
- **Mention notifications**: Always notify when @mentioned, even if muted
- **Activity summaries**: Group multiple activities into single notification
- **Quiet hours**: Respect user's notification schedule
- **Priority messages**: From close friends get priority

**Implementation Strategy:**
```typescript
interface GroupNotificationRule {
  type: 'mention' | 'message' | 'member_change' | 'group_update'
  priority: 'high' | 'normal' | 'low'
  respectMute: boolean
  batchable: boolean
}

const notificationRules: GroupNotificationRule[] = [
  { type: 'mention', priority: 'high', respectMute: false, batchable: false },
  { type: 'message', priority: 'normal', respectMute: true, batchable: true },
  { type: 'member_change', priority: 'normal', respectMute: false, batchable: true }
]
```

### D. Group UI/UX Patterns
**Message Display:**
- **Sender avatars**: Show in group messages for context
- **Name display**: Show sender name above message in groups
- **Message clustering**: Group consecutive messages from same sender
- **Typing indicators**: "John, Sarah and 2 others are typing..."

**Group Header:**
- **Group name**: Editable by admins
- **Member count**: "12 members"
- **Online count**: "3 active"
- **Group actions**: Info, call, search buttons

**Member List:**
- **Role indicators**: Owner, admin badges
- **Online status**: Green dot for online
- **Last seen**: "Active 2h ago"
- **Quick actions**: Message, call, remove buttons

## 10. Implementation Recommendations

### Phase 1: Core Group Features (Week 1-2)
1. ✅ **API Implementation**: Group member management APIs (COMPLETED)
2. ✅ **Enhanced Group Creation**: Multi-step group creation dialog (COMPLETED)
3. 🔄 **Group Member List**: Display members with roles and status
4. 🔄 **Basic Permissions**: Role-based permission system

### Phase 2: Advanced Features (Week 3-4)
1. **Group Settings UI**: Comprehensive settings management
2. **Smart Notifications**: Group-specific notification rules
3. **Real-time Updates**: WebSocket events for group changes
4. **Mobile Optimization**: Touch-friendly group management

### Phase 3: Polish & Enhancement (Week 5-6)
1. **Advanced UI**: Facebook Messenger-inspired design elements
2. **Performance**: Optimize for large groups
3. **Analytics**: Group usage insights
4. **Integration**: Connect with existing features

### Key Files Created/Modified:
1. ✅ `src/app/api/internal-chat/rooms/[roomId]/members/route.ts` - Group member management
2. ✅ `src/app/api/internal-chat/rooms/[roomId]/members/[userId]/route.ts` - Individual member actions
3. ✅ `src/components/chat/CreateGroupDialog.tsx` - Enhanced group creation UI
4. 📋 `design/group-chat-enhancements.md` - This comprehensive plan

### Next Steps:
1. **Test the APIs**: Verify group member management functionality
2. **Integrate UI**: Connect CreateGroupDialog with existing chat interface
3. **Add Group Info Panel**: Create group settings and member management UI
4. **Implement WebSocket Events**: Real-time group updates
5. **Mobile Testing**: Ensure responsive design works on all devices

This implementation follows Facebook Messenger's proven patterns while adapting to your existing architecture and maintaining consistency with your current design system.
