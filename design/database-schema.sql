-- Internal Domain Chat System Database Schema
-- This schema extends the existing system with internal chat capabilities

-- Chat Rooms Table
-- Supports different room types: direct, group, department, broadcast
CREATE TABLE v_chat_rooms (
    room_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain_uuid UUID NOT NULL,
    room_name VA<PERSON>HA<PERSON>(255),
    room_description TEXT,
    room_type VARCHAR(50) NOT NULL CHECK (room_type IN ('direct', 'group', 'department', 'broadcast')),
    room_avatar VARCHAR(500),
    created_by_user_uuid UUID NOT NULL,
    is_active BOOLEAN DEFAULT true,
    is_archived BOOLEAN DEFAULT false,
    max_participants INTEGER DEFAULT 100,
    room_settings JSONB DEFAULT '{}',
    insert_date TIMESTAMPTZ DEFAULT NOW(),
    insert_user UUID,
    update_date TIMESTAMPTZ DEFAULT NOW(),
    update_user UUID,
    
    CONSTRAINT fk_chat_rooms_domain FOREIGN KEY (domain_uuid) REFERENCES v_domains(domain_uuid),
    CONSTRAINT fk_chat_rooms_creator <PERSON><PERSON><PERSON><PERSON><PERSON> (created_by_user_uuid) REFERENCES v_users(user_uuid)
);

-- Chat Room Participants Table
-- Manages who can access which chat rooms
CREATE TABLE v_chat_room_participants (
    participant_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    room_uuid UUID NOT NULL,
    user_uuid UUID NOT NULL,
    participant_role VARCHAR(50) DEFAULT 'member' CHECK (participant_role IN ('owner', 'admin', 'moderator', 'member')),
    joined_date TIMESTAMPTZ DEFAULT NOW(),
    last_read_message_uuid UUID,
    is_muted BOOLEAN DEFAULT false,
    notification_settings JSONB DEFAULT '{"mentions": true, "all_messages": true}',
    insert_date TIMESTAMPTZ DEFAULT NOW(),
    insert_user UUID,
    update_date TIMESTAMPTZ DEFAULT NOW(),
    update_user UUID,
    
    CONSTRAINT fk_participants_room FOREIGN KEY (room_uuid) REFERENCES v_chat_rooms(room_uuid) ON DELETE CASCADE,
    CONSTRAINT fk_participants_user FOREIGN KEY (user_uuid) REFERENCES v_users(user_uuid) ON DELETE CASCADE,
    CONSTRAINT unique_room_participant UNIQUE (room_uuid, user_uuid)
);

-- Chat Messages Table (Discord-inspired minimal design)
-- Stores essential message data only, optimized for performance
CREATE TABLE v_chat_messages (
    message_id BIGSERIAL PRIMARY KEY,                    -- Sequential ID (simpler than UUID for your scale)
    room_uuid UUID NOT NULL,                             -- Which room (keep UUID for compatibility)
    author_uuid UUID NOT NULL,                           -- Who sent it (renamed from sender for clarity)
    content TEXT,                                        -- Message content (can be empty for system messages)
    message_type SMALLINT DEFAULT 0,                     -- 0=text, 1=image, 2=file, 3=system, 4=call
    reply_to BIGINT,                                     -- Reply to message_id (simpler than UUID)
    edited_at BIGINT,                                    -- Unix timestamp when edited (null if never edited)
    created_at BIGINT NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW())::BIGINT, -- Unix timestamp
    flags INTEGER DEFAULT 0,                             -- Bitfield: 1=deleted, 2=pinned, 4=system, etc.

    -- Minimal indexes for performance
    CONSTRAINT fk_messages_room FOREIGN KEY (room_uuid) REFERENCES v_chat_rooms(room_uuid) ON DELETE CASCADE,
    CONSTRAINT fk_messages_author FOREIGN KEY (author_uuid) REFERENCES v_users(user_uuid),
    CONSTRAINT fk_messages_reply FOREIGN KEY (reply_to) REFERENCES v_chat_messages(message_id)
);

-- Message Reactions Table (Discord-inspired)
-- Store reactions as JSONB in messages table for better performance
-- This table only for complex reaction queries if needed
CREATE TABLE v_chat_message_reactions (
    message_id BIGINT NOT NULL,
    user_uuid UUID NOT NULL,
    emoji VARCHAR(10) NOT NULL,
    created_at BIGINT NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW())::BIGINT,

    PRIMARY KEY (message_id, user_uuid, emoji),
    CONSTRAINT fk_reactions_message FOREIGN KEY (message_id) REFERENCES v_chat_messages(message_id) ON DELETE CASCADE,
    CONSTRAINT fk_reactions_user FOREIGN KEY (user_uuid) REFERENCES v_users(user_uuid) ON DELETE CASCADE
);

-- Message Attachments Table (Discord-inspired minimal)
-- Store attachments as JSONB in messages table for simple cases
-- This table for complex file management if needed
CREATE TABLE v_chat_message_attachments (
    attachment_id BIGSERIAL PRIMARY KEY,
    message_id BIGINT NOT NULL,
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER,                                   -- Size in bytes (INTEGER sufficient for 2GB limit)
    content_type VARCHAR(100),                           -- MIME type
    width INTEGER,                                       -- For images/videos
    height INTEGER,                                      -- For images/videos

    CONSTRAINT fk_attachments_message FOREIGN KEY (message_id) REFERENCES v_chat_messages(message_id) ON DELETE CASCADE
);

-- User Presence Table
-- Tracks online status and last seen for chat users
CREATE TABLE v_chat_user_presence (
    presence_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_uuid UUID NOT NULL UNIQUE,
    domain_uuid UUID NOT NULL,
    status VARCHAR(50) DEFAULT 'offline' CHECK (status IN ('online', 'away', 'busy', 'offline')),
    last_seen TIMESTAMPTZ DEFAULT NOW(),
    current_activity VARCHAR(100),
    socket_id VARCHAR(100),
    update_date TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT fk_presence_user FOREIGN KEY (user_uuid) REFERENCES v_users(user_uuid) ON DELETE CASCADE,
    CONSTRAINT fk_presence_domain FOREIGN KEY (domain_uuid) REFERENCES v_domains(domain_uuid)
);

-- Chat Notifications Table (Optimized for volume)
-- Manages push notifications for chat messages with auto-cleanup
CREATE TABLE v_chat_notifications (
    notification_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_uuid UUID NOT NULL,
    room_uuid UUID NOT NULL,
    message_uuid UUID NOT NULL,
    notification_type VARCHAR(50) DEFAULT 'message' CHECK (notification_type IN ('message', 'mention', 'reply', 'room_invite')),
    is_read BOOLEAN DEFAULT false,
    is_sent BOOLEAN DEFAULT false,
    insert_date TIMESTAMPTZ DEFAULT NOW(),
    read_date TIMESTAMPTZ,
    -- Auto-cleanup after 30 days
    expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '30 days'),

    CONSTRAINT fk_notifications_user FOREIGN KEY (user_uuid) REFERENCES v_users(user_uuid) ON DELETE CASCADE,
    CONSTRAINT fk_notifications_room FOREIGN KEY (room_uuid) REFERENCES v_chat_rooms(room_uuid) ON DELETE CASCADE,
    CONSTRAINT fk_notifications_message FOREIGN KEY (message_uuid) REFERENCES v_chat_messages(message_uuid) ON DELETE CASCADE
);

-- Message Archive Table for old messages
CREATE TABLE v_chat_messages_archive (
    message_uuid UUID PRIMARY KEY,
    room_uuid UUID NOT NULL,
    sender_user_uuid UUID NOT NULL,
    message_content TEXT,
    message_type VARCHAR(50),
    insert_date TIMESTAMPTZ,
    archived_date TIMESTAMPTZ DEFAULT NOW()
);

-- Unread message counters (denormalized for performance)
CREATE TABLE v_chat_room_unread_counts (
    user_uuid UUID NOT NULL,
    room_uuid UUID NOT NULL,
    unread_count INTEGER DEFAULT 0,
    last_read_message_uuid UUID,
    last_updated TIMESTAMPTZ DEFAULT NOW(),

    PRIMARY KEY (user_uuid, room_uuid),
    CONSTRAINT fk_unread_user FOREIGN KEY (user_uuid) REFERENCES v_users(user_uuid) ON DELETE CASCADE,
    CONSTRAINT fk_unread_room FOREIGN KEY (room_uuid) REFERENCES v_chat_rooms(room_uuid) ON DELETE CASCADE
);

-- Indexes for performance optimization
CREATE INDEX idx_chat_rooms_domain ON v_chat_rooms(domain_uuid);
CREATE INDEX idx_chat_rooms_type ON v_chat_rooms(room_type);
CREATE INDEX idx_chat_participants_room ON v_chat_room_participants(room_uuid);
CREATE INDEX idx_chat_participants_user ON v_chat_room_participants(user_uuid);
CREATE INDEX idx_chat_messages_room ON v_chat_messages(room_uuid);
CREATE INDEX idx_chat_messages_sender ON v_chat_messages(sender_user_uuid);
CREATE INDEX idx_chat_messages_date ON v_chat_messages(insert_date);
CREATE INDEX idx_chat_messages_room_date ON v_chat_messages(room_uuid, insert_date DESC);
CREATE INDEX idx_chat_presence_user ON v_chat_user_presence(user_uuid);
CREATE INDEX idx_chat_presence_domain ON v_chat_user_presence(domain_uuid);
CREATE INDEX idx_chat_notifications_user ON v_chat_notifications(user_uuid);
CREATE INDEX idx_chat_notifications_unread ON v_chat_notifications(user_uuid, is_read) WHERE is_read = false;
CREATE INDEX idx_chat_notifications_expires ON v_chat_notifications(expires_at) WHERE expires_at < NOW();
CREATE INDEX idx_chat_unread_counts_user ON v_chat_room_unread_counts(user_uuid);

-- Partitioning for messages table (by month)
-- This helps with performance and archiving
CREATE TABLE v_chat_messages_y2024m01 PARTITION OF v_chat_messages
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- Auto-cleanup procedures
CREATE OR REPLACE FUNCTION cleanup_old_notifications()
RETURNS void AS $$
BEGIN
    DELETE FROM v_chat_notifications WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- Schedule cleanup to run daily
-- SELECT cron.schedule('cleanup-chat-notifications', '0 2 * * *', 'SELECT cleanup_old_notifications();');
