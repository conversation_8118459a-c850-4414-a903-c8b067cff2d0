# API Implementation Structure

## Facebook Messenger Integration APIs

### 1. Webhook Endpoint
**Path**: `/src/app/api/internal-chat/facebook/webhook/route.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server'
import { FacebookMessageService } from '@/services/facebook/facebookMessageService'
import { prisma } from '@/libs/db/prisma'

// GET - Webhook verification (Facebook requirement)
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const mode = searchParams.get('hub.mode')
  const token = searchParams.get('hub.verify_token')
  const challenge = searchParams.get('hub.challenge')
  const domain_uuid = searchParams.get('domain')

  if (mode === 'subscribe' && token && domain_uuid) {
    // Verify token against stored configuration
    const config = await prisma.v_facebook_page_config.findFirst({
      where: { domain_uuid, is_active: true }
    })

    if (config && config.verify_token === token) {
      return new Response(challenge, { status: 200 })
    }
  }

  return new Response('Forbidden', { status: 403 })
}

// POST - Process webhook events
export async function POST(request: NextRequest) {
  try {
    const domain_uuid = request.nextUrl.searchParams.get('domain')
    if (!domain_uuid) {
      return NextResponse.json({ error: 'Domain UUID required' }, { status: 400 })
    }

    // Verify webhook signature
    const signature = request.headers.get('x-hub-signature-256')
    const body = await request.text()
    
    if (!await FacebookMessageService.verifyWebhookSignature(domain_uuid, body, signature)) {
      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 })
    }

    const webhookEvent = JSON.parse(body)

    // Log webhook event
    await prisma.v_facebook_webhook_event.create({
      data: {
        domain_uuid,
        facebook_user_id: webhookEvent.entry?.[0]?.messaging?.[0]?.sender?.id,
        event_name: webhookEvent.object,
        msg: webhookEvent,
        insert_date: new Date()
      }
    })

    // Process messaging events
    for (const entry of webhookEvent.entry || []) {
      for (const messagingEvent of entry.messaging || []) {
        if (messagingEvent.message) {
          await FacebookMessageService.processInboundMessage(domain_uuid, messagingEvent)
        }
      }
    }

    return NextResponse.json({ status: 'processed' }, { status: 200 })
  } catch (error) {
    console.error('Facebook webhook error:', error)
    return NextResponse.json({ error: 'Processing failed' }, { status: 500 })
  }
}
```

### 2. Configuration Management API
**Path**: `/src/app/api/internal-chat/facebook/page-config/route.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'
import getSession from '@/actions/getSession'


// GET - Retrieve configuration
export async function GET(request: NextRequest) {
  const session = await getSession()
  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const user = await prisma.v_users.findUnique({
    where: { user_uuid: session.user.id },
    select: { domain_uuid: true }
  })

  if (!user?.domain_uuid) {
    return NextResponse.json({ error: 'Domain not found' }, { status: 404 })
  }

  const config = await prisma.v_facebook_page_config.findFirst({
    where: { domain_uuid: user.domain_uuid }
  })

  return NextResponse.json(config || {})
}

// POST - Create/Update configuration
export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const user = await prisma.v_users.findUnique({
    where: { user_uuid: session.user.id },
    select: { domain_uuid: true }
  })

  if (!user?.domain_uuid) {
    return NextResponse.json({ error: 'Domain not found' }, { status: 404 })
  }

  const body = await request.json()
  const {
    app_id,
    app_secret,
    page_id,
    page_access_token,
    verify_token,
    webhook_url,
    allowed_events = ['messages', 'messaging_postbacks', 'messaging_optins'],
    page_settings = {}
  } = body

  // Validate required fields
  if (!app_id || !app_secret || !page_id || !page_access_token || !verify_token) {
    return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
  }

  const configData = {
    domain_uuid: user.domain_uuid,
    app_id: app_id.trim(),
    app_secret: app_secret.trim(),
    page_id: page_id.trim(),
    page_access_token: page_access_token.trim(),
    verify_token: verify_token.trim(),
    webhook_url: webhook_url?.trim() || null,
    is_active: true,
    allowed_events,
    page_settings,
    update_date: new Date(),
    update_user: session.user.id
  }

  const config = await prisma.v_facebook_page_config.upsert({
    where: { domain_uuid: user.domain_uuid },
    update: configData,
    create: {
      ...configData,
      insert_date: new Date(),
      insert_user: session.user.id
    }
  })

  return NextResponse.json(config)
}
```

### 3. Message Send API
**Path**: `/src/app/api/internal-chat/facebook/messages/send/route.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/libs/auth'
import { FacebookMessageService } from '@/services/facebook/facebookMessageService'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'Domain not found' }, { status: 404 })
    }

    const body = await request.json()
    const { room_uuid, content, message_type = 'text' } = body

    if (!room_uuid || !content) {
      return NextResponse.json({ error: 'Room UUID and content required' }, { status: 400 })
    }

    const result = await FacebookMessageService.processOutboundMessage(
      user.domain_uuid,
      room_uuid,
      session.user.id,
      content,
      message_type
    )

    return NextResponse.json(result)
  } catch (error) {
    console.error('Facebook send message error:', error)
    return NextResponse.json({ error: 'Send failed' }, { status: 500 })
  }
}
```

## ZALO Integration Enhanced APIs

### 1. Enhanced Webhook Endpoint
**Path**: `/src/app/api/internal-chat/zalo/webhook/route.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server'
import { ZaloMessageService } from '@/services/zalo/zaloMessageService'
import { prisma } from '@/libs/db/prisma'

export async function POST(request: NextRequest) {
  try {
    const domain_uuid = request.nextUrl.searchParams.get('domain')
    if (!domain_uuid) {
      return NextResponse.json({ error: 'Domain UUID required' }, { status: 400 })
    }

    const webhookEvent = await request.json()

    // Log webhook event
    await prisma.v_zalo_webhook_event.create({
      data: {
        domain_uuid,
        zalo_userid: webhookEvent.sender?.id,
        event_name: webhookEvent.event_name,
        msg: webhookEvent,
        insert_date: new Date()
      }
    })

    // Process message events
    if (['user_send_text', 'user_send_image', 'user_send_file'].includes(webhookEvent.event_name)) {
      const result = await ZaloMessageService.processInboundMessage(domain_uuid, webhookEvent)
      
      // Broadcast via Socket.IO using main chat system
      if ((global as any).socketBroadcast?.broadcastMessage) {
        const fullMessage = await prisma.v_chat_messages.findUnique({
          where: { message_id: BigInt(result.internal_message_id) }
        })

        if (fullMessage) {
          const formattedMessage = {
            ...fullMessage,
            message_id: fullMessage.message_id.toString(),
            reply_to: fullMessage.reply_to?.toString() || null,
            author_name: 'ZALO User',
            platform: 'zalo'
          }

          ;(global as any).socketBroadcast.broadcastMessage(
            result.room_uuid,
            formattedMessage,
            domain_uuid
          )
        }
      }

      return NextResponse.json({ status: 'processed', ...result })
    }

    return NextResponse.json({ status: 'ignored' })
  } catch (error) {
    console.error('ZALO webhook error:', error)
    return NextResponse.json({ error: 'Processing failed' }, { status: 500 })
  }
}
```

### 2. ZALO Configuration API
**Path**: `/src/app/api/internal-chat/zalo/oa-config/route.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'

// GET - Retrieve ZALO OA configuration
export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const user = await prisma.v_users.findUnique({
    where: { user_uuid: session.user.id },
    select: { domain_uuid: true }
  })

  if (!user?.domain_uuid) {
    return NextResponse.json({ error: 'Domain not found' }, { status: 404 })
  }

  const config = await prisma.v_zalo_oa_config.findFirst({
    where: { domain_uuid: user.domain_uuid }
  })

  return NextResponse.json(config || {})
}

// POST - Create/Update ZALO OA configuration
export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const user = await prisma.v_users.findUnique({
    where: { user_uuid: session.user.id },
    select: { domain_uuid: true }
  })

  if (!user?.domain_uuid) {
    return NextResponse.json({ error: 'Domain not found' }, { status: 404 })
  }

  const body = await request.json()
  const {
    app_id,
    app_secret,
    oa_id,
    access_token,
    refresh_token,
    webhook_url,
    webhook_secret,
    token_expires_at,
    allowed_events = ['user_send_text', 'user_send_image', 'user_send_file'],
    oa_settings = {}
  } = body

  // Validate required fields
  if (!app_id || !app_secret || !oa_id || !access_token) {
    return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
  }

  const configData = {
    domain_uuid: user.domain_uuid,
    app_id: app_id.trim(),
    app_secret: app_secret.trim(),
    oa_id: oa_id.trim(),
    access_token: access_token.trim(),
    refresh_token: refresh_token?.trim() || null,
    webhook_url: webhook_url?.trim() || null,
    webhook_secret: webhook_secret?.trim() || null,
    is_active: true,
    token_expires_at: token_expires_at ? new Date(token_expires_at) : null,
    allowed_events,
    oa_settings,
    update_date: new Date(),
    update_user: session.user.id
  }

  const config = await prisma.v_zalo_oa_config.upsert({
    where: { domain_uuid: user.domain_uuid },
    update: configData,
    create: {
      ...configData,
      insert_date: new Date(),
      insert_user: session.user.id
    }
  })

  return NextResponse.json(config)
}
```

## Service Layer Implementation

### Facebook Message Service
**Path**: `/src/services/facebook/facebookMessageService.ts`

### ZALO Enhanced Message Service  
**Path**: `/src/services/zalo/zaloMessageService.ts`

### Shared Utilities
**Path**: `/src/utils/social/platformUtils.ts`

## Type Definitions

### Facebook Types
**Path**: `/src/types/facebook/facebookTypes.ts`

### ZALO Enhanced Types
**Path**: `/src/types/zalo/zaloTypes.ts`

## Testing Structure

### Unit Tests
- `/src/__tests__/services/facebook/`
- `/src/__tests__/services/zalo/`
- `/src/__tests__/api/internal-chat/facebook/`
- `/src/__tests__/api/internal-chat/zalo/`

### Integration Tests
- `/src/__tests__/integration/facebook-webhook.test.ts`
- `/src/__tests__/integration/zalo-webhook.test.ts`
- `/src/__tests__/integration/social-platforms.test.ts`
