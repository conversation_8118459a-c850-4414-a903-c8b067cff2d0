# Internal Chat System API Architecture

## REST API Endpoints

### Chat Rooms Management

#### GET /api/chat/rooms
- **Description**: Get all chat rooms for the current user's domain
- **Query Parameters**:
  - `type`: Filter by room type (direct, group, department, broadcast)
  - `page`: Pagination page number
  - `limit`: Items per page
- **Response**: Array of room objects with participant counts and last message

#### POST /api/chat/rooms
- **Description**: Create a new chat room
- **Body**:
  ```json
  {
    "room_name": "string",
    "room_description": "string",
    "room_type": "group|department|broadcast",
    "participants": ["user_uuid1", "user_uuid2"],
    "room_settings": {}
  }
  ```

#### GET /api/chat/rooms/{room_uuid}
- **Description**: Get detailed room information
- **Response**: Room details with participants and recent messages

#### PUT /api/chat/rooms/{room_uuid}
- **Description**: Update room information (name, description, settings)
- **Permissions**: Owner/Admin only

#### DELETE /api/chat/rooms/{room_uuid}
- **Description**: Archive/delete a room
- **Permissions**: Owner only

### Room Participants Management

#### GET /api/chat/rooms/{room_uuid}/participants
- **Description**: Get all participants in a room

#### POST /api/chat/rooms/{room_uuid}/participants
- **Description**: Add participants to a room
- **Body**:
  ```json
  {
    "user_uuids": ["user_uuid1", "user_uuid2"],
    "role": "member|moderator|admin"
  }
  ```

#### PUT /api/chat/rooms/{room_uuid}/participants/{user_uuid}
- **Description**: Update participant role or settings

#### DELETE /api/chat/rooms/{room_uuid}/participants/{user_uuid}
- **Description**: Remove participant from room

### Messages Management

#### GET /api/chat/rooms/{room_uuid}/messages
- **Description**: Get messages for a room with pagination
- **Query Parameters**:
  - `before`: Get messages before this message UUID
  - `after`: Get messages after this message UUID
  - `limit`: Number of messages (default: 50)

#### POST /api/chat/rooms/{room_uuid}/messages
- **Description**: Send a new message
- **Body**:
  ```json
  {
    "message_content": "string",
    "message_type": "text|file|image|video|audio",
    "parent_message_uuid": "uuid", // For replies
    "message_metadata": {}
  }
  ```

#### PUT /api/chat/messages/{message_uuid}
- **Description**: Edit a message
- **Permissions**: Message sender only

#### DELETE /api/chat/messages/{message_uuid}
- **Description**: Delete a message
- **Permissions**: Message sender or room admin

#### POST /api/chat/messages/{message_uuid}/reactions
- **Description**: Add/remove reaction to a message
- **Body**:
  ```json
  {
    "reaction_emoji": "👍",
    "action": "add|remove"
  }
  ```

### File Upload & Attachments

#### POST /api/chat/upload
- **Description**: Upload file for chat message
- **Content-Type**: multipart/form-data
- **Response**: File metadata for message attachment

#### GET /api/chat/files/{attachment_uuid}
- **Description**: Download/view file attachment
- **Permissions**: Room participant only

### User Presence & Status

#### GET /api/chat/presence
- **Description**: Get presence status for domain users

#### PUT /api/chat/presence
- **Description**: Update current user's presence status
- **Body**:
  ```json
  {
    "status": "online|away|busy|offline",
    "current_activity": "string"
  }
  ```

### Notifications

#### GET /api/chat/notifications
- **Description**: Get unread notifications for current user

#### PUT /api/chat/notifications/{notification_uuid}/read
- **Description**: Mark notification as read

#### PUT /api/chat/notifications/read-all
- **Description**: Mark all notifications as read

### Search & Discovery

#### GET /api/chat/search
- **Description**: Search messages across accessible rooms
- **Query Parameters**:
  - `query`: Search term
  - `room_uuid`: Limit search to specific room
  - `message_type`: Filter by message type
  - `date_from`: Start date
  - `date_to`: End date

#### GET /api/chat/users/search
- **Description**: Search users in domain for adding to rooms
- **Query Parameters**:
  - `query`: Search term (name, email)
  - `exclude_room`: Exclude users already in this room

## WebSocket Events

### Connection & Authentication

#### Client → Server Events

##### `chat_connect`
- **Description**: Authenticate and join domain chat namespace
- **Payload**:
  ```json
  {
    "domain_uuid": "string",
    "user_uuid": "string",
    "auth_token": "string"
  }
  ```

##### `join_room`
- **Description**: Join a specific chat room for real-time updates
- **Payload**:
  ```json
  {
    "room_uuid": "string"
  }
  ```

##### `leave_room`
- **Description**: Leave a chat room
- **Payload**:
  ```json
  {
    "room_uuid": "string"
  }
  ```

#### Server → Client Events

##### `chat_connected`
- **Description**: Confirmation of successful connection
- **Payload**:
  ```json
  {
    "user_uuid": "string",
    "domain_uuid": "string",
    "online_users": ["user_uuid1", "user_uuid2"]
  }
  ```

##### `room_joined`
- **Description**: Confirmation of joining a room
- **Payload**:
  ```json
  {
    "room_uuid": "string",
    "room_name": "string",
    "participant_count": 5
  }
  ```

### Real-time Messaging

#### Client → Server Events

##### `send_message`
- **Description**: Send a message to a room
- **Payload**:
  ```json
  {
    "room_uuid": "string",
    "message_content": "string",
    "message_type": "text|file|image|video|audio",
    "parent_message_uuid": "uuid",
    "message_metadata": {}
  }
  ```

##### `typing_start`
- **Description**: Indicate user is typing
- **Payload**:
  ```json
  {
    "room_uuid": "string"
  }
  ```

##### `typing_stop`
- **Description**: Indicate user stopped typing
- **Payload**:
  ```json
  {
    "room_uuid": "string"
  }
  ```

##### `message_read`
- **Description**: Mark messages as read
- **Payload**:
  ```json
  {
    "room_uuid": "string",
    "last_read_message_uuid": "string"
  }
  ```

#### Server → Client Events

##### `new_message`
- **Description**: New message received in a room
- **Payload**:
  ```json
  {
    "message_uuid": "string",
    "room_uuid": "string",
    "sender_user_uuid": "string",
    "sender_name": "string",
    "message_content": "string",
    "message_type": "string",
    "insert_date": "timestamp",
    "message_metadata": {}
  }
  ```

##### `message_updated`
- **Description**: Message was edited
- **Payload**: Same as new_message with is_edited: true

##### `message_deleted`
- **Description**: Message was deleted
- **Payload**:
  ```json
  {
    "message_uuid": "string",
    "room_uuid": "string"
  }
  ```

##### `user_typing`
- **Description**: User is typing in a room
- **Payload**:
  ```json
  {
    "room_uuid": "string",
    "user_uuid": "string",
    "user_name": "string"
  }
  ```

### Presence & Status Updates

#### Server → Client Events

##### `user_presence_changed`
- **Description**: User's online status changed
- **Payload**:
  ```json
  {
    "user_uuid": "string",
    "status": "online|away|busy|offline",
    "last_seen": "timestamp"
  }
  ```

##### `user_joined_domain`
- **Description**: User came online in domain
- **Payload**:
  ```json
  {
    "user_uuid": "string",
    "user_name": "string"
  }
  ```

##### `user_left_domain`
- **Description**: User went offline in domain
- **Payload**:
  ```json
  {
    "user_uuid": "string"
  }
  ```

### Room Management Events

#### Server → Client Events

##### `room_created`
- **Description**: New room was created
- **Payload**: Room object

##### `room_updated`
- **Description**: Room information was updated
- **Payload**: Updated room object

##### `participant_added`
- **Description**: New participant added to room
- **Payload**:
  ```json
  {
    "room_uuid": "string",
    "user_uuid": "string",
    "user_name": "string",
    "role": "string"
  }
  ```

##### `participant_removed`
- **Description**: Participant removed from room
- **Payload**:
  ```json
  {
    "room_uuid": "string",
    "user_uuid": "string"
  }
  ```

## Error Handling

### HTTP Status Codes
- **200**: Success
- **201**: Created
- **400**: Bad Request (validation errors)
- **401**: Unauthorized
- **403**: Forbidden (insufficient permissions)
- **404**: Not Found
- **429**: Rate Limited
- **500**: Internal Server Error

### WebSocket Error Events

##### `error`
- **Description**: General error occurred
- **Payload**:
  ```json
  {
    "error_code": "string",
    "error_message": "string",
    "context": {}
  }
  ```

## Rate Limiting & Security

### API Rate Limits
- **Message sending**: 60 messages per minute per user
- **File uploads**: 10 uploads per minute per user
- **Room creation**: 5 rooms per hour per user
- **Search requests**: 30 requests per minute per user

### Security Measures
- **Domain isolation**: Users can only access rooms in their domain
- **Permission-based access**: Role-based permissions for room management
- **File upload validation**: Type, size, and malware scanning
- **Message content filtering**: Optional profanity and spam filtering
- **Audit logging**: All actions logged for compliance
