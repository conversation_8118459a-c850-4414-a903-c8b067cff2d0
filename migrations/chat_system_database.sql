-- Internal Domain Chat System - FIXED Database Setup
-- Discord-inspired optimized schema with critical stability fixes
-- Run this single file to set up the entire chat system database (FRESH INSTALL)
--
-- IMPORTANT CHANGES FOR EXTERNAL PLATFORM INTEGRATION:
-- - v_chat_rooms.created_by_user_uuid is nullable (no FK constraint) for system-created rooms
-- - v_chat_messages.author_uuid is nullable (no FK constraint) for system messages
-- - Added validation constraints to ensure data integrity for user vs system created content
-- - Supports Telegram, ZALO, and other external platform integrations

-- =====================================================
-- CORE CHAT TABLES
-- =====================================================

-- Chat Rooms Table
CREATE TABLE v_chat_rooms (
    room_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain_uuid UUID NOT NULL,
    room_name VARCHAR(255),
    room_description TEXT,
    room_type VARCHAR(50) NOT NULL CHECK (room_type IN ('direct', 'group', 'department', 'broadcast')),
    room_avatar VARCHAR(500),
    created_by_user_uuid UUID, -- Nullable for system-created rooms from external platforms
    is_active BOOLEAN DEFAULT true,
    is_archived BOOLEAN DEFAULT false,
    max_participants INTEGER DEFAULT 100,
    room_settings JSONB DEFAULT '{}',
    insert_date TIMESTAMPTZ DEFAULT NOW(),
    insert_user UUID,
    update_date TIMESTAMPTZ DEFAULT NOW(),
    update_user UUID,

    -- Validation constraint: rooms must either have a creator or be system-created
    CONSTRAINT chk_chat_rooms_creator_or_system
    CHECK (
        created_by_user_uuid IS NOT NULL OR
        (room_settings->>'auto_created')::boolean = true OR
        (room_settings->>'platform') IS NOT NULL
    )
);

-- Chat Room Participants Table
CREATE TABLE v_chat_room_participants (
    participant_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    room_uuid UUID NOT NULL,
    user_uuid UUID NOT NULL,
    participant_role VARCHAR(50) DEFAULT 'member' CHECK (participant_role IN ('owner', 'admin', 'moderator', 'member')),
    joined_date TIMESTAMPTZ DEFAULT NOW(),
    last_read_message_id BIGINT,
    is_muted BOOLEAN DEFAULT false,
    notification_settings JSONB DEFAULT '{"mentions": true, "all_messages": true}',
    deleted_at TIMESTAMPTZ NULL, -- Soft deletion timestamp for conversation hiding
    insert_date TIMESTAMPTZ DEFAULT NOW(),
    insert_user UUID,
    update_date TIMESTAMPTZ DEFAULT NOW(),
    update_user UUID,
    
    CONSTRAINT unique_room_participant UNIQUE (room_uuid, user_uuid)
);

-- Chat Messages Table (Discord-inspired minimal design)
CREATE TABLE v_chat_messages (
    message_id BIGSERIAL PRIMARY KEY,
    room_uuid UUID NOT NULL,
    author_uuid UUID, -- Nullable for system messages from external platforms
    content TEXT,
    message_type SMALLINT DEFAULT 0, -- 0=text, 1=image, 2=file, 3=system, 4=call
    reply_to BIGINT,
    edited_at BIGINT,
    created_at BIGINT NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW())::BIGINT,
    flags INTEGER DEFAULT 0, -- Bitfield: 1=deleted, 2=pinned, 4=system, etc.

    -- Validation constraint: messages must either have an author or be system messages
    CONSTRAINT chk_chat_messages_author_or_system
    CHECK (
        author_uuid IS NOT NULL OR
        (flags & 4) = 4 OR -- System message flag
        message_type = 3    -- System message type
    )
);

-- Message Reactions Table
CREATE TABLE v_chat_message_reactions (
    message_id BIGINT NOT NULL,
    user_uuid UUID NOT NULL,
    emoji VARCHAR(10) NOT NULL,
    created_at BIGINT NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW())::BIGINT,
    
    PRIMARY KEY (message_id, user_uuid, emoji)
);

-- Message Attachments Table
CREATE TABLE v_chat_message_attachments (
    attachment_id BIGSERIAL PRIMARY KEY,
    message_id BIGINT NOT NULL,
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER,
    content_type VARCHAR(100),
    width INTEGER,
    height INTEGER
);

-- User Presence Table
CREATE TABLE v_chat_user_presence (
    presence_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_uuid UUID NOT NULL UNIQUE,
    domain_uuid UUID NOT NULL,
    status VARCHAR(50) DEFAULT 'offline' CHECK (status IN ('online', 'away', 'busy', 'offline')),
    last_seen TIMESTAMPTZ DEFAULT NOW(),
    current_activity VARCHAR(100),
    socket_id VARCHAR(100),
    update_date TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- FIXED NOTIFICATION SYSTEM - SINGLE SOURCE OF TRUTH
-- =====================================================

-- Unified Unread Counts Table (REPLACES multiple counting systems)
-- This is the SINGLE SOURCE OF TRUTH for unread message counts
CREATE TABLE v_chat_unread_counts (
    count_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_uuid UUID NOT NULL,
    room_uuid UUID NOT NULL,
    unread_count INTEGER NOT NULL DEFAULT 0,
    last_read_message_id BIGINT,
    last_updated TIMESTAMPTZ DEFAULT NOW(),
    
    -- Ensure one count record per user-room combination
    CONSTRAINT unique_user_room_count UNIQUE (user_uuid, room_uuid),
    
    -- Ensure count is never negative
    CONSTRAINT chk_unread_count_positive CHECK (unread_count >= 0)
);

-- Simplified Notifications Table (for push notifications only)
-- No longer used for counting - only for actual notification delivery
CREATE TABLE v_chat_notifications (
    notification_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_uuid UUID NOT NULL,
    room_uuid UUID NOT NULL,
    message_id BIGINT NOT NULL,
    notification_type VARCHAR(50) DEFAULT 'message' CHECK (notification_type IN ('message', 'mention', 'reply', 'room_invite')),
    is_read BOOLEAN DEFAULT false,
    is_sent BOOLEAN DEFAULT false,
    insert_date TIMESTAMPTZ DEFAULT NOW(),
    read_date TIMESTAMPTZ,
    expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '30 days'),
    
    -- Prevent duplicate notifications for same message to same user
    CONSTRAINT unique_user_message_notification UNIQUE (user_uuid, message_id, notification_type)
);

-- =====================================================
-- FOREIGN KEY CONSTRAINTS
-- =====================================================

-- Chat Rooms Constraints
ALTER TABLE v_chat_rooms 
    ADD CONSTRAINT fk_chat_rooms_domain 
    FOREIGN KEY (domain_uuid) REFERENCES v_domains(domain_uuid) ON DELETE CASCADE;

-- Note: No foreign key constraint on created_by_user_uuid to allow system-created rooms
-- from external platforms (Telegram, ZALO, etc.) with NULL creator

-- Chat Room Participants Constraints
ALTER TABLE v_chat_room_participants 
    ADD CONSTRAINT fk_participants_room 
    FOREIGN KEY (room_uuid) REFERENCES v_chat_rooms(room_uuid) ON DELETE CASCADE;

ALTER TABLE v_chat_room_participants 
    ADD CONSTRAINT fk_participants_user 
    FOREIGN KEY (user_uuid) REFERENCES v_users(user_uuid) ON DELETE CASCADE;

-- Chat Messages Constraints
ALTER TABLE v_chat_messages 
    ADD CONSTRAINT fk_messages_room 
    FOREIGN KEY (room_uuid) REFERENCES v_chat_rooms(room_uuid) ON DELETE CASCADE;

-- Note: No foreign key constraint on author_uuid to allow system messages
-- from external platforms (Telegram, ZALO, etc.) with NULL author

ALTER TABLE v_chat_messages 
    ADD CONSTRAINT fk_messages_reply 
    FOREIGN KEY (reply_to) REFERENCES v_chat_messages(message_id);

-- Message Reactions Constraints
ALTER TABLE v_chat_message_reactions 
    ADD CONSTRAINT fk_reactions_message 
    FOREIGN KEY (message_id) REFERENCES v_chat_messages(message_id) ON DELETE CASCADE;

ALTER TABLE v_chat_message_reactions 
    ADD CONSTRAINT fk_reactions_user 
    FOREIGN KEY (user_uuid) REFERENCES v_users(user_uuid) ON DELETE CASCADE;

-- Message Attachments Constraints
ALTER TABLE v_chat_message_attachments 
    ADD CONSTRAINT fk_attachments_message 
    FOREIGN KEY (message_id) REFERENCES v_chat_messages(message_id) ON DELETE CASCADE;

-- User Presence Constraints
ALTER TABLE v_chat_user_presence 
    ADD CONSTRAINT fk_presence_user 
    FOREIGN KEY (user_uuid) REFERENCES v_users(user_uuid) ON DELETE CASCADE;

ALTER TABLE v_chat_user_presence 
    ADD CONSTRAINT fk_presence_domain 
    FOREIGN KEY (domain_uuid) REFERENCES v_domains(domain_uuid);

-- Unread Counts Constraints (CRITICAL for data integrity)
ALTER TABLE v_chat_unread_counts 
    ADD CONSTRAINT fk_unread_user 
    FOREIGN KEY (user_uuid) REFERENCES v_users(user_uuid) ON DELETE CASCADE;

ALTER TABLE v_chat_unread_counts
    ADD CONSTRAINT fk_unread_room
    FOREIGN KEY (room_uuid) REFERENCES v_chat_rooms(room_uuid) ON DELETE CASCADE;

ALTER TABLE v_chat_unread_counts
    ADD CONSTRAINT fk_unread_last_message
    FOREIGN KEY (last_read_message_id) REFERENCES v_chat_messages(message_id);

-- Notifications Constraints
ALTER TABLE v_chat_notifications 
    ADD CONSTRAINT fk_notifications_user 
    FOREIGN KEY (user_uuid) REFERENCES v_users(user_uuid) ON DELETE CASCADE;

ALTER TABLE v_chat_notifications 
    ADD CONSTRAINT fk_notifications_room 
    FOREIGN KEY (room_uuid) REFERENCES v_chat_rooms(room_uuid) ON DELETE CASCADE;

ALTER TABLE v_chat_notifications 
    ADD CONSTRAINT fk_notifications_message 
    FOREIGN KEY (message_id) REFERENCES v_chat_messages(message_id) ON DELETE CASCADE;

-- =====================================================
-- CRITICAL PERFORMANCE INDEXES
-- =====================================================

-- Chat Rooms Indexes
CREATE INDEX idx_chat_rooms_domain ON v_chat_rooms(domain_uuid);
CREATE INDEX idx_chat_rooms_active ON v_chat_rooms(domain_uuid, is_active) WHERE is_active = true;
CREATE INDEX idx_chat_rooms_type ON v_chat_rooms(domain_uuid, room_type);
CREATE INDEX idx_chat_rooms_creator ON v_chat_rooms(created_by_user_uuid) WHERE created_by_user_uuid IS NOT NULL;

-- Chat Room Participants Indexes
CREATE INDEX idx_chat_participants_room ON v_chat_room_participants(room_uuid);
CREATE INDEX idx_chat_participants_user ON v_chat_room_participants(user_uuid);
CREATE INDEX idx_chat_participants_active ON v_chat_room_participants(user_uuid, room_uuid) WHERE deleted_at IS NULL;
CREATE INDEX idx_chat_participants_deleted_at ON v_chat_room_participants(user_uuid, deleted_at);

-- Chat Messages Indexes (CRITICAL for performance)
CREATE INDEX idx_chat_messages_room_time ON v_chat_messages(room_uuid, created_at DESC);
CREATE INDEX idx_chat_messages_room_id_time ON v_chat_messages(room_uuid, message_id DESC);
CREATE INDEX idx_chat_messages_author ON v_chat_messages(author_uuid) WHERE author_uuid IS NOT NULL;
CREATE INDEX idx_chat_messages_reply ON v_chat_messages(reply_to) WHERE reply_to IS NOT NULL;

-- Exclude deleted messages from main queries
CREATE INDEX idx_chat_messages_room_active ON v_chat_messages(room_uuid, created_at DESC) WHERE (flags & 1) = 0;

-- Message Reactions Indexes
CREATE INDEX idx_chat_reactions_message ON v_chat_message_reactions(message_id);

-- Message Attachments Indexes
CREATE INDEX idx_chat_attachments_message ON v_chat_message_attachments(message_id);

-- User Presence Indexes
CREATE INDEX idx_chat_presence_domain_status ON v_chat_user_presence(domain_uuid, status) WHERE status != 'offline';

-- CRITICAL: Unread Counts Indexes (for fast count queries)
CREATE INDEX idx_chat_unread_counts_user ON v_chat_unread_counts(user_uuid);
CREATE INDEX idx_chat_unread_counts_user_nonzero ON v_chat_unread_counts(user_uuid) WHERE unread_count > 0;
CREATE INDEX idx_chat_unread_counts_room ON v_chat_unread_counts(room_uuid);

-- Notifications Indexes (for cleanup and delivery)
CREATE INDEX idx_chat_notifications_user_unread ON v_chat_notifications(user_uuid, is_read) WHERE is_read = false;
CREATE INDEX idx_chat_notifications_expires ON v_chat_notifications(expires_at);

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE v_chat_unread_counts IS 'SINGLE SOURCE OF TRUTH for unread message counts. Replaces multiple counting systems to prevent inconsistencies.';
COMMENT ON COLUMN v_chat_room_participants.deleted_at IS 'Soft deletion timestamp. When set, conversation is hidden from user. Reappears when new messages arrive.';
COMMENT ON TABLE v_chat_notifications IS 'Used only for push notification delivery, NOT for counting. Counts are managed in v_chat_unread_counts.';
COMMENT ON CONSTRAINT unique_user_room_count ON v_chat_unread_counts IS 'Ensures exactly one count record per user-room combination to prevent duplicates.';
COMMENT ON CONSTRAINT chk_unread_count_positive ON v_chat_unread_counts IS 'Prevents negative unread counts which would indicate a bug in the counting logic.';

-- =====================================================
-- ZALO OA INTEGRATION TABLES
-- =====================================================

-- ZALO OA Contacts Table
CREATE TABLE v_zalo_oa_contacts (
    contact_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain_uuid UUID NOT NULL,
    zalo_user_id VARCHAR(255) NOT NULL,
    display_name VARCHAR(255),
    avatar_url TEXT,
    phone VARCHAR(50),
    is_follower BOOLEAN DEFAULT false,
    last_interaction_date TIMESTAMPTZ,
    contact_info JSONB DEFAULT '{}',
    insert_date TIMESTAMPTZ DEFAULT NOW(),
    insert_user UUID,
    update_date TIMESTAMPTZ DEFAULT NOW(),
    update_user UUID,

    CONSTRAINT unique_domain_zalo_user UNIQUE (domain_uuid, zalo_user_id)
);

-- ZALO OA Chat Rooms Bridge Table
CREATE TABLE v_zalo_oa_chat_rooms (
    zalo_room_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain_uuid UUID NOT NULL,
    internal_room_uuid UUID NOT NULL,
    zalo_contact_uuid UUID NOT NULL,
    assigned_agent_uuid UUID,
    room_status VARCHAR(50) DEFAULT 'active' CHECK (room_status IN ('active', 'closed', 'transferred', 'pending')),
    last_message_at TIMESTAMPTZ,
    conversation_metadata JSONB DEFAULT '{}',
    insert_date TIMESTAMPTZ DEFAULT NOW(),
    insert_user UUID,
    update_date TIMESTAMPTZ DEFAULT NOW(),
    update_user UUID,

    CONSTRAINT unique_internal_room_zalo UNIQUE (internal_room_uuid),
    CONSTRAINT unique_domain_zalo_contact UNIQUE (domain_uuid, zalo_contact_uuid)
);

-- ZALO Message Mapping Table
CREATE TABLE v_zalo_message_mapping (
    mapping_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain_uuid UUID NOT NULL,
    internal_message_id BIGINT NOT NULL,
    zalo_message_id VARCHAR(255),
    zalo_user_id VARCHAR(255),
    message_direction VARCHAR(20) NOT NULL CHECK (message_direction IN ('inbound', 'outbound')),
    zalo_event_name VARCHAR(100),
    delivery_status VARCHAR(50) DEFAULT 'pending' CHECK (delivery_status IN ('pending', 'sent', 'delivered', 'failed')),
    error_message TEXT,
    insert_date TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT unique_internal_message_mapping UNIQUE (internal_message_id)
);

-- ZALO OA Foreign Key Constraints
ALTER TABLE v_zalo_oa_contacts
    ADD CONSTRAINT fk_zalo_contacts_domain
    FOREIGN KEY (domain_uuid) REFERENCES v_domains(domain_uuid) ON DELETE CASCADE;

ALTER TABLE v_zalo_oa_chat_rooms
    ADD CONSTRAINT fk_zalo_rooms_domain
    FOREIGN KEY (domain_uuid) REFERENCES v_domains(domain_uuid) ON DELETE CASCADE;

ALTER TABLE v_zalo_oa_chat_rooms
    ADD CONSTRAINT fk_zalo_rooms_internal_room
    FOREIGN KEY (internal_room_uuid) REFERENCES v_chat_rooms(room_uuid) ON DELETE CASCADE;

ALTER TABLE v_zalo_oa_chat_rooms
    ADD CONSTRAINT fk_zalo_rooms_contact
    FOREIGN KEY (zalo_contact_uuid) REFERENCES v_zalo_oa_contacts(contact_uuid) ON DELETE CASCADE;

ALTER TABLE v_zalo_message_mapping
    ADD CONSTRAINT fk_zalo_mapping_domain
    FOREIGN KEY (domain_uuid) REFERENCES v_domains(domain_uuid) ON DELETE CASCADE;

ALTER TABLE v_zalo_message_mapping
    ADD CONSTRAINT fk_zalo_mapping_message
    FOREIGN KEY (internal_message_id) REFERENCES v_chat_messages(message_id) ON DELETE CASCADE;

-- ZALO OA Performance Indexes
CREATE INDEX idx_zalo_contacts_domain ON v_zalo_oa_contacts(domain_uuid);
CREATE INDEX idx_zalo_contacts_domain_user ON v_zalo_oa_contacts(domain_uuid, zalo_user_id);
CREATE INDEX idx_zalo_rooms_domain ON v_zalo_oa_chat_rooms(domain_uuid);
CREATE INDEX idx_zalo_rooms_internal_room ON v_zalo_oa_chat_rooms(internal_room_uuid);
CREATE INDEX idx_zalo_mapping_domain_message ON v_zalo_message_mapping(domain_uuid, internal_message_id);

-- ZALO OA Security Constraints
ALTER TABLE v_zalo_oa_contacts ADD CONSTRAINT chk_zalo_contacts_domain_not_null CHECK (domain_uuid IS NOT NULL);
ALTER TABLE v_zalo_oa_chat_rooms ADD CONSTRAINT chk_zalo_rooms_domain_not_null CHECK (domain_uuid IS NOT NULL);
ALTER TABLE v_zalo_message_mapping ADD CONSTRAINT chk_zalo_mapping_domain_not_null CHECK (domain_uuid IS NOT NULL);

-- =====================================================
-- TELEGRAM INTEGRATION TABLES
-- =====================================================

-- Telegram Bot Contacts Table
-- Stores Telegram user information with strict domain isolation
CREATE TABLE v_telegram_contacts (
    contact_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain_uuid UUID NOT NULL,
    telegram_user_id BIGINT NOT NULL,
    telegram_chat_id BIGINT NOT NULL,
    username VARCHAR(255),
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    phone VARCHAR(50),
    is_bot BOOLEAN DEFAULT false,
    language_code VARCHAR(10),
    contact_info JSONB DEFAULT '{}', -- Store additional Telegram user data
    last_interaction_date TIMESTAMPTZ,
    insert_date TIMESTAMPTZ DEFAULT NOW(),
    insert_user UUID,
    update_date TIMESTAMPTZ DEFAULT NOW(),
    update_user UUID,

    -- Ensure unique Telegram user per domain (privacy isolation)
    CONSTRAINT unique_domain_telegram_user UNIQUE (domain_uuid, telegram_user_id),
    CONSTRAINT unique_domain_telegram_chat UNIQUE (domain_uuid, telegram_chat_id)
);

-- Telegram Chat Rooms Bridge Table
-- Links Telegram conversations with internal chat rooms
CREATE TABLE v_telegram_chat_rooms (
    telegram_room_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain_uuid UUID NOT NULL,
    internal_room_uuid UUID NOT NULL,
    telegram_contact_uuid UUID NOT NULL,
    assigned_agent_uuid UUID,
    room_status VARCHAR(50) DEFAULT 'active' CHECK (room_status IN ('active', 'closed', 'transferred', 'pending')),
    last_message_at TIMESTAMPTZ,
    conversation_metadata JSONB DEFAULT '{}', -- Store Telegram-specific conversation data
    insert_date TIMESTAMPTZ DEFAULT NOW(),
    insert_user UUID,
    update_date TIMESTAMPTZ DEFAULT NOW(),
    update_user UUID,

    -- Ensure one Telegram contact per internal room
    CONSTRAINT unique_internal_room_telegram UNIQUE (internal_room_uuid),
    CONSTRAINT unique_domain_telegram_contact UNIQUE (domain_uuid, telegram_contact_uuid)
);

-- Telegram Message Mapping Table
-- Maps internal chat messages to Telegram messages for bidirectional sync
CREATE TABLE v_telegram_message_mapping (
    mapping_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain_uuid UUID NOT NULL,
    internal_message_id BIGINT NOT NULL,
    telegram_message_id BIGINT,
    telegram_chat_id BIGINT,
    telegram_user_id BIGINT,
    message_direction VARCHAR(20) NOT NULL CHECK (message_direction IN ('inbound', 'outbound')),
    telegram_update_id BIGINT,
    delivery_status VARCHAR(50) DEFAULT 'pending' CHECK (delivery_status IN ('pending', 'sent', 'delivered', 'failed')),
    error_message TEXT,
    insert_date TIMESTAMPTZ DEFAULT NOW(),

    -- Ensure unique mapping per internal message
    CONSTRAINT unique_internal_message_telegram_mapping UNIQUE (internal_message_id)
);

-- Telegram Bot Configuration Table
-- Stores bot configuration per domain for multi-tenant support
CREATE TABLE v_telegram_bot_config (
    config_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain_uuid UUID NOT NULL UNIQUE,
    bot_token VARCHAR(500) NOT NULL,
    bot_username VARCHAR(255),
    webhook_url VARCHAR(500),
    webhook_secret VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    allowed_updates JSONB DEFAULT '["message", "edited_message", "callback_query"]',
    bot_settings JSONB DEFAULT '{}',
    insert_date TIMESTAMPTZ DEFAULT NOW(),
    insert_user UUID,
    update_date TIMESTAMPTZ DEFAULT NOW(),
    update_user UUID,

    -- Ensure unique bot per domain
    CONSTRAINT unique_domain_telegram_bot UNIQUE (domain_uuid)
);

-- Note: Assignment is handled through v_chat_room_participants table
-- External platform rooms (Telegram, ZALO) can have internal users as participants for assignment

-- =====================================================
-- TELEGRAM FOREIGN KEY CONSTRAINTS
-- =====================================================

-- Telegram Contacts Constraints
ALTER TABLE v_telegram_contacts
    ADD CONSTRAINT fk_telegram_contacts_domain
    FOREIGN KEY (domain_uuid) REFERENCES v_domains(domain_uuid) ON DELETE CASCADE;

ALTER TABLE v_telegram_contacts
    ADD CONSTRAINT fk_telegram_contacts_insert_user
    FOREIGN KEY (insert_user) REFERENCES v_users(user_uuid);

ALTER TABLE v_telegram_contacts
    ADD CONSTRAINT fk_telegram_contacts_update_user
    FOREIGN KEY (update_user) REFERENCES v_users(user_uuid);

-- Telegram Chat Rooms Constraints
ALTER TABLE v_telegram_chat_rooms
    ADD CONSTRAINT fk_telegram_rooms_domain
    FOREIGN KEY (domain_uuid) REFERENCES v_domains(domain_uuid) ON DELETE CASCADE;

ALTER TABLE v_telegram_chat_rooms
    ADD CONSTRAINT fk_telegram_rooms_internal_room
    FOREIGN KEY (internal_room_uuid) REFERENCES v_chat_rooms(room_uuid) ON DELETE CASCADE;

ALTER TABLE v_telegram_chat_rooms
    ADD CONSTRAINT fk_telegram_rooms_contact
    FOREIGN KEY (telegram_contact_uuid) REFERENCES v_telegram_contacts(contact_uuid) ON DELETE CASCADE;

ALTER TABLE v_telegram_chat_rooms
    ADD CONSTRAINT fk_telegram_rooms_agent
    FOREIGN KEY (assigned_agent_uuid) REFERENCES v_users(user_uuid);

ALTER TABLE v_telegram_chat_rooms
    ADD CONSTRAINT fk_telegram_rooms_insert_user
    FOREIGN KEY (insert_user) REFERENCES v_users(user_uuid);

ALTER TABLE v_telegram_chat_rooms
    ADD CONSTRAINT fk_telegram_rooms_update_user
    FOREIGN KEY (update_user) REFERENCES v_users(user_uuid);

-- Telegram Message Mapping Constraints
ALTER TABLE v_telegram_message_mapping
    ADD CONSTRAINT fk_telegram_mapping_domain
    FOREIGN KEY (domain_uuid) REFERENCES v_domains(domain_uuid) ON DELETE CASCADE;

ALTER TABLE v_telegram_message_mapping
    ADD CONSTRAINT fk_telegram_mapping_message
    FOREIGN KEY (internal_message_id) REFERENCES v_chat_messages(message_id) ON DELETE CASCADE;

-- Telegram Bot Configuration Constraints
ALTER TABLE v_telegram_bot_config
    ADD CONSTRAINT fk_telegram_bot_domain
    FOREIGN KEY (domain_uuid) REFERENCES v_domains(domain_uuid) ON DELETE CASCADE;

ALTER TABLE v_telegram_bot_config
    ADD CONSTRAINT fk_telegram_bot_insert_user
    FOREIGN KEY (insert_user) REFERENCES v_users(user_uuid);

ALTER TABLE v_telegram_bot_config
    ADD CONSTRAINT fk_telegram_bot_update_user
    FOREIGN KEY (update_user) REFERENCES v_users(user_uuid);

-- =====================================================
-- TELEGRAM PERFORMANCE INDEXES
-- =====================================================

-- Telegram Contacts Indexes (Critical for domain isolation)
CREATE INDEX idx_telegram_contacts_domain ON v_telegram_contacts(domain_uuid);
CREATE INDEX idx_telegram_contacts_domain_user ON v_telegram_contacts(domain_uuid, telegram_user_id);
CREATE INDEX idx_telegram_contacts_domain_chat ON v_telegram_contacts(domain_uuid, telegram_chat_id);
CREATE INDEX idx_telegram_contacts_username ON v_telegram_contacts(domain_uuid, username) WHERE username IS NOT NULL;
CREATE INDEX idx_telegram_contacts_phone ON v_telegram_contacts(domain_uuid, phone) WHERE phone IS NOT NULL;
CREATE INDEX idx_telegram_contacts_last_interaction ON v_telegram_contacts(domain_uuid, last_interaction_date DESC);

-- Telegram Chat Rooms Indexes (Critical for agent assignment and status)
CREATE INDEX idx_telegram_rooms_domain ON v_telegram_chat_rooms(domain_uuid);
CREATE INDEX idx_telegram_rooms_domain_contact ON v_telegram_chat_rooms(domain_uuid, telegram_contact_uuid);
CREATE INDEX idx_telegram_rooms_internal_room ON v_telegram_chat_rooms(internal_room_uuid);
CREATE INDEX idx_telegram_rooms_agent ON v_telegram_chat_rooms(assigned_agent_uuid) WHERE assigned_agent_uuid IS NOT NULL;
CREATE INDEX idx_telegram_rooms_status ON v_telegram_chat_rooms(domain_uuid, room_status);
CREATE INDEX idx_telegram_rooms_active ON v_telegram_chat_rooms(domain_uuid, room_status, last_message_at DESC) WHERE room_status = 'active';

-- Telegram Message Mapping Indexes (Critical for message sync performance)
CREATE INDEX idx_telegram_mapping_domain ON v_telegram_message_mapping(domain_uuid);
CREATE INDEX idx_telegram_mapping_domain_message ON v_telegram_message_mapping(domain_uuid, internal_message_id);
CREATE INDEX idx_telegram_mapping_telegram_message ON v_telegram_message_mapping(telegram_message_id) WHERE telegram_message_id IS NOT NULL;
CREATE INDEX idx_telegram_mapping_chat_id ON v_telegram_message_mapping(domain_uuid, telegram_chat_id);
CREATE INDEX idx_telegram_mapping_direction ON v_telegram_message_mapping(domain_uuid, message_direction);
CREATE INDEX idx_telegram_mapping_status ON v_telegram_message_mapping(delivery_status, insert_date) WHERE delivery_status IN ('pending', 'failed');
CREATE INDEX idx_telegram_mapping_user_id ON v_telegram_message_mapping(domain_uuid, telegram_user_id);

-- Telegram Bot Configuration Indexes
CREATE INDEX idx_telegram_bot_domain ON v_telegram_bot_config(domain_uuid);
CREATE INDEX idx_telegram_bot_active ON v_telegram_bot_config(domain_uuid, is_active) WHERE is_active = true;

-- Assignment indexes are handled by existing v_chat_room_participants indexes

-- =====================================================
-- TELEGRAM SECURITY CONSTRAINTS
-- =====================================================

-- Ensure domain_uuid is never null (critical for privacy isolation)
ALTER TABLE v_telegram_contacts ADD CONSTRAINT chk_telegram_contacts_domain_not_null CHECK (domain_uuid IS NOT NULL);
ALTER TABLE v_telegram_chat_rooms ADD CONSTRAINT chk_telegram_rooms_domain_not_null CHECK (domain_uuid IS NOT NULL);
ALTER TABLE v_telegram_message_mapping ADD CONSTRAINT chk_telegram_mapping_domain_not_null CHECK (domain_uuid IS NOT NULL);
ALTER TABLE v_telegram_bot_config ADD CONSTRAINT chk_telegram_bot_domain_not_null CHECK (domain_uuid IS NOT NULL);

-- Ensure Telegram user ID and chat ID are valid
ALTER TABLE v_telegram_contacts ADD CONSTRAINT chk_telegram_contacts_user_id_valid CHECK (telegram_user_id > 0);
ALTER TABLE v_telegram_contacts ADD CONSTRAINT chk_telegram_contacts_chat_id_valid CHECK (telegram_chat_id IS NOT NULL);

-- Ensure message direction is valid
ALTER TABLE v_telegram_message_mapping ADD CONSTRAINT chk_telegram_mapping_direction_not_empty CHECK (message_direction IS NOT NULL AND LENGTH(TRIM(message_direction)) > 0);

-- Ensure bot token is not empty
ALTER TABLE v_telegram_bot_config ADD CONSTRAINT chk_telegram_bot_token_not_empty CHECK (bot_token IS NOT NULL AND LENGTH(TRIM(bot_token)) > 0);

-- Assignment constraints are handled by existing v_chat_room_participants constraints

-- =====================================================
-- MULTI-PLATFORM INTEGRATION VIEWS
-- =====================================================

-- Unified view of all external platform contacts
CREATE VIEW v_external_platform_contacts AS
SELECT
    'zalo' as platform,
    contact_uuid,
    domain_uuid,
    zalo_user_id as external_user_id,
    display_name,
    avatar_url,
    phone,
    last_interaction_date,
    insert_date,
    update_date
FROM v_zalo_oa_contacts
UNION ALL
SELECT
    'telegram' as platform,
    contact_uuid,
    domain_uuid,
    telegram_user_id::text as external_user_id,
    COALESCE(first_name || ' ' || last_name, username, first_name) as display_name,
    NULL as avatar_url,
    phone,
    last_interaction_date,
    insert_date,
    update_date
FROM v_telegram_contacts;

-- Unified view of all external platform chat rooms
CREATE VIEW v_external_platform_rooms AS
SELECT
    'zalo' as platform,
    zalo_room_uuid as platform_room_uuid,
    domain_uuid,
    internal_room_uuid,
    assigned_agent_uuid,
    room_status,
    last_message_at,
    insert_date,
    update_date
FROM v_zalo_oa_chat_rooms
UNION ALL
SELECT
    'telegram' as platform,
    telegram_room_uuid as platform_room_uuid,
    domain_uuid,
    internal_room_uuid,
    assigned_agent_uuid,
    room_status,
    last_message_at,
    insert_date,
    update_date
FROM v_telegram_chat_rooms;

-- Unified view of all external platform message mappings
CREATE VIEW v_external_platform_messages AS
SELECT
    'zalo' as platform,
    mapping_uuid,
    domain_uuid,
    internal_message_id,
    zalo_message_id as external_message_id,
    zalo_user_id as external_user_id,
    message_direction,
    delivery_status,
    error_message,
    insert_date
FROM v_zalo_message_mapping
UNION ALL
SELECT
    'telegram' as platform,
    mapping_uuid,
    domain_uuid,
    internal_message_id,
    telegram_message_id::text as external_message_id,
    telegram_user_id::text as external_user_id,
    message_direction,
    delivery_status,
    error_message,
    insert_date
FROM v_telegram_message_mapping;

-- =====================================================
-- HELPFUL COMMENTS FOR CENTRALIZED CHAT SYSTEM
-- =====================================================

COMMENT ON VIEW v_external_platform_contacts IS 'Unified view of all external platform contacts (ZALO, Telegram) for centralized management';
COMMENT ON VIEW v_external_platform_rooms IS 'Unified view of all external platform chat rooms linked to internal chat system';
COMMENT ON VIEW v_external_platform_messages IS 'Unified view of all external platform message mappings for sync tracking';

COMMENT ON TABLE v_telegram_contacts IS 'Telegram user contacts with domain isolation for multi-tenant support';
COMMENT ON TABLE v_telegram_chat_rooms IS 'Bridge table linking Telegram conversations to internal chat rooms';
COMMENT ON TABLE v_telegram_message_mapping IS 'Bidirectional message mapping between internal chat and Telegram';
COMMENT ON TABLE v_telegram_bot_config IS 'Per-domain Telegram bot configuration for multi-tenant bot management';
-- Assignment is handled through v_chat_room_participants table
