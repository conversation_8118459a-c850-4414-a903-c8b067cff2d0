-- Social Platforms Integration Migration
-- Adds Facebook Messenger integration and enhances ZALO integration
-- Following the established Telegram integration pattern

-- =====================================================
-- FACEBOOK MESSENGER INTEGRATION
-- =====================================================

-- Facebook Page Configuration Table
-- Stores Facebook app and page configuration per domain for multi-tenant support
CREATE TABLE v_facebook_page_config (
    config_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain_uuid UUID NOT NULL UNIQUE,
    app_id VARCHAR(255) NOT NULL,
    app_secret VARCHAR(500) NOT NULL,
    page_id VARCHAR(255) NOT NULL,
    page_access_token VARCHAR(1000) NOT NULL,
    verify_token VARCHAR(255) NOT NULL,
    webhook_url VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    allowed_events JSONB DEFAULT '["messages", "messaging_postbacks", "messaging_optins"]',
    page_settings JSONB DEFAULT '{}',
    insert_date TIMESTAMPTZ DEFAULT NOW(),
    insert_user UUID,
    update_date TIMESTAMPTZ DEFAULT NOW(),
    update_user UUID,

    -- Ensure unique configuration per domain
    CONSTRAINT unique_domain_facebook_page UNIQUE (domain_uuid)
);

-- Facebook Contacts Table
-- Stores Facebook user information for conversations
CREATE TABLE v_facebook_contacts (
    contact_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain_uuid UUID NOT NULL,
    facebook_user_id VARCHAR(255) NOT NULL,
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    profile_pic VARCHAR(500),
    locale VARCHAR(10),
    timezone INT,
    gender VARCHAR(20),
    is_payment_enabled BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    last_interaction_date TIMESTAMPTZ,
    contact_info JSONB DEFAULT '{}',
    insert_date TIMESTAMPTZ DEFAULT NOW(),
    insert_user UUID,
    update_date TIMESTAMPTZ DEFAULT NOW(),
    update_user UUID,

    CONSTRAINT unique_domain_facebook_user UNIQUE (domain_uuid, facebook_user_id)
);

-- Facebook Chat Rooms Bridge Table
-- Links internal chat rooms with Facebook conversations
CREATE TABLE v_facebook_chat_rooms (
    facebook_room_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain_uuid UUID NOT NULL,
    internal_room_uuid UUID NOT NULL,
    facebook_contact_uuid UUID NOT NULL,
    assigned_agent_uuid UUID,
    room_status VARCHAR(50) DEFAULT 'active' CHECK (room_status IN ('active', 'closed', 'transferred', 'pending')),
    last_message_at TIMESTAMPTZ,
    conversation_metadata JSONB DEFAULT '{}',
    insert_date TIMESTAMPTZ DEFAULT NOW(),
    insert_user UUID,
    update_date TIMESTAMPTZ DEFAULT NOW(),
    update_user UUID,

    CONSTRAINT unique_internal_room_facebook UNIQUE (internal_room_uuid),
    CONSTRAINT unique_domain_facebook_contact UNIQUE (domain_uuid, facebook_contact_uuid)
);

-- Facebook Message Mapping Table
-- Tracks message synchronization between internal system and Facebook
CREATE TABLE v_facebook_message_mapping (
    mapping_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain_uuid UUID NOT NULL,
    internal_message_id BIGINT NOT NULL,
    facebook_message_id VARCHAR(255),
    facebook_user_id VARCHAR(255),
    message_direction VARCHAR(20) NOT NULL CHECK (message_direction IN ('inbound', 'outbound')),
    facebook_event_type VARCHAR(100),
    delivery_status VARCHAR(50) DEFAULT 'pending' CHECK (delivery_status IN ('pending', 'sent', 'delivered', 'read', 'failed')),
    error_message TEXT,
    message_metadata JSONB DEFAULT '{}',
    retry_count INT DEFAULT 0,
    last_retry_at TIMESTAMPTZ,
    insert_date TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT unique_internal_message_facebook_mapping UNIQUE (internal_message_id)
);

-- Facebook Webhook Event Log Table
-- Audit log for all Facebook webhook events
CREATE TABLE v_facebook_webhook_event (
    event_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain_uuid UUID,
    facebook_user_id VARCHAR(255),
    event_name VARCHAR(100),
    msg JSONB,
    insert_date TIMESTAMPTZ DEFAULT NOW(),
    insert_user UUID,
    update_date TIMESTAMPTZ DEFAULT NOW(),
    update_user UUID
);

-- =====================================================
-- ZALO INTEGRATION ENHANCEMENTS
-- =====================================================

-- ZALO OA Configuration Table (New - replaces v_zalo_oauth)
-- Stores ZALO Official Account configuration per domain
CREATE TABLE v_zalo_oa_config (
    config_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain_uuid UUID NOT NULL UNIQUE,
    app_id VARCHAR(255) NOT NULL,
    app_secret VARCHAR(500) NOT NULL,
    oa_id VARCHAR(255) NOT NULL,
    access_token VARCHAR(1000) NOT NULL,
    refresh_token VARCHAR(1000),
    webhook_url VARCHAR(500),
    webhook_secret VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    token_expires_at TIMESTAMPTZ,
    allowed_events JSONB DEFAULT '["user_send_text", "user_send_image", "user_send_file"]',
    oa_settings JSONB DEFAULT '{}',
    insert_date TIMESTAMPTZ DEFAULT NOW(),
    insert_user UUID,
    update_date TIMESTAMPTZ DEFAULT NOW(),
    update_user UUID,

    CONSTRAINT unique_domain_zalo_oa UNIQUE (domain_uuid)
);

-- Enhance existing ZALO Contacts Table
-- Add missing fields to match Telegram pattern
ALTER TABLE v_zalo_oa_contacts 
ADD COLUMN IF NOT EXISTS username VARCHAR(255),
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS contact_metadata JSONB DEFAULT '{}';

-- Enhance existing ZALO Message Mapping Table
-- Add missing fields for better tracking
ALTER TABLE v_zalo_message_mapping
ADD COLUMN IF NOT EXISTS zalo_event_type VARCHAR(100),
ADD COLUMN IF NOT EXISTS message_metadata JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS retry_count INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_retry_at TIMESTAMPTZ;

-- =====================================================
-- FOREIGN KEY CONSTRAINTS
-- =====================================================

-- Facebook Foreign Key Constraints
ALTER TABLE v_facebook_page_config
    ADD CONSTRAINT fk_facebook_config_domain
    FOREIGN KEY (domain_uuid) REFERENCES v_domains(domain_uuid) ON DELETE CASCADE;

ALTER TABLE v_facebook_contacts
    ADD CONSTRAINT fk_facebook_contacts_domain
    FOREIGN KEY (domain_uuid) REFERENCES v_domains(domain_uuid) ON DELETE CASCADE;

ALTER TABLE v_facebook_chat_rooms
    ADD CONSTRAINT fk_facebook_rooms_domain
    FOREIGN KEY (domain_uuid) REFERENCES v_domains(domain_uuid) ON DELETE CASCADE;

ALTER TABLE v_facebook_chat_rooms
    ADD CONSTRAINT fk_facebook_rooms_internal_room
    FOREIGN KEY (internal_room_uuid) REFERENCES v_chat_rooms(room_uuid) ON DELETE CASCADE;

ALTER TABLE v_facebook_chat_rooms
    ADD CONSTRAINT fk_facebook_rooms_contact
    FOREIGN KEY (facebook_contact_uuid) REFERENCES v_facebook_contacts(contact_uuid) ON DELETE CASCADE;

ALTER TABLE v_facebook_message_mapping
    ADD CONSTRAINT fk_facebook_mapping_domain
    FOREIGN KEY (domain_uuid) REFERENCES v_domains(domain_uuid) ON DELETE CASCADE;

ALTER TABLE v_facebook_message_mapping
    ADD CONSTRAINT fk_facebook_mapping_message
    FOREIGN KEY (internal_message_id) REFERENCES v_chat_messages(message_id) ON DELETE CASCADE;

-- ZALO Enhanced Foreign Key Constraints
ALTER TABLE v_zalo_oa_config
    ADD CONSTRAINT fk_zalo_config_domain
    FOREIGN KEY (domain_uuid) REFERENCES v_domains(domain_uuid) ON DELETE CASCADE;
