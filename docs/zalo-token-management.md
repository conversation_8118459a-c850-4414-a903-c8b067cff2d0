# Zalo Token Management

This document explains the centralized Zalo token management system that automatically handles token refresh for the internal chat app.

## Overview

The `ZaloTokenService` provides automatic token refresh functionality for Zalo API calls, eliminating the need for manual token management throughout the application.

## Key Features

- **Automatic Token Refresh**: Tokens are automatically refreshed when they expire or are about to expire
- **Concurrent Request Handling**: Multiple simultaneous requests for the same domain share a single refresh operation
- **Error Detection**: Automatically detects token expiration errors and retries with refreshed tokens
- **Fallback Handling**: Graceful fallback to current tokens if refresh fails

## Usage

### 1. Basic Token Retrieval

```typescript
import { ZaloTokenService } from '@/services/zalo/zaloTokenService'

// Get a valid access token (automatically refreshes if needed)
const accessToken = await ZaloTokenService.getValidAccessToken(domainUuid)
```

### 2. API Calls with Automatic Refresh

```typescript
// Make API call with automatic token refresh on expiration
const result = await ZaloTokenService.makeApiCallWithTokenRefresh(
  domainUuid,
  async (accessToken: string) => {
    const response = await fetch('https://openapi.zalo.me/v3.0/oa/message/cs', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        access_token: accessToken
      },
      body: JSON.stringify(payload)
    })
    
    if (!response.ok) {
      const error: any = new Error(`API error: ${response.status}`)
      error.status = response.status
      throw error
    }
    
    const result = await response.json()
    
    // Check for token errors in response
    if (result.error === -201) {
      const tokenError: any = new Error('Invalid access token')
      tokenError.error_code = -201
      throw tokenError
    }
    
    return result
  }
)
```

### 3. Using the React Hook

```typescript
import { useZaloToken } from '@/hooks/zalo/useZaloToken'

function MyComponent() {
  const { 
    accessToken, 
    isLoading, 
    error, 
    makeApiCall, 
    forceRefresh 
  } = useZaloToken()

  const sendMessage = async (message: string) => {
    try {
      const result = await makeApiCall(async (token) => {
        // Your API call here
        return await sendZaloMessage(token, message)
      })
      console.log('Message sent:', result)
    } catch (error) {
      console.error('Failed to send message:', error)
    }
  }

  return (
    <div>
      {isLoading && <p>Loading...</p>}
      {error && <p>Error: {error}</p>}
      {accessToken && <p>Token available</p>}
      <button onClick={() => sendMessage('Hello')}>Send Message</button>
      <button onClick={forceRefresh}>Force Refresh Token</button>
    </div>
  )
}
```

## Token Expiration Detection

The service automatically detects token expiration through:

1. **Proactive Expiry Check**: Tokens are refreshed 5 minutes before expiration
2. **API Error Codes**: Zalo API error code -201 (Invalid access token)
3. **HTTP Status Codes**: 401 Unauthorized responses
4. **Error Messages**: Messages containing "access token" and "invalid/expired"

## Configuration

The service uses the `v_zalo_oa_config` table for token storage:

```sql
-- Required fields for token management
- domain_uuid: Domain identifier
- app_id: Zalo app ID
- app_secret: Zalo app secret
- access_token: Current access token
- refresh_token: Refresh token for renewal
- token_expires_at: Token expiration timestamp
```

## Error Handling

### Token Refresh Failures

If token refresh fails, the service:
1. Logs the error
2. Returns the current token as fallback
3. Allows the API call to proceed (may fail with token error)

### API Call Failures

The service retries API calls once if a token expiration error is detected:
1. First attempt with current token
2. If token error detected, refresh token and retry
3. If second attempt fails, throw the error

## Migration from Old System

### Before (Manual Token Management)

```typescript
// Old approach - manual token checking and refresh
const checkAndRefreshToken = async () => {
  const config = await getZaloConfig()
  if (isTokenExpired(config.token_expires_at)) {
    await refreshToken(config.refresh_token)
  }
  return config.access_token
}

const token = await checkAndRefreshToken()
const result = await sendZaloMessage(token, message)
```

### After (Automatic Token Management)

```typescript
// New approach - automatic token management
const result = await ZaloTokenService.makeApiCallWithTokenRefresh(
  domainUuid,
  (token) => sendZaloMessage(token, message)
)
```

## Best Practices

1. **Use `makeApiCallWithTokenRefresh`** for all Zalo API calls
2. **Handle token errors properly** in your API functions by throwing errors with appropriate properties
3. **Don't cache tokens manually** - let the service manage token lifecycle
4. **Use the React hook** for frontend components that need token access
5. **Monitor token refresh logs** for debugging authentication issues

## Troubleshooting

### Common Issues

1. **"No valid access token available"**
   - Check if Zalo configuration exists for the domain
   - Verify refresh token is present and valid

2. **Token refresh loops**
   - Check if refresh token is expired
   - Verify app_secret is correct

3. **API calls still failing after refresh**
   - Check if the API endpoint is correct
   - Verify the API call function properly throws token errors

### Debug Logging

The service provides detailed console logging:
- Token refresh attempts
- API call retries
- Error conditions
- Token expiration checks

Enable debug logging by checking browser console or server logs.
