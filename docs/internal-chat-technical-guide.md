# Internal Chat System - Technical Implementation Guide

## Overview

This document provides detailed technical information for developers working with the Internal Chat System. It covers implementation patterns, API specifications, database schemas, and integration guidelines.

## Core Architecture Patterns

### 1. Domain-Driven Design (DDD)
The system follows DDD principles with clear domain boundaries:

```typescript
// Domain Models
interface ChatDomain {
  rooms: ChatRoom[]
  participants: Participant[]
  messages: Message[]
  integrations: PlatformIntegration[]
}

// Aggregate Roots
class ChatRoom {
  private participants: Participant[]
  private messages: Message[]
  
  addParticipant(user: User): void
  sendMessage(content: string, author: User): Message
  assignToAgent(agent: User): void
}
```

### 2. Event-Driven Architecture
Real-time updates use event-driven patterns:

```typescript
// Event Types
type ChatEvent = 
  | { type: 'MESSAGE_SENT'; payload: Message }
  | { type: 'USER_JOINED'; payload: Participant }
  | { type: 'TYPING_STARTED'; payload: TypingIndicator }
  | { type: 'PRESENCE_CHANGED'; payload: UserPresence }

// Event Handlers
class ChatEventHandler {
  async handleMessageSent(event: MessageSentEvent): Promise<void> {
    await this.broadcastToRoom(event.roomId, event.message)
    await this.updateUnreadCounts(event.roomId, event.message)
    await this.sendNotifications(event.roomId, event.message)
  }
}
```

### 3. Repository Pattern
Data access abstraction for testability:

```typescript
interface ChatRepository {
  findRoomsByDomain(domainId: string): Promise<ChatRoom[]>
  createRoom(room: CreateRoomRequest): Promise<ChatRoom>
  addMessage(roomId: string, message: Message): Promise<Message>
  updateUnreadCount(userId: string, roomId: string): Promise<void>
}

class PrismaChatRepository implements ChatRepository {
  async findRoomsByDomain(domainId: string): Promise<ChatRoom[]> {
    return await prisma.v_chat_rooms.findMany({
      where: { domain_uuid: domainId },
      include: { v_chat_room_participants: true }
    })
  }
}
```

## Database Schema Details

### Core Tables Structure

#### v_chat_rooms
```sql
CREATE TABLE v_chat_rooms (
    room_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain_uuid UUID NOT NULL,
    room_name VARCHAR(255),
    room_description TEXT,
    room_type VARCHAR(50) NOT NULL CHECK (room_type IN ('direct', 'group', 'department', 'broadcast')),
    room_avatar VARCHAR(500),
    created_by_user_uuid UUID NOT NULL,
    is_active BOOLEAN DEFAULT true,
    is_archived BOOLEAN DEFAULT false,
    max_participants INTEGER DEFAULT 100,
    room_settings JSONB DEFAULT '{}',
    insert_date TIMESTAMPTZ DEFAULT NOW(),
    update_date TIMESTAMPTZ DEFAULT NOW()
);
```

#### v_chat_messages (Optimized for Scale)
```sql
CREATE TABLE v_chat_messages (
    message_id BIGSERIAL PRIMARY KEY,
    room_uuid UUID NOT NULL,
    author_uuid UUID,
    content TEXT,
    message_type SMALLINT DEFAULT 0, -- 0=text, 1=image, 2=file
    reply_to BIGINT REFERENCES v_chat_messages(message_id),
    edited_at BIGINT,
    created_at BIGINT NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW())::BIGINT,
    flags INTEGER DEFAULT 0 -- Bitfield: 1=deleted, 2=pinned, 4=system
);

-- Partitioning for performance
CREATE TABLE v_chat_messages_y2024m01 PARTITION OF v_chat_messages
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

### Integration Bridge Pattern

#### ZALO Integration Tables
```sql
-- Contact management with domain isolation
CREATE TABLE v_zalo_oa_contacts (
    contact_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain_uuid UUID NOT NULL,
    zalo_user_id VARCHAR(255) NOT NULL,
    display_name VARCHAR(255),
    avatar_url TEXT,
    phone VARCHAR(50),
    is_follower BOOLEAN DEFAULT false,
    last_interaction_date TIMESTAMPTZ,
    contact_info JSONB DEFAULT '{}',
    CONSTRAINT unique_domain_zalo_user UNIQUE (domain_uuid, zalo_user_id)
);

-- Room bridging for conversation mapping
CREATE TABLE v_zalo_oa_chat_rooms (
    zalo_room_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain_uuid UUID NOT NULL,
    internal_room_uuid UUID NOT NULL UNIQUE,
    zalo_contact_uuid UUID NOT NULL,
    assigned_agent_uuid UUID,
    room_status VARCHAR(50) DEFAULT 'active',
    last_message_at TIMESTAMPTZ,
    conversation_metadata JSONB DEFAULT '{}'
);

-- Message synchronization tracking
CREATE TABLE v_zalo_message_mapping (
    mapping_uuid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain_uuid UUID NOT NULL,
    internal_message_id BIGINT NOT NULL,
    zalo_message_id VARCHAR(255),
    message_direction VARCHAR(20) NOT NULL, -- 'inbound' or 'outbound'
    delivery_status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

## API Implementation Patterns

### RESTful API Design

#### Room Management API
```typescript
// GET /api/internal-chat/rooms
export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions)
  const { searchParams } = new URL(request.url)
  
  const roomType = searchParams.get('type')
  const limit = parseInt(searchParams.get('limit') || '20')
  const offset = parseInt(searchParams.get('offset') || '0')

  // Domain-based filtering for security
  const user = await prisma.v_users.findUnique({
    where: { user_uuid: session.user!.id },
    select: { domain_uuid: true }
  })

  const rooms = await prisma.v_chat_rooms.findMany({
    where: {
      domain_uuid: user.domain_uuid!,
      ...(roomType && { room_type: roomType }),
      v_chat_room_participants: {
        some: { user_uuid: session.user!.id }
      }
    },
    include: {
      v_chat_room_participants: {
        include: { v_users: true }
      },
      v_chat_messages: {
        orderBy: { created_at: 'desc' },
        take: 1
      }
    },
    orderBy: { update_date: 'desc' },
    skip: offset,
    take: limit
  })

  return NextResponse.json({ rooms, total: rooms.length })
}
```

#### Message API with Integration Support
```typescript
// POST /api/internal-chat/rooms/[roomId]/messages
export async function POST(request: NextRequest, { params }: { params: { roomId: string } }) {
  const { content, message_type, reply_to } = await request.json()
  const session = await getServerSession(authOptions)

  // Create message in transaction
  const result = await prisma.$transaction(async tx => {
    const message = await tx.v_chat_messages.create({
      data: {
        room_uuid: params.roomId,
        author_uuid: session.user!.id,
        content: content.trim(),
        message_type,
        reply_to: reply_to ? BigInt(reply_to) : null,
        created_at: Math.floor(Date.now() / 1000),
        flags: 0
      }
    })

    // Check for external platform integration
    await handleExternalPlatformForwarding(tx, params.roomId, message)
    
    return message
  })

  // Broadcast via WebSocket
  await broadcastMessage(params.roomId, result)
  
  return NextResponse.json(result)
}

async function handleExternalPlatformForwarding(
  tx: PrismaTransaction, 
  roomId: string, 
  message: Message
) {
  // Check for ZALO integration
  const zaloRoom = await tx.v_zalo_oa_chat_rooms.findFirst({
    where: { internal_room_uuid: roomId }
  })
  
  if (zaloRoom) {
    await ZaloMessageService.processOutboundMessage(
      zaloRoom.domain_uuid,
      roomId,
      message.author_uuid,
      message.content,
      message.message_type
    )
  }

  // Check for Telegram integration
  const telegramRoom = await tx.v_telegram_chat_rooms.findFirst({
    where: { internal_room_uuid: roomId }
  })
  
  if (telegramRoom) {
    await TelegramMessageService.processOutboundMessage(
      telegramRoom.domain_uuid,
      roomId,
      message.author_uuid,
      message.content,
      message.message_type
    )
  }
}
```

### WebSocket Implementation

#### Connection Management
```typescript
class ChatSocketManager {
  private connectedUsers = new Map<string, UserConnection>()
  private roomSubscriptions = new Map<string, Set<string>>()

  async handleConnection(socket: Socket) {
    const { userId, domain } = socket.handshake.query
    
    // Authenticate and store connection
    const user = await this.authenticateUser(userId as string)
    if (!user || user.domain_uuid !== domain) {
      socket.disconnect()
      return
    }

    this.connectedUsers.set(socket.id, {
      socket,
      user_uuid: user.user_uuid,
      domain_uuid: user.domain_uuid,
      connected_at: new Date()
    })

    // Set up event handlers
    this.setupEventHandlers(socket)
    
    // Update presence
    await this.updateUserPresence(user.user_uuid, 'online')
  }

  private setupEventHandlers(socket: Socket) {
    socket.on('join_room', (data) => this.handleJoinRoom(socket, data))
    socket.on('leave_room', (data) => this.handleLeaveRoom(socket, data))
    socket.on('send_message', (data) => this.handleSendMessage(socket, data))
    socket.on('typing_start', (data) => this.handleTypingStart(socket, data))
    socket.on('typing_stop', (data) => this.handleTypingStop(socket, data))
    socket.on('disconnect', () => this.handleDisconnect(socket))
  }
}
```

## Integration Service Patterns

### ZALO Integration Service
```typescript
export class ZaloIntegrationService {
  static async processInboundMessage(
    domain_uuid: string,
    zaloMessage: ZaloWebhookMessage
  ): Promise<void> {
    // Find or create contact
    const contact = await this.findOrCreateContact(domain_uuid, zaloMessage.sender)
    
    // Find or create room bridge
    const roomBridge = await this.findOrCreateRoomBridge(domain_uuid, contact)
    
    // Create internal message
    const internalMessage = await prisma.v_chat_messages.create({
      data: {
        room_uuid: roomBridge.internal_room_uuid,
        author_uuid: contact.contact_uuid,
        content: zaloMessage.message.text || '[Attachment]',
        message_type: this.mapMessageType(zaloMessage.message),
        created_at: Math.floor(Date.now() / 1000),
        flags: 4 // External message flag
      }
    })

    // Create message mapping for tracking
    await prisma.v_zalo_message_mapping.create({
      data: {
        domain_uuid,
        internal_message_id: internalMessage.message_id,
        zalo_message_id: zaloMessage.message.msg_id,
        message_direction: 'inbound',
        delivery_status: 'delivered'
      }
    })

    // Broadcast to connected agents
    await this.broadcastToAgents(roomBridge.internal_room_uuid, internalMessage)
  }

  static async processOutboundMessage(
    domain_uuid: string,
    roomId: string,
    authorId: string,
    content: string,
    messageType: number
  ): Promise<void> {
    const roomBridge = await prisma.v_zalo_oa_chat_rooms.findFirst({
      where: { internal_room_uuid: roomId },
      include: { v_zalo_oa_contacts: true }
    })

    if (!roomBridge) return

    // Send to ZALO API
    const zaloResult = await this.sendToZaloAPI(
      roomBridge.v_zalo_oa_contacts.zalo_user_id,
      content,
      messageType
    )

    // Update message mapping
    await prisma.v_zalo_message_mapping.create({
      data: {
        domain_uuid,
        internal_message_id: /* message_id */,
        zalo_message_id: zaloResult.message_id,
        message_direction: 'outbound',
        delivery_status: zaloResult.error_code === 0 ? 'sent' : 'failed'
      }
    })
  }
}
```

## Performance Optimization Strategies

### Database Optimizations
```sql
-- Strategic indexing for common queries
CREATE INDEX idx_chat_rooms_domain_type ON v_chat_rooms(domain_uuid, room_type);
CREATE INDEX idx_chat_messages_room_created ON v_chat_messages(room_uuid, created_at DESC);
CREATE INDEX idx_chat_participants_user_room ON v_chat_room_participants(user_uuid, room_uuid);
CREATE INDEX idx_chat_unread_user ON v_chat_unread_counts(user_uuid) WHERE unread_count > 0;

-- Partial indexes for active data
CREATE INDEX idx_chat_rooms_active ON v_chat_rooms(domain_uuid) WHERE is_active = true;
CREATE INDEX idx_chat_messages_recent ON v_chat_messages(room_uuid, created_at DESC) 
  WHERE created_at > EXTRACT(EPOCH FROM NOW() - INTERVAL '30 days')::BIGINT;
```

### Frontend Performance
```typescript
// Virtual scrolling for large message lists
const MessageVirtualList = ({ messages, roomId }: MessageListProps) => {
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 50 })
  
  const visibleMessages = useMemo(() => 
    messages.slice(visibleRange.start, visibleRange.end),
    [messages, visibleRange]
  )

  return (
    <VirtualizedList
      itemCount={messages.length}
      itemSize={60}
      onScroll={handleScroll}
    >
      {visibleMessages.map(renderMessage)}
    </VirtualizedList>
  )
}

// Debounced typing indicators
const useTypingIndicator = (roomId: string) => {
  const debouncedStopTyping = useMemo(
    () => debounce(() => {
      socket.emit('typing_stop', { room_uuid: roomId })
    }, 2000),
    [roomId]
  )

  const startTyping = useCallback(() => {
    socket.emit('typing_start', { room_uuid: roomId })
    debouncedStopTyping()
  }, [roomId, debouncedStopTyping])

  return { startTyping }
}
```

## Security Implementation

### Domain Isolation
```typescript
// Middleware for domain-based access control
export async function withDomainAccess(
  handler: (req: NextRequest, context: any) => Promise<Response>
) {
  return async (req: NextRequest, context: any) => {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'Invalid domain' }, { status: 403 })
    }

    // Add domain context
    context.domain_uuid = user.domain_uuid
    context.user_uuid = session.user.id

    return handler(req, context)
  }
}
```

### Input Validation
```typescript
// Message content validation
const messageSchema = z.object({
  content: z.string().min(1).max(4000),
  message_type: z.number().int().min(0).max(2),
  reply_to: z.number().optional(),
  room_uuid: z.string().uuid()
})

export async function validateMessage(data: unknown): Promise<MessageData> {
  try {
    return messageSchema.parse(data)
  } catch (error) {
    throw new ValidationError('Invalid message format')
  }
}
```

## Testing Strategies

### Unit Testing
```typescript
describe('ChatRepository', () => {
  let repository: PrismaChatRepository
  let mockPrisma: jest.Mocked<PrismaClient>

  beforeEach(() => {
    mockPrisma = createMockPrisma()
    repository = new PrismaChatRepository(mockPrisma)
  })

  it('should create room with participants', async () => {
    const roomData = {
      domain_uuid: 'test-domain',
      room_name: 'Test Room',
      room_type: 'group',
      participant_uuids: ['user1', 'user2']
    }

    mockPrisma.v_chat_rooms.create.mockResolvedValue(mockRoom)
    
    const result = await repository.createRoom(roomData)
    
    expect(result).toEqual(mockRoom)
    expect(mockPrisma.v_chat_rooms.create).toHaveBeenCalledWith({
      data: expect.objectContaining({
        domain_uuid: roomData.domain_uuid,
        room_name: roomData.room_name
      })
    })
  })
})
```

### Integration Testing
```typescript
describe('Chat API Integration', () => {
  it('should handle complete message flow', async () => {
    // Create test room
    const room = await createTestRoom()
    
    // Send message via API
    const response = await request(app)
      .post(`/api/internal-chat/rooms/${room.room_uuid}/messages`)
      .send({ content: 'Test message', message_type: 0 })
      .expect(200)

    // Verify message stored
    const message = await prisma.v_chat_messages.findFirst({
      where: { room_uuid: room.room_uuid }
    })
    
    expect(message).toBeTruthy()
    expect(message.content).toBe('Test message')
    
    // Verify WebSocket broadcast
    expect(mockSocket.emit).toHaveBeenCalledWith(
      'new_message',
      expect.objectContaining({ content: 'Test message' })
    )
  })
})
```

This technical guide provides the foundation for understanding and extending the Internal Chat System. The patterns and examples shown here ensure maintainable, scalable, and secure implementation of chat functionality with external platform integrations.
