# Internal Chat System - Business Overview & Architecture

## Executive Summary

The Internal Chat System is a comprehensive real-time communication platform designed for multi-tenant environments. It provides seamless integration between internal domain users and external platforms (ZALO, Telegram, Facebook Messenger) while maintaining strict data isolation and security.

## Current Implementation Status (Updated: 2025-07-26)

### ✅ Completed Components
- **Core Chat Infrastructure**: Database schema, message storage, room management
- **Telegram Integration**: Full implementation with webhook processing, contact management, and real-time messaging
- **ZALO Integration**: Partial implementation with enhanced configuration system
- **Facebook Integration**: Database schema and basic API structure implemented
- **Frontend State Management**: Redux store with comprehensive chat state management
- **Real-time Communication**: Socket.IO integration for live messaging

### 🔄 In Progress
- **Documentation Updates**: Reflecting current implementation status and next steps
- **API Gap Analysis**: Identifying missing endpoints for complete integration

### ⏳ Planned Components
- **Enhanced ZALO Integration**: Alignment with Telegram integration patterns
- **Facebook Webhook Implementation**: Complete message processing pipeline
- **Frontend UI Components**: Social platform management interfaces
- **Performance Optimizations**: Facebook-inspired architecture improvements
- **Comprehensive Testing**: Unit, integration, and end-to-end testing

## Business Value Proposition

### Core Benefits
- **Unified Communication Hub**: Single interface for internal team communication and external customer support
- **Multi-Platform Integration**: Native support for ZALO OA, Telegram, and Facebook Messenger with extensible architecture
- **Real-Time Collaboration**: Instant messaging with presence indicators and typing notifications
- **Scalable Architecture**: Designed to handle high message volumes with efficient data management
- **Security-First Design**: Domain-based isolation ensures complete data privacy in multi-tenant environments

### Key Features
- **Internal Chat**: Direct and group messaging between domain users
- **External Platform Integration**: Seamless ZALO, Telegram, and Facebook Messenger customer support
- **Agent Assignment**: Intelligent routing of external conversations to available agents
- **Notification System**: Browser notifications and unread count management
- **Responsive Design**: Full-featured desktop interface with mobile-optimized popup
- **Message Management**: Rich text, file attachments, message reactions, and threading

### Integration Status by Platform

#### 🟢 Telegram Integration (Production Ready)
- **Status**: Fully implemented and operational
- **Features**: Complete webhook processing, contact management, message sync, real-time broadcasting
- **Components**: Dynamic bot configuration, unified message processing, Socket.IO integration
- **Database**: All tables implemented and indexed for performance

#### 🟡 ZALO Integration (Enhancement Phase)
- **Status**: Partially implemented, undergoing enhancement
- **Features**: Basic webhook processing, contact management, message sync
- **Current Issues**: Dual implementation patterns, incomplete real-time integration
- **Next Steps**: Consolidate to match Telegram architecture, enhance configuration system

#### 🟠 Facebook Messenger Integration (Development Phase)
- **Status**: Database schema complete, API implementation in progress
- **Features**: Page configuration management, webhook event logging
- **Components**: Database tables created, basic API endpoints implemented
- **Next Steps**: Complete webhook processing, message sync, real-time integration

## System Architecture Overview

### Business Architecture Diagram

```mermaid
graph TB
    %% External Users and Platforms
    subgraph "External Platforms"
        ZU[ZALO Users]
        TU[Telegram Users]
        ZA[ZALO OA API]
        TA[Telegram Bot API]
    end

    %% Internal Users
    subgraph "Internal Domain"
        IU[Internal Users]
        AG[Support Agents]
    end

    %% Frontend Layer
    subgraph "Frontend Interface"
        CP[Chat Popup]
        FP[Full Page Chat]
        NF[Notifications]
    end

    %% Backend Services
    subgraph "Backend Services"
        API[REST API Layer]
        WS[WebSocket Service]
        ZS[ZALO Integration Service]
        TS[Telegram Integration Service]
    end

    %% Database Layer
    subgraph "Database Layer"
        subgraph "Core Chat Tables"
            CR[v_chat_rooms]
            CP_DB[v_chat_room_participants]
            CM[v_chat_messages]
            CU[v_chat_unread_counts]
        end

        subgraph "ZALO Bridge Tables"
            ZC[v_zalo_oa_contacts]
            ZR[v_zalo_oa_chat_rooms]
            ZM[v_zalo_message_mapping]
        end

        subgraph "Telegram Bridge Tables"
            TC[v_telegram_contacts]
            TR[v_telegram_chat_rooms]
            TM[v_telegram_message_mapping]
        end
    end

    %% User Interactions
    ZU -->|Messages| ZA
    TU -->|Messages| TA
    IU -->|Internal Chat| CP
    IU -->|Internal Chat| FP
    AG -->|Customer Support| CP
    AG -->|Customer Support| FP

    %% Frontend to Backend
    CP -->|API Calls| API
    FP -->|API Calls| API
    CP <-->|Real-time| WS
    FP <-->|Real-time| WS
    NF <-->|Updates| WS

    %% External Platform Integration
    ZA -->|Webhooks| ZS
    TA -->|Webhooks| TS
    ZS -->|Send Messages| ZA
    TS -->|Send Messages| TA

    %% Backend Services Integration
    ZS -->|Process Messages| API
    TS -->|Process Messages| API
    API -->|Store Data| CR
    API -->|Store Data| CM
    WS -->|Broadcast| CP
    WS -->|Broadcast| FP

    %% Database Relationships
    ZS -->|Create/Update| ZC
    ZS -->|Bridge Rooms| ZR
    ZS -->|Track Messages| ZM
    TS -->|Create/Update| TC
    TS -->|Bridge Rooms| TR
    TS -->|Track Messages| TM

    %% Core Database Flow
    ZR -->|Links to| CR
    TR -->|Links to| CR
    CR -->|Contains| CM
    CR -->|Has| CP_DB
    CP_DB -->|Tracks| CU
```

### Service Architecture Diagram

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Browser]
        MOB[Mobile App]
    end

    subgraph "API Gateway"
        GW[API Gateway]
        AUTH[Authentication Service]
        RATE[Rate Limiting]
    end

    subgraph "Application Services"
        CHAT[Chat Service]
        USER[User Service]
        NOTIF[Notification Service]
        FILE[File Service]
        PRES[Presence Service]
    end

    subgraph "Integration Services"
        ZALO[ZALO Integration Service]
        TELE[Telegram Integration Service]
        WEBHOOK[Webhook Handler Service]
    end

    subgraph "Infrastructure Services"
        WS[WebSocket Service]
        QUEUE[Message Queue]
        CACHE[Redis Cache]
        STORAGE[File Storage]
    end

    subgraph "Data Layer"
        DB[(PostgreSQL Database)]
        SEARCH[(Search Index)]
        ARCHIVE[(Message Archive)]
    end

    subgraph "External APIs"
        ZAPI[ZALO OA API]
        TAPI[Telegram Bot API]
        PUSH[Push Notification Service]
    end

    %% Client connections
    WEB --> GW
    MOB --> GW

    %% Gateway routing
    GW --> AUTH
    GW --> RATE
    GW --> CHAT
    GW --> USER
    GW --> FILE

    %% Service interactions
    CHAT --> NOTIF
    CHAT --> PRES
    CHAT --> WS
    CHAT --> DB

    USER --> DB
    USER --> CACHE

    NOTIF --> PUSH
    NOTIF --> WS
    NOTIF --> QUEUE

    FILE --> STORAGE
    PRES --> CACHE
    PRES --> WS

    %% Integration flows
    WEBHOOK --> ZALO
    WEBHOOK --> TELE
    ZALO --> CHAT
    TELE --> CHAT
    ZALO --> ZAPI
    TELE --> TAPI

    %% Infrastructure
    WS --> CACHE
    QUEUE --> NOTIF
    CHAT --> QUEUE
    CHAT --> SEARCH
    CHAT --> ARCHIVE

    %% Data persistence
    CHAT --> DB
    ZALO --> DB
    TELE --> DB
    USER --> DB
```

## Core Database Schema

### Primary Tables

#### 1. Chat Rooms (`v_chat_rooms`)
- **Purpose**: Central room management for all conversation types
- **Types**: `direct`, `group`, `department`, `broadcast`
- **Key Features**: Domain isolation, archiving, participant limits

#### 2. Room Participants (`v_chat_room_participants`)
- **Purpose**: User membership and permissions management
- **Roles**: `owner`, `admin`, `moderator`, `member`
- **Features**: Soft deletion, notification preferences, read tracking

#### 3. Messages (`v_chat_messages`)
- **Purpose**: Optimized message storage (Discord-inspired design)
- **Features**: Unix timestamps, bitfield flags, reply threading
- **Types**: Text (0), Image (1), File (2), System messages

#### 4. Unread Counts (`v_chat_unread_counts`)
- **Purpose**: Denormalized counters for performance
- **Features**: Per-user, per-room tracking with efficient updates

### Integration Bridge Tables

#### ZALO Integration
- **`v_zalo_oa_contacts`**: ZALO user information with domain isolation
- **`v_zalo_oa_chat_rooms`**: Bridge between ZALO conversations and internal rooms
- **`v_zalo_message_mapping`**: Message synchronization tracking

#### Telegram Integration  
- **`v_telegram_contacts`**: Telegram user profiles with domain security
- **`v_telegram_chat_rooms`**: Telegram-to-internal room mapping
- **`v_telegram_message_mapping`**: Bidirectional message tracking

## Data Flow Architecture

### Internal Chat Flow
```
User Input → Frontend → Redux Store → WebSocket → Backend API → Database
                ↓                                      ↓
         Real-time UI ←─── WebSocket Broadcast ←── Message Processing
```

### External Platform Integration Flow
```
External Platform → Webhook → Integration Service → Internal Chat API
                                      ↓
                              Bridge Table Update → Internal Room Creation
                                      ↓
                              Message Creation → WebSocket Broadcast → UI Update
```

### Agent Assignment Flow
```
External Message → Contact Detection → Room Creation/Lookup → Agent Assignment
                                                                    ↓
                                                            Notification → Agent UI
```

## Security & Multi-Tenancy

### Domain Isolation Strategy
- **Database Level**: All tables include `domain_uuid` with strict constraints
- **API Level**: Session-based domain filtering on all queries
- **Integration Level**: Platform credentials and data isolated per domain

### Data Privacy Measures
- **Separate Bridge Tables**: Each platform has isolated contact/room tables
- **No Cross-Domain Access**: Queries filtered by user's domain_uuid
- **Secure Credentials**: Platform tokens encrypted and domain-specific

## Real-Time Communication

### WebSocket Architecture
- **Namespace Strategy**: Domain-specific channels for isolation
- **Event Types**: Messages, presence, notifications, typing indicators
- **Connection Management**: Automatic reconnection with exponential backoff
- **Scalability**: Designed for horizontal scaling with Redis adapter

### Notification System
- **Browser Notifications**: Permission-based with smart suppression
- **Unread Counters**: Real-time updates with efficient batch processing
- **Presence Tracking**: Online/offline status with automatic cleanup

## Frontend Architecture

### Component Hierarchy
```
ChatIntegration (Main Controller)
├── ChatPopup (Floating Window)
│   ├── ChatRoomList
│   ├── MessageList
│   └── MessageInput
├── InternalChatPage (Full Screen)
│   ├── ChatSidebar
│   ├── ChatHeader
│   ├── MessageList
│   └── SmartMessageInput
└── NotificationSystem
    ├── BrowserNotifications
    └── UnreadBadges
```

### State Management
- **Redux Integration**: Centralized state with real-time synchronization
- **Optimistic Updates**: Immediate UI feedback with server confirmation
- **Caching Strategy**: Recent messages and room data in memory
- **Error Handling**: Graceful degradation with retry mechanisms

## Integration Services

### ZALO OA Integration
- **Webhook Processing**: Real-time message reception and processing
- **Message Forwarding**: Bidirectional message synchronization
- **Contact Management**: Automatic user profile creation and updates
- **Agent Assignment**: Manual and automatic assignment workflows

### Telegram Bot Integration
- **Bot API Integration**: Full Telegram Bot API support
- **Webhook Management**: Secure webhook processing with domain isolation
- **Message Types**: Text, images, files, and rich media support
- **Contact Synchronization**: User profile management with privacy controls

## Performance Optimizations

### Database Optimizations
- **Strategic Indexing**: Domain-based and query-specific indexes
- **Message Partitioning**: Monthly partitions for scalable message storage
- **Denormalized Counters**: Unread counts for fast UI updates
- **Connection Pooling**: Efficient database connection management

### Frontend Optimizations
- **Virtual Scrolling**: Efficient rendering of large message lists
- **Lazy Loading**: On-demand message and room loading
- **Debounced Updates**: Optimized typing indicators and presence updates
- **Responsive Design**: Mobile-first with progressive enhancement

## Monitoring & Analytics

### System Health Metrics
- **Message Volume**: Real-time message processing statistics
- **Connection Status**: WebSocket connection health and performance
- **Integration Status**: External platform connectivity and error rates
- **User Engagement**: Active users, message frequency, and feature usage

### Business Intelligence
- **Response Times**: Agent response time tracking for customer support
- **Platform Usage**: Comparative usage across internal chat and external platforms
- **User Adoption**: Feature adoption rates and user engagement patterns
- **Performance Metrics**: System performance and scalability indicators

## Message Flow Diagrams

### Data Flow Sequence Diagram

```mermaid
sequenceDiagram
    participant EU as External User
    participant EP as External Platform
    participant WH as Webhook Handler
    participant IS as Integration Service
    participant API as Chat API
    participant DB as Database
    participant WS as WebSocket
    participant AG as Agent UI
    participant IU as Internal User

    Note over EU,IU: External Message Flow (Inbound)
    EU->>EP: Send Message
    EP->>WH: Webhook Notification
    WH->>IS: Process Message
    IS->>DB: Find/Create Contact
    IS->>DB: Find/Create Room Bridge
    IS->>API: Create Internal Message
    API->>DB: Store Message
    API->>WS: Broadcast Message
    WS->>AG: Real-time Update
    WS->>AG: Show Notification

    Note over EU,IU: Agent Response Flow (Outbound)
    AG->>API: Send Reply
    API->>DB: Store Message
    API->>IS: Forward to Platform
    IS->>EP: Send via Platform API
    EP->>EU: Deliver Message
    IS->>DB: Update Message Status
    API->>WS: Broadcast Confirmation
    WS->>AG: Update UI Status

    Note over EU,IU: Internal Chat Flow
    IU->>API: Send Internal Message
    API->>DB: Store Message
    API->>WS: Broadcast Message
    WS->>IU: Real-time Update
    WS->>AG: Real-time Update

    Note over EU,IU: Room Creation & Management
    AG->>API: Create Room
    API->>DB: Create Room Record
    API->>DB: Add Participants
    API->>WS: Notify Participants
    WS->>IU: Room Invitation
    WS->>AG: Room Created
```

### Message Flow Patterns

#### Internal Chat Message Flow
```
[User Types] → [Frontend Input] → [Redux Store] → [WebSocket Emit]
                                                        ↓
[Database] ← [API Processing] ← [Backend Validation] ← [Socket Server]
     ↓
[WebSocket Broadcast] → [All Connected Clients] → [UI Update]
```

#### External Platform Message Flow (Inbound)
```
[External User] → [Platform API] → [Webhook] → [Integration Service]
                                                        ↓
[Bridge Table Update] → [Internal Room Creation] → [Message Storage]
                                                        ↓
[WebSocket Broadcast] → [Agent Interface] → [Real-time Notification]
```

#### External Platform Message Flow (Outbound)
```
[Agent Types] → [Internal Chat UI] → [Message API] → [Integration Service]
                                                           ↓
[Platform API Call] → [External Delivery] → [Status Update] → [UI Confirmation]
```

## Technical Implementation Details

### API Endpoints Structure
```
/api/internal-chat/
├── rooms/                    # Room management
│   ├── GET /                # List rooms
│   ├── POST /               # Create room
│   └── [roomId]/
│       ├── GET /            # Get room details
│       ├── PUT /            # Update room
│       ├── DELETE /         # Delete room
│       └── messages/
│           ├── GET /        # Get messages
│           └── POST /       # Send message
├── users/                   # User management
│   ├── GET /search         # Search users
│   └── [userId]/presence   # User presence
└── notifications/           # Notification management
    ├── GET /               # Get notifications
    └── [notificationId]/
        └── PUT /read       # Mark as read
```

### WebSocket Event Structure
```typescript
// Client to Server Events
interface ClientEvents {
  'chat_connect': { user_uuid: string }
  'join_room': { room_uuid: string }
  'leave_room': { room_uuid: string }
  'send_message': MessageData
  'typing_start': { room_uuid: string }
  'typing_stop': { room_uuid: string }
  'presence_update': { status: 'online' | 'away' | 'offline' }
}

// Server to Client Events
interface ServerEvents {
  'new_message': ChatMessage
  'message_updated': ChatMessage
  'user_joined': UserPresence
  'user_left': UserPresence
  'typing_indicator': TypingData
  'presence_changed': UserPresence
  'notification': ChatNotification
}
```

### Database Relationships
```sql
-- Core relationship structure
v_chat_rooms (1) ←→ (N) v_chat_room_participants
v_chat_rooms (1) ←→ (N) v_chat_messages
v_chat_room_participants (1) ←→ (N) v_chat_unread_counts

-- ZALO integration bridges
v_zalo_oa_contacts (1) ←→ (1) v_zalo_oa_chat_rooms
v_zalo_oa_chat_rooms (1) ←→ (1) v_chat_rooms
v_chat_messages (1) ←→ (0..1) v_zalo_message_mapping

-- Telegram integration bridges
v_telegram_contacts (1) ←→ (1) v_telegram_chat_rooms
v_telegram_chat_rooms (1) ←→ (1) v_chat_rooms
v_chat_messages (1) ←→ (0..1) v_telegram_message_mapping
```

## Future Extensibility

### Planned Enhancements
- **Additional Platforms**: Framework ready for Discord, WhatsApp, etc.
- **Advanced Features**: Message scheduling, auto-responses, chatbots
- **Analytics Dashboard**: Comprehensive reporting and insights
- **Mobile Applications**: Native mobile app development
- **API Ecosystem**: Public APIs for third-party integrations

### Scalability Roadmap
- **Microservices Migration**: Service decomposition for independent scaling
- **Message Archiving**: Automated archiving for long-term storage
- **Global Distribution**: Multi-region deployment capabilities
- **Advanced Security**: Enhanced encryption and compliance features

## Conclusion

The Internal Chat System represents a comprehensive solution for modern business communication needs. By combining internal team collaboration with external customer support through popular platforms like ZALO and Telegram, it provides a unified communication hub that scales with business growth while maintaining the highest standards of security and data privacy.

The system's architecture is designed for extensibility, allowing for easy integration of additional platforms and features as business requirements evolve. The clean separation of concerns, robust database design, and real-time communication capabilities make it a solid foundation for enterprise-level communication needs.
