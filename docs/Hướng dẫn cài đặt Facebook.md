# Hướng Dẫn Tích Hợp Facebook Messenger

Hướng dẫn này sẽ giúp bạn thiết lập tích hợp Facebook Messenger với AgentDesk Lite.

## Yêu Cầu Trước Khi Bắt Đầu

- Một Facebook Page (trang doanh nghiệp)
- T<PERSON><PERSON> kho<PERSON>n Facebook Developer
- Phiên bản AgentDesk Lite với cấu hình domain phù hợp

## Bước 1: Tạo Facebook App

1. T<PERSON>y cập [Facebook Developers](https://developers.facebook.com/apps/creation/)
2. Nhấp "Create App" (Tạo Ứng Dụng)
3. <PERSON><PERSON>n "Business" làm loại ứng dụng
4. Điền thông tin bắt buộc:
   - Tên App: Chọn tên mô tả cho ứng dụng của bạn
   - Email <PERSON>ên Hệ App: Email doanh nghiệp của bạn
   - Tà<PERSON>hiệp: Chọn tài khoản do<PERSON>h nghi<PERSON> (<PERSON><PERSON><PERSON>)

## Bước 2: <PERSON>h<PERSON><PERSON>

1. Trong bảng điều khiển <PERSON>ng dụng, nhấp "Add Product" (Thêm Sản Phẩm)
2. Tìm "Messenger" và nhấp "Set Up" (Thiết Lập)
3. Điều này sẽ thêm khả năng Messenger vào ứng dụng của bạn

## Bước 3: Tạo Page Access Token

1. Trong cài đặt Messenger, cuộn đến "Access Tokens" (Mã Truy Cập)
2. Nhấp "Add or Remove Pages" (Thêm hoặc Xóa Trang)
3. Chọn Facebook Page bạn muốn kết nối
4. Tạo Page Access Token cho trang đã chọn
5. **Quan trọng**: Sao chép và lưu token này một cách an toàn

## Bước 4: Cấu Hình Webhooks

### Thiết Lập Webhook URL
1. Trong cài đặt Messenger, cuộn đến "Webhooks"
2. Nhấp "Add Callback URL" (Thêm URL Callback)
3. Nhập webhook URL của bạn:
   ```
   https://YOUR_DOMAIN/api/internal-chat/facebook/webhook?domain=YOUR_DOMAIN_UUID
   ```
   Thay thế:
   - `YOUR_DOMAIN` bằng domain thực tế của bạn
   - `YOUR_DOMAIN_UUID` bằng UUID domain từ AgentDesk Lite

### Tạo Verify Token
1. Tạo một chuỗi ngẫu nhiên để xác minh webhook (ví dụ: `fb_webhook_verify_token_123`)
2. Nhập token này vào trường "Verify Token"
3. **Quan trọng**: Lưu token này - bạn sẽ cần nó cho cấu hình AgentDesk Lite

### Đăng Ký Webhook Events
Chọn các trường webhook sau:
- `messages`
- `messaging_postbacks`
- `messaging_optins`
- `message_deliveries`
- `message_reads`

## Bước 5: Cấu Hình AgentDesk Lite

Trong cấu hình Facebook của AgentDesk Lite, nhập thông tin sau:

| Trường | Giá Trị | Nguồn |
|--------|---------|-------|
| **Facebook App ID** | ID Ứng Dụng | App Dashboard → Settings → Basic |
| **Facebook App Secret** | Khóa Bí Mật Ứng Dụng | App Dashboard → Settings → Basic |
| **Facebook Page ID** | ID Trang | Cài đặt Trang hoặc Graph API Explorer |
| **Page Access Token** | Token đã tạo | Messenger → Access Tokens |
| **Webhook Verify Token** | Chuỗi ngẫu nhiên của bạn | Token bạn tạo ở Bước 4 |

### Tìm Page ID Của Bạn
Bạn có thể tìm Page ID bằng cách:
1. Truy cập Facebook Page của bạn
2. Nhấp tab "About" (Giới Thiệu)
3. Cuộn xuống để tìm "Page ID"

Hoặc sử dụng Facebook Graph API Explorer:
1. Truy cập [Graph API Explorer](https://developers.facebook.com/tools/explorer/)
2. Chọn ứng dụng và tạo token
3. Truy vấn: `me/accounts` để lấy các trang và ID của chúng

## Bước 6: Kiểm Tra Tích Hợp

1. Gửi tin nhắn thử nghiệm đến Facebook Page của bạn
2. Kiểm tra AgentDesk Lite để xem tin nhắn có xuất hiện không
3. Trả lời từ AgentDesk Lite và xác minh phản hồi đến Facebook

## Bước 7: Chuyển Sang Chế Độ Live (Production)

### Yêu Cầu App Review
Trước khi chuyển sang live, đảm bảo ứng dụng đáp ứng yêu cầu của Facebook:

1. **Chính Sách Bảo Mật**: Thêm URL chính sách bảo mật hợp lệ trong App Settings
2. **Điều Khoản Dịch Vụ**: Thêm URL điều khoản dịch vụ
3. **Biểu Tượng App**: Tải lên biểu tượng app phù hợp (1024x1024px)
4. **Xác Minh Doanh Nghiệp**: Hoàn thành xác minh doanh nghiệp nếu cần

### Gửi Để Review
1. Truy cập "App Review" trong bảng điều khiển ứng dụng
2. Gửi các quyền cần thiết để review:
   - `pages_messaging`
   - `pages_show_list`
3. Cung cấp mô tả chi tiết về trường hợp sử dụng
4. Chờ phê duyệt của Facebook (thường 2-7 ngày làm việc)

### Chuyển Sang Chế Độ Live
Sau khi được phê duyệt:
1. Truy cập App Settings → Basic
2. Chuyển "App Mode" từ "Development" sang "Live"

## Khắc Phục Sự Cố

### Các Vấn Đề Thường Gặp

**Webhook không nhận tin nhắn:**
- Xác minh webhook URL đúng và có thể truy cập
- Kiểm tra verify token khớp chính xác
- Đảm bảo các trường webhook được đăng ký đúng
- Xác minh page access token hợp lệ

**Tin nhắn không gửi được:**
- Kiểm tra quyền page access token
- Xác minh app ở chế độ live cho production
- Đảm bảo trang được kết nối với app

**Lỗi token hết hạn:**
- Page access tokens không hết hạn cho system users
- Đối với token thường, tạo lại trong Facebook Developer Console

### Kiểm Tra Webhook
Bạn có thể kiểm tra webhook bằng công cụ Webhook Testing của Facebook:
1. Truy cập Messenger → Webhooks
2. Nhấp "Test" bên cạnh webhook của bạn
3. Gửi test events để xác minh kết nối

## Ghi Chú Bảo Mật

- Giữ App Secret an toàn và không bao giờ để lộ trong client-side code
- Sử dụng HTTPS cho tất cả webhook URLs
- Thường xuyên xoay vòng access tokens nếu cần
- Giám sát webhook logs để phát hiện hoạt động đáng ngờ

## Khi Thay Đổi Domains/Hosts

Nếu bạn cần thay đổi domain hoặc host:

1. **Cập Nhật Webhook URL**: Thay đổi callback URL trong Facebook Developer Console
2. **Cập Nhật Cấu Hình AgentDesk**: Cập nhật domain UUID trong webhook URL
3. **Xác Minh Domain**: Đảm bảo domain mới được xác minh đúng cách
4. **Kiểm Tra Tích Hợp**: Gửi tin nhắn thử để xác minh mọi thứ hoạt động

## Hỗ Trợ

Để được trợ giúp thêm:
- [Tài Liệu Facebook Messenger Platform](https://developers.facebook.com/docs/messenger-platform/)
- [Cộng Đồng Facebook Developer](https://developers.facebook.com/community/)
- Tài liệu hỗ trợ AgentDesk Lite
