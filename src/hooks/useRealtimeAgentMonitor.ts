import { useState, useEffect, useCallback, useRef, useMemo } from 'react'

import type { AgentRealtimeResponse } from '@/types/apps/pbx/callCenter'

interface UseRealtimeAgentMonitorOptions {
  categoryFilter?: string
  enabled?: boolean
  onError?: (error: Error) => void
  onConnect?: () => void
  onDisconnect?: () => void
}

interface UseRealtimeAgentMonitorReturn {
  data: AgentRealtimeResponse | null
  isLoading: boolean
  isConnected: boolean
  error: Error | null
  reconnect: () => void
  disconnect: () => void
}

export const useRealtimeAgentMonitor = (
  options: UseRealtimeAgentMonitorOptions = {}
): UseRealtimeAgentMonitorReturn => {
  console.log('useRealtimeAgentMonitor hook called with options:', options)

  const {
    categoryFilter = 'voice', // Default to voice category
    enabled = true,
    onError,
    onConnect,
    onDisconnect
  } = options

  const [data, setData] = useState<AgentRealtimeResponse | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  const eventSourceRef = useRef<EventSource | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const reconnectAttemptsRef = useRef(0)
  const lastConnectAttemptRef = useRef(0)
  const isConnectingRef = useRef(false)
  const isMountedRef = useRef(true)
  const maxReconnectAttempts = 10
  const baseReconnectDelay = 1000 // Start with 1 second
  const maxReconnectDelay = 30000 // Max 30 seconds
  const backoffMultiplier = 2

  const disconnect = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close()
      eventSourceRef.current = null
    }

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }

    setIsConnected(false)
    setIsLoading(false)
    onDisconnect?.()
  }, [onDisconnect])

  // Calculate exponential backoff delay
  const getReconnectDelay = useCallback(() => {
    const delay = Math.min(
      baseReconnectDelay * Math.pow(backoffMultiplier, reconnectAttemptsRef.current),
      maxReconnectDelay
    )

    // Add jitter to prevent thundering herd
    const jitter = Math.random() * 0.3 * delay

    return delay + jitter
  }, [])

  // Rate limiting for connection attempts
  const canAttemptConnection = useCallback(() => {
    const now = Date.now()
    const timeSinceLastAttempt = now - lastConnectAttemptRef.current
    const minInterval = 2000 // Minimum 2 seconds between attempts

    return timeSinceLastAttempt >= minInterval
  }, [])

  const connect = useCallback(() => {
    if (!enabled || !isMountedRef.current) return

    // Prevent multiple simultaneous connection attempts
    if (isConnectingRef.current) {
      console.log('Connection already in progress, skipping...')

      return
    }

    // Rate limiting check
    if (!canAttemptConnection()) {
      console.log('Connection attempt rate limited, skipping...')

      return
    }

    isConnectingRef.current = true
    lastConnectAttemptRef.current = Date.now()

    // Clean up existing connection
    if (eventSourceRef.current) {
      console.log('Closing existing EventSource connection')
      eventSourceRef.current.close()
      eventSourceRef.current = null
    }

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }

    try {
      const url = new URL('/api/pbx/callcenter/agents/realtime-monitor/stream', window.location.origin)

      if (categoryFilter) {
        url.searchParams.append('category', categoryFilter)
      }

      console.log(
        `Connecting to realtime agent monitor SSE (attempt ${reconnectAttemptsRef.current + 1}):`,
        url.toString()
      )

      // Create EventSource with proper credentials
      eventSourceRef.current = new EventSource(url.toString(), {
        withCredentials: true
      })

      const eventSource = eventSourceRef.current

      console.log('EventSource created, readyState:', eventSource.readyState)

      // Set a timeout to handle cases where connection never opens
      const connectionTimeout = setTimeout(() => {
        if (eventSource.readyState === EventSource.CONNECTING) {
          console.error('SSE connection timeout - connection never opened')
          setIsLoading(false)
          setIsConnected(false)
          const timeoutError = new Error('Connection timeout')

          setError(timeoutError)
          onError?.(timeoutError)
          eventSource.close()
        }
      }, 15000) // 15 second timeout

      eventSource.onopen = () => {
        if (!isMountedRef.current) return

        clearTimeout(connectionTimeout)
        console.log('Realtime agent monitor SSE connected successfully')
        console.log('EventSource readyState after open:', eventSource.readyState)
        setIsConnected(true)
        setIsLoading(false)
        setError(null)
        reconnectAttemptsRef.current = 0 // Reset on successful connection
        isConnectingRef.current = false
        onConnect?.()
      }

      eventSource.onmessage = event => {
        if (!isMountedRef.current) return

        try {
          console.log('SSE message received:', event.data)
          const parsedData = JSON.parse(event.data) as AgentRealtimeResponse

          // Log every message for debugging
          console.log('Received realtime agent data:', {
            timestamp: parsedData.timestamp,
            agentCount: parsedData.agents?.length || 0,
            totalCount: parsedData.total_count
          })

          setData(parsedData)
          setError(null)
        } catch (parseError) {
          console.error('Error parsing SSE data:', parseError, 'Raw data:', event.data)
          const error = new Error('Failed to parse realtime agent data')

          setError(error)
          onError?.(error)
        }
      }

      eventSource.onerror = event => {
        clearTimeout(connectionTimeout)
        isConnectingRef.current = false

        if (!isMountedRef.current) return

        console.error('Realtime agent monitor SSE error:', event)
        console.error('EventSource readyState:', eventSource.readyState)
        console.error('EventSource url:', eventSource.url)
        setIsConnected(false)
        setIsLoading(false)

        const error = new Error(`SSE connection error - readyState: ${eventSource.readyState}`)

        setError(error)
        onError?.(error)

        // Only attempt to reconnect if we haven't exceeded max attempts and component is still mounted
        if (reconnectAttemptsRef.current < maxReconnectAttempts && isMountedRef.current) {
          reconnectAttemptsRef.current++
          const delay = getReconnectDelay()

          console.log(
            `Attempting to reconnect in ${Math.round(delay)}ms (${reconnectAttemptsRef.current}/${maxReconnectAttempts})...`
          )

          reconnectTimeoutRef.current = setTimeout(() => {
            if (isMountedRef.current) {
              connect()
            }
          }, delay)
        } else {
          console.error('Max reconnection attempts reached or component unmounted, giving up')
        }
      }
    } catch (error) {
      console.error('Error creating SSE connection:', error)
      isConnectingRef.current = false

      if (isMountedRef.current) {
        const connectionError = new Error('Failed to create SSE connection')

        setError(connectionError)
        setIsLoading(false)
        onError?.(connectionError)
      }
    }
  }, [enabled, categoryFilter, onConnect, onError, canAttemptConnection, getReconnectDelay])

  const reconnect = useCallback(() => {
    reconnectAttemptsRef.current = 0
    connect()
  }, [connect])

  // Connect when enabled or category filter changes
  useEffect(() => {
    console.log('useRealtimeAgentMonitor useEffect triggered:', { enabled, categoryFilter })
    isMountedRef.current = true

    if (enabled) {
      setIsLoading(true)

      // Add a small delay to prevent immediate cancellation
      const timer = setTimeout(() => {
        if (isMountedRef.current) {
          connect()
        }
      }, 100)

      return () => {
        clearTimeout(timer)
        disconnect()
      }
    } else {
      disconnect()
    }
  }, [enabled, categoryFilter, connect, disconnect])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false
      disconnect()
    }
  }, [disconnect])

  return {
    data,
    isLoading,
    isConnected,
    error,
    reconnect,
    disconnect
  }
}

// Additional hook for filtering agents by status
export const useFilteredRealtimeAgents = (data: AgentRealtimeResponse | null, filter?: string) => {
  return useMemo(() => {
    if (!data?.agents) return []

    if (!filter) return data.agents

    switch (filter.toLowerCase()) {
      case 'available':
        return data.agents.filter(agent => agent.is_available)
      case 'online':
        return data.agents.filter(agent => agent.is_logged_in)
      case 'on_call':
        return data.agents.filter(agent => agent.is_on_call)
      case 'offline':
        return data.agents.filter(agent => !agent.is_logged_in)
      default:
        return data.agents.filter(
          agent =>
            agent.agent_name?.toLowerCase().includes(filter.toLowerCase()) ||
            agent.agent_id?.toLowerCase().includes(filter.toLowerCase()) ||
            agent.agent_status.toLowerCase().includes(filter.toLowerCase())
        )
    }
  }, [data, filter])
}

// Hook for agent statistics
export const useRealtimeAgentStats = (data: AgentRealtimeResponse | null) => {
  return useMemo(() => {
    if (!data) {
      return {
        total: 0,
        online: 0,
        available: 0,
        on_call: 0,
        offline: 0
      }
    }

    return {
      total: data.total_count,
      online: data.online_count,
      available: data.available_count,
      on_call: data.on_call_count,
      offline: data.total_count - data.online_count
    }
  }, [data])
}
