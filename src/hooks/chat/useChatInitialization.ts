// Chat Initialization Hook
// Centralized hook to prevent duplicate API calls for chat rooms

'use client'

import { useEffect, useRef, useCallback } from 'react'

import { useAppDispatch, useAppSelector } from '@/redux-store/hooks'
import { fetchChatRooms } from '@/redux-store/slices/internal-chat/internalChatSlice'

interface UseChatInitializationOptions {
  autoInitialize?: boolean
  params?: { type?: string; page?: number; limit?: number }
}

export const useChatInitialization = (options: UseChatInitializationOptions = {}) => {
  const { autoInitialize = true, params = {} } = options
  const dispatch = useAppDispatch()
  const initializationAttempted = useRef(false)

  const { roomsLoading, roomsInitialized, rooms } = useAppSelector(state => state.internalChatReducer)

  // Initialize chat rooms if not already initialized
  const initializeChatRooms = useCallback(() => {
    if (!roomsLoading && !roomsInitialized) {
      initializationAttempted.current = true
      dispatch(fetchChatRooms(params))
    }
  }, [dispatch, roomsLoading, roomsInitialized, params])

  // Force refresh rooms (ignores initialization state)
  const forceRefreshRooms = useCallback(() => {
    if (!roomsLoading) {
      dispatch(fetchChatRooms(params))
    }
  }, [dispatch, roomsLoading, params])

  // Auto-initialize on mount if enabled
  useEffect(() => {
    if (autoInitialize) {
      initializeChatRooms()
    }
  }, [autoInitialize, initializeChatRooms])

  // Reset initialization flag when rooms are reset
  useEffect(() => {
    if (!roomsInitialized) {
      initializationAttempted.current = false
    }
  }, [roomsInitialized])

  // Auto-refresh when roomsInitialized is set to false (for refreshRooms action)
  useEffect(() => {
    if (!roomsInitialized && initializationAttempted.current) {
      // This means refreshRooms was called, so we should re-fetch
      initializeChatRooms()
    }
  }, [roomsInitialized, initializeChatRooms])

  return {
    initializeChatRooms,
    forceRefreshRooms,
    isInitialized: roomsInitialized,
    isLoading: roomsLoading,
    hasRooms: Object.keys(rooms).length > 0,
    roomsCount: Object.keys(rooms).length
  }
}
