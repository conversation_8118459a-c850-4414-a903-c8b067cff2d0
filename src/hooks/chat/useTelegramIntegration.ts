// Telegram Integration Hook
// Provides utilities for working with Telegram integration in internal chat

'use client'

import { useState, useEffect, useCallback } from 'react'

import { useAppSelector } from '@/redux-store/hooks'

interface TelegramRoomInfo {
  telegram_room_uuid: string
  internal_room_uuid: string
  contact: {
    contact_uuid: string
    telegram_user_id: string
    telegram_chat_id: string
    display_name: string
    username: string | null
    first_name: string | null
    last_name: string | null
    language_code: string | null
    last_interaction_date: Date | null
  }
  room_status: string
  last_message_at: Date | null
  conversation_metadata: any
}

interface TelegramStats {
  total_contacts: number
  active_rooms: number
  unassigned_rooms: number
  total_messages_sent: number
  total_messages_received: number
  failed_messages: number
  last_activity: Date | null
}

interface UseTelegramIntegrationReturn {

  // Room management
  isTelegramRoom: (roomId: string) => Promise<boolean>
  getTelegramRoomInfo: (roomId: string) => Promise<TelegramRoomInfo | null>
  closeRoom: (roomId: string) => Promise<void>
  reopenRoom: (roomId: string) => Promise<void>
  assignAgent: (roomId: string, agentId: string) => Promise<void>

  // Message handling
  sendTelegramMessage: (roomId: string, content: string) => Promise<any>

  // Statistics
  getStats: () => Promise<TelegramStats>

  // State
  loading: boolean
  error: string | null
}

export const useTelegramIntegration = (): UseTelegramIntegrationReturn => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Get current user from Redux store
  const {} = useAppSelector(state => state.auth || {})

  // Helper function to handle API calls
  const apiCall = useCallback(async (url: string, options: RequestInit = {}) => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))

        throw new Error(errorData.error || `HTTP ${response.status}`)
      }

      return await response.json()
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'

      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  // Get Telegram room information
  const getTelegramRoomInfo = useCallback(
    async (roomId: string): Promise<TelegramRoomInfo | null> => {
      try {
        // This would be implemented as an API endpoint
        const response = await apiCall(`/api/internal-chat/telegram/rooms/${roomId}`)

        return response.room_info || null
      } catch {
        return null
      }
    },
    [apiCall]
  )

  // Check if room is a Telegram room
  const isTelegramRoom = useCallback(
    async (roomId: string): Promise<boolean> => {
      try {
        const roomInfo = await getTelegramRoomInfo(roomId)

        return !!roomInfo
      } catch {
        return false
      }
    },
    [getTelegramRoomInfo]
  )

  // Close Telegram room
  const closeRoom = useCallback(
    async (roomId: string): Promise<void> => {
      await apiCall(`/api/internal-chat/telegram/rooms/${roomId}/close`, {
        method: 'POST'
      })
    },
    [apiCall]
  )

  // Reopen Telegram room
  const reopenRoom = useCallback(
    async (roomId: string): Promise<void> => {
      await apiCall(`/api/internal-chat/telegram/rooms/${roomId}/reopen`, {
        method: 'POST'
      })
    },
    [apiCall]
  )

  // Assign agent to Telegram room
  const assignAgent = useCallback(
    async (roomId: string, agentId: string): Promise<void> => {
      await apiCall(`/api/internal-chat/telegram/rooms/${roomId}/assign`, {
        method: 'POST',
        body: JSON.stringify({ agent_uuid: agentId })
      })
    },
    [apiCall]
  )

  // Send message to Telegram
  const sendTelegramMessage = useCallback(
    async (roomId: string, content: string) => {
      return await apiCall('/api/internal-chat/telegram/send-message', {
        method: 'POST',
        body: JSON.stringify({
          room_uuid: roomId,
          content,
          message_type: 0 // TEXT
        })
      })
    },
    [apiCall]
  )

  // Get Telegram integration statistics
  const getStats = useCallback(async (): Promise<TelegramStats> => {
    try {
      const response = await apiCall('/api/internal-chat/telegram/stats')

      if (response.stats) {
        return response.stats
      } else {
        // Handle configuration errors
        throw new Error(response.message || response.error || 'Failed to get statistics')
      }
    } catch (error) {
      console.error('Error fetching Telegram stats:', error)
      throw error
    }
  }, [apiCall])

  return {
    // Room management
    isTelegramRoom,
    getTelegramRoomInfo,
    closeRoom,
    reopenRoom,
    assignAgent,

    // Message handling
    sendTelegramMessage,

    // Statistics
    getStats,

    // State
    loading,
    error
  }
}

// Hook for Telegram room detection in message components
export const useTelegramRoomDetection = (roomId: string | null) => {
  const [isTelegramRoom, setIsTelegramRoom] = useState(false)
  const [telegramInfo, setTelegramInfo] = useState<TelegramRoomInfo | null>(null)
  const [loading, setLoading] = useState(false)

  const { getTelegramRoomInfo } = useTelegramIntegration()

  useEffect(() => {
    if (!roomId) {
      setIsTelegramRoom(false)
      setTelegramInfo(null)

      return
    }

    const checkTelegramRoom = async () => {
      setLoading(true)

      try {
        const info = await getTelegramRoomInfo(roomId)

        setIsTelegramRoom(!!info)
        setTelegramInfo(info)
      } catch (error) {
        console.error('Error checking Telegram room:', error)
        setIsTelegramRoom(false)
        setTelegramInfo(null)
      } finally {
        setLoading(false)
      }
    }

    checkTelegramRoom()
  }, [roomId, getTelegramRoomInfo])

  return {
    isTelegramRoom,
    telegramInfo,
    loading
  }
}

// Hook for Telegram message formatting
export const useTelegramMessageFormatting = () => {
  // Format message content for Telegram display
  const formatTelegramContent = useCallback((content: string) => {
    if (!content) return ''

    // Convert basic HTML to display format
    return content
      .replace(/<strong>(.*?)<\/strong>/g, '**$1**')
      .replace(/<em>(.*?)<\/em>/g, '*$1*')
      .replace(/<code>(.*?)<\/code>/g, '`$1`')
  }, [])

  // Check if message is from Telegram
  const isTelegramMessage = useCallback((message: any) => {
    return message.author_uuid === 'telegram-user' || message.platform === 'telegram' || !!message.telegram_message_id
  }, [])

  // Get delivery status for Telegram messages
  const getTelegramDeliveryStatus = useCallback(
    (message: any) => {
      if (!isTelegramMessage(message)) return null

      return message.delivery_status || 'unknown'
    },
    [isTelegramMessage]
  )

  return {
    formatTelegramContent,
    isTelegramMessage,
    getTelegramDeliveryStatus
  }
}

export default useTelegramIntegration
