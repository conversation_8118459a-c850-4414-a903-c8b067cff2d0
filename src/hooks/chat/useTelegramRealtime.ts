// Telegram Real-time Integration Hook
// Handles real-time events for Telegram integration

'use client'

import { useEffect, useCallback, useRef, useState } from 'react'

import { useChatSocket } from '@/hooks/chat/useChatSocket'

interface TelegramRealtimeOptions {
  roomId?: string | null
  onNewMessage?: (message: any) => void
  onDeliveryStatusUpdate?: (messageId: string, status: string, error?: string) => void
  onContactUpdate?: (contact: any) => void
  onRoomStatusChange?: (roomId: string, status: string, metadata?: any) => void
}

export const useTelegramRealtime = (options: TelegramRealtimeOptions = {}) => {
  const { roomId } = options

  const { socket, isSocketConnected } = useChatSocket()
  const handlersRef = useRef(options)

  // Update handlers ref when options change
  useEffect(() => {
    handlersRef.current = options
  }, [options])

  // Handle Telegram-specific message events
  const handleTelegramMessage = useCallback((data: any) => {
    if (data.platform === 'telegram') {
      console.log('Received Telegram message:', data)

      // Call custom handler if provided
      if (handlersRef.current.onNewMessage) {
        handlersRef.current.onNewMessage(data)
      }

      // Update Redux store if needed
      // This would integrate with your existing chat slice
      // dispatch(addMessage(data))
    }
  }, [])

  // Handle delivery status updates
  const handleDeliveryStatus = useCallback((data: any) => {
    if (data.platform === 'telegram') {
      console.log('Telegram delivery status update:', data)

      if (handlersRef.current.onDeliveryStatusUpdate) {
        handlersRef.current.onDeliveryStatusUpdate(data.message_id, data.delivery_status, data.error)
      }
    }
  }, [])

  // Handle contact updates
  const handleContactUpdate = useCallback((data: any) => {
    console.log('Telegram contact update:', data)

    if (handlersRef.current.onContactUpdate) {
      handlersRef.current.onContactUpdate(data.contact)
    }
  }, [])

  // Handle room status changes
  const handleRoomStatusChange = useCallback((data: any) => {
    if (data.platform === 'telegram') {
      console.log('Telegram room status change:', data)

      if (handlersRef.current.onRoomStatusChange) {
        handlersRef.current.onRoomStatusChange(data.room_id, data.status, data.metadata)
      }
    }
  }, [])

  // Handle general Telegram events
  const handleTelegramEvent = useCallback(
    (data: any) => {
      console.log('Telegram event:', data)

      switch (data.event_type) {
        case 'new_message':
          handleTelegramMessage(data.data.message)
          break
        case 'contact_updated':
          handleContactUpdate(data.data)
          break
        case 'room_status_changed':
          handleRoomStatusChange(data.data)
          break
        default:
          console.log('Unhandled Telegram event:', data.event_type)
      }
    },
    [handleTelegramMessage, handleContactUpdate, handleRoomStatusChange]
  )

  // Set up socket event listeners
  useEffect(() => {
    if (!socket || !isSocketConnected) return

    // Listen for new messages
    socket.on('new_message', handleTelegramMessage)

    // Listen for delivery status updates
    socket.on('message_delivery_status', handleDeliveryStatus)

    // Listen for room status changes
    socket.on('room_status_changed', handleRoomStatusChange)

    // Listen for general Telegram events
    socket.on('telegram_event', handleTelegramEvent)

    // Cleanup listeners
    return () => {
      socket.off('new_message', handleTelegramMessage)
      socket.off('message_delivery_status', handleDeliveryStatus)
      socket.off('room_status_changed', handleRoomStatusChange)
      socket.off('telegram_event', handleTelegramEvent)
    }
  }, [
    socket,
    isSocketConnected,
    handleTelegramMessage,
    handleDeliveryStatus,
    handleRoomStatusChange,
    handleTelegramEvent
  ])

  // Join room-specific events if roomId is provided
  useEffect(() => {
    if (!socket || !isSocketConnected || !roomId) return

    // Join room for real-time updates
    socket.emit('join_room', { room_uuid: roomId })

    return () => {
      socket.emit('leave_room', { room_uuid: roomId })
    }
  }, [socket, isSocketConnected, roomId])

  return {
    isConnected: isSocketConnected,
    socket
  }
}

// Hook specifically for Telegram message delivery tracking
export const useTelegramDeliveryTracking = (roomId: string | null) => {
  const [deliveryStatuses, setDeliveryStatuses] = useState<
    Record<
      string,
      {
        status: string
        error?: string
        timestamp: Date
      }
    >
  >({})

  const handleDeliveryUpdate = useCallback((messageId: string, status: string, error?: string) => {
    setDeliveryStatuses(prev => ({
      ...prev,
      [messageId]: {
        status,
        error,
        timestamp: new Date()
      }
    }))
  }, [])

  useTelegramRealtime({
    roomId,
    onDeliveryStatusUpdate: handleDeliveryUpdate
  })

  const getMessageDeliveryStatus = useCallback(
    (messageId: string) => {
      return deliveryStatuses[messageId] || null
    },
    [deliveryStatuses]
  )

  return {
    deliveryStatuses,
    getMessageDeliveryStatus
  }
}

// Hook for Telegram typing indicators
export const useTelegramTyping = (roomId: string | null) => {
  const [typingUsers, setTypingUsers] = useState<Set<string>>(new Set())
  const { socket, isSocketConnected } = useChatSocket()

  // Handle typing events
  useEffect(() => {
    if (!socket || !isSocketConnected || !roomId) return

    const handleUserTyping = (data: any) => {
      if (data.room_uuid === roomId && data.platform === 'telegram') {
        setTypingUsers(prev => new Set([...prev, data.user_uuid]))

        // Remove typing indicator after timeout
        setTimeout(() => {
          setTypingUsers(prev => {
            const newSet = new Set(prev)

            newSet.delete(data.user_uuid)

            return newSet
          })
        }, 3000)
      }
    }

    const handleUserStoppedTyping = (data: any) => {
      if (data.room_uuid === roomId && data.platform === 'telegram') {
        setTypingUsers(prev => {
          const newSet = new Set(prev)

          newSet.delete(data.user_uuid)

          return newSet
        })
      }
    }

    socket.on('user_typing', handleUserTyping)
    socket.on('user_stopped_typing', handleUserStoppedTyping)

    return () => {
      socket.off('user_typing', handleUserTyping)
      socket.off('user_stopped_typing', handleUserStoppedTyping)
    }
  }, [socket, isSocketConnected, roomId])

  // Send typing indicator
  const sendTypingIndicator = useCallback(() => {
    if (socket && isSocketConnected && roomId) {
      socket.emit('typing_start', {
        room_uuid: roomId,
        platform: 'telegram'
      })
    }
  }, [socket, isSocketConnected, roomId])

  // Stop typing indicator
  const stopTypingIndicator = useCallback(() => {
    if (socket && isSocketConnected && roomId) {
      socket.emit('typing_stop', {
        room_uuid: roomId,
        platform: 'telegram'
      })
    }
  }, [socket, isSocketConnected, roomId])

  return {
    typingUsers: Array.from(typingUsers),
    sendTypingIndicator,
    stopTypingIndicator
  }
}

export default useTelegramRealtime
