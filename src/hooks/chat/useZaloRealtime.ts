// Zalo Real-time Integration Hook
// Handles real-time events for Zalo integration

'use client'

import { useEffect, useCallback, useRef, useState } from 'react'

import { useChatSocket } from '@/hooks/chat/useChatSocket'

interface ZaloRealtimeOptions {
  roomId?: string | null
  onNewMessage?: (message: any) => void
  onDeliveryStatusUpdate?: (messageId: string, status: string, error?: string) => void
  onContactUpdate?: (contact: any) => void
  onRoomStatusChange?: (roomId: string, status: string, metadata?: any) => void
}

export const useZaloRealtime = (options: ZaloRealtimeOptions = {}) => {
  const { roomId } = options

  const { socket, isSocketConnected } = useChatSocket()
  const handlersRef = useRef(options)

  // Update handlers ref when options change
  useEffect(() => {
    handlersRef.current = options
  }, [options])

  // Handle Zalo-specific message events
  const handleZaloMessage = useCallback((data: any) => {
    if (data.platform === 'zalo') {
      console.log('Received Zalo message:', data)

      // Call custom handler if provided
      if (handlersRef.current.onNewMessage) {
        handlersRef.current.onNewMessage(data)
      }

      // Update Redux store if needed
      // This would integrate with your existing chat slice
      // dispatch(addMessage(data))
    }
  }, [])

  // Handle delivery status updates
  const handleDeliveryStatus = useCallback((data: any) => {
    console.log('Zalo delivery status update:', data)

    if (handlersRef.current.onDeliveryStatusUpdate) {
      handlersRef.current.onDeliveryStatusUpdate(data.message_id, data.status, data.error)
    }
  }, [])

  // Handle contact updates
  const handleContactUpdate = useCallback((data: any) => {
    console.log('Zalo contact update:', data)

    if (handlersRef.current.onContactUpdate) {
      handlersRef.current.onContactUpdate(data)
    }
  }, [])

  // Handle room status changes
  const handleRoomStatusChange = useCallback((data: any) => {
    console.log('Zalo room status change:', data)

    if (handlersRef.current.onRoomStatusChange) {
      handlersRef.current.onRoomStatusChange(data.room_id, data.status, data.metadata)
    }
  }, [])

  // Handle general Zalo events
  const handleZaloEvent = useCallback(
    (data: any) => {
      console.log('Zalo event:', data)

      switch (data.event_type) {
        case 'new_message':
          handleZaloMessage(data.data.message)
          break
        case 'contact_updated':
          handleContactUpdate(data.data)
          break
        case 'room_status_changed':
          handleRoomStatusChange(data.data)
          break
        default:
          console.log('Unhandled Zalo event:', data.event_type)
      }
    },
    [handleZaloMessage, handleContactUpdate, handleRoomStatusChange]
  )

  // Set up socket event listeners
  useEffect(() => {
    if (!socket || !isSocketConnected) return

    // Listen for Zalo-specific events
    socket.on('zalo_message', handleZaloMessage)
    socket.on('zalo_delivery_status', handleDeliveryStatus)
    socket.on('zalo_room_status', handleRoomStatusChange)
    socket.on('zalo_event', handleZaloEvent)

    return () => {
      socket.off('zalo_message', handleZaloMessage)
      socket.off('zalo_delivery_status', handleDeliveryStatus)
      socket.off('zalo_room_status', handleRoomStatusChange)
      socket.off('zalo_event', handleZaloEvent)
    }
  }, [
    socket,
    isSocketConnected,
    handleZaloMessage,
    handleDeliveryStatus,
    handleRoomStatusChange,
    handleZaloEvent
  ])

  // Join room-specific events if roomId is provided
  useEffect(() => {
    if (!socket || !isSocketConnected || !roomId) return

    // Join room for real-time updates
    socket.emit('join_room', { room_uuid: roomId })

    return () => {
      socket.emit('leave_room', { room_uuid: roomId })
    }
  }, [socket, isSocketConnected, roomId])

  return {
    isConnected: isSocketConnected,
    socket
  }
}

// Hook for Zalo typing indicators
export const useZaloTyping = (roomId: string | null) => {
  const { socket, isSocketConnected } = useChatSocket()
  const [isTyping, setIsTyping] = useState(false)
  const [typingUsers, setTypingUsers] = useState<string[]>([])
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Stop typing indicator
  const stopTypingIndicator = useCallback(() => {
    if (!socket || !isSocketConnected || !roomId) return

    socket.emit('zalo_typing_stop', { room_uuid: roomId })
    setIsTyping(false)

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
      typingTimeoutRef.current = null
    }
  }, [socket, isSocketConnected, roomId])

  // Send typing indicator
  const sendTypingIndicator = useCallback(() => {
    if (!socket || !isSocketConnected || !roomId) return

    socket.emit('zalo_typing_start', { room_uuid: roomId })
    setIsTyping(true)

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }

    // Auto-stop typing after 3 seconds
    typingTimeoutRef.current = setTimeout(() => {
      stopTypingIndicator()
    }, 3000)
  }, [socket, isSocketConnected, roomId, stopTypingIndicator])

  // Listen for typing events from other users
  useEffect(() => {
    if (!socket || !isSocketConnected) return

    const handleTypingStart = (data: any) => {
      if (data.room_uuid === roomId) {
        setTypingUsers(prev => [...prev.filter(id => id !== data.user_id), data.user_id])
      }
    }

    const handleTypingStop = (data: any) => {
      if (data.room_uuid === roomId) {
        setTypingUsers(prev => prev.filter(id => id !== data.user_id))
      }
    }

    socket.on('zalo_user_typing_start', handleTypingStart)
    socket.on('zalo_user_typing_stop', handleTypingStop)

    return () => {
      socket.off('zalo_user_typing_start', handleTypingStart)
      socket.off('zalo_user_typing_stop', handleTypingStop)
    }
  }, [socket, isSocketConnected, roomId])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current)
      }
    }
  }, [])

  return {
    sendTypingIndicator,
    stopTypingIndicator,
    isTyping,
    typingUsers
  }
}

// Hook for Zalo delivery tracking
export const useZaloDeliveryTracking = (roomId: string | null) => {
  const { socket, isSocketConnected } = useChatSocket()
  const [deliveryStatuses, setDeliveryStatuses] = useState<Record<string, string>>({})

  useEffect(() => {
    if (!socket || !isSocketConnected) return

    const handleDeliveryUpdate = (data: any) => {
      if (data.room_uuid === roomId) {
        setDeliveryStatuses(prev => ({
          ...prev,
          [data.message_id]: data.status
        }))
      }
    }

    socket.on('zalo_message_delivered', handleDeliveryUpdate)
    socket.on('zalo_message_failed', handleDeliveryUpdate)

    return () => {
      socket.off('zalo_message_delivered', handleDeliveryUpdate)
      socket.off('zalo_message_failed', handleDeliveryUpdate)
    }
  }, [socket, isSocketConnected, roomId])

  const getDeliveryStatus = useCallback((messageId: string) => {
    return deliveryStatuses[messageId] || 'pending'
  }, [deliveryStatuses])

  return {
    deliveryStatuses,
    getDeliveryStatus
  }
}
