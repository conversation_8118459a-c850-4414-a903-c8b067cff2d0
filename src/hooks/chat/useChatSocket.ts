// Chat Socket Hook
// Manages WebSocket connection for real-time chat functionality
// Updated to use SocketManager for proper connection isolation

'use client'

import { useEffect, useRef, useCallback } from 'react'

import type { Socket } from 'socket.io-client'
import { io } from 'socket.io-client'

import type { ChatMessage, UserPresence, ChatNotification } from '@/types/apps/internal-chat/chatTypes'

interface UseChatSocketOptions {
  onMessage?: (message: ChatMessage) => void
  onPresenceUpdate?: (presence: UserPresence) => void
  onNotification?: (notification: ChatNotification) => void
  onNewRoom?: (room: any) => void
  onConnectionChange?: (status: 'connecting' | 'connected' | 'disconnected' | 'error') => void
  onTyping?: (data: { roomId: string; userId: string; userName: string }) => void
  onStoppedTyping?: (data: { roomId: string; userId: string }) => void
  domain?: string
}

export const useChatSocket = (options: UseChatSocketOptions = {}) => {
  const socketRef = useRef<Socket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>()
  const reconnectAttempts = useRef(0)
  const maxReconnectAttempts = 5
  const currentUserRef = useRef<string | null>(null)
  const currentDomainRef = useRef<string | null>(null)

  // Connect to socket with simplified approach
  const connect = useCallback(
    async (userId: string) => {
      const domain = options.domain || 'default'

      try {
        // Store current user and domain
        currentUserRef.current = userId
        currentDomainRef.current = domain

        // Set connecting status
        options.onConnectionChange?.('connecting')

        // Disconnect existing socket if any
        if (socketRef.current) {
          socketRef.current.disconnect()
        }

        // Create new socket connection
        const socketUrl = process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3000'

        console.log('Connecting to socket:', socketUrl, { userId, domain })

        const socket = io(socketUrl, {
          transports: ['websocket', 'polling'],
          timeout: 5000, // Reduced timeout for faster feedback
          forceNew: true,
          autoConnect: true,
          reconnection: true,
          reconnectionAttempts: 3, // Reduced attempts for faster feedback
          reconnectionDelay: 1000,
          reconnectionDelayMax: 3000,
          query: {
            userId,
            domain,
            type: 'internal_chat'
          }
        })

        socketRef.current = socket

        // Connection events
        socket.on('connect', () => {
          options.onConnectionChange?.('connected')
          reconnectAttempts.current = 0

          // Join chat namespace
          socket.emit('chat_connect', { user_uuid: userId })
        })

        socket.on('disconnect', (reason: string) => {
          console.log('Socket disconnected:', reason)
          options.onConnectionChange?.('disconnected')

          // Auto-reconnect unless manually disconnected
          if (reason !== 'io client disconnect') {
            scheduleReconnect(userId)
          }
        })

        socket.on('connect_error', (error: any) => {
          console.error('Socket connection error:', error)
          options.onConnectionChange?.('error')
          scheduleReconnect(userId)
        })

        // Add connection timeout
        const connectionTimeout = setTimeout(() => {
          if (!socket.connected) {
            console.warn('Socket connection timeout')
            options.onConnectionChange?.('error')
            socket.disconnect()
          }
        }, 10000) // 10 second timeout

        // Clear timeout when connected
        socket.on('connect', () => {
          clearTimeout(connectionTimeout)
        })

        // Chat events
        socket.on('chat_connected', (data: any) => {
          console.log('Chat connected:', data)
        })

        socket.on('rooms_joined', (data: any) => {
          console.log('Auto-joined rooms:', data)
        })

        socket.on('rooms_join_error', (data: any) => {
          console.error('Error joining rooms:', data)
        })

        socket.on('new_message', (message: ChatMessage) => {
          console.log('Received new message:', message.content, 'for room:', message.room_uuid)
          options.onMessage?.(message)
        })

        socket.on('new_room', (room: any) => {
          console.log('Received new room notification:', room.room_name, 'room_uuid:', room.room_uuid)
          options.onNewRoom?.(room)

          // Auto-join the new room - this is critical for receiving messages
          if (room.room_uuid) {
            socket.emit('join_room', { room_uuid: room.room_uuid })
            console.log('Auto-joining new room via manual join_room event:', room.room_uuid)
          }
        })

        socket.on('room_joined', (data: { room_uuid: string }) => {
          console.log('Successfully joined room:', data.room_uuid)
        })

        socket.on('message_updated', (message: ChatMessage) => {
          options.onMessage?.(message)
        })

        socket.on('user_presence_changed', (presence: UserPresence) => {
          options.onPresenceUpdate?.(presence)
        })

        socket.on('notification', (notification: ChatNotification) => {
          console.log('Socket received notification:', notification)
          options.onNotification?.(notification)
        })

        socket.on('user_typing', data => {
          options.onTyping?.(data)
        })

        socket.on('user_stopped_typing', data => {
          options.onStoppedTyping?.(data)
        })

        socket.on('error', error => {
          console.error('Chat socket error:', error)
        })

        // Unified broadcast listeners for optimized message delivery
        socket.on('batch_messages', (data: any) => {
          console.log('Received batch messages:', data.batch_size, 'messages for room:', data.room_uuid)

          // Process batch messages directly here for now
          if (data.messages && Array.isArray(data.messages)) {
            data.messages.forEach((message: ChatMessage) => {
              options.onMessage?.(message)
            })
          }
        })

        socket.on('single_message', (data: any) => {
          console.log('Received single unified message for room:', data.room_uuid)

          // Process single message directly here for now
          if (data.message) {
            options.onMessage?.(data.message)
          }
        })
      } catch (error) {
        console.error('Failed to create socket connection:', error)
        options.onConnectionChange?.('error')
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [options]
  )

  // Disconnect socket
  const disconnect = useCallback(async () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
    }

    // Close socket connection
    if (socketRef.current) {
      socketRef.current.disconnect()
      socketRef.current = null
    }

    currentUserRef.current = null
    currentDomainRef.current = null
    options.onConnectionChange?.('disconnected')
  }, [options])

  // Schedule reconnection
  const scheduleReconnect = useCallback(
    (userId: string) => {
      if (reconnectAttempts.current >= maxReconnectAttempts) {
        console.error('Max reconnection attempts reached')

        return
      }

      const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000)

      reconnectAttempts.current++

      reconnectTimeoutRef.current = setTimeout(() => {
        console.log(`Attempting to reconnect (${reconnectAttempts.current}/${maxReconnectAttempts})`)
        connect(userId)
      }, delay)
    },
    [connect]
  )

  // Join room
  const joinRoom = useCallback((roomId: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('join_room', { room_uuid: roomId })
    }
  }, [])

  // Leave room
  const leaveRoom = useCallback((roomId: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('leave_room', { room_uuid: roomId })
    }
  }, [])

  // Send typing indicator
  const sendTyping = useCallback((roomId: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('typing_start', { room_uuid: roomId })
    }
  }, [])

  // Stop typing indicator
  const stopTyping = useCallback((roomId: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('typing_stop', { room_uuid: roomId })
    }
  }, [])

  // Update presence
  const updatePresence = useCallback((status: string, activity?: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('update_presence', {
        status,
        current_activity: activity
      })
    }
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clean up socket connection
      if (socketRef.current) {
        socketRef.current.disconnect()
      }

      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }

      socketRef.current = null
      currentUserRef.current = null
      currentDomainRef.current = null
    }
  }, [])

  return {
    connect,
    disconnect,
    joinRoom,
    leaveRoom,
    sendTyping,
    stopTyping,
    updatePresence,
    isSocketConnected: socketRef.current?.connected || false,
    socket: socketRef.current
  }
}
