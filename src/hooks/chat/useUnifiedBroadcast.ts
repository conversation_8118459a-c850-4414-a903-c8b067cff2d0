// Unified Broadcast Hook
// <PERSON>les unified broadcast messages on the client side
// Supports Issue 1.1 fix: Zalo Chat Message Display Delays

import { useEffect, useCallback } from 'react'

import { useAppDispatch } from '@/redux-store/hooks'
import { addMessage, updateUnreadCount, addNotification } from '@/redux-store/slices/internal-chat/internalChatSlice'
import type { ChatMessage } from '@/types/apps/internal-chat/chatTypes'
import { NotificationType } from '@/types/apps/internal-chat/chatTypes'

interface UnifiedBroadcastMessage {
  type: 'message' | 'notification' | 'status_update'
  room_uuid: string
  message_id: string
  content: string
  author_uuid: string
  author_name?: string
  message_type: number
  created_at: number
  flags: number
  platform: 'internal' | 'zalo' | 'telegram' | 'facebook'
  platform_message_id?: string
  delivery_status?: 'pending' | 'sent' | 'delivered' | 'failed'
  unread_counts?: Record<string, number>
  metadata?: {
    zalo_message_id?: string
    telegram_message_id?: string
    facebook_message_id?: string
    original_platform?: string
    is_external?: boolean
  }
}

interface BatchBroadcastMessage {
  type: 'batch_messages' | 'single_message'
  room_uuid: string
  messages?: UnifiedBroadcastMessage[]
  timestamp: number
  batch_size?: number
}

export const useUnifiedBroadcast = (socket: any, userId: string) => {
  const dispatch = useAppDispatch()

  // Process individual unified message
  const processUnifiedMessage = useCallback((message: UnifiedBroadcastMessage) => {
    try {
      // Convert to ChatMessage format for Redux store
      const chatMessage: ChatMessage = {
        message_id: message.message_id.toString(),
        room_uuid: message.room_uuid,
        author_uuid: message.author_uuid,
        content: message.content,
        message_type: message.message_type,
        reply_to: null,
        edited_at: null,
        created_at: message.created_at,
        flags: message.flags,
        author_name: message.author_name || getAuthorDisplayName(message.platform)
      }

      // Add message to Redux store
      dispatch(addMessage({
        roomId: message.room_uuid,
        message: chatMessage
      }))

      // Update unread counts if provided
      if (message.unread_counts) {
        Object.entries(message.unread_counts).forEach(([userUuid, count]) => {
          if (userUuid !== userId) { // Don't update count for current user
            dispatch(updateUnreadCount({
              roomId: message.room_uuid,
              count
            }))
          }
        })
      }

      // Create notification for external messages
      if (message.metadata?.is_external && message.author_uuid !== userId) {
        dispatch(addNotification({
          notification_uuid: `${message.message_id}_${Date.now()}`,
          user_uuid: userId,
          room_uuid: message.room_uuid,
          message_id: message.message_id.toString(),
          notification_type: NotificationType.MESSAGE,
          is_read: false,
          is_sent: false,
          insert_date: new Date().toISOString(),
          read_date: null,
          expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
        }))
      }

      console.log(`Processed ${message.platform} message:`, {
        message_id: message.message_id,
        room_uuid: message.room_uuid,
        platform: message.platform,
        delivery_status: message.delivery_status
      })
    } catch (error) {
      console.error('Error processing unified message:', error, message)
    }
  }, [dispatch, userId])

  // Handle batch messages
  const handleBatchMessages = useCallback((data: BatchBroadcastMessage) => {
    if (data.type === 'batch_messages' && data.messages) {
      // Process batch of messages
      data.messages.forEach(message => {
        processUnifiedMessage(message)
      })

      console.log(`Processed batch of ${data.messages.length} messages for room ${data.room_uuid}`)
    } else if (data.type === 'single_message') {
      // Process single message (fallback format)
      const message = data as any as UnifiedBroadcastMessage

      processUnifiedMessage(message)
    }
  }, [processUnifiedMessage])

  // Get display name for author based on platform
  const getAuthorDisplayName = (platform: string): string => {
    switch (platform) {
      case 'zalo':
        return 'ZALO User'
      case 'telegram':
        return 'Telegram User'
      case 'facebook':
        return 'Facebook User'
      case 'internal':
        return 'Agent'
      default:
        return 'User'
    }
  }

  // Set up socket event listeners
  useEffect(() => {
    if (!socket) return

    // Listen for unified broadcast messages
    socket.on('batch_messages', handleBatchMessages)
    socket.on('single_message', handleBatchMessages)
    
    // Legacy support for existing message formats
    socket.on('new_message', (data: any) => {
      // Convert legacy format to unified format
      if (data.data || data.message_id) {
        const messageData = data.data || data

        const unifiedMessage: UnifiedBroadcastMessage = {
          type: 'message',
          room_uuid: messageData.room_uuid,
          message_id: messageData.message_id?.toString() || Date.now().toString(),
          content: messageData.content || '',
          author_uuid: messageData.author_uuid || '',
          author_name: messageData.author_name,
          message_type: messageData.message_type || 0,
          created_at: messageData.created_at || Math.floor(Date.now() / 1000),
          flags: messageData.flags || 0,
          platform: messageData.platform || 'internal',
          platform_message_id: messageData.zalo_message_id || messageData.telegram_message_id || messageData.facebook_message_id,
          delivery_status: messageData.delivery_status || 'sent',
          unread_counts: messageData.unread_counts,
          metadata: {
            zalo_message_id: messageData.zalo_message_id,
            telegram_message_id: messageData.telegram_message_id,
            facebook_message_id: messageData.facebook_message_id,
            is_external: messageData.platform !== 'internal'
          }
        }
        
        processUnifiedMessage(unifiedMessage)
      }
    })

    // Cleanup listeners
    return () => {
      socket.off('batch_messages', handleBatchMessages)
      socket.off('single_message', handleBatchMessages)
      socket.off('new_message')
    }
  }, [socket, handleBatchMessages, processUnifiedMessage])

  return {
    processUnifiedMessage,
    handleBatchMessages
  }
}

// Export types for use in other components
export type { UnifiedBroadcastMessage, BatchBroadcastMessage }
