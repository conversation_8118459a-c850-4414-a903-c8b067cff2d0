// Zalo Integration Hook
// Provides utilities for working with Zalo integration in internal chat

'use client'

import { useState, useEffect, useCallback } from 'react'

interface ZaloRoomInfo {
  zalo_room_uuid: string
  internal_room_uuid: string
  contact: {
    contact_uuid: string
    zalo_user_id: string
    display_name: string
    username: string | null
    avatar_url: string | null
    phone: string | null
    is_follower: boolean | null
    last_interaction_date: Date | null
  }
  room_status: string
  last_message_at: Date | null
  conversation_metadata: any
}

interface ZaloStats {
  total_contacts: number
  active_rooms: number
  unassigned_rooms: number
  total_messages_sent: number
  total_messages_received: number
  failed_messages: number
  last_activity: Date | null
}

interface UseZaloIntegrationReturn {

  // Room management
  isZaloRoom: (roomId: string) => Promise<boolean>
  getZaloRoomInfo: (roomId: string) => Promise<ZaloRoomInfo | null>
  closeRoom: (roomId: string) => Promise<void>
  reopenRoom: (roomId: string) => Promise<void>
  assignAgent: (roomId: string, agentId: string) => Promise<void>

  // Message handling
  sendZaloMessage: (roomId: string, content: string) => Promise<any>

  // Statistics
  getStats: () => Promise<ZaloStats>

  // State
  loading: boolean
  error: string | null
}

export const useZaloIntegration = (): UseZaloIntegrationReturn => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Generic API call helper with error handling
  const apiCall = useCallback(async (url: string, options: RequestInit = {}) => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))

        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.json()
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'

      setError(errorMessage)

      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  // Get Zalo room information
  const getZaloRoomInfo = useCallback(
    async (roomId: string): Promise<ZaloRoomInfo | null> => {
      try {
        const response = await apiCall(`/api/internal-chat/zalo/room-info?room_uuid=${roomId}`)

        return response.room_info || null
      } catch (error) {
        // Not a Zalo room or error occurred
        return null
      }
    },
    [apiCall]
  )

  // Check if room is a Zalo room
  const isZaloRoom = useCallback(
    async (roomId: string): Promise<boolean> => {
      try {
        const info = await getZaloRoomInfo(roomId)

        return !!info
      } catch (error) {
        console.error('Error checking Zalo room:', error)

        return false
      }
    },
    [getZaloRoomInfo]
  )

  // Close Zalo room
  const closeRoom = useCallback(
    async (roomId: string) => {
      await apiCall(`/api/internal-chat/zalo/rooms/${roomId}/close`, {
        method: 'POST'
      })
    },
    [apiCall]
  )

  // Reopen Zalo room
  const reopenRoom = useCallback(
    async (roomId: string) => {
      await apiCall(`/api/internal-chat/zalo/rooms/${roomId}/reopen`, {
        method: 'POST'
      })
    },
    [apiCall]
  )

  // Assign agent to Zalo room
  const assignAgent = useCallback(
    async (roomId: string, agentId: string) => {
      await apiCall(`/api/internal-chat/zalo/rooms/${roomId}/assign`, {
        method: 'POST',
        body: JSON.stringify({ agent_uuid: agentId })
      })
    },
    [apiCall]
  )

  // Send message to Zalo
  const sendZaloMessage = useCallback(
    async (roomId: string, content: string) => {
      return await apiCall('/api/internal-chat/zalo/send-message', {
        method: 'POST',
        body: JSON.stringify({
          room_uuid: roomId,
          content,
          message_type: 0 // TEXT
        })
      })
    },
    [apiCall]
  )

  // Get Zalo integration statistics
  const getStats = useCallback(async (): Promise<ZaloStats> => {
    try {
      const response = await apiCall('/api/internal-chat/zalo/stats')

      if (response.stats) {
        return response.stats
      } else {
        // Handle configuration errors
        throw new Error(response.message || response.error || 'Failed to get statistics')
      }
    } catch (error) {
      console.error('Error fetching Zalo stats:', error)
      throw error
    }
  }, [apiCall])

  return {
    // Room management
    isZaloRoom,
    getZaloRoomInfo,
    closeRoom,
    reopenRoom,
    assignAgent,

    // Message handling
    sendZaloMessage,

    // Statistics
    getStats,

    // State
    loading,
    error
  }
}

// Hook for Zalo room detection in message components
export const useZaloRoomDetection = (roomId: string | null) => {
  const [isZaloRoom, setIsZaloRoom] = useState(false)
  const [zaloInfo, setZaloInfo] = useState<ZaloRoomInfo | null>(null)
  const [loading, setLoading] = useState(false)

  const { getZaloRoomInfo } = useZaloIntegration()

  useEffect(() => {
    if (!roomId) {
      setIsZaloRoom(false)
      setZaloInfo(null)

      return
    }

    const checkZaloRoom = async () => {
      setLoading(true)

      try {
        const info = await getZaloRoomInfo(roomId)

        setIsZaloRoom(!!info)
        setZaloInfo(info)
      } catch (error) {
        console.error('Error checking Zalo room:', error)
        setIsZaloRoom(false)
        setZaloInfo(null)
      } finally {
        setLoading(false)
      }
    }

    checkZaloRoom()
  }, [roomId, getZaloRoomInfo])

  return {
    isZaloRoom,
    zaloInfo,
    loading
  }
}

// Hook for Zalo message formatting
export const useZaloMessageFormatting = () => {
  // Format message content for Zalo display
  const formatZaloContent = useCallback((content: string) => {
    if (!content) return ''

    // Convert basic HTML to display format
    return content
      .replace(/<strong>(.*?)<\/strong>/g, '**$1**')
      .replace(/<em>(.*?)<\/em>/g, '*$1*')
      .replace(/<code>(.*?)<\/code>/g, '`$1`')
  }, [])

  // Check if message is from Zalo
  const isZaloMessage = useCallback((message: any) => {
    return message.author_uuid === 'zalo-user' || message.platform === 'zalo' || !!message.zalo_message_id
  }, [])

  return {
    formatZaloContent,
    isZaloMessage
  }
}
