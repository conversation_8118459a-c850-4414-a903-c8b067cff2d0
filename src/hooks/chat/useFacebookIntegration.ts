// Facebook Integration Hook
// Provides Facebook-specific functionality for internal chat integration

import { useState, useCallback, useEffect } from 'react'

// Types
interface FacebookRoomInfo {
  internal_room_uuid: string
  facebook_contact_uuid: string
  room_status: string
  contact_info: {
    facebook_user_id: string
    first_name: string | null
    last_name: string | null
    profile_pic: string | null
    is_active: boolean
  }
}

interface FacebookStats {
  contacts_count: number
  active_rooms_count: number
  unassigned_rooms_count: number
  messages_sent: number
  messages_received: number
  failed_messages: number
  last_activity: string | null
}

interface UseFacebookIntegrationReturn {

  // Room management
  isFacebookRoom: (roomId: string) => Promise<boolean>
  getFacebookRoomInfo: (roomId: string) => Promise<FacebookRoomInfo | null>
  closeRoom: (roomId: string) => Promise<void>
  reopenRoom: (roomId: string) => Promise<void>
  assignAgent: (roomId: string, agentId: string) => Promise<void>

  // Message handling
  sendFacebookMessage: (roomId: string, content: string) => Promise<any>

  // Statistics
  getStats: () => Promise<FacebookStats>

  // State
  loading: boolean
  error: string | null
}

export const useFacebookIntegration = (): UseFacebookIntegrationReturn => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Generic API call helper
  const apiCall = useCallback(async (url: string, options: RequestInit = {}) => {
    setError(null)
    setLoading(true)

    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))

        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      return await response.json()
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'

      setError(errorMessage)

      throw err
    } finally {
      setLoading(false)
    }
  }, [])


  // Get Facebook room information
  const getFacebookRoomInfo = useCallback(
    async (roomId: string): Promise<FacebookRoomInfo | null> => {
      try {
        const response = await apiCall(`/api/internal-chat/facebook/room-info?room_uuid=${roomId}`)

        return response.room_info || null
      } catch (error) {
        // Not a Facebook room or error occurred
        return null
      }
    },
    [apiCall]
  )

  // Check if room is a Facebook room
  const isFacebookRoom = useCallback(
    async (roomId: string): Promise<boolean> => {
      try {
        const info = await getFacebookRoomInfo(roomId)

        return !!info
      } catch (error) {
        console.error('Error checking Facebook room:', error)

        return false
      }
    },
    [getFacebookRoomInfo]
  )

  // Close Facebook room
  const closeRoom = useCallback(
    async (roomId: string) => {
      await apiCall(`/api/internal-chat/facebook/rooms/${roomId}/close`, {
        method: 'POST'
      })
    },
    [apiCall]
  )

  // Reopen Facebook room
  const reopenRoom = useCallback(
    async (roomId: string) => {
      await apiCall(`/api/internal-chat/facebook/rooms/${roomId}/reopen`, {
        method: 'POST'
      })
    },
    [apiCall]
  )

  // Assign agent to Facebook room
  const assignAgent = useCallback(
    async (roomId: string, agentId: string) => {
      await apiCall(`/api/internal-chat/facebook/rooms/${roomId}/assign`, {
        method: 'POST',
        body: JSON.stringify({ agent_uuid: agentId })
      })
    },
    [apiCall]
  )

  // Send message to Facebook
  const sendFacebookMessage = useCallback(
    async (roomId: string, content: string) => {
      return await apiCall('/api/internal-chat/facebook/messages/send', {
        method: 'POST',
        body: JSON.stringify({
          room_uuid: roomId,
          content,
          message_type: 'text'
        })
      })
    },
    [apiCall]
  )

  // Get Facebook integration statistics
  const getStats = useCallback(async (): Promise<FacebookStats> => {
    try {
      const response = await apiCall('/api/internal-chat/facebook/stats')

      if (response.stats) {
        return response.stats
      } else {
        // Handle configuration errors
        throw new Error(response.message || response.error || 'Failed to get statistics')
      }
    } catch (error) {
      console.error('Error fetching Facebook stats:', error)
      throw error
    }
  }, [apiCall])

  return {
    // Room management
    isFacebookRoom,
    getFacebookRoomInfo,
    closeRoom,
    reopenRoom,
    assignAgent,

    // Message handling
    sendFacebookMessage,

    // Statistics
    getStats,

    // State
    loading,
    error
  }
}

// Hook for Facebook room detection in message components
export const useFacebookRoomDetection = (roomId: string | null) => {
  const [isFacebookRoom, setIsFacebookRoom] = useState(false)
  const [facebookInfo, setFacebookInfo] = useState<FacebookRoomInfo | null>(null)
  const [loading, setLoading] = useState(false)

  const { getFacebookRoomInfo } = useFacebookIntegration()

  const checkFacebookRoom = useCallback(async () => {
    if (!roomId) {
      setIsFacebookRoom(false)
      setFacebookInfo(null)

      return
    }

    setLoading(true)

    try {
      const info = await getFacebookRoomInfo(roomId)

      setIsFacebookRoom(!!info)
      setFacebookInfo(info)
    } catch (error) {
      console.error('Error checking Facebook room:', error)
      setIsFacebookRoom(false)
      setFacebookInfo(null)
    } finally {
      setLoading(false)
    }
  }, [roomId, getFacebookRoomInfo])

  // Check room type when roomId changes
  useEffect(() => {
    checkFacebookRoom()
  }, [checkFacebookRoom])

  return {
    isFacebookRoom,
    facebookInfo,
    loading,
    refetch: checkFacebookRoom
  }
}
