/* eslint-disable */
// ZALO OA Chat Socket Hook
// Extends existing chat socket to handle ZALO OA events with domain isolation

'use client'

import { useEffect, useRef, useCallback } from 'react'
import type { Socket } from 'socket.io-client'
import { io } from 'socket.io-client'

import type { ChatMessage, UserPresence, ChatNotification } from '@/types/apps/internal-chat/chatTypes'
import type { ZaloOaSocketEvents } from '@/types/apps/zalo-oa/zaloOaTypes'

interface UseZaloOaChatSocketOptions {
  // Existing chat events
  onMessage?: (message: ChatMessage) => void
  onPresenceUpdate?: (presence: UserPresence) => void
  onNotification?: (notification: ChatNotification) => void
  onConnectionChange?: (status: 'connecting' | 'connected' | 'disconnected' | 'error') => void
  onTyping?: (data: { roomId: string; userId: string; userName: string }) => void
  onStoppedTyping?: (data: { roomId: string; userId: string }) => void

  // ZALO OA specific events
  onZaloMessageReceived?: ZaloOaSocketEvents['zalo_message_received']
  onZaloContactStatusChanged?: ZaloOaSocketEvents['zalo_contact_status_changed']
  onZaloRoomAssigned?: ZaloOaSocketEvents['zalo_room_assigned']
  onZaloMessageDelivered?: ZaloOaSocketEvents['zalo_message_delivered']

  domain?: string
}

export const useZaloOaChatSocket = (options: UseZaloOaChatSocketOptions = {}) => {
  const socketRef = useRef<Socket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>()
  const reconnectAttempts = useRef(0)
  const maxReconnectAttempts = 5

  // Schedule reconnection with exponential backoff
  const scheduleReconnect = useCallback(
    (userId: string) => {
      if (reconnectAttempts.current >= maxReconnectAttempts) {
        console.error('Max reconnection attempts reached')
        options.onConnectionChange?.('error')
        return
      }

      const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000)
      reconnectAttempts.current++

      reconnectTimeoutRef.current = setTimeout(() => {
        console.log(`Attempting to reconnect (${reconnectAttempts.current}/${maxReconnectAttempts})`)
        connect(userId)
      }, delay)
    },
    [options]
  )

  // Connect to socket with ZALO OA support
  const connect = useCallback(
    (userId: string) => {
      if (socketRef.current?.connected) {
        return
      }

      try {
        // Get domain from options or fallback
        const domain = options.domain || 'default'

        // Create socket connection
        socketRef.current = io(process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3000', {
          transports: ['websocket', 'polling'],
          timeout: 10000,
          forceNew: true,
          query: {
            userId,
            domain,
            type: 'zalo_oa_chat' // Distinguish from regular internal chat
          }
        })

        const socket = socketRef.current

        // Connection events
        socket.on('connect', () => {
          console.log('ZALO OA Chat socket connected')
          options.onConnectionChange?.('connected')
          reconnectAttempts.current = 0

          // Join domain-specific rooms
          socket.emit('join_zalo_chat', {
            user_uuid: userId,
            domain_uuid: domain
          })
        })

        socket.on('disconnect', reason => {
          console.log('ZALO OA Chat socket disconnected:', reason)
          options.onConnectionChange?.('disconnected')

          // Auto-reconnect unless manually disconnected
          if (reason !== 'io client disconnect') {
            scheduleReconnect(userId)
          }
        })

        socket.on('connect_error', error => {
          console.error('ZALO OA Chat socket connection error:', error)
          options.onConnectionChange?.('error')
          scheduleReconnect(userId)
        })

        // Standard chat events
        socket.on('chat_connected', data => {
          console.log('ZALO OA Chat connected:', data)
        })

        socket.on('new_message', (message: ChatMessage) => {
          options.onMessage?.(message)
        })

        socket.on('message_updated', (message: ChatMessage) => {
          options.onMessage?.(message)
        })

        socket.on('user_presence_changed', (presence: UserPresence) => {
          options.onPresenceUpdate?.(presence)
        })

        socket.on('notification', (notification: ChatNotification) => {
          options.onNotification?.(notification)
        })

        socket.on('user_typing', data => {
          options.onTyping?.(data)
        })

        socket.on('user_stopped_typing', data => {
          options.onStoppedTyping?.(data)
        })

        // ZALO OA specific events
        socket.on('zalo_message_received', data => {
          console.log('ZALO message received:', data)
          options.onZaloMessageReceived?.(data)
        })

        socket.on('zalo_contact_status_changed', data => {
          console.log('ZALO contact status changed:', data)
          options.onZaloContactStatusChanged?.(data)
        })

        socket.on('zalo_room_assigned', data => {
          console.log('ZALO room assigned:', data)
          options.onZaloRoomAssigned?.(data)
        })

        socket.on('zalo_message_delivered', data => {
          console.log('ZALO message delivered:', data)
          options.onZaloMessageDelivered?.(data)
        })

        socket.on('error', error => {
          console.error('ZALO OA Chat socket error:', error)
        })
      } catch (error) {
        console.error('Failed to create ZALO OA socket connection:', error)
        options.onConnectionChange?.('error')
      }
    },
    [options, scheduleReconnect]
  )

  // Disconnect socket
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
    }

    if (socketRef.current) {
      socketRef.current.disconnect()
      socketRef.current = null
    }

    options.onConnectionChange?.('disconnected')
  }, [options])

  // Send ZALO message
  const sendZaloMessage = useCallback(
    (data: { room_uuid: string; zalo_user_id: string; content: string; message_type?: string }) => {
      if (socketRef.current?.connected) {
        socketRef.current.emit('send_zalo_message', data)
      }
    },
    []
  )

  // Join ZALO chat room
  const joinZaloRoom = useCallback((room_uuid: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('join_zalo_room', { room_uuid })
    }
  }, [])

  // Leave ZALO chat room
  const leaveZaloRoom = useCallback((room_uuid: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('leave_zalo_room', { room_uuid })
    }
  }, [])

  // Update ZALO typing status
  const updateZaloTyping = useCallback((data: { room_uuid: string; is_typing: boolean }) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('zalo_typing', data)
    }
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect()
    }
  }, [disconnect])

  return {
    connect,
    disconnect,
    sendZaloMessage,
    joinZaloRoom,
    leaveZaloRoom,
    updateZaloTyping,
    isSocketConnected: socketRef.current?.connected || false,
    socket: socketRef.current
  }
}
