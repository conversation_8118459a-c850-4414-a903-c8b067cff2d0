// Chat Notifications Hook
// Manages browser notifications for chat messages

'use client'

import { useState, useEffect, useCallback } from 'react'

interface NotificationOptions {
  title: string
  body: string
  icon?: string
  onClick?: () => void
}

export const useChatNotifications = () => {
  const [permission, setPermission] = useState<NotificationPermission>('default')
  const [isSupported, setIsSupported] = useState(false)

  // Check if notifications are supported
  useEffect(() => {
    setIsSupported('Notification' in window)

    if ('Notification' in window) {
      setPermission(Notification.permission)
    }
  }, [])

  // Request notification permission
  const requestNotificationPermission = useCallback(async () => {
    if (!isSupported) {
      console.warn('Notifications are not supported in this browser')

      return false
    }

    if (permission === 'granted') {
      return true
    }

    try {
      const result = await Notification.requestPermission()

      setPermission(result)

      return result === 'granted'
    } catch (error) {
      console.error('Error requesting notification permission:', error)

      return false
    }
  }, [isSupported, permission])

  // Show browser notification
  const showBrowserNotification = useCallback(
    (options: NotificationOptions) => {
      if (!isSupported || permission !== 'granted') {
        return null
      }

      try {
        const notification = new Notification(options.title, {
          body: options.body,
          icon: options.icon || '/icons/chat-notification.png',
          tag: 'chat-message', // Replace previous notifications
          requireInteraction: false,
          silent: false
        })

        // Handle click
        if (options.onClick) {
          notification.onclick = () => {
            window.focus()
            options.onClick?.()
            notification.close()
          }
        }

        // Auto close after 5 seconds
        setTimeout(() => {
          notification.close()
        }, 5000)

        return notification
      } catch (error) {
        console.error('Error showing notification:', error)

        return null
      }
    },
    [isSupported, permission]
  )

  // Check if user is currently active (for notification suppression)
  const isUserActive = useCallback(() => {
    return document.hasFocus() && document.visibilityState === 'visible'
  }, [])

  // Show notification only if user is not active
  const showNotificationIfInactive = useCallback(
    (options: NotificationOptions) => {
      if (!isUserActive()) {
        return showBrowserNotification(options)
      }

      return null
    },
    [isUserActive, showBrowserNotification]
  )

  return {
    isSupported,
    permission,
    requestNotificationPermission,
    showBrowserNotification,
    showNotificationIfInactive,
    isUserActive
  }
}
