'use client'

import { useState, useEffect, useCallback } from 'react'

import { useSession } from 'next-auth/react'

import { ZaloTokenService } from '@/services/zalo/zaloTokenService'

/**
 * Hook for managing Zalo access tokens with automatic refresh
 * Replaces the old useAccessToken hook with centralized token management
 */
export const useZaloToken = () => {
  const { data: session } = useSession()
  const [accessToken, setAccessToken] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null)

  const domainUuid = session?.user?.domain_uuid

  /**
   * Get valid access token, refreshing if necessary
   */
  const getValidToken = useCallback(async (): Promise<string | null> => {
    if (!domainUuid) {
      setError('No domain UUID available')

      return null
    }

    setIsLoading(true)
    setError(null)

    try {
      const token = await ZaloTokenService.getValidAccessToken(domainUuid)

      setAccessToken(token)
      setLastRefresh(new Date())

      return token
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get access token'

      setError(errorMessage)
      console.error('Error getting Zalo access token:', err)

      return null
    } finally {
      setIsLoading(false)
    }
  }, [domainUuid])

  /**
   * Make API call with automatic token refresh
   */
  const makeApiCall = useCallback(async <T>(
    apiCall: (accessToken: string) => Promise<T>
  ): Promise<T> => {
    if (!domainUuid) {
      throw new Error('No domain UUID available')
    }

    setIsLoading(true)
    setError(null)

    try {
      const result = await ZaloTokenService.makeApiCallWithTokenRefresh(
        domainUuid,
        apiCall
      )
      
      // Update local token state after successful API call
      const currentToken = await ZaloTokenService.getValidAccessToken(domainUuid)

      setAccessToken(currentToken)
      setLastRefresh(new Date())

      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'API call failed'

      setError(errorMessage)
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [domainUuid])

  /**
   * Force refresh the access token
   */
  const forceRefresh = useCallback(async (): Promise<string | null> => {
    if (!domainUuid) {
      setError('No domain UUID available')

      return null
    }

    setIsLoading(true)
    setError(null)

    try {
      // Force token expiry to trigger refresh
      await fetch('/api/internal-chat/zalo/oa-config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          refresh_token_now: true
        })
      })

      // Get the refreshed token
      const token = await ZaloTokenService.getValidAccessToken(domainUuid)

      setAccessToken(token)
      setLastRefresh(new Date())

      return token
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh token'

      setError(errorMessage)
      console.error('Error forcing token refresh:', err)

      return null
    } finally {
      setIsLoading(false)
    }
  }, [domainUuid])

  /**
   * Check if token is available and valid
   */
  const isTokenValid = useCallback((): boolean => {
    return !!accessToken && !error
  }, [accessToken, error])

  // Initialize token on mount or when domain changes
  useEffect(() => {
    if (domainUuid) {
      getValidToken()
    } else {
      setAccessToken(null)
      setError(null)
    }
  }, [domainUuid, getValidToken])

  return {
    accessToken,
    isLoading,
    error,
    lastRefresh,
    getValidToken,
    makeApiCall,
    forceRefresh,
    isTokenValid,
    domainUuid
  }
}

export default useZaloToken
