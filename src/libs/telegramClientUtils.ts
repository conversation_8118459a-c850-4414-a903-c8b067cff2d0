import { TelegramClient } from 'messaging-api-telegram'

import { prisma } from '@/libs/db/prisma'

/**
 * Get Telegram client with dynamic token from database configuration
 */
export async function getTelegramClient(domain_uuid: string): Promise<TelegramClient | null> {
  try {
    // Get bot configuration from database
    const botConfig = await prisma.v_telegram_bot_config.findFirst({
      where: {
        domain_uuid,
        is_active: true
      },
      select: {
        bot_token: true,
        bot_username: true
      }
    })

    if (!botConfig || !botConfig.bot_token) {
      console.error(`No active Telegram bot configuration found for domain: ${domain_uuid}`)

      return null
    }

    // Create client with dynamic token
    const client = new TelegramClient({
      accessToken: botConfig.bot_token
    })

    return client
  } catch (error) {
    console.error('Error getting Telegram client:', error)

    return null
  }
}

/**
 * Validate Telegram bot token format
 */
export function isValidTelegramToken(token: string): boolean {
  // Telegram bot tokens follow the format: <bot_id>:<auth_token>
  // Example: 123456789:ABCdefGHIjklMNOpqrsTUVwxyz
  const tokenRegex = /^\d+:[A-Za-z0-9_-]{35}$/

  return tokenRegex.test(token)
}

/**
 * Get bot information from token
 */
export async function getBotInfo(token: string): Promise<any> {
  try {
    const client = new TelegramClient({
      accessToken: token
    })

    const botInfo = await client.getMe()

    return botInfo
  } catch (error) {
    console.error('Error getting bot info:', error)
    throw error
  }
}
