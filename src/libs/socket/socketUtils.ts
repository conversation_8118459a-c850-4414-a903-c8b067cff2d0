// Socket Utilities for Real-time Communication
// Provides utilities for emitting events to rooms and users

import { io } from 'socket.io-client'

// Socket client instance (reuse existing connection if available)
let socketClient: any = null

// Utility function to convert BigInt values to strings for JSON serialization
function serializeBigInt(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj
  }

  if (typeof obj === 'bigint') {
    return obj.toString()
  }

  if (Array.isArray(obj)) {
    return obj.map(serializeBigInt)
  }

  if (typeof obj === 'object') {
    const serialized: any = {}

    for (const [key, value] of Object.entries(obj)) {
      serialized[key] = serializeBigInt(value)
    }

    return serialized
  }

  return obj
}

// Initialize socket connection
export const initializeSocket = (serverUrl: string = 'http://localhost:3000') => {
  if (!socketClient) {
    socketClient = io(serverUrl, {
      transports: ['websocket', 'polling']
    })

    socketClient.on('connect', () => {
      console.log('Socket connected for real-time events')
    })

    socketClient.on('disconnect', () => {
      console.log('Socket disconnected')
    })

    socketClient.on('connect_error', (error: any) => {
      console.error('Socket connection error:', error)
    })
  }

  return socketClient
}

// Emit event to specific room
export const emitToRoom = async (roomId: string, event: string, data: any) => {
  try {
    const socket = initializeSocket()

    // Serialize BigInt values to prevent JSON serialization errors
    const serializedData = serializeBigInt(data)

    if (socket.connected) {
      socket.emit('room_event', {
        room_id: roomId,
        event_type: event,
        data: serializedData
      })
    } else {
      // Queue event if not connected
      socket.on('connect', () => {
        socket.emit('room_event', {
          room_id: roomId,
          event_type: event,
          data: serializedData
        })
      })
    }
  } catch (error) {
    console.error('Failed to emit room event:', error)
    throw error
  }
}

// Emit event to specific user
export const emitToUser = async (userId: string, event: string, data: any) => {
  try {
    const socket = initializeSocket()

    // Serialize BigInt values to prevent JSON serialization errors
    const serializedData = serializeBigInt(data)

    if (socket.connected) {
      socket.emit('user_event', {
        user_id: userId,
        event_type: event,
        data: serializedData
      })
    } else {
      // Queue event if not connected
      socket.on('connect', () => {
        socket.emit('user_event', {
          user_id: userId,
          event_type: event,
          data: serializedData
        })
      })
    }
  } catch (error) {
    console.error('Failed to emit user event:', error)
    throw error
  }
}

// Emit event to domain
export const emitToDomain = async (domainId: string, event: string, data: any) => {
  try {
    const socket = initializeSocket()

    // Serialize BigInt values to prevent JSON serialization errors
    const serializedData = serializeBigInt(data)

    if (socket.connected) {
      socket.emit('domain_event', {
        domain_id: domainId,
        event_type: event,
        data: serializedData
      })
    } else {
      // Queue event if not connected
      socket.on('connect', () => {
        socket.emit('domain_event', {
          domain_id: domainId,
          event_type: event,
          data: serializedData
        })
      })
    }
  } catch (error) {
    console.error('Failed to emit domain event:', error)
    throw error
  }
}

// Telegram-specific socket events
export const emitTelegramEvent = async (domainId: string, eventType: string, data: any) => {
  try {
    const socket = initializeSocket()

    // Serialize BigInt values to prevent JSON serialization errors
    const serializedData = serializeBigInt(data)

    const telegramEvent = {
      platform: 'telegram',
      domain_id: domainId,
      event_type: eventType,
      timestamp: new Date().toISOString(),
      data: serializedData
    }

    if (socket.connected) {
      socket.emit('telegram_event', telegramEvent)
    } else {
      // Queue event if not connected
      socket.on('connect', () => {
        socket.emit('telegram_event', telegramEvent)
      })
    }
  } catch (error) {
    console.error('Failed to emit Telegram event:', error)
    throw error
  }
}

// Emit new Telegram message event
export const emitTelegramMessage = async (domainId: string, roomId: string, messageData: any) => {
  await Promise.all([
    emitToRoom(roomId, 'new_message', {
      ...messageData,
      platform: 'telegram'
    }),
    emitTelegramEvent(domainId, 'new_message', {
      room_id: roomId,
      message: messageData
    })
  ])
}

// Emit Telegram contact update event
export const emitTelegramContactUpdate = async (domainId: string, contactData: any) => {
  await emitTelegramEvent(domainId, 'contact_updated', {
    contact: contactData
  })
}

// Emit Telegram room status change
export const emitTelegramRoomStatusChange = async (
  domainId: string,
  roomId: string,
  status: string,
  metadata?: any
) => {
  await Promise.all([
    emitToRoom(roomId, 'room_status_changed', {
      room_id: roomId,
      status,
      platform: 'telegram',
      metadata
    }),
    emitTelegramEvent(domainId, 'room_status_changed', {
      room_id: roomId,
      status,
      metadata
    })
  ])
}

// Emit Telegram delivery status update
export const emitTelegramDeliveryStatus = async (
  domainId: string,
  roomId: string,
  messageId: string,
  deliveryStatus: string,
  error?: string
) => {
  await emitToRoom(roomId, 'message_delivery_status', {
    message_id: messageId,
    delivery_status: deliveryStatus,
    platform: 'telegram',
    error
  })
}

// Close socket connection
export const closeSocket = () => {
  if (socketClient) {
    socketClient.disconnect()
    socketClient = null
  }
}

// Get socket connection status
export const getSocketStatus = () => {
  return {
    connected: socketClient?.connected || false,
    id: socketClient?.id || null
  }
}

const socketUtils = {
  initializeSocket,
  emitToRoom,
  emitToUser,
  emitToDomain,
  emitTelegramEvent,
  emitTelegramMessage,
  emitTelegramContactUpdate,
  emitTelegramRoomStatusChange,
  emitTelegramDeliveryStatus,
  closeSocket,
  getSocketStatus
}

export default socketUtils
