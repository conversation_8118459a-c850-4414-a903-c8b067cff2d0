// Unified Notification Service
// Centralizes notification handling across all social platforms
// Fixes Issue 2.1: Missing notifications for unassigned conversations

import { prisma } from '@/libs/db/prisma'
import { NotificationType } from '@/types/apps/internal-chat/chatTypes'

export interface UnifiedNotificationData {

  // Core notification data
  user_uuid: string
  room_uuid: string
  message_id: string
  notification_type: NotificationType
  
  // Message context
  message_content: string
  author_name: string
  author_uuid?: string
  
  // Room context
  room_name?: string
  platform?: 'internal' | 'facebook' | 'telegram' | 'zalo'
  
  // Assignment context
  is_assigned?: boolean
  assigned_agent_uuid?: string
  assigned_agent_name?: string
  
  // Metadata
  metadata?: Record<string, any>
  expires_hours?: number
}

export interface NotificationBroadcast {
  notification_uuid: string
  user_uuid: string
  room_uuid: string
  message_id: string
  notification_type: NotificationType
  is_read: boolean
  is_sent: boolean
  insert_date: string
  read_date: string | null
  expires_at: string
  
  // Additional data for frontend
  message_content: string
  author_name: string
  room_name: string
  platform?: string
  metadata?: Record<string, any>
}

export class UnifiedNotificationService {
  private static instance: UnifiedNotificationService

  static getInstance(): UnifiedNotificationService {
    if (!UnifiedNotificationService.instance) {
      UnifiedNotificationService.instance = new UnifiedNotificationService()
    }

    return UnifiedNotificationService.instance
  }

  /**
   * Create and broadcast notifications for multiple users
   */
  async createAndBroadcastNotifications(
    domainUuid: string,
    notifications: UnifiedNotificationData[]
  ): Promise<void> {
    if (notifications.length === 0) return

    try {
      // Group notifications by room for efficient processing
      const notificationsByRoom = this.groupNotificationsByRoom(notifications)

      // Process each room's notifications
      for (const [roomUuid, roomNotifications] of notificationsByRoom.entries()) {
        await this.processRoomNotifications(domainUuid, roomUuid, roomNotifications)
      }

      console.log(`Successfully processed ${notifications.length} unified notifications`)
    } catch (error) {
      console.error('Error in unified notification service:', error)

      throw error
    }
  }

  /**
   * Create notification for unassigned conversation
   */
  async createUnassignedConversationNotification(
    domainUuid: string,
    roomUuid: string,
    messageData: {
      message_id: string
      content: string
      author_name: string
      platform: string
    }
  ): Promise<void> {
    try {
      // Get all agents who should receive unassigned notifications
      const eligibleAgents = await this.getEligibleAgentsForUnassigned(domainUuid, messageData.platform)

      if (eligibleAgents.length === 0) {
        console.log('No eligible agents found for unassigned conversation notification')

        return
      }

      // Create notifications for all eligible agents
      const notifications: UnifiedNotificationData[] = eligibleAgents.map(agent => ({
        user_uuid: agent.user_uuid,
        room_uuid: roomUuid,
        message_id: messageData.message_id,
        notification_type: NotificationType.MESSAGE,
        message_content: messageData.content,
        author_name: messageData.author_name,
        room_name: `Unassigned ${messageData.platform} Chat`,
        platform: messageData.platform as any,
        is_assigned: false,
        expires_hours: 72 // 3 days for unassigned conversations
      }))

      await this.createAndBroadcastNotifications(domainUuid, notifications)

      console.log(`Created unassigned conversation notifications for ${eligibleAgents.length} agents`)
    } catch (error) {
      console.error('Error creating unassigned conversation notification:', error)
    }
  }

  /**
   * Create notification for agent assignment
   */
  async createAgentAssignmentNotification(
    domainUuid: string,
    roomUuid: string,
    assignedAgentUuid: string,
    assignmentData: {
      assigner_name: string
      contact_name: string
      platform: string
    }
  ): Promise<void> {
    try {
      const notification: UnifiedNotificationData = {
        user_uuid: assignedAgentUuid,
        room_uuid: roomUuid,
        message_id: `assignment_${Date.now()}`,
        notification_type: NotificationType.ROOM_INVITE,
        message_content: `You have been assigned to a ${assignmentData.platform} conversation with ${assignmentData.contact_name}`,
        author_name: assignmentData.assigner_name,
        room_name: `${assignmentData.platform} - ${assignmentData.contact_name}`,
        platform: assignmentData.platform as any,
        is_assigned: true,
        assigned_agent_uuid: assignedAgentUuid,
        expires_hours: 168 // 7 days for assignments
      }

      await this.createAndBroadcastNotifications(domainUuid, [notification])

      console.log(`Created agent assignment notification for ${assignedAgentUuid}`)
    } catch (error) {
      console.error('Error creating agent assignment notification:', error)
    }
  }

  /**
   * Update unread counts for room participants
   */
  async updateUnreadCounts(
    roomUuid: string,
    excludeUserUuid?: string
  ): Promise<Record<string, number>> {
    try {
      // Get all room participants except the sender
      const participants = await prisma.v_chat_room_participants.findMany({
        where: {
          room_uuid: roomUuid,
          deleted_at: null,
          ...(excludeUserUuid && { user_uuid: { not: excludeUserUuid } })
        },
        select: {
          user_uuid: true
        }
      })

      const unreadCounts: Record<string, number> = {}

      // Update unread count for each participant
      for (const participant of participants) {
        const result = await prisma.v_chat_unread_counts.upsert({
          where: {
            user_uuid_room_uuid: {
              user_uuid: participant.user_uuid,
              room_uuid: roomUuid
            }
          },
          update: {
            unread_count: {
              increment: 1
            },
            last_updated: new Date()
          },
          create: {
            user_uuid: participant.user_uuid,
            room_uuid: roomUuid,
            unread_count: 1,
            last_updated: new Date()
          },
          select: {
            unread_count: true
          }
        })

        unreadCounts[participant.user_uuid] = result.unread_count
      }

      return unreadCounts
    } catch (error) {
      console.error('Error updating unread counts:', error)

      return {}
    }
  }

  /**
   * Broadcast notification via Socket.IO
   */
  async broadcastNotification(
    domainUuid: string,
    notification: NotificationBroadcast
  ): Promise<void> {
    try {
      const socketBroadcast = (global as any).socketBroadcast

      if (!socketBroadcast?.broadcastMessage) {
        console.warn('Socket broadcast not available for notification')
        
return
      }

      // Get user's socket connections
      const userSocketIds = socketBroadcast.getUserSocketIds?.(notification.user_uuid) || []

      if (userSocketIds.length === 0) {
        console.log(`No socket connections found for user ${notification.user_uuid}`)
        
return
      }

      // Send notification to all user's connections
      userSocketIds.forEach((socketId: string) => {
        const socketConnection = socketBroadcast.connectedUsers.get(socketId)

        if (socketConnection) {
          console.log(`Sending unified notification to socket ${socketId} for user ${notification.user_uuid}`)
          socketConnection.socket.emit('notification', notification)

          // Also emit platform-specific event if needed
          if (notification.platform) {
            socketConnection.socket.emit(`${notification.platform}_notification`, notification)
          }
        }
      })

      // Also broadcast to room if it's a room-level notification
      if (notification.notification_type === NotificationType.ROOM_INVITE) {
        socketBroadcast.broadcastMessage(notification.room_uuid, {
          event: 'room_notification',
          data: notification
        }, domainUuid)
      }

    } catch (error) {
      console.error('Error broadcasting unified notification:', error)
    }
  }

  /**
   * Group notifications by room for efficient processing
   */
  private groupNotificationsByRoom(
    notifications: UnifiedNotificationData[]
  ): Map<string, UnifiedNotificationData[]> {
    const grouped = new Map<string, UnifiedNotificationData[]>()

    for (const notification of notifications) {
      const existing = grouped.get(notification.room_uuid) || []

      existing.push(notification)
      grouped.set(notification.room_uuid, existing)
    }

    return grouped
  }

  /**
   * Process notifications for a specific room
   */
  private async processRoomNotifications(
    domainUuid: string,
    roomUuid: string,
    notifications: UnifiedNotificationData[]
  ): Promise<void> {
    try {
      // Create database notifications
      const dbNotifications = notifications.map(notification => ({
        user_uuid: notification.user_uuid,
        room_uuid: notification.room_uuid,
        message_id: BigInt(notification.message_id),
        notification_type: notification.notification_type,
        is_read: false,
        is_sent: false,
        insert_date: new Date(),
        expires_at: new Date(Date.now() + (notification.expires_hours || 24) * 60 * 60 * 1000)
      }))

      // Batch create notifications
      await prisma.v_chat_notifications.createMany({
        data: dbNotifications,
        skipDuplicates: true
      })

      // Create broadcast notifications and send them
      for (const notification of notifications) {
        const broadcastNotification: NotificationBroadcast = {
          notification_uuid: `unified_${Date.now()}_${notification.user_uuid}`,
          user_uuid: notification.user_uuid,
          room_uuid: notification.room_uuid,
          message_id: notification.message_id,
          notification_type: notification.notification_type,
          is_read: false,
          is_sent: true,
          insert_date: new Date().toISOString(),
          read_date: null,
          expires_at: new Date(Date.now() + (notification.expires_hours || 24) * 60 * 60 * 1000).toISOString(),
          message_content: notification.message_content,
          author_name: notification.author_name,
          room_name: notification.room_name || 'Chat',
          platform: notification.platform,
          metadata: notification.metadata
        }

        await this.broadcastNotification(domainUuid, broadcastNotification)
      }

    } catch (error) {
      console.error('Error processing room notifications:', error)
      throw error
    }
  }

  /**
   * Get eligible agents for unassigned conversation notifications
   */
  private async getEligibleAgentsForUnassigned(
    domainUuid: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _platform: string
  ): Promise<Array<{ user_uuid: string; user_name: string }>> {
    try {

      // Get agents who are available for the specific platform
      const agents = await prisma.v_users.findMany({
        where: {
          domain_uuid: domainUuid,
          user_enabled: 'true',

          // Add platform-specific filtering if needed
          // This could be extended to check agent availability, working hours, etc.
        },
        select: {
          user_uuid: true,
          username: true
        }
      })

      return agents.map(agent => ({
        user_uuid: agent.user_uuid,
        user_name: agent.username || 'Agent'
      }))
    } catch (error) {
      console.error('Error getting eligible agents:', error)

      return []
    }
  }
}

// Export singleton instance
export const unifiedNotificationService = UnifiedNotificationService.getInstance()
