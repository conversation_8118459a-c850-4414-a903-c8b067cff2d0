// Unified Notification Service Tests
// Tests for Issue 2.1 fix: Missing notifications for unassigned conversations

import { UnifiedNotificationService } from '../unifiedNotificationService'
import { NotificationType } from '@/types/apps/internal-chat/chatTypes'

// Mock Prisma
const mockPrisma = {
  v_chat_notifications: {
    createMany: jest.fn()
  },
  v_chat_room_participants: {
    findMany: jest.fn()
  },
  v_chat_unread_counts: {
    upsert: jest.fn()
  },
  v_users: {
    findMany: jest.fn()
  }
}

jest.mock('@/libs/db/prisma', () => ({
  prisma: mockPrisma
}))

// Mock global socket broadcast
const mockSocketBroadcast = {
  broadcastMessage: jest.fn(),
  getUserSocketIds: jest.fn(),
  connectedUsers: new Map()
}

beforeEach(() => {
  ;(global as any).socketBroadcast = mockSocketBroadcast
})

describe('UnifiedNotificationService', () => {
  let service: UnifiedNotificationService

  beforeEach(() => {
    service = UnifiedNotificationService.getInstance()
    jest.clearAllMocks()
  })

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = UnifiedNotificationService.getInstance()
      const instance2 = UnifiedNotificationService.getInstance()
      
      expect(instance1).toBe(instance2)
    })
  })

  describe('createUnassignedConversationNotification', () => {
    it('should create notifications for all eligible agents', async () => {
      const domainUuid = 'test-domain'
      const roomUuid = 'test-room'

      const messageData = {
        message_id: 'msg-123',
        content: 'Hello from Facebook',
        author_name: 'Facebook User',
        platform: 'facebook'
      }

      // Mock eligible agents
      mockPrisma.v_users.findMany.mockResolvedValueOnce([
        { user_uuid: 'agent-1', username: 'Agent One' },
        { user_uuid: 'agent-2', username: 'Agent Two' }
      ])

      // Mock notification creation
      mockPrisma.v_chat_notifications.createMany.mockResolvedValueOnce({ count: 2 })

      // Mock socket connections
      mockSocketBroadcast.getUserSocketIds
        .mockReturnValueOnce(['socket-1'])
        .mockReturnValueOnce(['socket-2'])

      const mockSocket1 = { emit: jest.fn() }
      const mockSocket2 = { emit: jest.fn() }
      
      mockSocketBroadcast.connectedUsers
        .set('socket-1', { socket: mockSocket1 })
        .set('socket-2', { socket: mockSocket2 })

      await service.createUnassignedConversationNotification(
        domainUuid,
        roomUuid,
        messageData
      )

      // Verify agents were fetched
      expect(mockPrisma.v_users.findMany).toHaveBeenCalledWith({
        where: {
          domain_uuid: domainUuid,
          user_enabled: 'true'
        },
        select: {
          user_uuid: true,
          username: true
        }
      })

      // Verify notifications were created
      expect(mockPrisma.v_chat_notifications.createMany).toHaveBeenCalledWith({
        data: expect.arrayContaining([
          expect.objectContaining({
            user_uuid: 'agent-1',
            room_uuid: roomUuid,
            message_id: messageData.message_id,
            notification_type: NotificationType.MESSAGE
          }),
          expect.objectContaining({
            user_uuid: 'agent-2',
            room_uuid: roomUuid,
            message_id: messageData.message_id,
            notification_type: NotificationType.MESSAGE
          })
        ]),
        skipDuplicates: true
      })

      // Verify socket emissions
      expect(mockSocket1.emit).toHaveBeenCalledWith('notification', expect.objectContaining({
        user_uuid: 'agent-1',
        message_content: messageData.content,
        platform: 'facebook'
      }))

      expect(mockSocket2.emit).toHaveBeenCalledWith('notification', expect.objectContaining({
        user_uuid: 'agent-2',
        message_content: messageData.content,
        platform: 'facebook'
      }))
    })

    it('should handle no eligible agents gracefully', async () => {
      const domainUuid = 'test-domain'
      const roomUuid = 'test-room'

      const messageData = {
        message_id: 'msg-123',
        content: 'Hello',
        author_name: 'User',
        platform: 'telegram'
      }

      // Mock no eligible agents
      mockPrisma.v_users.findMany.mockResolvedValueOnce([])

      await service.createUnassignedConversationNotification(
        domainUuid,
        roomUuid,
        messageData
      )

      // Should not create any notifications
      expect(mockPrisma.v_chat_notifications.createMany).not.toHaveBeenCalled()
    })
  })

  describe('createAgentAssignmentNotification', () => {
    it('should create assignment notification for specific agent', async () => {
      const domainUuid = 'test-domain'
      const roomUuid = 'test-room'
      const assignedAgentUuid = 'agent-123'

      const assignmentData = {
        assigner_name: 'Admin User',
        contact_name: 'John Doe',
        platform: 'facebook'
      }

      // Mock notification creation
      mockPrisma.v_chat_notifications.createMany.mockResolvedValueOnce({ count: 1 })

      // Mock socket connection
      mockSocketBroadcast.getUserSocketIds.mockReturnValueOnce(['socket-123'])
      const mockSocket = { emit: jest.fn() }

      mockSocketBroadcast.connectedUsers.set('socket-123', { socket: mockSocket })

      await service.createAgentAssignmentNotification(
        domainUuid,
        roomUuid,
        assignedAgentUuid,
        assignmentData
      )

      // Verify notification was created
      expect(mockPrisma.v_chat_notifications.createMany).toHaveBeenCalledWith({
        data: expect.arrayContaining([
          expect.objectContaining({
            user_uuid: assignedAgentUuid,
            room_uuid: roomUuid,
            notification_type: NotificationType.ROOM_INVITE,
            message_id: expect.stringContaining('assignment_')
          })
        ]),
        skipDuplicates: true
      })

      // Verify socket emission
      expect(mockSocket.emit).toHaveBeenCalledWith('notification', expect.objectContaining({
        user_uuid: assignedAgentUuid,
        notification_type: NotificationType.ROOM_INVITE,
        message_content: expect.stringContaining('You have been assigned'),
        platform: 'facebook'
      }))

      // Verify room-level broadcast for assignment
      expect(mockSocketBroadcast.broadcastMessage).toHaveBeenCalledWith(
        roomUuid,
        expect.objectContaining({
          event: 'room_notification'
        }),
        domainUuid
      )
    })
  })

  describe('updateUnreadCounts', () => {
    it('should update unread counts for all room participants', async () => {
      const roomUuid = 'test-room'
      const excludeUserUuid = 'sender-123'

      // Mock room participants
      mockPrisma.v_chat_room_participants.findMany.mockResolvedValueOnce([
        { user_uuid: 'user-1' },
        { user_uuid: 'user-2' }
      ])

      // Mock unread count updates
      mockPrisma.v_chat_unread_counts.upsert
        .mockResolvedValueOnce({ unread_count: 3 })
        .mockResolvedValueOnce({ unread_count: 1 })

      const result = await service.updateUnreadCounts(roomUuid, excludeUserUuid)

      // Verify participants were fetched correctly
      expect(mockPrisma.v_chat_room_participants.findMany).toHaveBeenCalledWith({
        where: {
          room_uuid: roomUuid,
          is_active: true,
          user_uuid: { not: excludeUserUuid }
        },
        select: {
          user_uuid: true
        }
      })

      // Verify unread counts were updated
      expect(mockPrisma.v_chat_unread_counts.upsert).toHaveBeenCalledTimes(2)
      
      expect(result).toEqual({
        'user-1': 3,
        'user-2': 1
      })
    })

    it('should handle database errors gracefully', async () => {
      const roomUuid = 'test-room'

      // Mock database error
      mockPrisma.v_chat_room_participants.findMany.mockRejectedValueOnce(
        new Error('Database connection failed')
      )

      const result = await service.updateUnreadCounts(roomUuid)

      expect(result).toEqual({})
    })
  })

  describe('broadcastNotification', () => {
    it('should broadcast notification to all user socket connections', async () => {
      const domainUuid = 'test-domain'

      const notification = {
        notification_uuid: 'notif-123',
        user_uuid: 'user-123',
        room_uuid: 'room-123',
        message_id: 'msg-123',
        notification_type: NotificationType.MESSAGE,
        is_read: false,
        is_sent: true,
        insert_date: new Date().toISOString(),
        read_date: null,
        expires_at: new Date().toISOString(),
        message_content: 'Test message',
        author_name: 'Test User',
        room_name: 'Test Room',
        platform: 'facebook'
      }

      // Mock multiple socket connections for user
      mockSocketBroadcast.getUserSocketIds.mockReturnValueOnce(['socket-1', 'socket-2'])
      
      const mockSocket1 = { emit: jest.fn() }
      const mockSocket2 = { emit: jest.fn() }
      
      mockSocketBroadcast.connectedUsers
        .set('socket-1', { socket: mockSocket1 })
        .set('socket-2', { socket: mockSocket2 })

      await service.broadcastNotification(domainUuid, notification)

      // Verify notification was sent to all connections
      expect(mockSocket1.emit).toHaveBeenCalledWith('notification', notification)
      expect(mockSocket2.emit).toHaveBeenCalledWith('notification', notification)

      // Verify platform-specific event was also emitted
      expect(mockSocket1.emit).toHaveBeenCalledWith('facebook_notification', notification)
      expect(mockSocket2.emit).toHaveBeenCalledWith('facebook_notification', notification)
    })

    it('should handle missing socket broadcast gracefully', async () => {
      const domainUuid = 'test-domain'

      const notification = {
        notification_uuid: 'notif-123',
        user_uuid: 'user-123',
        room_uuid: 'room-123',
        message_id: 'msg-123',
        notification_type: NotificationType.MESSAGE,
        is_read: false,
        is_sent: true,
        insert_date: new Date().toISOString(),
        read_date: null,
        expires_at: new Date().toISOString(),
        message_content: 'Test message',
        author_name: 'Test User',
        room_name: 'Test Room'
      }

      // Remove socket broadcast
      delete (global as any).socketBroadcast

      // Should not throw error
      await expect(service.broadcastNotification(domainUuid, notification))
        .resolves.not.toThrow()
    })
  })

  describe('Error Handling', () => {
    it('should handle notification creation errors gracefully', async () => {
      const domainUuid = 'test-domain'

      const notifications = [{
        user_uuid: 'user-1',
        room_uuid: 'room-1',
        message_id: 'msg-1',
        notification_type: NotificationType.MESSAGE,
        message_content: 'Test',
        author_name: 'Test User'
      }]

      // Mock database error
      mockPrisma.v_chat_notifications.createMany.mockRejectedValueOnce(
        new Error('Database error')
      )

      await expect(service.createAndBroadcastNotifications(domainUuid, notifications))
        .rejects.toThrow('Database error')
    })
  })
})
