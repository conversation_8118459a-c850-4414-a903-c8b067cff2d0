import { prisma } from '@/libs/db/prisma'

/**
 * Centralized Zalo Token Management Service
 * Handles automatic token refresh for internal chat app
 */
export class ZaloTokenService {
  private static refreshPromises: Map<string, Promise<string | null>> = new Map()

  /**
   * Get valid access token for domain, automatically refreshing if expired
   */
  static async getValidAccessToken(domainUuid: string): Promise<string | null> {
    try {
      // Check if there's already a refresh in progress for this domain
      const existingRefresh = this.refreshPromises.get(domainUuid)

      if (existingRefresh) {
        console.log('Token refresh already in progress for domain:', domainUuid)

        return await existingRefresh
      }

      // Get current configuration
      const config = await prisma.v_zalo_oa_config.findUnique({
        where: { domain_uuid: domainUuid }
      })

      if (!config || !config.access_token) {
        console.warn('No Zalo configuration found for domain:', domainUuid)

        return null
      }

      // Check if token is expired or about to expire (within 5 minutes)
      const now = new Date()
      const expiresAt = config.token_expires_at
      const shouldRefresh = !expiresAt || (expiresAt.getTime() - now.getTime()) < 5 * 60 * 1000

      if (!shouldRefresh) {
        console.log('Token is still valid for domain:', domainUuid)

        return config.access_token
      }

      // Token needs refresh
      console.log('Token expired or expiring soon, refreshing for domain:', domainUuid)

      if (!config.refresh_token) {
        console.error('No refresh token available for domain:', domainUuid)

        return config.access_token // Return current token as fallback
      }

      // Start refresh process and cache the promise
      const refreshPromise = this.performTokenRefresh(domainUuid, config)

      this.refreshPromises.set(domainUuid, refreshPromise)

      try {
        const newToken = await refreshPromise

        return newToken || config.access_token // Fallback to current token if refresh fails
      } finally {
        // Clean up the promise cache
        this.refreshPromises.delete(domainUuid)
      }

    } catch (error) {
      console.error('Error getting valid access token:', error)

      // Fallback: try to get current token from database
      try {
        const config = await prisma.v_zalo_oa_config.findUnique({
          where: { domain_uuid: domainUuid },
          select: { access_token: true }
        })

        return config?.access_token || null
      } catch (fallbackError) {
        console.error('Fallback token retrieval failed:', fallbackError)

        return null
      }
    }
  }

  /**
   * Perform the actual token refresh
   */
  private static async performTokenRefresh(
    domainUuid: string, 
    config: any
  ): Promise<string | null> {
    try {
      console.log('Performing token refresh for domain:', domainUuid)

      const refreshResult = await this.refreshZaloToken(
        config.app_id,
        config.refresh_token,
        config.app_secret
      )

      if (!refreshResult.success) {
        console.error('Token refresh failed:', refreshResult.error)

        return null
      }

      // Update database with new tokens
      const updateData: any = {
        access_token: refreshResult.accessToken,
        refresh_token: refreshResult.refreshToken,
        token_expires_at: refreshResult.expiresIn
          ? new Date(Date.now() + refreshResult.expiresIn * 1000)
          : null
      }

      await prisma.v_zalo_oa_config.update({
        where: { domain_uuid: domainUuid },
        data: updateData
      })

      console.log('Token refreshed successfully for domain:', domainUuid)

      return refreshResult.accessToken || null

    } catch (error) {
      console.error('Token refresh process failed:', error)

      return null
    }
  }

  /**
   * Refresh ZALO access token using refresh token
   */
  private static async refreshZaloToken(
    appId: string,
    refreshToken: string,
    appSecret: string
  ): Promise<{ 
    success: boolean; 
    accessToken?: string; 
    refreshToken?: string; 
    expiresIn?: number; 
    error?: string 
  }> {
    try {
      const response = await fetch('https://oauth.zaloapp.com/v4/oa/access_token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          secret_key: appSecret
        },
        body: new URLSearchParams({
          refresh_token: refreshToken,
          app_id: appId,
          grant_type: 'refresh_token'
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()

      if (result.error !== 0) {
        return {
          success: false,
          error: result.message || 'Token refresh failed'
        }
      }

      return {
        success: true,
        accessToken: result.access_token,
        refreshToken: result.refresh_token,
        expiresIn: result.expires_in
      }

    } catch (error) {
      console.error('Zalo token refresh API call failed:', error)
      
return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Check if an API error indicates token expiration
   */
  static isTokenExpiredError(error: any): boolean {
    if (typeof error === 'object' && error !== null) {
      // Check for Zalo API error codes that indicate token issues
      if (error.error_code === -201 || error.error === -201) {
        return true // Invalid access token
      }

      // Check for HTTP 401 Unauthorized
      if (error.status === 401) {
        return true
      }

      // Check error message content
      if (typeof error.message === 'string') {
        const message = error.message.toLowerCase()

        return message.includes('access token') &&
               (message.includes('invalid') || message.includes('expired'))
      }
    }

    return false
  }

  /**
   * Make API call with automatic token refresh on expiration
   */
  static async makeApiCallWithTokenRefresh<T>(
    domainUuid: string,
    apiCall: (accessToken: string) => Promise<T>,
    maxRetries: number = 1
  ): Promise<T> {
    let lastError: any = null

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        // Get valid access token (will refresh if needed)
        const accessToken = await this.getValidAccessToken(domainUuid)

        if (!accessToken) {
          throw new Error('No valid access token available')
        }

        // Make the API call
        return await apiCall(accessToken)

      } catch (error) {
        lastError = error

        // Check if this is a token expiration error and we have retries left
        if (this.isTokenExpiredError(error) && attempt < maxRetries) {
          console.log(`Token expired during API call, attempt ${attempt + 1}/${maxRetries + 1}`)

          // Force refresh by clearing any cached promises and updating expiry
          this.refreshPromises.delete(domainUuid)

          // Set token as expired in database to force refresh
          try {
            await prisma.v_zalo_oa_config.update({
              where: { domain_uuid: domainUuid },
              data: { token_expires_at: new Date(Date.now() - 1000) } // Set to past
            })
          } catch (dbError) {
            console.warn('Failed to update token expiry in database:', dbError)
          }

          continue // Retry the loop
        }

        // If not a token error or no retries left, throw the error
        throw error
      }
    }

    throw lastError
  }
}
