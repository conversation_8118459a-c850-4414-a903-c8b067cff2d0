// ZALO Assignment Service
// Handles agent assignment logic for ZALO conversations using chat room participants

import { prisma } from '@/libs/db/prisma'

export interface AssignmentResult {
  success: boolean
  assigned_agent_uuid: string | null
  assignment_method: 'manual' | 'none'
  message: string
}

export class ZaloAssignmentService {
  /**
   * Assign agent to ZALO room by adding them as a participant
   */
  static async assignAgent(zalo_room_uuid: string, agent_uuid: string, user_uuid: string): Promise<AssignmentResult> {
    try {
      // Get the ZALO room and its internal room
      const zaloRoom = await prisma.v_zalo_oa_chat_rooms.findFirst({
        where: {
          zalo_room_uuid
        },
        include: {
          v_chat_rooms: true
        }
      })

      if (!zaloRoom || !zaloRoom.v_chat_rooms) {
        return {
          success: false,
          assigned_agent_uuid: null,
          assignment_method: 'none',
          message: 'ZALO room not found'
        }
      }

      // Check if agent is already assigned
      const existingParticipant = await prisma.v_chat_room_participants.findFirst({
        where: {
          room_uuid: zaloRoom.internal_room_uuid,
          user_uuid: agent_uuid,
          deleted_at: null
        }
      })

      if (existingParticipant) {
        return {
          success: false,
          assigned_agent_uuid: agent_uuid,
          assignment_method: 'manual',
          message: 'Agent is already assigned to this conversation'
        }
      }

      // Verify agent exists and belongs to the same domain
      const agent = await prisma.v_users.findFirst({
        where: {
          user_uuid: agent_uuid,
          domain_uuid: zaloRoom.domain_uuid,
          user_enabled: 'true'
        }
      })

      if (!agent) {
        return {
          success: false,
          assigned_agent_uuid: null,
          assignment_method: 'none',
          message: 'Agent not found or not enabled'
        }
      }

      // Add agent as participant to the internal room
      await prisma.v_chat_room_participants.create({
        data: {
          room_uuid: zaloRoom.internal_room_uuid,
          user_uuid: agent_uuid,
          participant_role: 'member', // or 'agent' if you have that role
          joined_date: new Date(),
          insert_date: new Date(),
          insert_user: user_uuid,
          update_date: new Date(),
          update_user: user_uuid
        }
      })

      return {
        success: true,
        assigned_agent_uuid: agent_uuid,
        assignment_method: 'manual',
        message: 'Agent assigned successfully'
      }
    } catch (error) {
      console.error('Error assigning agent:', error)

      return {
        success: false,
        assigned_agent_uuid: null,
        assignment_method: 'manual',
        message: 'Failed to assign agent due to system error'
      }
    }
  }

  /**
   * Unassign agent from ZALO room by removing them as participant
   */
  static async unassignAgent(zalo_room_uuid: string, agent_uuid: string, user_uuid: string): Promise<boolean> {
    try {
      // Get the ZALO room and its internal room
      const zaloRoom = await prisma.v_zalo_oa_chat_rooms.findFirst({
        where: {
          zalo_room_uuid
        }
      })

      if (!zaloRoom) {
        return false
      }

      // Soft delete the participant (set deleted_at)
      await prisma.v_chat_room_participants.updateMany({
        where: {
          room_uuid: zaloRoom.internal_room_uuid,
          user_uuid: agent_uuid,
          deleted_at: null
        },
        data: {
          deleted_at: new Date(),
          update_date: new Date(),
          update_user: user_uuid
        }
      })

      return true
    } catch (error) {
      console.error('Error unassigning agent:', error)

      return false
    }
  }

  /**
   * Get assigned agents for a ZALO room
   */
  static async getAssignedAgents(zalo_room_uuid: string) {
    try {
      const zaloRoom = await prisma.v_zalo_oa_chat_rooms.findFirst({
        where: { zalo_room_uuid },
        include: {
          v_chat_rooms: {
            include: {
              v_chat_room_participants: {
                where: { deleted_at: null },
                include: {
                  v_users: {
                    select: {
                      user_uuid: true,
                      username: true,
                      user_email: true
                    }
                  }
                }
              }
            }
          }
        }
      })

      if (!zaloRoom?.v_chat_rooms) {
        return []
      }

      return zaloRoom.v_chat_rooms.v_chat_room_participants.map(participant => ({
        user_uuid: participant.v_users.user_uuid,
        username: participant.v_users.username,
        user_email: participant.v_users.user_email,
        participant_role: participant.participant_role,
        joined_date: participant.joined_date
      }))
    } catch (error) {
      console.error('Error getting assigned agents:', error)
      
return []
    }
  }

  /**
   * Get available agents for assignment (agents in the same domain)
   */
  static async getAvailableAgents(domain_uuid: string) {
    try {
      const users = await prisma.v_users.findMany({
        where: {
          domain_uuid,
          user_enabled: 'true'
        },
        select: {
          user_uuid: true,
          username: true
        },
        orderBy: {
          username: 'asc'
        }
      })

      // Get active chat participation counts for each user
      const agentsWithCounts = await Promise.all(
        users.map(async user => {
          // Count active ZALO rooms where user is a participant
          // First get all rooms where user is a participant
          const participantRooms = await prisma.v_chat_room_participants.findMany({
            where: {
              user_uuid: user.user_uuid,
              deleted_at: null
            },
            select: {
              room_uuid: true
            }
          })

          // Then count how many of those rooms are ZALO rooms with active status
          const activeChats = await prisma.v_zalo_oa_chat_rooms.count({
            where: {
              internal_room_uuid: {
                in: participantRooms.map(p => p.room_uuid)
              },
              room_status: 'active'
            }
          })

          return {
            ...user,
            active_chats: activeChats
          }
        })
      )

      return agentsWithCounts
    } catch (error) {
      console.error('Error getting available agents:', error)

      return []
    }
  }

  /**
   * Get room assignment status
   */
  static async getRoomAssignmentStatus(zalo_room_uuid: string) {
    try {
      const assignedAgents = await this.getAssignedAgents(zalo_room_uuid)

      return {
        is_assigned: assignedAgents.length > 0,
        agent_count: assignedAgents.length,
        agents: assignedAgents
      }
    } catch (error) {
      console.error('Error getting room assignment status:', error)
      
return {
        is_assigned: false,
        agent_count: 0,
        agents: []
      }
    }
  }
}
