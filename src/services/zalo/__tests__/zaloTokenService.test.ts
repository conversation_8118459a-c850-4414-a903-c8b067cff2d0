/**
 * Tests for ZaloTokenService
 * Verifies automatic token refresh functionality
 */

import { ZaloTokenService } from '../zaloTokenService'

// Mock Prisma
jest.mock('@/libs/db/prisma', () => ({
  prisma: {
    v_zalo_oa_config: {
      findUnique: jest.fn(),
      update: jest.fn()
    }
  }
}))

// Mock fetch
global.fetch = jest.fn()

describe('ZaloTokenService', () => {
  const mockDomainUuid = 'test-domain-uuid'

  const mockConfig = {
    config_uuid: 'test-config-uuid',
    domain_uuid: mockDomainUuid,
    app_id: 'test-app-id',
    app_secret: 'test-app-secret',
    access_token: 'current-access-token',
    refresh_token: 'current-refresh-token',
    token_expires_at: new Date(Date.now() + 10 * 60 * 1000) // 10 minutes from now
  }

  beforeEach(() => {
    jest.clearAllMocks()

    ZaloTokenService['refreshPromises'].clear()
  })

  describe('getValidAccessToken', () => {
    it('should return current token if not expired', async () => {
      const { prisma } = require('@/libs/db/prisma')

      prisma.v_zalo_oa_config.findUnique.mockResolvedValue(mockConfig)

      const token = await ZaloTokenService.getValidAccessToken(mockDomainUuid)

      expect(token).toBe('current-access-token')

      expect(prisma.v_zalo_oa_config.findUnique).toHaveBeenCalledWith({
        where: { domain_uuid: mockDomainUuid }
      })
    })

    it('should refresh token if expired', async () => {
      const expiredConfig = {
        ...mockConfig,
        token_expires_at: new Date(Date.now() - 1000) // 1 second ago
      }

      const { prisma } = require('@/libs/db/prisma')

      prisma.v_zalo_oa_config.findUnique.mockResolvedValue(expiredConfig)
      prisma.v_zalo_oa_config.update.mockResolvedValue({})

      // Mock successful token refresh
      ;(global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          error: 0,
          access_token: 'new-access-token',
          refresh_token: 'new-refresh-token',
          expires_in: 3600
        })
      })

      const token = await ZaloTokenService.getValidAccessToken(mockDomainUuid)

      expect(token).toBe('new-access-token')

      expect(fetch).toHaveBeenCalledWith('https://oauth.zaloapp.com/v4/oa/access_token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          secret_key: 'test-app-secret'
        },
        body: expect.any(URLSearchParams)
      })

      expect(prisma.v_zalo_oa_config.update).toHaveBeenCalled()
    })

    it('should return null if no config found', async () => {
      const { prisma } = require('@/libs/db/prisma')

      prisma.v_zalo_oa_config.findUnique.mockResolvedValue(null)

      const token = await ZaloTokenService.getValidAccessToken(mockDomainUuid)

      expect(token).toBeNull()
    })
  })

  describe('isTokenExpiredError', () => {
    it('should detect Zalo API token error codes', () => {
      expect(ZaloTokenService.isTokenExpiredError({ error_code: -201 })).toBe(true)
      expect(ZaloTokenService.isTokenExpiredError({ error: -201 })).toBe(true)
    })

    it('should detect HTTP 401 errors', () => {
      expect(ZaloTokenService.isTokenExpiredError({ status: 401 })).toBe(true)
    })

    it('should detect token-related error messages', () => {
      expect(ZaloTokenService.isTokenExpiredError({ 
        message: 'Invalid access token' 
      })).toBe(true)
      expect(ZaloTokenService.isTokenExpiredError({ 
        message: 'Access token expired' 
      })).toBe(true)
    })

    it('should return false for non-token errors', () => {
      expect(ZaloTokenService.isTokenExpiredError({ error_code: -124 })).toBe(false)
      expect(ZaloTokenService.isTokenExpiredError({ status: 500 })).toBe(false)
      expect(ZaloTokenService.isTokenExpiredError({ message: 'Network error' })).toBe(false)
    })
  })

  describe('makeApiCallWithTokenRefresh', () => {
    it('should make successful API call without refresh', async () => {
      const { prisma } = require('@/libs/db/prisma')

      prisma.v_zalo_oa_config.findUnique.mockResolvedValue(mockConfig)

      const mockApiCall = jest.fn().mockResolvedValue({ success: true, data: 'test' })

      const result = await ZaloTokenService.makeApiCallWithTokenRefresh(
        mockDomainUuid,
        mockApiCall
      )

      expect(result).toEqual({ success: true, data: 'test' })

      expect(mockApiCall).toHaveBeenCalledWith('current-access-token')

      expect(mockApiCall).toHaveBeenCalledTimes(1)
    })

    it('should retry with refreshed token on token error', async () => {
      const { prisma } = require('@/libs/db/prisma')

      prisma.v_zalo_oa_config.findUnique
        .mockResolvedValueOnce(mockConfig) // First call
        .mockResolvedValueOnce({
          ...mockConfig,
          token_expires_at: new Date(Date.now() - 1000) // Force refresh
        }) // Second call after error
      prisma.v_zalo_oa_config.update.mockResolvedValue({})

      // Mock token refresh
      ;(global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          error: 0,
          access_token: 'refreshed-access-token',
          refresh_token: 'refreshed-refresh-token',
          expires_in: 3600
        })
      })

      const mockApiCall = jest.fn()
        .mockRejectedValueOnce({ error_code: -201 }) // First call fails with token error
        .mockResolvedValueOnce({ success: true, data: 'test' }) // Second call succeeds

      const result = await ZaloTokenService.makeApiCallWithTokenRefresh(
        mockDomainUuid,
        mockApiCall,
        1 // maxRetries
      )

      expect(result).toEqual({ success: true, data: 'test' })

      expect(mockApiCall).toHaveBeenCalledTimes(2)

      expect(mockApiCall).toHaveBeenNthCalledWith(1, 'current-access-token')

      expect(mockApiCall).toHaveBeenNthCalledWith(2, 'refreshed-access-token')
    })
  })
})
