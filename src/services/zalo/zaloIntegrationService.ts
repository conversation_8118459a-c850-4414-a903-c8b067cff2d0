// ZALO Integration Service
// Provides utilities for ZALO integration with internal chat system

import { prisma } from '@/libs/db/prisma'

export interface ZaloRoomInfo {
  zalo_room_uuid: string
  internal_room_uuid: string
  assigned_agent_uuid?: string | null
  contact: {
    contact_uuid: string
    zalo_user_id: string
    display_name: string
    username: string | null
    avatar_url: string | null
    phone: string | null
    is_follower: boolean | null
    last_interaction_date: Date | null
  }
  room_status: string
  last_message_at: Date | null
  conversation_metadata: any
}

export interface ZaloStats {
  total_contacts: number
  active_rooms: number
  unassigned_rooms: number
  total_messages_sent: number
  total_messages_received: number
  failed_messages: number
  last_activity: Date | null
}

export class ZaloIntegrationService {
  /**
   * Get ZALO rooms for internal chat integration
   */
  static async getZaloRoomsForDomain(domain_uuid: string): Promise<ZaloRoomInfo[]> {
    const zaloRooms = await prisma.v_zalo_oa_chat_rooms.findMany({
      where: {
        domain_uuid,
        room_status: 'active'
      },
      include: {
        v_zalo_oa_contacts: true,
        v_chat_rooms: {
          select: {
            room_uuid: true,
            room_name: true,
            room_type: true,
            is_active: true,
            is_archived: true
          }
        }
      },
      orderBy: { last_message_at: 'desc' }
    })

    return zaloRooms.map(room => ({
      zalo_room_uuid: room.zalo_room_uuid,
      internal_room_uuid: room.internal_room_uuid,
      assigned_agent_uuid: room.assigned_agent_uuid,
      contact: {
        contact_uuid: room.v_zalo_oa_contacts.contact_uuid,
        zalo_user_id: room.v_zalo_oa_contacts.zalo_user_id,
        display_name: room.v_zalo_oa_contacts.display_name || `User ${room.v_zalo_oa_contacts.zalo_user_id}`,
        username: room.v_zalo_oa_contacts.username,
        avatar_url: room.v_zalo_oa_contacts.avatar_url,
        phone: room.v_zalo_oa_contacts.phone,
        is_follower: room.v_zalo_oa_contacts.is_follower,
        last_interaction_date: room.v_zalo_oa_contacts.last_interaction_date
      },
      room_status: room.room_status || 'active',
      last_message_at: room.last_message_at,
      conversation_metadata: room.conversation_metadata || {}
    }))
  }

  /**
   * Get ZALO contacts for domain
   */
  static async getZaloContactsForDomain(
    domain_uuid: string,
    options: {
      search?: string
      limit?: number
      offset?: number
      is_follower?: boolean
    } = {}
  ) {
    const { search, limit = 50, offset = 0, is_follower } = options

    const whereClause: any = {
      domain_uuid,
      is_active: true
    }

    if (search) {
      whereClause.OR = [
        { display_name: { contains: search, mode: 'insensitive' } },
        { username: { contains: search, mode: 'insensitive' } },
        { zalo_user_id: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (is_follower !== undefined) {
      whereClause.is_follower = is_follower
    }

    const contacts = await prisma.v_zalo_oa_contacts.findMany({
      where: whereClause,
      include: {
        v_zalo_oa_chat_rooms: {
          where: { room_status: 'active' },
          include: {
            v_chat_rooms: {
              select: {
                room_uuid: true,
                room_name: true,
                is_active: true
              }
            }
          }
        }
      },
      orderBy: { last_interaction_date: 'desc' },
      take: limit,
      skip: offset
    })

    return contacts.map(contact => ({
      contact_uuid: contact.contact_uuid,
      zalo_user_id: contact.zalo_user_id,
      display_name: contact.display_name || `User ${contact.zalo_user_id}`,
      username: contact.username,
      avatar_url: contact.avatar_url,
      phone: contact.phone,
      is_follower: contact.is_follower,
      last_interaction_date: contact.last_interaction_date,
      contact_info: contact.contact_info,
      chat_room: contact.v_zalo_oa_chat_rooms[0]
        ? {
            zalo_room_uuid: contact.v_zalo_oa_chat_rooms[0].zalo_room_uuid,
            internal_room_uuid: contact.v_zalo_oa_chat_rooms[0].internal_room_uuid,
            room_status: contact.v_zalo_oa_chat_rooms[0].room_status,
            last_message_at: contact.v_zalo_oa_chat_rooms[0].last_message_at
          }
        : null
    }))
  }

  /**
   * Get ZALO integration statistics
   */
  static async getZaloStats(domain_uuid: string): Promise<ZaloStats> {
    const [
      contactsCount,
      activeRoomsCount,
      unassignedRoomsCount,
      messagesSent,
      messagesReceived,
      failedMessages,
      lastActivity
    ] = await Promise.all([
      // Total contacts
      prisma.v_zalo_oa_contacts.count({
        where: { domain_uuid, is_active: true }
      }),

      // Active rooms
      prisma.v_zalo_oa_chat_rooms.count({
        where: { domain_uuid, room_status: 'active' }
      }),

      // Unassigned rooms (rooms without participants)
      prisma.v_zalo_oa_chat_rooms.count({
        where: {
          domain_uuid,
          room_status: 'active',
          v_chat_rooms: {
            v_chat_room_participants: {
              none: {
                deleted_at: null
              }
            }
          }
        }
      }),

      // Messages sent (outbound)
      prisma.v_zalo_message_mapping.count({
        where: {
          domain_uuid,
          message_direction: 'outbound',
          delivery_status: 'delivered'
        }
      }),

      // Messages received (inbound)
      prisma.v_zalo_message_mapping.count({
        where: {
          domain_uuid,
          message_direction: 'inbound'
        }
      }),

      // Failed messages
      prisma.v_zalo_message_mapping.count({
        where: {
          domain_uuid,
          delivery_status: 'failed'
        }
      }),

      // Last activity
      prisma.v_zalo_oa_chat_rooms.findFirst({
        where: { domain_uuid },
        orderBy: { last_message_at: 'desc' },
        select: { last_message_at: true }
      })
    ])

    return {
      total_contacts: contactsCount,
      active_rooms: activeRoomsCount,
      unassigned_rooms: unassignedRoomsCount,
      total_messages_sent: messagesSent,
      total_messages_received: messagesReceived,
      failed_messages: failedMessages,
      last_activity: lastActivity?.last_message_at || null
    }
  }

  /**
   * Create a new ZALO chat room for a contact
   */
  static async createRoomForContact(domain_uuid: string, contact_uuid: string): Promise<string | null> {
    try {
      const contact = await prisma.v_zalo_oa_contacts.findFirst({
        where: { contact_uuid, domain_uuid }
      })

      if (!contact) {
        throw new Error('Contact not found')
      }

      // Check if room already exists
      const existingRoom = await prisma.v_zalo_oa_chat_rooms.findFirst({
        where: { zalo_contact_uuid: contact_uuid, domain_uuid }
      })

      if (existingRoom) {
        return existingRoom.internal_room_uuid
      }

      // Create internal chat room first
      const internalRoom = await prisma.v_chat_rooms.create({
        data: {
          domain_uuid,
          room_name: `ZALO Chat - ${contact.display_name || contact.zalo_user_id}`,
          room_description: `ZALO OA conversation with user ${contact.zalo_user_id}`,
          room_type: 'direct',
          created_by_user_uuid: domain_uuid, // Use domain as creator for system rooms
          room_settings: {
            platform: 'zalo_oa',
            zalo_user_id: contact.zalo_user_id,
            auto_created: true
          }
        }
      })

      // Create ZALO chat room bridge
      await prisma.v_zalo_oa_chat_rooms.create({
        data: {
          domain_uuid,
          internal_room_uuid: internalRoom.room_uuid,
          zalo_contact_uuid: contact_uuid,
          room_status: 'active',
          last_message_at: new Date(),
          conversation_metadata: {
            zalo_user_id: contact.zalo_user_id,
            created_manually: true
          }
        }
      })

      return internalRoom.room_uuid
    } catch (error) {
      console.error('Error creating ZALO room for contact:', error)

return null
    }
  }

  /**
   * Assign agent to Zalo room (enhanced version)
   */
  static async assignAgent(
    zalo_room_uuid: string,
    agent_uuid: string,
    assigned_by_user_uuid: string
  ): Promise<{
    success: boolean
    message: string
    agent_name?: string
    contact_name?: string
  }> {
    try {
      // Get the Zalo room with contact and internal room info
      const zaloRoom = await prisma.v_zalo_oa_chat_rooms.findFirst({
        where: {
          zalo_room_uuid
        },
        include: {
          v_zalo_oa_contacts: true,
          v_chat_rooms: true
        }
      })

      if (!zaloRoom || !zaloRoom.v_zalo_oa_contacts || !zaloRoom.v_chat_rooms) {
        return {
          success: false,
          message: 'Zalo room not found'
        }
      }

      // Get agent information
      const agent = await prisma.v_users.findFirst({
        where: {
          user_uuid: agent_uuid
        },
        select: {
          user_uuid: true,
          username: true
        }
      })

      if (!agent) {
        return {
          success: false,
          message: 'Agent not found'
        }
      }

      const agentName = agent.username || agent.user_uuid
      const contactName = zaloRoom.v_zalo_oa_contacts.display_name || `Zalo User ${zaloRoom.v_zalo_oa_contacts.zalo_user_id}`

      // Update assignment in transaction
      await prisma.$transaction(async tx => {
        // Update Zalo room assignment
        await tx.v_zalo_oa_chat_rooms.update({
          where: { zalo_room_uuid },
          data: {
            assigned_agent_uuid: agent_uuid,
            update_date: new Date(),
            update_user: assigned_by_user_uuid
          }
        })

        // Add agent as participant to internal room if not already present
        const existingParticipant = await tx.v_chat_room_participants.findFirst({
          where: {
            room_uuid: zaloRoom.internal_room_uuid,
            user_uuid: agent_uuid,
            deleted_at: null
          }
        })

        if (!existingParticipant) {
          await tx.v_chat_room_participants.create({
            data: {
              room_uuid: zaloRoom.internal_room_uuid,
              user_uuid: agent_uuid,
              participant_role: 'member' as any,
              insert_user: assigned_by_user_uuid,
              update_user: assigned_by_user_uuid,
              notification_settings: {
                mentions: true,
                messages: true,
                reactions: true
              }
            }
          })
        }

        // Create system message for assignment
        await tx.v_chat_messages.create({
          data: {
            room_uuid: zaloRoom.internal_room_uuid,
            author_uuid: null, // System message
            content: `Agent ${agentName} has been assigned to this Zalo conversation.`,
            message_type: 3, // System message type
            created_at: Math.floor(Date.now() / 1000),
            flags: 1 // System flag
          }
        })
      })

      return {
        success: true,
        message: `Agent ${agentName} assigned successfully`,
        agent_name: agentName,
        contact_name: contactName
      }
    } catch (error) {
      console.error('Error assigning agent to Zalo room:', error)

      return {
        success: false,
        message: 'Failed to assign agent'
      }
    }
  }

  /**
   * Get available agents for Zalo assignment
   */
  static async getAvailableAgents(domain_uuid: string) {
    const agents = await prisma.v_users.findMany({
      where: {
        domain_uuid,
        user_enabled: 'true'

        // Add any additional filters for agents (e.g., specific roles)
      },
      select: {
        user_uuid: true,
        username: true,
        user_email: true,
        contact_uuid: true
      },
      orderBy: [
        { username: 'asc' }
      ]
    })

    return agents.map(agent => ({
      user_uuid: agent.user_uuid,
      username: agent.username || agent.user_uuid,
      display_name: agent.username || agent.user_uuid,
      first_name: null,
      last_name: null
    }))
  }
}
