// Telegram Integration Service
// Simplified implementation for Telegram bot integration
// Follows clean architecture principles with proper error handling

import type {
  ExternalMessage,
  ExternalContact,
  MessageMappingResult,
  IntegrationConfig
} from './baseIntegrationService'
import { BaseIntegrationService } from './baseIntegrationService'
import { chatLogger } from '@/utils/logging/logger'
import { prisma } from '@/libs/db/prisma'

// =====================================================
// TELEGRAM-SPECIFIC TYPES
// =====================================================

interface TelegramMessage {
  message_id: number
  from: {
    id: number
    is_bot: boolean
    first_name: string
    last_name?: string
    username?: string
    language_code?: string
  }
  chat: {
    id: number
    type: string
  }
  date: number
  text?: string
  photo?: Array<{
    file_id: string
    file_unique_id: string
    width: number
    height: number
    file_size?: number
  }>
  document?: {
    file_id: string
    file_unique_id: string
    file_name?: string
    mime_type?: string
    file_size?: number
  }
}

interface TelegramWebhookPayload {
  update_id: number
  message?: TelegramMessage
  edited_message?: TelegramMessage
}

// =====================================================
// TELEGRAM INTEGRATION SERVICE
// =====================================================

export class TelegramIntegrationService extends BaseIntegrationService {
  private botToken: string
  private apiBaseUrl: string

  constructor(domain_uuid: string, config: IntegrationConfig) {
    super('telegram', domain_uuid, config)
    this.botToken = config.api_credentials.bot_token
    this.apiBaseUrl = `https://api.telegram.org/bot${this.botToken}`

    if (!this.botToken) {
      throw new Error('Telegram bot token is required')
    }
  }

  // =====================================================
  // IMPLEMENTATION OF ABSTRACT METHODS
  // =====================================================

  async mapExternalMessage(externalMessage: ExternalMessage): Promise<MessageMappingResult> {
    try {
      const internal_message = {
        content: externalMessage.content,
        message_type: this.mapMessageType(externalMessage.message_type),
        created_at: externalMessage.timestamp
      }

      const contact: ExternalContact = {
        contact_uuid: '', // Will be set when finding/creating
        external_id: externalMessage.sender_id,
        display_name: this.extractDisplayName(externalMessage),
        last_interaction_date: new Date(externalMessage.timestamp * 1000)
      }

      return { internal_message, contact }
    } catch (error) {
      chatLogger.error('Error mapping Telegram message', {
        error: error instanceof Error ? error.message : 'Unknown error',
        external_message_id: externalMessage.external_message_id
      })
      throw error
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async sendExternalMessage(contactId: string, content: string, _messageType?: number): Promise<boolean> {
    try {
      const contact = await this.getTelegramContact(contactId)

      if (!contact) {
        chatLogger.error('Telegram contact not found', { contact_id: contactId })

        return false
      }

      const response = await fetch(`${this.apiBaseUrl}/sendMessage`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chat_id: contact.telegram_chat_id,
          text: content,
          parse_mode: 'HTML'
        })
      })

      const result = await response.json()

      if (result.ok) {
        chatLogger.debug('Telegram message sent successfully', {
          contact_id: contactId,
          telegram_message_id: result.result.message_id
        })

        return true
      } else {
        chatLogger.error('Telegram API error', {
          error: result.description,
          error_code: result.error_code,
          contact_id: contactId
        })

        return false
      }
    } catch (error) {
      chatLogger.error('Error sending Telegram message', {
        error: error instanceof Error ? error.message : 'Unknown error',
        contact_id: contactId
      })

      return false
    }
  }

  validateWebhookPayload(payload: any): boolean {
    try {
      if (!payload || typeof payload !== 'object') return false
      if (typeof payload.update_id !== 'number') return false
      if (!payload.message && !payload.edited_message) return false

      const message = payload.message || payload.edited_message

      if (!message.from || !message.chat || typeof message.date !== 'number') return false

      return true
    } catch (error) {
      chatLogger.error('Error validating Telegram webhook payload', {
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      return false
    }
  }

  async processWebhook(payload: TelegramWebhookPayload): Promise<void> {
    try {
      if (!this.validateWebhookPayload(payload)) {
        throw new Error('Invalid webhook payload')
      }

      const message = payload.message || payload.edited_message

      if (!message) return

      const externalMessage: ExternalMessage = {
        external_message_id: message.message_id.toString(),
        content: message.text || '[Media]',
        message_type: this.getTelegramMessageType(message),
        timestamp: message.date,
        sender_id: message.from.id.toString(),
        attachments: this.extractAttachments(message)
      }

      await this.handleIncomingMessage(externalMessage)
    } catch (error) {
      chatLogger.error('Error processing Telegram webhook', {
        error: error instanceof Error ? error.message : 'Unknown error',
        update_id: payload.update_id
      })
      throw error
    }
  }

  // =====================================================
  // HELPER METHODS
  // =====================================================

  private async getTelegramContact(contactUuid: string): Promise<any> {
    try {
      return await prisma.v_telegram_contacts.findUnique({
        where: { contact_uuid: contactUuid },
        select: {
          contact_uuid: true,
          telegram_user_id: true,
          telegram_chat_id: true,
          username: true,
          first_name: true,
          last_name: true
        }
      })
    } catch (error) {
      chatLogger.error('Error getting Telegram contact', {
        error: error instanceof Error ? error.message : 'Unknown error',
        contact_uuid: contactUuid
      })

      return null
    }
  }

  private mapMessageType(telegramType: number): number {
    const typeMap: Record<number, number> = { 1: 1, 2: 2 } // Photo: 1, Document: 2

    return typeMap[telegramType] || 0 // Default to text
  }

  private getTelegramMessageType(message: TelegramMessage): number {
    if (message.photo) return 1
    if (message.document) return 2

    return 0
  }

  private extractDisplayName(externalMessage: ExternalMessage): string {
    return `Telegram User ${externalMessage.sender_id}`
  }

  private extractAttachments(message: TelegramMessage): any[] {
    const attachments: any[] = []

    if (message.photo?.length) {
      const photo = message.photo[message.photo.length - 1]

      attachments.push({
        filename: `photo_${photo.file_id}.jpg`,
        file_url: `${this.apiBaseUrl}/${photo.file_id}`,
        file_type: 'image/jpeg',
        file_size: photo.file_size
      })
    }

    if (message.document) {
      attachments.push({
        filename: message.document.file_name || `document_${message.document.file_id}`,
        file_url: `${this.apiBaseUrl}/${message.document.file_id}`,
        file_type: message.document.mime_type || 'application/octet-stream',
        file_size: message.document.file_size
      })
    }

    return attachments
  }

  // =====================================================
  // PUBLIC METHODS
  // =====================================================

  async setWebhook(webhookUrl: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/setWebhook`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          url: webhookUrl,
          allowed_updates: ['message', 'edited_message']
        })
      })

      const result = await response.json()

      if (result.ok) {
        chatLogger.info('Telegram webhook set successfully', { webhook_url: webhookUrl })

        return true
      } else {
        chatLogger.error('Failed to set Telegram webhook', {
          error: result.description,
          webhook_url: webhookUrl
        })

        return false
      }
    } catch (error) {
      chatLogger.error('Error setting Telegram webhook', {
        error: error instanceof Error ? error.message : 'Unknown error',
        webhook_url: webhookUrl
      })

      return false
    }
  }

  async getWebhookInfo(): Promise<any> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/getWebhookInfo`)
      const result = await response.json()

      return result.ok ? result.result : null
    } catch (error) {
      chatLogger.error('Error getting Telegram webhook info', {
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      return null
    }
  }
}
