// Base Integration Service
// Simplified architecture for external platform integrations
// Follows clean architecture principles with proper error handling

import { prisma } from '@/libs/db/prisma'
import { chatLogger } from '@/utils/logging/logger'
import type { ChatMessage, ChatRoom } from '@/types/apps/internal-chat/chatTypes'

// =====================================================
// BASE TYPES AND INTERFACES
// =====================================================

export interface ExternalContact {
  contact_uuid: string
  external_id: string
  display_name: string
  avatar_url?: string
  phone?: string
  email?: string
  last_interaction_date?: Date
}

export interface ExternalMessage {
  external_message_id: string
  content: string
  message_type: number
  timestamp: number
  sender_id: string
  attachments?: ExternalAttachment[]
}

export interface ExternalAttachment {
  filename: string
  file_url: string
  file_type: string
  file_size?: number
}

export interface IntegrationConfig {
  platform: 'telegram' | 'zalo' | 'discord' | 'whatsapp'
  domain_uuid: string
  api_credentials: Record<string, any>
  webhook_url?: string
  is_active: boolean
}

export interface MessageMappingResult {
  internal_message: Partial<ChatMessage>
  contact: ExternalContact
  room_data?: Partial<ChatRoom>
}

// =====================================================
// BASE INTEGRATION SERVICE
// =====================================================

export abstract class BaseIntegrationService {
  protected platform: string
  protected domain_uuid: string
  protected config: IntegrationConfig

  constructor(platform: string, domain_uuid: string, config: IntegrationConfig) {
    this.platform = platform
    this.domain_uuid = domain_uuid
    this.config = config
  }

  // =====================================================
  // ABSTRACT METHODS (Must be implemented by subclasses)
  // =====================================================

  /**
   * Map external message to internal format
   */
  abstract mapExternalMessage(externalMessage: ExternalMessage): Promise<MessageMappingResult>

  /**
   * Send message to external platform
   */
  abstract sendExternalMessage(contactId: string, content: string, messageType?: number): Promise<boolean>

  /**
   * Validate webhook payload
   */
  abstract validateWebhookPayload(payload: any): boolean

  /**
   * Process incoming webhook
   */
  abstract processWebhook(payload: any): Promise<void>

  // =====================================================
  // COMMON METHODS (Shared across all integrations)
  // =====================================================

  /**
   * Find or create contact from external data
   */
  protected async findOrCreateContact(externalContact: ExternalContact): Promise<ExternalContact> {
    try {
      chatLogger.debug('Finding or creating contact', {
        platform: this.platform,
        external_id: externalContact.external_id,
        domain_uuid: this.domain_uuid
      })

      // Return the contact (implementation depends on specific platform)
      return externalContact
    } catch (error) {
      chatLogger.error('Error finding/creating contact', {
        platform: this.platform,
        error: error instanceof Error ? error.message : 'Unknown error',
        external_id: externalContact.external_id
      })
      throw error
    }
  }

  /**
   * Find or create chat room for contact
   */
  protected async findOrCreateRoom(contact: ExternalContact, roomType: string): Promise<ChatRoom> {
    try {
      chatLogger.debug('Finding or creating room', {
        platform: this.platform,
        contact_uuid: contact.contact_uuid,
        room_type: roomType
      })

      // Check if room already exists for this contact
      const existingRoom = await this.findExistingRoom(contact.contact_uuid)

      if (existingRoom) {
        return existingRoom
      }

      // Create new room
      const newRoom = await this.createNewRoom(contact, roomType)

      return newRoom
    } catch (error) {
      chatLogger.error('Error finding/creating room', {
        platform: this.platform,
        error: error instanceof Error ? error.message : 'Unknown error',
        contact_uuid: contact.contact_uuid
      })
      throw error
    }
  }

  /**
   * Find existing room for contact
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private async findExistingRoom(_contactUuid: string): Promise<ChatRoom | null> {
    // Implementation depends on platform-specific room tables
    return null
  }

  /**
   * Create new room for contact
   */
  private async createNewRoom(contact: ExternalContact, roomType: string): Promise<ChatRoom> {
    return await prisma.$transaction(async tx => {
      // Create the room
      const room = await tx.v_chat_rooms.create({
        data: {
          domain_uuid: this.domain_uuid,
          room_name: contact.display_name || `${this.platform} Chat`,
          room_description: `${this.platform} integration chat with ${contact.display_name}`,
          room_type: roomType,
          room_settings: {
            platform: this.platform,
            external_contact_id: contact.external_id
          },
          created_by_user_uuid: 'system',
          insert_user: 'system',
          update_user: 'system'
        }
      })

      return room as unknown as ChatRoom
    })
  }

  /**
   * Save message to database
   */
  protected async saveMessage(
    roomUuid: string,
    content: string,
    messageType: number,
    authorUuid: string,
    externalMessageId?: string,
    timestamp?: number
  ): Promise<ChatMessage> {
    try {
      const message = await prisma.v_chat_messages.create({
        data: {
          room_uuid: roomUuid,
          author_uuid: authorUuid,
          content: content.trim(),
          message_type: messageType,
          created_at: timestamp || Math.floor(Date.now() / 1000),
          flags: 0
        }
      })

      chatLogger.debug('Message saved to database', {
        platform: this.platform,
        message_id: message.message_id,
        room_uuid: roomUuid,
        external_message_id: externalMessageId
      })

      return message as unknown as ChatMessage
    } catch (error) {
      chatLogger.error('Error saving message', {
        platform: this.platform,
        error: error instanceof Error ? error.message : 'Unknown error',
        room_uuid: roomUuid
      })
      throw error
    }
  }

  /**
   * Broadcast message to connected clients
   */
  protected broadcastMessage(roomUuid: string, message: ChatMessage): void {
    try {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      if ((global as any).socketBroadcast) {
        const roomName = `${this.domain_uuid}_internal_chat`

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ;(global as any).socketBroadcast.broadcastMessage(
          roomName,
          {
            type: 'new_message',
            data: message
          },
          this.domain_uuid
        )

        chatLogger.debug('Message broadcasted', {
          platform: this.platform,
          room_uuid: roomUuid,
          message_id: message.message_id
        })
      }
    } catch (error) {
      chatLogger.error('Error broadcasting message', {
        platform: this.platform,
        error: error instanceof Error ? error.message : 'Unknown error',
        room_uuid: roomUuid
      })
    }
  }

  /**
   * Handle incoming message from external platform
   */
  public async handleIncomingMessage(externalMessage: ExternalMessage): Promise<void> {
    try {
      chatLogger.info('Processing incoming message', {
        platform: this.platform,
        external_message_id: externalMessage.external_message_id,
        sender_id: externalMessage.sender_id
      })

      // Map external message to internal format
      const mappingResult = await this.mapExternalMessage(externalMessage)

      // Find or create contact
      const contact = await this.findOrCreateContact(mappingResult.contact)

      // Find or create room
      const room = await this.findOrCreateRoom(contact, `${this.platform}_chat`)

      // Save message to database
      const savedMessage = await this.saveMessage(
        room.room_uuid,
        mappingResult.internal_message.content || '',
        mappingResult.internal_message.message_type || 0,
        contact.contact_uuid,
        externalMessage.external_message_id,
        externalMessage.timestamp
      )

      // Broadcast to connected clients
      this.broadcastMessage(room.room_uuid, savedMessage)

      chatLogger.info('Message processed successfully', {
        platform: this.platform,
        message_id: savedMessage.message_id,
        room_uuid: room.room_uuid
      })
    } catch (error) {
      chatLogger.error('Error handling incoming message', {
        platform: this.platform,
        error: error instanceof Error ? error.message : 'Unknown error',
        external_message_id: externalMessage.external_message_id
      })
      throw error
    }
  }

  /**
   * Handle outgoing message to external platform
   */
  public async handleOutgoingMessage(contactId: string, content: string, messageType: number = 0): Promise<boolean> {
    try {
      chatLogger.info('Sending outgoing message', {
        platform: this.platform,
        contact_id: contactId,
        message_type: messageType
      })

      const success = await this.sendExternalMessage(contactId, content, messageType)

      if (success) {
        chatLogger.info('Message sent successfully', {
          platform: this.platform,
          contact_id: contactId
        })
      } else {
        chatLogger.warn('Failed to send message', {
          platform: this.platform,
          contact_id: contactId
        })
      }

      return success
    } catch (error) {
      chatLogger.error('Error sending outgoing message', {
        platform: this.platform,
        error: error instanceof Error ? error.message : 'Unknown error',
        contact_id: contactId
      })

      return false
    }
  }

  /**
   * Get integration statistics
   */
  public async getStats(): Promise<Record<string, any>> {
    try {
      return {
        platform: this.platform,
        domain_uuid: this.domain_uuid,
        is_active: this.config.is_active,
        timestamp: Date.now()
      }
    } catch (error) {
      chatLogger.error('Error getting integration stats', {
        platform: this.platform,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      return {}
    }
  }
}
