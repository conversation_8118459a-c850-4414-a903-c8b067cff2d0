// Chat Service Layer
// Centralized database operations for chat functionality
// Follows clean architecture principles with proper error handling

import { prisma } from '@/libs/db/prisma'
import type { ChatRoom, CreateRoomRequest, SendMessageRequest } from '@/types/apps/internal-chat/chatTypes'

// =====================================================
// UTILITY FUNCTIONS
// =====================================================

/**
 * Safely convert BigInt to string for JSON serialization
 */
export function safeBigIntToString(value: any): string {
  if (value === null || value === undefined) return '0'

  return typeof value === 'bigint' ? value.toString() : String(value)
}

// =====================================================
// ROOM OPERATIONS
// =====================================================

export class ChatRoomService {
  /**
   * Get rooms for a user with optimized queries
   */
  static async getRoomsForUser(
    userId: string,
    domainUuid: string,
    options: {
      type?: string
      page?: number
      limit?: number
    } = {}
  ): Promise<{ rooms: any[]; unreadCounts: Record<string, number> }> {
    const { type: roomType, page = 1, limit = 50 } = options
    const skip = (page - 1) * limit

    // Build where clause
    const whereClause: any = {
      domain_uuid: domainUuid,
      v_chat_room_participants: {
        some: {
          user_uuid: userId,
          deleted_at: null
        }
      }
    }

    if (roomType) {
      whereClause.room_type = roomType
    }

    // Get rooms with optimized query
    const rooms = await prisma.v_chat_rooms.findMany({
      where: {
        ...whereClause,
        v_chat_messages: {
          some: {} // Only rooms with at least one message
        }
      },
      select: {
        room_uuid: true,
        domain_uuid: true,
        room_name: true,
        room_description: true,
        room_type: true,
        room_avatar: true,
        created_by_user_uuid: true,
        is_active: true,
        is_archived: true,
        insert_date: true,
        update_date: true,

        // Minimal participant data
        v_chat_room_participants: {
          select: {
            user_uuid: true,
            participant_role: true,
            joined_date: true,
            is_muted: true,
            v_users: {
              select: {
                user_uuid: true,
                username: true,
                user_email: true
              }
            }
          }
        },

        // Latest message only
        v_chat_messages: {
          select: {
            message_id: true,
            author_uuid: true,
            content: true,
            message_type: true,
            created_at: true,
            flags: true
          },
          orderBy: { created_at: 'desc' },
          take: 1
        },

        _count: {
          select: {
            v_chat_room_participants: true,
            v_chat_messages: true
          }
        }
      },
      orderBy: [{ update_date: 'desc' }, { insert_date: 'desc' }],
      skip,
      take: limit
    })

    // Get unread counts in a separate optimized query
    const roomUuids = rooms.map(room => room.room_uuid)

    const unreadCountsData = await prisma.v_chat_unread_counts.findMany({
      where: {
        user_uuid: userId,
        room_uuid: { in: roomUuids }
      },
      select: {
        room_uuid: true,
        unread_count: true
      }
    })

    // Convert to lookup object
    const unreadCounts = unreadCountsData.reduce(
      (acc, item) => {
        acc[item.room_uuid] = item.unread_count

        return acc
      },
      {} as Record<string, number>
    )

    return { rooms, unreadCounts }
  }

  /**
   * Create a new room with participants
   */
  static async createRoom(userId: string, domainUuid: string, roomData: CreateRoomRequest): Promise<ChatRoom> {
    return await prisma.$transaction(async tx => {
      // Create the room
      const room = await tx.v_chat_rooms.create({
        data: {
          domain_uuid: domainUuid,
          room_name: roomData.room_name,
          room_description: roomData.room_description,
          room_type: roomData.room_type,
          room_settings: roomData.room_settings || {},
          created_by_user_uuid: userId,
          insert_user: userId,
          update_user: userId
        }
      })

      // Add creator as owner
      await tx.v_chat_room_participants.create({
        data: {
          room_uuid: room.room_uuid,
          user_uuid: userId,
          participant_role: 'owner',
          insert_user: userId,
          update_user: userId
        }
      })

      // Add other participants if specified
      if (roomData.participant_uuids && roomData.participant_uuids.length > 0) {
        const participantData = roomData.participant_uuids.map(participantId => ({
          room_uuid: room.room_uuid,
          user_uuid: participantId,
          participant_role: 'member' as const,
          insert_user: userId,
          update_user: userId
        }))

        await tx.v_chat_room_participants.createMany({
          data: participantData
        })
      }

      return room as unknown as ChatRoom
    })
  }
}

// =====================================================
// MESSAGE OPERATIONS
// =====================================================

export class ChatMessageService {
  /**
   * Get messages for a room with pagination
   */
  static async getMessagesForRoom(
    roomId: string,
    userId: string,
    options: {
      before?: string
      after?: string
      limit?: number
    } = {}
  ): Promise<{ messages: any[]; hasMore: boolean }> {
    const { before, after, limit = 50 } = options

    // Build where clause
    const whereClause: any = {
      room_uuid: roomId,
      flags: {
        not: 1 // Exclude deleted messages (flag 1 = deleted)
      }
    }

    if (before) {
      whereClause.message_id = { lt: BigInt(before) }
    } else if (after) {
      whereClause.message_id = { gt: BigInt(after) }
    }

    // Get messages with optimized query
    const messages = await prisma.v_chat_messages.findMany({
      where: whereClause,
      select: {
        message_id: true,
        room_uuid: true,
        author_uuid: true,
        content: true,
        message_type: true,
        reply_to: true,
        edited_at: true,
        created_at: true,
        flags: true,

        // Minimal attachment data
        v_chat_message_attachments: {
          select: {
            attachment_id: true,
            filename: true,
            file_size: true,
            content_type: true,
            file_path: true
          }
        },

        // Simplified reactions
        v_chat_message_reactions: {
          select: {
            user_uuid: true,
            emoji: true,
            created_at: true
          }
        },

        // Reply parent with minimal data
        v_chat_messages: {
          select: {
            message_id: true,
            content: true,
            author_uuid: true,
            message_type: true,
            created_at: true
          }
        }
      },
      orderBy: { message_id: before ? 'desc' : 'asc' },
      take: limit
    })

    // Check if there are more messages
    const hasMore = messages.length === limit

    return { messages, hasMore }
  }

  /**
   * Send a message to a room
   */
  static async sendMessage(roomId: string, userId: string, messageData: SendMessageRequest): Promise<any> {
    return await prisma.$transaction(async tx => {
      // Create the message
      const message = await tx.v_chat_messages.create({
        data: {
          room_uuid: roomId,
          author_uuid: userId,
          content: messageData.content.trim(),
          message_type: messageData.message_type || 0,
          reply_to: messageData.reply_to ? BigInt(messageData.reply_to) : null,
          created_at: Math.floor(Date.now() / 1000),
          flags: 0
        }
      })

      // Update room's last activity
      await tx.v_chat_rooms.update({
        where: { room_uuid: roomId },
        data: {
          update_date: new Date(),
          update_user: userId
        }
      })

      return message
    })
  }
}
