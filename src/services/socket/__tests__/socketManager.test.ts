// Socket Manager Tests
// Tests for Issue 4.2 fix: Socket Connection Management Problems

import { SocketManager } from '../socketManager'

// Mock socket.io-client
const mockSocket = {
  connected: true,
  disconnect: jest.fn(),
  on: jest.fn(),
  once: jest.fn(),
  onAny: jest.fn(),
  handshake: {
    query: {
      userId: 'test-user',
      domain: 'test-domain',
      type: 'internal_chat'
    }
  }
}

const mockIo = jest.fn(() => mockSocket)

jest.mock('socket.io-client', () => ({
  io: mockIo
}))

describe('SocketManager', () => {
  let socketManager: SocketManager

  beforeEach(() => {
    // Create new instance for each test
    ;(SocketManager as any).instance = null
    socketManager = SocketManager.getInstance({
      maxConnections: 5,
      healthCheckInterval: 1000,
      connectionTimeout: 5000,
      reconnectionAttempts: 3,
      reconnectionDelay: 500
    })

    jest.clearAllMocks()
    jest.clearAllTimers()
    jest.useFakeTimers()
  })

  afterEach(() => {
    jest.useRealTimers()
    socketManager.shutdown()
  })

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = SocketManager.getInstance()
      const instance2 = SocketManager.getInstance()
      
      expect(instance1).toBe(instance2)
    })
  })

  describe('Connection Management', () => {
    it('should create new connection for new user', async () => {
      const userId = 'user-1'
      const domain = 'domain-1'
      const connectionType = 'internal_chat'

      // Mock successful connection
      mockSocket.connected = true
      const connectCallback = jest.fn()

      mockSocket.on.mockImplementation((event, callback) => {
        if (event === 'connect') {
          connectCallback.mockImplementation(callback)

          // Simulate immediate connection
          setTimeout(callback, 0)
        }
      })

      const connection = await socketManager.getConnection(userId, domain, connectionType)

      expect(mockIo).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          query: {
            userId,
            domain,
            type: connectionType
          },
          forceNew: true
        })
      )

      expect(connection.userId).toBe(userId)
      expect(connection.domain).toBe(domain)
      expect(connection.connectionType).toBe(connectionType)
      expect(connection.socket).toBe(mockSocket)
    })

    it('should reuse existing healthy connection', async () => {
      const userId = 'user-1'
      const domain = 'domain-1'
      const connectionType = 'internal_chat'

      // Mock successful connection
      mockSocket.connected = true
      mockSocket.on.mockImplementation((event, callback) => {
        if (event === 'connect') {
          setTimeout(callback, 0)
        }
      })

      // Create first connection
      const connection1 = await socketManager.getConnection(userId, domain, connectionType)
      
      // Get same connection again
      const connection2 = await socketManager.getConnection(userId, domain, connectionType)

      expect(connection1).toBe(connection2)
      expect(mockIo).toHaveBeenCalledTimes(1) // Should only create socket once
    })

    it('should create new connection if existing is unhealthy', async () => {
      const userId = 'user-1'
      const domain = 'domain-1'
      const connectionType = 'internal_chat'

      // Mock first connection as unhealthy
      const unhealthySocket = { ...mockSocket, connected: false }

      mockIo.mockReturnValueOnce(unhealthySocket)

      mockSocket.on.mockImplementation((event, callback) => {
        if (event === 'connect') {
          setTimeout(callback, 0)
        }
      })

      // Create first connection (will be unhealthy)
      await socketManager.getConnection(userId, domain, connectionType)

      // Mock second connection as healthy
      mockIo.mockReturnValueOnce(mockSocket)
      
      // Get connection again (should create new one)
      const connection2 = await socketManager.getConnection(userId, domain, connectionType)

      expect(mockIo).toHaveBeenCalledTimes(2)
      expect(connection2.socket).toBe(mockSocket)
    })
  })

  describe('Connection Limits', () => {
    it('should enforce maximum connection limit', async () => {
      // Mock successful connections
      mockSocket.connected = true
      mockSocket.on.mockImplementation((event, callback) => {
        if (event === 'connect') {
          setTimeout(callback, 0)
        }
      })

      // Create maximum number of connections
      const connections = []

      for (let i = 0; i < 5; i++) {
        const connection = await socketManager.getConnection(
          `user-${i}`,
          `domain-${i}`,
          'internal_chat'
        )

        connections.push(connection)
      }

      // Creating one more should clean up oldest
      const newConnection = await socketManager.getConnection(
        'user-new',
        'domain-new',
        'internal_chat'
      )

      expect(newConnection).toBeDefined()
      expect(connections[0].socket.disconnect).toHaveBeenCalled()
    })
  })

  describe('Connection Cleanup', () => {
    it('should close specific connection', async () => {
      const userId = 'user-1'
      const domain = 'domain-1'
      const connectionType = 'internal_chat'

      // Mock successful connection
      mockSocket.connected = true
      mockSocket.on.mockImplementation((event, callback) => {
        if (event === 'connect') {
          setTimeout(callback, 0)
        }
      })

      await socketManager.getConnection(userId, domain, connectionType)

      await socketManager.closeUserConnection(userId, domain, connectionType)

      expect(mockSocket.disconnect).toHaveBeenCalled()
    })

    it('should close all connections for a user', async () => {
      const userId = 'user-1'

      // Mock successful connections
      mockSocket.connected = true
      mockSocket.on.mockImplementation((event, callback) => {
        if (event === 'connect') {
          setTimeout(callback, 0)
        }
      })

      // Create multiple connections for same user
      await socketManager.getConnection(userId, 'domain-1', 'internal_chat')
      await socketManager.getConnection(userId, 'domain-2', 'zalo_chat')

      await socketManager.closeUserConnections(userId)

      expect(mockSocket.disconnect).toHaveBeenCalledTimes(2)
    })

    it('should close all connections', async () => {
      // Mock successful connections
      mockSocket.connected = true
      mockSocket.on.mockImplementation((event, callback) => {
        if (event === 'connect') {
          setTimeout(callback, 0)
        }
      })

      // Create multiple connections
      await socketManager.getConnection('user-1', 'domain-1', 'internal_chat')
      await socketManager.getConnection('user-2', 'domain-2', 'zalo_chat')

      await socketManager.closeAllConnections()

      expect(mockSocket.disconnect).toHaveBeenCalledTimes(2)
    })
  })

  describe('Health Monitoring', () => {
    it('should perform health checks', async () => {
      const userId = 'user-1'
      const domain = 'domain-1'

      // Mock successful connection
      mockSocket.connected = true
      mockSocket.on.mockImplementation((event, callback) => {
        if (event === 'connect') {
          setTimeout(callback, 0)
        }
      })

      const connection = await socketManager.getConnection(userId, domain, 'internal_chat')

      // Make connection stale
      connection.lastActivity = new Date(Date.now() - 10 * 60 * 1000) // 10 minutes ago

      // Trigger health check
      jest.advanceTimersByTime(1000)

      expect(mockSocket.disconnect).toHaveBeenCalled()
    })

    it('should mark disconnected sockets as unhealthy', async () => {
      const userId = 'user-1'
      const domain = 'domain-1'

      // Mock connection that becomes disconnected
      mockSocket.connected = false
      let disconnectCallback: (reason: string) => void

      mockSocket.on.mockImplementation((event, callback) => {
        if (event === 'connect') {
          setTimeout(callback, 0)
        } else if (event === 'disconnect') {
          disconnectCallback = callback
        }
      })

      const connection = await socketManager.getConnection(userId, domain, 'internal_chat')

      // Simulate disconnect
      disconnectCallback('transport close')

      expect(connection.isHealthy).toBe(false)
    })
  })

  describe('Connection Statistics', () => {
    it('should provide accurate connection statistics', async () => {
      // Mock successful connections
      mockSocket.connected = true
      mockSocket.on.mockImplementation((event, callback) => {
        if (event === 'connect') {
          setTimeout(callback, 0)
        }
      })

      // Create various connections
      await socketManager.getConnection('user-1', 'domain-1', 'internal_chat')
      await socketManager.getConnection('user-2', 'domain-1', 'internal_chat')
      await socketManager.getConnection('user-3', 'domain-2', 'zalo_chat')

      const stats = socketManager.getConnectionStats()

      expect(stats.totalConnections).toBe(3)
      expect(stats.healthyConnections).toBe(3)
      expect(stats.connectionsByType).toEqual({
        internal_chat: 2,
        zalo_chat: 1
      })
      expect(stats.connectionsByDomain).toEqual({
        'domain-1': 2,
        'domain-2': 1
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle connection timeout', async () => {
      const userId = 'user-1'
      const domain = 'domain-1'

      // Mock connection that never connects
      mockSocket.connected = false
      mockSocket.on.mockImplementation(() => {
        // Don't call connect callback to simulate timeout
      })

      await expect(
        socketManager.getConnection(userId, domain, 'internal_chat')
      ).rejects.toThrow('Socket connection timeout')
    })

    it('should handle connection errors', async () => {
      const userId = 'user-1'
      const domain = 'domain-1'

      // Mock connection error
      const error = new Error('Connection failed')

      mockSocket.on.mockImplementation((event, callback) => {
        if (event === 'connect_error') {
          setTimeout(() => callback(error), 0)
        }
      })

      await expect(
        socketManager.getConnection(userId, domain, 'internal_chat')
      ).rejects.toThrow('Connection failed')
    })
  })

  describe('Shutdown', () => {
    it('should properly shutdown and cleanup', async () => {
      // Mock successful connections
      mockSocket.connected = true
      mockSocket.on.mockImplementation((event, callback) => {
        if (event === 'connect') {
          setTimeout(callback, 0)
        }
      })

      // Create connections
      await socketManager.getConnection('user-1', 'domain-1', 'internal_chat')
      await socketManager.getConnection('user-2', 'domain-2', 'zalo_chat')

      await socketManager.shutdown()

      expect(mockSocket.disconnect).toHaveBeenCalledTimes(2)

      const stats = socketManager.getConnectionStats()

      expect(stats.totalConnections).toBe(0)
    })
  })
})
