// Socket Manager Service
// Manages socket connections with proper isolation and cleanup
// Fixes Issue 4.2: Socket Connection Management Problems

import type { Socket } from 'socket.io-client'
import { io } from 'socket.io-client'

export interface SocketConnection {
  socket: Socket
  userId: string
  domain: string
  connectionType: string
  connectedAt: Date
  lastActivity: Date
  isHealthy: boolean
}

export interface SocketManagerOptions {
  maxConnections?: number
  healthCheckInterval?: number
  connectionTimeout?: number
  reconnectionAttempts?: number
  reconnectionDelay?: number
}

export class SocketManager {
  private static instance: SocketManager
  private connections: Map<string, SocketConnection> = new Map()
  private healthCheckInterval: NodeJS.Timeout | null = null
  private options: Required<SocketManagerOptions>

  private constructor(options: SocketManagerOptions = {}) {
    this.options = {
      maxConnections: options.maxConnections || 10,
      healthCheckInterval: options.healthCheckInterval || 30000, // 30 seconds
      connectionTimeout: options.connectionTimeout || 10000, // 10 seconds
      reconnectionAttempts: options.reconnectionAttempts || 5,
      reconnectionDelay: options.reconnectionDelay || 1000
    }

    this.startHealthCheck()
  }

  static getInstance(options?: SocketManagerOptions): SocketManager {
    if (!SocketManager.instance) {
      SocketManager.instance = new SocketManager(options)
    }

    return SocketManager.instance
  }

  /**
   * Create or get existing socket connection
   */
  async getConnection(
    userId: string,
    domain: string,
    connectionType: string = 'internal_chat'
  ): Promise<SocketConnection> {
    const connectionId = this.generateConnectionId(userId, domain, connectionType)

    // Return existing healthy connection
    const existing = this.connections.get(connectionId)

    if (existing && existing.socket.connected && existing.isHealthy) {
      existing.lastActivity = new Date()

      return existing
    }

    // Clean up existing unhealthy connection
    if (existing) {
      await this.closeConnection(connectionId)
    }

    // Create new connection
    return await this.createConnection(userId, domain, connectionType)
  }

  /**
   * Create new socket connection
   */
  private async createConnection(
    userId: string,
    domain: string,
    connectionType: string
  ): Promise<SocketConnection> {
    // Check connection limits
    if (this.connections.size >= this.options.maxConnections) {
      await this.cleanupOldestConnection()
    }

    const connectionId = this.generateConnectionId(userId, domain, connectionType)

    try {
      const socket = io(process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3000', {
        transports: ['websocket', 'polling'],
        timeout: this.options.connectionTimeout,
        forceNew: true, // Always create new connection for isolation
        upgrade: true,
        rememberUpgrade: true,
        autoConnect: true,
        reconnection: true,
        reconnectionAttempts: this.options.reconnectionAttempts,
        reconnectionDelay: this.options.reconnectionDelay,
        reconnectionDelayMax: 5000,
        query: {
          userId,
          domain,
          type: connectionType
        }
      })

      const connection: SocketConnection = {
        socket,
        userId,
        domain,
        connectionType,
        connectedAt: new Date(),
        lastActivity: new Date(),
        isHealthy: true
      }

      // Set up connection monitoring
      this.setupConnectionMonitoring(connectionId, connection)

      // Store connection
      this.connections.set(connectionId, connection)

      // Wait for connection to establish
      await this.waitForConnection(socket)

      console.log(`Socket connection created: ${connectionId}`)

      return connection
    } catch (error) {
      console.error(`Failed to create socket connection ${connectionId}:`, error)
      throw error
    }
  }

  /**
   * Set up connection monitoring and event handlers
   */
  private setupConnectionMonitoring(connectionId: string, connection: SocketConnection): void {
    const { socket } = connection

    socket.on('connect', () => {
      connection.isHealthy = true
      connection.lastActivity = new Date()
      console.log(`Socket connected: ${connectionId}`)
    })

    socket.on('disconnect', (reason) => {
      connection.isHealthy = false
      console.log(`Socket disconnected: ${connectionId}, reason: ${reason}`)

      // Clean up if manually disconnected
      if (reason === 'io client disconnect') {
        this.connections.delete(connectionId)
      }
    })

    socket.on('connect_error', (error) => {
      connection.isHealthy = false
      console.error(`Socket connection error: ${connectionId}`, error)
    })

    socket.on('error', (error) => {
      connection.isHealthy = false
      console.error(`Socket error: ${connectionId}`, error)
    })

    // Update activity on any message
    socket.onAny(() => {
      connection.lastActivity = new Date()
    })
  }

  /**
   * Wait for socket connection to establish
   */
  private async waitForConnection(socket: Socket): Promise<void> {
    return new Promise((resolve, reject) => {
      if (socket.connected) {
        resolve()

        return
      }

      const timeout = setTimeout(() => {
        reject(new Error('Socket connection timeout'))
      }, this.options.connectionTimeout)

      socket.once('connect', () => {
        clearTimeout(timeout)
        resolve()
      })

      socket.once('connect_error', (error) => {
        clearTimeout(timeout)
        reject(error)
      })
    })
  }

  /**
   * Close specific connection
   */
  async closeConnection(connectionId: string): Promise<void> {
    const connection = this.connections.get(connectionId)

    if (connection) {
      try {
        connection.socket.disconnect()
        this.connections.delete(connectionId)
        console.log(`Socket connection closed: ${connectionId}`)
      } catch (error) {
        console.error(`Error closing socket connection ${connectionId}:`, error)
      }
    }
  }

  /**
   * Close connection by user and domain
   */
  async closeUserConnection(
    userId: string,
    domain: string,
    connectionType: string = 'internal_chat'
  ): Promise<void> {
    const connectionId = this.generateConnectionId(userId, domain, connectionType)

    await this.closeConnection(connectionId)
  }

  /**
   * Close all connections for a user
   */
  async closeUserConnections(userId: string): Promise<void> {
    const userConnections = Array.from(this.connections.entries())
      .filter(([, connection]) => connection.userId === userId)

    for (const [connectionId] of userConnections) {
      await this.closeConnection(connectionId)
    }
  }

  /**
   * Close all connections
   */
  async closeAllConnections(): Promise<void> {
    const connectionIds = Array.from(this.connections.keys())

    for (const connectionId of connectionIds) {
      await this.closeConnection(connectionId)
    }
  }

  /**
   * Get connection statistics
   */
  getConnectionStats(): {
    totalConnections: number
    healthyConnections: number
    connectionsByType: Record<string, number>
    connectionsByDomain: Record<string, number>
  } {
    const connections = Array.from(this.connections.values())

    const connectionsByType: Record<string, number> = {}
    const connectionsByDomain: Record<string, number> = {}

    for (const connection of connections) {
      connectionsByType[connection.connectionType] = (connectionsByType[connection.connectionType] || 0) + 1
      connectionsByDomain[connection.domain] = (connectionsByDomain[connection.domain] || 0) + 1
    }

    return {
      totalConnections: connections.length,
      healthyConnections: connections.filter(c => c.isHealthy).length,
      connectionsByType,
      connectionsByDomain
    }
  }

  /**
   * Generate unique connection ID
   */
  private generateConnectionId(userId: string, domain: string, connectionType: string): string {
    return `${userId}:${domain}:${connectionType}`
  }

  /**
   * Clean up oldest connection when limit reached
   */
  private async cleanupOldestConnection(): Promise<void> {
    const connections = Array.from(this.connections.entries())
      .sort(([, a], [, b]) => a.lastActivity.getTime() - b.lastActivity.getTime())

    if (connections.length > 0) {
      const [oldestId] = connections[0]

      console.log(`Cleaning up oldest connection: ${oldestId}`)
      await this.closeConnection(oldestId)
    }
  }

  /**
   * Start health check monitoring
   */
  private startHealthCheck(): void {
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck()
    }, this.options.healthCheckInterval)
  }

  /**
   * Perform health check on all connections
   */
  private performHealthCheck(): void {
    const now = new Date()
    const staleThreshold = 5 * 60 * 1000 // 5 minutes

    for (const [connectionId, connection] of this.connections.entries()) {
      // Check if connection is stale
      const isStale = now.getTime() - connection.lastActivity.getTime() > staleThreshold

      // Check if socket is actually connected
      const isConnected = connection.socket.connected

      if (!isConnected || isStale) {
        console.log(`Unhealthy connection detected: ${connectionId}`)
        connection.isHealthy = false

        // Close stale connections
        if (isStale) {
          this.closeConnection(connectionId).catch(error => {
            console.error(`Error closing stale connection ${connectionId}:`, error)
          })
        }
      }
    }
  }

  /**
   * Stop health check monitoring
   */
  private stopHealthCheck(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval)
      this.healthCheckInterval = null
    }
  }

  /**
   * Cleanup and shutdown
   */
  async shutdown(): Promise<void> {
    this.stopHealthCheck()
    await this.closeAllConnections()
    console.log('Socket manager shutdown complete')
  }
}

// Export singleton instance
export const socketManager = SocketManager.getInstance()
