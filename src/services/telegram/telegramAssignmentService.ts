// Telegram Assignment Service
// Handles agent assignment logic for Telegram conversations using chat room participants

import { prisma } from '@/libs/db/prisma'

export interface AssignmentResult {
  success: boolean
  assigned_agent_uuid: string | null
  assignment_method: 'manual' | 'none'
  message: string
}

export class TelegramAssignmentService {
  /**
   * Assign agent to Telegram room by adding them as a participant
   */
  static async assignAgent(
    telegram_room_uuid: string,
    agent_uuid: string,
    user_uuid: string
  ): Promise<AssignmentResult> {
    try {
      // Get the Telegram room and its internal room
      const telegramRoom = await prisma.v_telegram_chat_rooms.findFirst({
        where: {
          telegram_room_uuid
        },
        include: {
          v_chat_rooms: true
        }
      })

      if (!telegramRoom || !telegramRoom.v_chat_rooms) {
        return {
          success: false,
          assigned_agent_uuid: null,
          assignment_method: 'none',
          message: 'Telegram room not found'
        }
      }

      // Check if agent exists and is active
      const agent = await prisma.v_users.findFirst({
        where: {
          user_uuid: agent_uuid,
          user_enabled: 'true'
        },
        select: {
          user_uuid: true,
          username: true,
          domain_uuid: true
        }
      })

      if (!agent) {
        return {
          success: false,
          assigned_agent_uuid: null,
          assignment_method: 'manual',
          message: 'Agent not found or inactive'
        }
      }

      // Check if agent is already a participant
      const existingParticipant = await prisma.v_chat_room_participants.findFirst({
        where: {
          room_uuid: telegramRoom.internal_room_uuid,
          user_uuid: agent_uuid,
          deleted_at: null
        }
      })

      if (existingParticipant) {
        return {
          success: false,
          assigned_agent_uuid: null,
          assignment_method: 'manual',
          message: 'Agent is already assigned to this conversation'
        }
      }

      // Add agent as participant to the internal room
      await prisma.v_chat_room_participants.create({
        data: {
          room_uuid: telegramRoom.internal_room_uuid,
          user_uuid: agent_uuid,
          participant_role: 'member', // or 'agent' if you have that role
          joined_date: new Date(),
          insert_date: new Date(),
          insert_user: user_uuid,
          update_date: new Date(),
          update_user: user_uuid
        }
      })

      return {
        success: true,
        assigned_agent_uuid: agent_uuid,
        assignment_method: 'manual',
        message: 'Agent assigned successfully'
      }
    } catch (error) {
      console.error('Error assigning agent:', error)

      return {
        success: false,
        assigned_agent_uuid: null,
        assignment_method: 'manual',
        message: 'Failed to assign agent due to system error'
      }
    }
  }

  /**
   * Unassign agent from Telegram room by removing them as participant
   */
  static async unassignAgent(telegram_room_uuid: string, agent_uuid: string, user_uuid: string): Promise<boolean> {
    try {
      // Get the Telegram room and its internal room
      const telegramRoom = await prisma.v_telegram_chat_rooms.findFirst({
        where: {
          telegram_room_uuid
        }
      })

      if (!telegramRoom) {
        return false
      }

      // Soft delete the participant (set deleted_at)
      await prisma.v_chat_room_participants.updateMany({
        where: {
          room_uuid: telegramRoom.internal_room_uuid,
          user_uuid: agent_uuid,
          deleted_at: null
        },
        data: {
          deleted_at: new Date(),
          update_date: new Date(),
          update_user: user_uuid
        }
      })

      return true
    } catch (error) {
      console.error('Error unassigning agent:', error)

      return false
    }
  }

  /**
   * Get assigned agents for a Telegram room
   */
  static async getAssignedAgents(telegram_room_uuid: string): Promise<any[]> {
    try {
      // Get the Telegram room and its internal room
      const telegramRoom = await prisma.v_telegram_chat_rooms.findFirst({
        where: {
          telegram_room_uuid
        }
      })

      if (!telegramRoom) {
        return []
      }

      // Get active participants (agents) in the room
      const participants = await prisma.v_chat_room_participants.findMany({
        where: {
          room_uuid: telegramRoom.internal_room_uuid,
          deleted_at: null
        },
        include: {
          v_users: {
            select: {
              user_uuid: true,
              username: true,
              user_enabled: true
            }
          }
        }
      })

      return participants
        .filter(p => p.v_users?.user_enabled === 'true')
        .map(p => ({
          user_uuid: p.user_uuid,
          username: p.v_users?.username,
          participant_role: p.participant_role,
          joined_at: p.joined_date
        }))
    } catch (error) {
      console.error('Error fetching assigned agents:', error)

      return []
    }
  }

  /**
   * Get available agents for assignment
   */
  static async getAvailableAgents(domain_uuid: string): Promise<any[]> {
    try {
      // Get all users in domain
      const users = await prisma.v_users.findMany({
        where: {
          domain_uuid,
          user_enabled: 'true'
        },
        select: {
          user_uuid: true,
          username: true
        },
        orderBy: {
          username: 'asc'
        }
      })

      // Get active chat participation counts for each user
      const agentsWithCounts = await Promise.all(
        users.map(async user => {
          // Count active Telegram rooms where user is a participant
          // First get all rooms where user is a participant
          const participantRooms = await prisma.v_chat_room_participants.findMany({
            where: {
              user_uuid: user.user_uuid,
              deleted_at: null
            },
            select: {
              room_uuid: true
            }
          })

          // Then count how many of those rooms are Telegram rooms with active status
          const activeChats = await prisma.v_telegram_chat_rooms.count({
            where: {
              internal_room_uuid: {
                in: participantRooms.map(p => p.room_uuid)
              },
              room_status: 'active'
            }
          })

          return {
            ...user,
            active_chats: activeChats
          }
        })
      )

      return agentsWithCounts
    } catch (error) {
      console.error('Error fetching available agents:', error)

      return []
    }
  }
}
