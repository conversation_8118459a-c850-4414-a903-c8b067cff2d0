// Telegram Message Processing Service
// Handles message processing, contact management, and room bridging for Telegram integration

import { prisma } from '@/libs/db/prisma'
import type { TelegramMessageEvent } from '@/types/apps/telegramTypes'
import { MessageType } from '@/types/apps/internal-chat/chatTypes'
import { safeBigIntToString } from '@/utils/chat/messageUtils'
import { getTelegramClient } from '@/libs/telegramClientUtils'

export interface TelegramContact {
  contact_uuid: string
  telegram_user_id: bigint
  telegram_chat_id: bigint
  username: string | null
  first_name: string | null
  last_name: string | null
  display_name: string
  phone: string | null
  language_code: string | null
  contact_info: any
  last_interaction_date: Date | null
}

export interface TelegramRoom {
  telegram_room_uuid: string
  internal_room_uuid: string
  room_status: string | null
  last_message_at: Date | null
  conversation_metadata: any
}

export interface ProcessedMessage {
  internal_message_id: string
  room_uuid: string
  contact_uuid: string
  telegram_message_id: string
  delivery_status: string
}

export class TelegramMessageService {
  /**
   * Process inbound Telegram message and create internal chat message
   */
  static async processInboundMessage(domain_uuid: string, message: TelegramMessageEvent): Promise<ProcessedMessage> {
    console.log('=== TELEGRAM MESSAGE PROCESSING START ===')
    console.log('Domain:', domain_uuid)
    console.log('Message ID:', message.message_id)
    console.log('From User ID:', message.from.id)
    console.log('Chat ID:', message.chat.id)
    console.log('Message Text:', message.text)

    // Find or create contact
    console.log('Step 1: Finding or creating contact...')
    const contact = await this.findOrCreateContact(domain_uuid, message)

    console.log('Contact created/found:', contact.contact_uuid, contact.display_name)

    // Find or create room bridge
    console.log('Step 2: Finding or creating room bridge...')
    const telegramRoom = await this.findOrCreateRoom(domain_uuid, contact, message)

    console.log('Room created/found:', telegramRoom.telegram_room_uuid, telegramRoom.internal_room_uuid)

    // Create internal message
    console.log('Step 3: Creating internal message...')

    const internalMessage = await this.createInternalMessage(telegramRoom.internal_room_uuid, message)

    console.log('Internal message created:', internalMessage.message_id.toString())

    // Create message mapping
    console.log('Step 4: Creating message mapping...')
    await this.createMessageMapping(domain_uuid, internalMessage.message_id, message, 'inbound')
    console.log('Message mapping created successfully')

    // Update room timestamp
    console.log('Step 5: Updating room timestamp...')
    await this.updateRoomTimestamp(telegramRoom.telegram_room_uuid)

    // Step 6: Handle unread counts and notifications (same as main chat system)
    console.log('Step 6: Handling unread counts and notifications...')
    await this.handleUnreadCountsAndNotifications(telegramRoom.internal_room_uuid, internalMessage.message_id)

    // Note: Real-time events are handled by the webhook route using the main chat system's broadcasting mechanism
    // This ensures Telegram messages integrate properly with notifications, unread counts, and popups

    const result = {
      internal_message_id: safeBigIntToString(internalMessage.message_id) || '0',
      room_uuid: telegramRoom.internal_room_uuid,
      contact_uuid: contact.contact_uuid,
      telegram_message_id: message.message_id,
      delivery_status: 'delivered'
    }

    console.log('=== TELEGRAM MESSAGE PROCESSING COMPLETE ===')
    console.log('Result:', result)
    console.log('============================================')

    return result
  }

  /**
   * Process outbound message from internal chat to Telegram
   */
  static async processOutboundMessage(
    domain_uuid: string,
    room_uuid: string,
    author_uuid: string,
    content: string,
    message_type: MessageType = MessageType.TEXT
  ): Promise<ProcessedMessage> {
    console.log('=== TELEGRAM OUTBOUND MESSAGE START ===')
    console.log('Domain:', domain_uuid)
    console.log('Room UUID:', room_uuid)
    console.log('Author UUID:', author_uuid)
    console.log('Content length:', content.length)

    // Find Telegram room bridge
    const telegramRoom = await prisma.v_telegram_chat_rooms.findFirst({
      where: {
        domain_uuid,
        internal_room_uuid: room_uuid
      },
      include: {
        v_telegram_contacts: true
      }
    })

    if (!telegramRoom) {
      throw new Error('Telegram room bridge not found')
    }

    const contact = telegramRoom.v_telegram_contacts

    // Generate a unique timestamp to prevent race conditions
    const timestamp = Math.floor(Date.now() / 1000)
    const uniqueKey = `${room_uuid}-${author_uuid}-${timestamp}-${content.substring(0, 50)}`

    console.log('Creating message with unique key:', uniqueKey)

    // Create internal message first with transaction to prevent race conditions
    const internalMessage = await prisma.$transaction(async (tx) => {
      // Check if a similar message was created very recently (within 1 second) to prevent duplicates
      const recentMessage = await tx.v_chat_messages.findFirst({
        where: {
          room_uuid,
          author_uuid,
          content: content.trim(),
          created_at: {
            gte: timestamp - 1,
            lte: timestamp + 1
          }
        },
        orderBy: { created_at: 'desc' }
      })

      if (recentMessage) {
        console.log('Found recent duplicate message, returning existing:', recentMessage.message_id.toString())

        return recentMessage
      }

      // Create new message
      return await tx.v_chat_messages.create({
        data: {
          room_uuid,
          author_uuid,
          content: content.trim(),
          message_type,
          created_at: timestamp,
          flags: 0
        }
      })
    })

    console.log('Internal message created/found:', internalMessage.message_id.toString())

    let telegramMessageId: number | null = null
    let deliveryStatus = 'pending'
    let errorMessage: string | null = null

    try {
      // Get dynamic Telegram client with domain-specific token
      const telegramClient = await getTelegramClient(domain_uuid)

      if (!telegramClient) {
        throw new Error('Failed to get Telegram client - check bot configuration')
      }

      // Send to Telegram
      console.log('=== SENDING TO TELEGRAM ===')
      console.log('Contact info:', {
        telegram_user_id: contact.telegram_user_id,
        telegram_chat_id: contact.telegram_chat_id,
        username: contact.username,
        first_name: contact.first_name,
        last_name: contact.last_name
      })
      console.log('Sending to chat_id:', contact.telegram_chat_id.toString())
      console.log('Message content:', content.trim())

      const telegramResponse = await telegramClient.sendMessage(contact.telegram_chat_id.toString(), content.trim(), {
        parseMode: 'HTML' as any
      })

      console.log('Telegram response:', telegramResponse)

      telegramMessageId = telegramResponse.messageId
      deliveryStatus = 'sent'
    } catch (error: any) {
      deliveryStatus = 'failed'

      // Handle specific Telegram API errors
      if (error.message && error.message.includes('404 Not Found')) {
        errorMessage = 'Chat not found - User may have blocked the bot or deleted their account'
        console.warn(`Telegram chat not found for room ${telegramRoom.telegram_room_uuid}:`, errorMessage)
      } else if (error.message && error.message.includes('403 Forbidden')) {
        errorMessage = 'Bot blocked by user or insufficient permissions'
        console.warn(`Telegram access forbidden for room ${telegramRoom.telegram_room_uuid}:`, errorMessage)
      } else if (error.message && error.message.includes('400 Bad Request')) {
        errorMessage = 'Invalid message format or content'
        console.warn(`Telegram bad request for room ${telegramRoom.telegram_room_uuid}:`, errorMessage)
      } else {
        errorMessage = error.message || 'Failed to send message to Telegram'
        console.error('Failed to send Telegram message:', error)
      }
    }

    // Create message mapping
    await prisma.v_telegram_message_mapping.create({
      data: {
        domain_uuid,
        internal_message_id: internalMessage.message_id,
        telegram_message_id: telegramMessageId ? BigInt(telegramMessageId) : null,
        telegram_chat_id: contact.telegram_chat_id,
        telegram_user_id: contact.telegram_user_id,
        message_direction: 'outbound',
        delivery_status: deliveryStatus,
        error_message: errorMessage,
        insert_date: new Date()
      }
    })

    // Update room timestamp
    await this.updateRoomTimestamp(telegramRoom.telegram_room_uuid)

    // Note: Real-time events for outbound messages are handled by the main chat system's message API
    // This ensures consistent broadcasting and notification handling across all platforms

    return {
      internal_message_id: safeBigIntToString(internalMessage.message_id) || '0',
      room_uuid,
      contact_uuid: contact.contact_uuid,
      telegram_message_id: telegramMessageId?.toString() || '',
      delivery_status: deliveryStatus
    }
  }

  /**
   * Find or create Telegram contact
   */
  private static async findOrCreateContact(
    domain_uuid: string,
    message: TelegramMessageEvent
  ): Promise<TelegramContact> {
    const telegramUserId = BigInt(message.from.id)
    const telegramChatId = BigInt(message.chat.id)

    let contact = await prisma.v_telegram_contacts.findFirst({
      where: {
        domain_uuid,
        telegram_user_id: telegramUserId,
        telegram_chat_id: telegramChatId
      }
    })

    if (!contact) {
      // Create new contact
      contact = await prisma.v_telegram_contacts.create({
        data: {
          domain_uuid,
          telegram_user_id: telegramUserId,
          telegram_chat_id: telegramChatId,
          username: message.from.username || null,
          first_name: message.from.first_name || null,
          last_name: message.from.last_name || null,
          language_code: message.from.language_code || null,
          is_bot: message.from.is_bot || false,
          contact_info: {
            chat_type: message.chat.type,
            is_premium: message.from.is_premium || false
          },
          last_interaction_date: new Date(),
          insert_date: new Date(),
          update_date: new Date()
        }
      })
    } else {
      // Update existing contact
      contact = await prisma.v_telegram_contacts.update({
        where: { contact_uuid: contact.contact_uuid },
        data: {
          username: message.from.username || contact.username,
          first_name: message.from.first_name || contact.first_name,
          last_name: message.from.last_name || contact.last_name,
          language_code: message.from.language_code || contact.language_code,
          last_interaction_date: new Date(),
          update_date: new Date()
        }
      })
    }

    return {
      ...contact,
      display_name: this.getContactDisplayName(contact)
    }
  }

  /**
   * Find or create Telegram room bridge
   */
  private static async findOrCreateRoom(
    domain_uuid: string,
    contact: TelegramContact,
    message: TelegramMessageEvent
  ): Promise<TelegramRoom> {
    let telegramRoom = await prisma.v_telegram_chat_rooms.findFirst({
      where: {
        domain_uuid,
        telegram_contact_uuid: contact.contact_uuid
      }
    })

    if (!telegramRoom) {
      // Create internal chat room (system-created, no user reference needed)
      const internalRoom = await prisma.v_chat_rooms.create({
        data: {
          domain_uuid,
          room_name: `Telegram: ${contact.display_name}`,
          room_description: `Telegram conversation with ${contact.display_name}`,
          room_type: 'direct',
          created_by_user_uuid: null, // System-created room (nullable field)
          room_settings: {
            platform: 'telegram',
            telegram_user_id: contact.telegram_user_id.toString(),
            telegram_chat_id: contact.telegram_chat_id.toString(),
            auto_created: true
          },
          insert_date: new Date(),
          update_date: new Date()
        }
      })

      // Create room bridge
      telegramRoom = await prisma.v_telegram_chat_rooms.create({
        data: {
          domain_uuid,
          internal_room_uuid: internalRoom.room_uuid,
          telegram_contact_uuid: contact.contact_uuid,
          room_status: 'active',
          last_message_at: new Date(),
          conversation_metadata: {
            telegram_chat_type: message.chat.type,
            contact_display_name: contact.display_name
          },
          insert_date: new Date(),
          update_date: new Date()
        }
      })
    }

    return telegramRoom
  }

  /**
   * Create internal chat message using raw SQL to handle nullable author_uuid
   */
  private static async createInternalMessage(room_uuid: string, message: TelegramMessageEvent) {
    // Use raw SQL since Prisma client hasn't been regenerated for nullable author_uuid
    const createdAt = Math.floor(Date.now() / 1000)

    await prisma.$executeRaw`
      INSERT INTO v_chat_messages (
        room_uuid, author_uuid, content, message_type, created_at, flags
      ) VALUES (
        ${room_uuid}::uuid,
        NULL,
        ${message.text || ''},
        ${MessageType.TEXT},
        ${createdAt},
        4
      )
    `

    // Get the created message
    const createdMessage = await prisma.v_chat_messages.findFirst({
      where: {
        room_uuid,
        content: message.text || '',
        created_at: createdAt
      },
      orderBy: { message_id: 'desc' }
    })

    if (!createdMessage) {
      throw new Error('Failed to create internal chat message')
    }

    return createdMessage
  }

  /**
   * Create message mapping
   */
  private static async createMessageMapping(
    domain_uuid: string,
    internal_message_id: bigint,
    message: TelegramMessageEvent,
    direction: 'inbound' | 'outbound'
  ) {
    return await prisma.v_telegram_message_mapping.create({
      data: {
        domain_uuid,
        internal_message_id,
        telegram_message_id: BigInt(message.message_id),
        telegram_chat_id: BigInt(message.chat.id),
        telegram_user_id: BigInt(message.from.id),
        message_direction: direction,
        delivery_status: 'delivered',
        insert_date: new Date()
      }
    })
  }

  /**
   * Update room timestamp
   */
  private static async updateRoomTimestamp(telegram_room_uuid: string) {
    return await prisma.v_telegram_chat_rooms.update({
      where: { telegram_room_uuid },
      data: {
        last_message_at: new Date(),
        update_date: new Date()
      }
    })
  }

  /**
   * Get rooms with invalid chat_ids for cleanup
   */
  static async getInvalidChatRooms(domain_uuid: string): Promise<any[]> {
    return await prisma.v_telegram_chat_rooms.findMany({
      where: {
        domain_uuid,
        room_status: 'closed',
        conversation_metadata: {
          path: ['error_reason'],
          equals: 'chat_not_found'
        }
      },
      include: {
        v_telegram_contacts: {
          select: {
            contact_uuid: true,
            telegram_user_id: true,
            telegram_chat_id: true,
            username: true,
            first_name: true,
            last_name: true
          }
        }
      },
      orderBy: { update_date: 'desc' }
    })
  }

  /**
   * Handle unread counts and notifications for Telegram messages
   * Uses the same logic as the main chat system
   */
  private static async handleUnreadCountsAndNotifications(room_uuid: string, message_id: bigint): Promise<void> {
    await prisma.$transaction(async tx => {
      // Get all room participants
      const allParticipants = await tx.v_chat_room_participants.findMany({
        where: {
          room_uuid: room_uuid
        },
        select: {
          user_uuid: true,
          is_muted: true,
          notification_settings: true
        }
      })

      // For external messages (Telegram), all participants should get notifications
      const participantsForNotifications = allParticipants.filter(participant => {
        // Skip if user has muted the room
        if (participant.is_muted) return false

        // Check notification settings
        const settings = participant.notification_settings as any

        return settings?.all_messages !== false
      })

      // Create notifications for participants
      const notificationsToCreate = participantsForNotifications.map(participant => ({
        user_uuid: participant.user_uuid,
        room_uuid: room_uuid,
        message_id: message_id,
        notification_type: 'message' as const,
        is_read: false,
        is_sent: false,
        insert_date: new Date(),
        expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
      }))

      if (notificationsToCreate.length > 0) {
        await tx.v_chat_notifications.createMany({
          data: notificationsToCreate
        })
      }

      // Handle unread counts for all participants
      const allParticipantUuids = allParticipants.map(p => p.user_uuid)

      // Get existing unread count records
      const existingCounts = await tx.v_chat_unread_counts.findMany({
        where: {
          room_uuid: room_uuid,
          user_uuid: { in: allParticipantUuids }
        }
      })

      const existingUserIds = new Set(existingCounts.map(c => c.user_uuid))

      // Create missing records for participants who don't have them
      const missingParticipants = allParticipantUuids.filter(uuid => !existingUserIds.has(uuid))

      if (missingParticipants.length > 0) {
        const newCountRecords = missingParticipants.map(uuid => ({
          user_uuid: uuid,
          room_uuid: room_uuid,
          unread_count: 1, // All participants get +1 for external messages
          last_updated: new Date()
        }))

        await tx.v_chat_unread_counts.createMany({
          data: newCountRecords
        })
      }

      // Update existing records for all participants (increment by 1)
      if (allParticipantUuids.length > 0) {
        await tx.v_chat_unread_counts.updateMany({
          where: {
            room_uuid: room_uuid,
            user_uuid: { in: allParticipantUuids }
          },
          data: {
            unread_count: { increment: 1 },
            last_updated: new Date()
          }
        })
      }

      // Update room's last activity
      await tx.v_chat_rooms.update({
        where: { room_uuid: room_uuid },
        data: {
          update_date: new Date()
        }
      })
    })
  }

  /**
   * Get contact display name
   */
  private static getContactDisplayName(contact: any): string {
    return (
      [contact.first_name, contact.last_name].filter(Boolean).join(' ') ||
      contact.username ||
      `Telegram User ${contact.telegram_user_id}`
    )
  }
}
