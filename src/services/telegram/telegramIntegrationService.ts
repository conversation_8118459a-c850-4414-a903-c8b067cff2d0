// Telegram Integration Service
// Provides utilities for Telegram integration with internal chat system

import { prisma } from '@/libs/db/prisma'

export interface TelegramRoomInfo {
  telegram_room_uuid: string
  internal_room_uuid: string
  contact: {
    contact_uuid: string
    telegram_user_id: string
    telegram_chat_id: string
    display_name: string
    username: string | null
    first_name: string | null
    last_name: string | null
    language_code: string | null
    last_interaction_date: Date | null
  }
  room_status: string
  last_message_at: Date | null
  conversation_metadata: any
}

export interface TelegramStats {
  total_contacts: number
  active_rooms: number
  unassigned_rooms: number
  total_messages_sent: number
  total_messages_received: number
  failed_messages: number
  last_activity: Date | null
}

export class TelegramIntegrationService {
  /**
   * Get Telegram rooms for internal chat integration
   */
  static async getTelegramRoomsForDomain(domain_uuid: string): Promise<TelegramRoomInfo[]> {
    const telegramRooms = await prisma.v_telegram_chat_rooms.findMany({
      where: {
        domain_uuid,
        room_status: 'active'
      },
      include: {
        v_telegram_contacts: true,
        v_chat_rooms: {
          select: {
            room_uuid: true,
            room_name: true,
            room_type: true,
            is_active: true,
            is_archived: true
          }
        }
      },
      orderBy: { last_message_at: 'desc' }
    })

    return telegramRooms.map(room => ({
      telegram_room_uuid: room.telegram_room_uuid,
      internal_room_uuid: room.internal_room_uuid,
      contact: {
        contact_uuid: room.v_telegram_contacts.contact_uuid,
        telegram_user_id: room.v_telegram_contacts.telegram_user_id.toString(),
        telegram_chat_id: room.v_telegram_contacts.telegram_chat_id.toString(),
        display_name: this.getContactDisplayName(room.v_telegram_contacts),
        username: room.v_telegram_contacts.username,
        first_name: room.v_telegram_contacts.first_name,
        last_name: room.v_telegram_contacts.last_name,
        language_code: room.v_telegram_contacts.language_code,
        last_interaction_date: room.v_telegram_contacts.last_interaction_date
      },
      room_status: room.room_status || 'active',
      last_message_at: room.last_message_at,
      conversation_metadata: room.conversation_metadata
    }))
  }

  /**
   * Check if room is a Telegram room
   */
  static async isTelegramRoom(room_uuid: string): Promise<boolean> {
    const telegramRoom = await prisma.v_telegram_chat_rooms.findFirst({
      where: {
        internal_room_uuid: room_uuid
      }
    })

    return !!telegramRoom
  }

  /**
   * Get Telegram room info by internal room UUID
   */
  static async getTelegramRoomInfo(room_uuid: string): Promise<TelegramRoomInfo | null> {
    const telegramRoom = await prisma.v_telegram_chat_rooms.findFirst({
      where: {
        internal_room_uuid: room_uuid
      },
      include: {
        v_telegram_contacts: true
      }
    })

    if (!telegramRoom) {
      return null
    }

    return {
      telegram_room_uuid: telegramRoom.telegram_room_uuid,
      internal_room_uuid: telegramRoom.internal_room_uuid,
      contact: {
        contact_uuid: telegramRoom.v_telegram_contacts.contact_uuid,
        telegram_user_id: telegramRoom.v_telegram_contacts.telegram_user_id.toString(),
        telegram_chat_id: telegramRoom.v_telegram_contacts.telegram_chat_id.toString(),
        display_name: this.getContactDisplayName(telegramRoom.v_telegram_contacts),
        username: telegramRoom.v_telegram_contacts.username,
        first_name: telegramRoom.v_telegram_contacts.first_name,
        last_name: telegramRoom.v_telegram_contacts.last_name,
        language_code: telegramRoom.v_telegram_contacts.language_code,
        last_interaction_date: telegramRoom.v_telegram_contacts.last_interaction_date
      },
      room_status: telegramRoom.room_status || 'active',
      last_message_at: telegramRoom.last_message_at,
      conversation_metadata: telegramRoom.conversation_metadata
    }
  }

  /**
   * Get Telegram integration statistics for domain
   */
  static async getTelegramStats(domain_uuid: string): Promise<TelegramStats> {
    const [totalContacts, activeRooms, messageStats, lastActivity] = await Promise.all([
      // Total contacts
      prisma.v_telegram_contacts.count({
        where: { domain_uuid }
      }),

      // Active rooms
      prisma.v_telegram_chat_rooms.count({
        where: {
          domain_uuid,
          room_status: 'active'
        }
      }),

      // Message statistics
      prisma.v_telegram_message_mapping.groupBy({
        by: ['message_direction', 'delivery_status'],
        where: { domain_uuid },
        _count: true
      }),

      // Last activity
      prisma.v_telegram_chat_rooms.findFirst({
        where: { domain_uuid },
        orderBy: { last_message_at: 'desc' },
        select: { last_message_at: true }
      })
    ])

    // Count unassigned rooms (rooms without any active participants)
    const telegramRooms = await prisma.v_telegram_chat_rooms.findMany({
      where: {
        domain_uuid,
        room_status: 'active'
      },
      include: {
        v_chat_rooms: {
          include: {
            v_chat_room_participants: {
              where: {
                deleted_at: null
              }
            }
          }
        }
      }
    })

    const unassignedRooms = telegramRooms.filter(
      room => !room.v_chat_rooms?.v_chat_room_participants || room.v_chat_rooms.v_chat_room_participants.length === 0
    ).length

    // Process message statistics
    let totalMessagesSent = 0
    let totalMessagesReceived = 0
    let failedMessages = 0

    messageStats.forEach(stat => {
      if (stat.message_direction === 'outbound') {
        if (stat.delivery_status === 'failed') {
          failedMessages += stat._count
        } else {
          totalMessagesSent += stat._count
        }
      } else if (stat.message_direction === 'inbound') {
        totalMessagesReceived += stat._count
      }
    })

    return {
      total_contacts: totalContacts,
      active_rooms: activeRooms,
      unassigned_rooms: unassignedRooms,
      total_messages_sent: totalMessagesSent,
      total_messages_received: totalMessagesReceived,
      failed_messages: failedMessages,
      last_activity: lastActivity?.last_message_at || null
    }
  }

  /**
   * Mark Telegram room as closed
   */
  static async closeRoom(room_uuid: string, user_uuid: string): Promise<void> {
    const telegramRoom = await prisma.v_telegram_chat_rooms.findFirst({
      where: {
        internal_room_uuid: room_uuid
      }
    })

    if (telegramRoom) {
      await prisma.v_telegram_chat_rooms.update({
        where: { telegram_room_uuid: telegramRoom.telegram_room_uuid },
        data: {
          room_status: 'closed',
          update_date: new Date(),
          update_user: user_uuid
        }
      })
    }
  }

  /**
   * Reopen Telegram room
   */
  static async reopenRoom(room_uuid: string, user_uuid: string): Promise<void> {
    const telegramRoom = await prisma.v_telegram_chat_rooms.findFirst({
      where: {
        internal_room_uuid: room_uuid
      }
    })

    if (telegramRoom) {
      await prisma.v_telegram_chat_rooms.update({
        where: { telegram_room_uuid: telegramRoom.telegram_room_uuid },
        data: {
          room_status: 'active',
          update_date: new Date(),
          update_user: user_uuid
        }
      })
    }
  }

  /**
   * Assign agent to Telegram room (enhanced version)
   */
  static async assignAgent(
    telegram_room_uuid: string,
    agent_uuid: string,
    assigned_by_user_uuid: string
  ): Promise<{
    success: boolean
    message: string
    agent_name?: string
    contact_name?: string
  }> {
    try {
      // Get the Telegram room with contact and internal room info
      const telegramRoom = await prisma.v_telegram_chat_rooms.findFirst({
        where: {
          telegram_room_uuid
        },
        include: {
          v_telegram_contacts: true,
          v_chat_rooms: true
        }
      })

      if (!telegramRoom || !telegramRoom.v_telegram_contacts || !telegramRoom.v_chat_rooms) {
        return {
          success: false,
          message: 'Telegram room not found'
        }
      }

      // Get agent information
      const agent = await prisma.v_users.findFirst({
        where: {
          user_uuid: agent_uuid
        },
        select: {
          user_uuid: true,
          username: true
        }
      })

      if (!agent) {
        return {
          success: false,
          message: 'Agent not found'
        }
      }

      const agentName = agent.username || agent.user_uuid

      const contactName = [telegramRoom.v_telegram_contacts.first_name, telegramRoom.v_telegram_contacts.last_name]
        .filter(Boolean).join(' ') || telegramRoom.v_telegram_contacts.username || `Telegram User ${telegramRoom.v_telegram_contacts.telegram_user_id}`

      // Update assignment in transaction
      await prisma.$transaction(async tx => {
        // Update Telegram room assignment
        await tx.v_telegram_chat_rooms.update({
          where: { telegram_room_uuid },
          data: {
            assigned_agent_uuid: agent_uuid,
            update_date: new Date(),
            update_user: assigned_by_user_uuid
          }
        })

        // Add agent as participant to internal room if not already present
        const existingParticipant = await tx.v_chat_room_participants.findFirst({
          where: {
            room_uuid: telegramRoom.internal_room_uuid,
            user_uuid: agent_uuid,
            deleted_at: null
          }
        })

        if (!existingParticipant) {
          await tx.v_chat_room_participants.create({
            data: {
              room_uuid: telegramRoom.internal_room_uuid,
              user_uuid: agent_uuid,
              participant_role: 'member' as any,
              insert_user: assigned_by_user_uuid,
              update_user: assigned_by_user_uuid,
              notification_settings: {
                mentions: true,
                messages: true,
                reactions: true
              }
            }
          })
        }

        // Create system message for assignment
        await tx.v_chat_messages.create({
          data: {
            room_uuid: telegramRoom.internal_room_uuid,
            author_uuid: null, // System message
            content: `Agent ${agentName} has been assigned to this Telegram conversation.`,
            message_type: 3, // System message type
            created_at: Math.floor(Date.now() / 1000),
            flags: 1 // System flag
          }
        })
      })

      return {
        success: true,
        message: `Agent ${agentName} assigned successfully`,
        agent_name: agentName,
        contact_name: contactName
      }
    } catch (error) {
      console.error('Error assigning agent to Telegram room:', error)

      return {
        success: false,
        message: 'Failed to assign agent'
      }
    }
  }

  /**
   * Get available agents for Telegram assignment
   */
  static async getAvailableAgents(domain_uuid: string) {
    const agents = await prisma.v_users.findMany({
      where: {
        domain_uuid,
        user_enabled: 'true'

        // Add any additional filters for agents (e.g., specific roles)
      },
      select: {
        user_uuid: true,
        username: true,
        user_email: true,
        contact_uuid: true
      },
      orderBy: [
        { username: 'asc' }
      ]
    })

    return agents.map(agent => ({
      user_uuid: agent.user_uuid,
      username: agent.username || agent.user_uuid,
      display_name: agent.username || agent.user_uuid,
      first_name: null,
      last_name: null
    }))
  }

  /**
   * Get failed message delivery attempts for retry
   */
  static async getFailedMessages(domain_uuid: string, limit: number = 50) {
    return await prisma.v_telegram_message_mapping.findMany({
      where: {
        domain_uuid,
        delivery_status: 'failed',
        message_direction: 'outbound'
      },
      include: {
        v_chat_messages: {
          select: {
            room_uuid: true,
            content: true,
            message_type: true,
            author_uuid: true
          }
        }
      },
      orderBy: { insert_date: 'asc' },
      take: limit
    })
  }

  /**
   * Retry failed message delivery
   */
  static async retryFailedMessage(mapping_uuid: string): Promise<boolean> {
    // Implementation would depend on your retry logic
    // This is a placeholder for the retry mechanism
    try {
      await prisma.v_telegram_message_mapping.update({
        where: { mapping_uuid },
        data: {
          delivery_status: 'pending',
          error_message: null
        }
      })

      return true
    } catch (error) {
      console.error('Failed to retry message:', error)

      return false
    }
  }

  /**
   * Get contact display name
   */
  private static getContactDisplayName(contact: any): string {
    return (
      [contact.first_name, contact.last_name].filter(Boolean).join(' ') ||
      contact.username ||
      `Telegram User ${contact.telegram_user_id}`
    )
  }

  /**
   * Validate Telegram room access for user
   */
  static async validateRoomAccess(room_uuid: string, user_uuid: string): Promise<boolean> {
    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid },
      select: { domain_uuid: true }
    })

    if (!user) {
      return false
    }

    // Check if room exists in user's domain
    if (!user.domain_uuid) {
      return false
    }

    const telegramRoom = await prisma.v_telegram_chat_rooms.findFirst({
      where: {
        internal_room_uuid: room_uuid,
        domain_uuid: user.domain_uuid
      }
    })

    return !!telegramRoom
  }
}
