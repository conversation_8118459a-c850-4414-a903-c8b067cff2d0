// Facebook Contact Cache Service
// Implements caching and fallback logic for Facebook user information
// Fixes Issue 3.1: Facebook Chat Missing User Names

import { prisma } from '@/libs/db/prisma'

export interface FacebookUser {
  id: string
  first_name?: string
  last_name?: string
  profile_pic?: string
  locale?: string
  timezone?: number
  gender?: string
  is_payment_enabled?: boolean
}

export interface CachedFacebookContact {
  facebook_user_id: string
  first_name: string | null
  last_name: string | null
  profile_pic: string | null
  locale: string | null
  timezone: number | null
  gender: string | null
  is_payment_enabled: boolean
  last_interaction_date: Date | null
  insert_date: Date | null
}

export class FacebookContactCache {
  private static instance: Facebook<PERSON>ontactCache
  private memoryCache: Map<string, CachedFacebookContact> = new Map()
  private readonly CACHE_TTL = 24 * 60 * 60 * 1000 // 24 hours in milliseconds
  private readonly API_RETRY_DELAY = 1000 // 1 second
  private readonly MAX_RETRIES = 3

  static getInstance(): FacebookContactCache {
    if (!FacebookContactCache.instance) {
      FacebookContactCache.instance = new FacebookContactCache()
    }

    return Facebook<PERSON>ontactCache.instance
  }

  /**
   * Get Facebook user information with caching and fallback
   */
  async getFacebookUserInfo(
    domainUuid: string, 
    facebookUserId: string
  ): Promise<FacebookUser | null> {
    try {
      // 1. Check memory cache first
      const memoryCached = this.getFromMemoryCache(facebookUserId)

      if (memoryCached && this.isCacheValid(memoryCached)) {
        console.log('Facebook user info retrieved from memory cache:', facebookUserId)
        
return this.convertToFacebookUser(memoryCached)
      }

      // 2. Check database cache
      const dbCached = await this.getFromDatabaseCache(domainUuid, facebookUserId)

      if (dbCached && this.isCacheValid(dbCached)) {
        // Update memory cache
        this.setMemoryCache(facebookUserId, dbCached)
        console.log('Facebook user info retrieved from database cache:', facebookUserId)
        
return this.convertToFacebookUser(dbCached)
      }

      // 3. Try to fetch from Facebook API with retry logic
      const apiResult = await this.fetchFromFacebookAPI(domainUuid, facebookUserId)

      if (apiResult) {
        // Cache the successful API result
        await this.cacheUserInfo(domainUuid, facebookUserId, apiResult)
        console.log('Facebook user info fetched from API and cached:', facebookUserId)
        
return apiResult
      }

      // 4. Fallback to any existing database record (even if expired)
      const fallbackData = await this.getFallbackFromDatabase(domainUuid, facebookUserId)

      if (fallbackData) {
        console.log('Facebook user info retrieved from fallback database record:', facebookUserId)

        return this.convertToFacebookUser(fallbackData)
      }

      // 5. Return null if no data available anywhere
      console.warn('No Facebook user info available for user:', facebookUserId)

      return null
    } catch (error) {
      console.error('Error in Facebook contact cache:', error)

      // Try fallback even on error
      const fallbackData = await this.getFallbackFromDatabase(domainUuid, facebookUserId)

      if (fallbackData) {
        console.log('Facebook user info retrieved from error fallback:', facebookUserId)

        return this.convertToFacebookUser(fallbackData)
      }

      return null
    }
  }

  /**
   * Get user info from memory cache
   */
  private getFromMemoryCache(facebookUserId: string): CachedFacebookContact | null {
    return this.memoryCache.get(facebookUserId) || null
  }

  /**
   * Set user info in memory cache
   */
  private setMemoryCache(facebookUserId: string, contact: CachedFacebookContact): void {
    this.memoryCache.set(facebookUserId, contact)
  }

  /**
   * Get user info from database cache
   */
  private async getFromDatabaseCache(
    domainUuid: string, 
    facebookUserId: string
  ): Promise<CachedFacebookContact | null> {
    try {
      const contact = await prisma.v_facebook_contacts.findFirst({
        where: {
          domain_uuid: domainUuid,
          facebook_user_id: facebookUserId,
          is_active: true
        },
        select: {
          facebook_user_id: true,
          first_name: true,
          last_name: true,
          profile_pic: true,
          locale: true,
          timezone: true,
          gender: true,
          is_payment_enabled: true,
          last_interaction_date: true,
          insert_date: true
        }
      })

      return contact ? {
        facebook_user_id: contact.facebook_user_id,
        first_name: contact.first_name,
        last_name: contact.last_name,
        profile_pic: contact.profile_pic,
        locale: contact.locale,
        timezone: contact.timezone,
        gender: contact.gender,
        is_payment_enabled: contact.is_payment_enabled || false,
        last_interaction_date: contact.last_interaction_date,
        insert_date: contact.insert_date
      } : null

    } catch (error) {
      console.error('Error fetching from database cache:', error)
      
return null
    }
  }

  /**
   * Fetch user info from Facebook API with retry logic
   */
  private async fetchFromFacebookAPI(
    domainUuid: string, 
    facebookUserId: string
  ): Promise<FacebookUser | null> {
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
      try {
        // Get page access token
        const config = await prisma.v_facebook_page_config.findFirst({
          where: {
            domain_uuid: domainUuid,
            is_active: true
          },
          select: { page_access_token: true }
        })

        if (!config?.page_access_token) {
          console.warn('Facebook page access token not found for domain:', domainUuid)
          
return null
        }

        // Make API request
        const response = await fetch(
          `https://graph.facebook.com/v18.0/${facebookUserId}?fields=first_name,last_name,profile_pic,locale,timezone,gender,is_payment_enabled&access_token=${config.page_access_token}`,
          {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )

        if (!response.ok) {
          throw new Error(`Facebook API error: ${response.status} ${response.statusText}`)
        }

        const userData = await response.json()

        if (userData.error) {
          throw new Error(`Facebook API error: ${userData.error.message}`)
        }

        return {
          id: facebookUserId,
          first_name: userData.first_name,
          last_name: userData.last_name,
          profile_pic: userData.profile_pic?.data?.url,
          locale: userData.locale,
          timezone: userData.timezone,
          gender: userData.gender,
          is_payment_enabled: userData.is_payment_enabled || false
        }

      } catch (error) {
        lastError = error as Error
        console.warn(`Facebook API attempt ${attempt} failed:`, error)

        if (attempt < this.MAX_RETRIES) {
          // Exponential backoff
          const delay = this.API_RETRY_DELAY * Math.pow(2, attempt - 1)

          console.log(`Retrying Facebook API call in ${delay}ms...`)
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }

    console.error('All Facebook API attempts failed:', lastError)
    
return null
  }

  /**
   * Cache user info in database
   */
  private async cacheUserInfo(
    domainUuid: string,
    facebookUserId: string,
    userInfo: FacebookUser
  ): Promise<void> {
    try {
      const now = new Date()

      const cachedContact: CachedFacebookContact = {
        facebook_user_id: facebookUserId,
        first_name: userInfo.first_name || null,
        last_name: userInfo.last_name || null,
        profile_pic: userInfo.profile_pic || null,
        locale: userInfo.locale || null,
        timezone: userInfo.timezone || null,
        gender: userInfo.gender || null,
        is_payment_enabled: userInfo.is_payment_enabled || false,
        last_interaction_date: now,
        insert_date: now
      }

      // Update database cache
      await prisma.v_facebook_contacts.upsert({
        where: {
          domain_uuid_facebook_user_id: {
            domain_uuid: domainUuid,
            facebook_user_id: facebookUserId
          }
        },
        update: {
          first_name: cachedContact.first_name,
          last_name: cachedContact.last_name,
          profile_pic: cachedContact.profile_pic,
          locale: cachedContact.locale,
          timezone: cachedContact.timezone,
          gender: cachedContact.gender,
          is_payment_enabled: cachedContact.is_payment_enabled,
          last_interaction_date: now,
          is_active: true
        },
        create: {
          domain_uuid: domainUuid,
          facebook_user_id: facebookUserId,
          first_name: cachedContact.first_name,
          last_name: cachedContact.last_name,
          profile_pic: cachedContact.profile_pic,
          locale: cachedContact.locale,
          timezone: cachedContact.timezone,
          gender: cachedContact.gender,
          is_payment_enabled: cachedContact.is_payment_enabled,
          last_interaction_date: now,
          is_active: true
        }
      })

      // Update memory cache
      this.setMemoryCache(facebookUserId, cachedContact)

    } catch (error) {
      console.error('Error caching Facebook user info:', error)
    }
  }

  /**
   * Get fallback data from database (even if expired)
   */
  private async getFallbackFromDatabase(
    domainUuid: string, 
    facebookUserId: string
  ): Promise<CachedFacebookContact | null> {
    try {
      const contact = await prisma.v_facebook_contacts.findFirst({
        where: {
          domain_uuid: domainUuid,
          facebook_user_id: facebookUserId
        },
        select: {
          facebook_user_id: true,
          first_name: true,
          last_name: true,
          profile_pic: true,
          locale: true,
          timezone: true,
          gender: true,
          is_payment_enabled: true,
          last_interaction_date: true,
          insert_date: true
        },
        orderBy: {
          last_interaction_date: 'desc'
        }
      })

      return contact ? {
        facebook_user_id: contact.facebook_user_id,
        first_name: contact.first_name,
        last_name: contact.last_name,
        profile_pic: contact.profile_pic,
        locale: contact.locale,
        timezone: contact.timezone,
        gender: contact.gender,
        is_payment_enabled: contact.is_payment_enabled || false,
        last_interaction_date: contact.last_interaction_date,
        insert_date: contact.insert_date
      } : null

    } catch (error) {
      console.error('Error fetching fallback data:', error)
      
return null
    }
  }

  /**
   * Check if cache is still valid
   */
  private isCacheValid(contact: CachedFacebookContact): boolean {
    if (!contact.last_interaction_date) return false

    // Consider cache valid if last interaction was within 24 hours
    const cacheExpiry = new Date(contact.last_interaction_date.getTime() + this.CACHE_TTL)

    return new Date() < cacheExpiry
  }

  /**
   * Convert cached contact to FacebookUser format
   */
  private convertToFacebookUser(contact: CachedFacebookContact): FacebookUser {
    return {
      id: contact.facebook_user_id,
      first_name: contact.first_name || undefined,
      last_name: contact.last_name || undefined,
      profile_pic: contact.profile_pic || undefined,
      locale: contact.locale || undefined,
      timezone: contact.timezone || undefined,
      gender: contact.gender || undefined,
      is_payment_enabled: contact.is_payment_enabled
    }
  }

  /**
   * Clear expired entries from memory cache
   */
  clearExpiredMemoryCache(): void {
    const now = new Date()

    for (const [key, contact] of this.memoryCache.entries()) {
      if (contact.last_interaction_date) {
        const cacheExpiry = new Date(contact.last_interaction_date.getTime() + this.CACHE_TTL)

        if (now > cacheExpiry) {
          this.memoryCache.delete(key)
        }
      }
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { memorySize: number; totalCached: number } {
    return {
      memorySize: this.memoryCache.size,
      totalCached: this.memoryCache.size
    }
  }
}

// Export singleton instance
export const facebookContactCache = FacebookContactCache.getInstance()
