// Facebook Contact Cache Tests
// Tests for Issue 3.1 fix: Facebook Chat Missing User Names

import { FacebookContactCache } from '../facebookContactCache'

// Mock Prisma
const mockPrisma = {
  v_facebook_page_config: {
    findFirst: jest.fn()
  },
  v_facebook_contacts: {
    findFirst: jest.fn(),
    upsert: jest.fn()
  }
}

jest.mock('@/libs/db/prisma', () => ({
  prisma: mockPrisma
}))

// Mock fetch
global.fetch = jest.fn()

describe('FacebookContactCache', () => {
  let cache: FacebookContactCache

  beforeEach(() => {
    cache = FacebookContactCache.getInstance()
    cache.clearExpiredMemoryCache()
    jest.clearAllMocks()
    jest.clearAllTimers()
    jest.useFakeTimers()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  describe('Cache Hierarchy', () => {
    it('should return data from memory cache first', async () => {
      const domainUuid = 'test-domain'
      const facebookUserId = 'fb-user-123'
      
      // Set up memory cache with valid data
      const cachedContact = {
        facebook_user_id: facebookUserId,
        first_name: 'John',
        last_name: 'Doe',
        profile_pic: null,
        locale: null,
        timezone: null,
        gender: null,
        is_payment_enabled: false,
        last_api_fetch: new Date(),
        cache_expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
      }

      // Manually set memory cache
      ;(cache as any).setMemoryCache(facebookUserId, cachedContact)

      const result = await cache.getFacebookUserInfo(domainUuid, facebookUserId)

      expect(result).toEqual({
        first_name: 'John',
        last_name: 'Doe',
        profile_pic: undefined,
        locale: undefined,
        timezone: undefined,
        gender: undefined,
        is_payment_enabled: false
      })

      // Should not call database or API
      expect(mockPrisma.v_facebook_contacts.findFirst).not.toHaveBeenCalled()
      expect(fetch).not.toHaveBeenCalled()
    })

    it('should fallback to database cache when memory cache is expired', async () => {
      const domainUuid = 'test-domain'
      const facebookUserId = 'fb-user-123'

      // Mock database response
      mockPrisma.v_facebook_contacts.findFirst.mockResolvedValueOnce({
        facebook_user_id: facebookUserId,
        first_name: 'Jane',
        last_name: 'Smith',
        profile_pic: 'https://example.com/pic.jpg',
        locale: 'en_US',
        timezone: -5,
        gender: 'female',
        is_payment_enabled: true,
        last_api_fetch: new Date(),
        cache_expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000)
      })

      const result = await cache.getFacebookUserInfo(domainUuid, facebookUserId)

      expect(result).toEqual({
        first_name: 'Jane',
        last_name: 'Smith',
        profile_pic: 'https://example.com/pic.jpg',
        locale: 'en_US',
        timezone: -5,
        gender: 'female',
        is_payment_enabled: true
      })

      expect(mockPrisma.v_facebook_contacts.findFirst).toHaveBeenCalledWith({
        where: {
          domain_uuid: domainUuid,
          facebook_user_id: facebookUserId,
          is_active: true
        },
        select: expect.any(Object)
      })
    })

    it('should fetch from API when cache is expired', async () => {
      const domainUuid = 'test-domain'
      const facebookUserId = 'fb-user-123'

      // Mock expired database cache
      mockPrisma.v_facebook_contacts.findFirst.mockResolvedValueOnce({
        facebook_user_id: facebookUserId,
        first_name: 'Old',
        last_name: 'Data',
        profile_pic: null,
        locale: null,
        timezone: null,
        gender: null,
        is_payment_enabled: false,
        last_api_fetch: new Date(Date.now() - 48 * 60 * 60 * 1000), // 48 hours ago
        cache_expires_at: new Date(Date.now() - 24 * 60 * 60 * 1000) // Expired 24 hours ago
      })

      // Mock API config
      mockPrisma.v_facebook_page_config.findFirst.mockResolvedValueOnce({
        page_access_token: 'test-token'
      })

      // Mock API response
      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          first_name: 'Fresh',
          last_name: 'Data',
          profile_pic: { data: { url: 'https://example.com/fresh.jpg' } },
          locale: 'en_US',
          timezone: -8,
          gender: 'male',
          is_payment_enabled: true
        })
      })

      // Mock upsert
      mockPrisma.v_facebook_contacts.upsert.mockResolvedValueOnce({})

      const result = await cache.getFacebookUserInfo(domainUuid, facebookUserId)

      expect(result).toEqual({
        first_name: 'Fresh',
        last_name: 'Data',
        profile_pic: 'https://example.com/fresh.jpg',
        locale: 'en_US',
        timezone: -8,
        gender: 'male',
        is_payment_enabled: true
      })

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining(`https://graph.facebook.com/v18.0/${facebookUserId}`),
        expect.any(Object)
      )
      expect(mockPrisma.v_facebook_contacts.upsert).toHaveBeenCalled()
    })

    it('should fallback to any database record when API fails', async () => {
      const domainUuid = 'test-domain'
      const facebookUserId = 'fb-user-123'

      // Mock no valid cache
      mockPrisma.v_facebook_contacts.findFirst
        .mockResolvedValueOnce(null) // No active cache
        .mockResolvedValueOnce({ // Fallback data
          facebook_user_id: facebookUserId,
          first_name: 'Fallback',
          last_name: 'User',
          profile_pic: null,
          locale: null,
          timezone: null,
          gender: null,
          is_payment_enabled: false,
          last_api_fetch: new Date(Date.now() - 48 * 60 * 60 * 1000),
          cache_expires_at: new Date(Date.now() - 24 * 60 * 60 * 1000)
        })

      // Mock API config
      mockPrisma.v_facebook_page_config.findFirst.mockResolvedValueOnce({
        page_access_token: 'test-token'
      })

      // Mock API failure
      ;(fetch as jest.Mock).mockRejectedValue(new Error('API Error'))

      const result = await cache.getFacebookUserInfo(domainUuid, facebookUserId)

      expect(result).toEqual({
        first_name: 'Fallback',
        last_name: 'User',
        profile_pic: undefined,
        locale: undefined,
        timezone: undefined,
        gender: undefined,
        is_payment_enabled: false
      })
    })
  })

  describe('API Retry Logic', () => {
    it('should retry API calls with exponential backoff', async () => {
      const domainUuid = 'test-domain'
      const facebookUserId = 'fb-user-123'

      // Mock no cache
      mockPrisma.v_facebook_contacts.findFirst.mockResolvedValue(null)

      // Mock API config
      mockPrisma.v_facebook_page_config.findFirst.mockResolvedValue({
        page_access_token: 'test-token'
      })

      // Mock API failures then success
      ;(fetch as jest.Mock)
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Timeout'))
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            first_name: 'Retry',
            last_name: 'Success'
          })
        })

      // Mock upsert
      mockPrisma.v_facebook_contacts.upsert.mockResolvedValue({})

      const result = await cache.getFacebookUserInfo(domainUuid, facebookUserId)

      expect(result).toEqual({
        first_name: 'Retry',
        last_name: 'Success',
        profile_pic: undefined,
        locale: undefined,
        timezone: undefined,
        gender: undefined,
        is_payment_enabled: false
      })

      // Should have made 3 attempts
      expect(fetch).toHaveBeenCalledTimes(3)
    })

    it('should give up after max retries and use fallback', async () => {
      const domainUuid = 'test-domain'
      const facebookUserId = 'fb-user-123'

      // Mock no cache but fallback data
      mockPrisma.v_facebook_contacts.findFirst
        .mockResolvedValueOnce(null) // No active cache
        .mockResolvedValueOnce({ // Fallback
          facebook_user_id: facebookUserId,
          first_name: 'Fallback',
          last_name: 'After',
          profile_pic: null,
          locale: null,
          timezone: null,
          gender: null,
          is_payment_enabled: false,
          last_api_fetch: new Date(),
          cache_expires_at: new Date()
        })

      // Mock API config
      mockPrisma.v_facebook_page_config.findFirst.mockResolvedValue({
        page_access_token: 'test-token'
      })

      // Mock all API calls fail
      ;(fetch as jest.Mock).mockRejectedValue(new Error('Persistent error'))

      const result = await cache.getFacebookUserInfo(domainUuid, facebookUserId)

      expect(result).toEqual({
        first_name: 'Fallback',
        last_name: 'After',
        profile_pic: undefined,
        locale: undefined,
        timezone: undefined,
        gender: undefined,
        is_payment_enabled: false
      })

      // Should have made 3 attempts
      expect(fetch).toHaveBeenCalledTimes(3)
    })
  })

  describe('Cache Management', () => {
    it('should provide cache statistics', () => {
      const stats = cache.getCacheStats()
      
      expect(stats).toHaveProperty('memorySize')
      expect(stats).toHaveProperty('totalCached')
      expect(typeof stats.memorySize).toBe('number')
      expect(typeof stats.totalCached).toBe('number')
    })

    it('should clear expired memory cache entries', () => {
      const facebookUserId = 'fb-user-123'
      
      // Add expired entry
      const expiredContact = {
        facebook_user_id: facebookUserId,
        first_name: 'Expired',
        last_name: 'User',
        profile_pic: null,
        locale: null,
        timezone: null,
        gender: null,
        is_payment_enabled: false,
        last_api_fetch: new Date(),
        cache_expires_at: new Date(Date.now() - 1000) // Expired 1 second ago
      }

      ;(cache as any).setMemoryCache(facebookUserId, expiredContact)
      
      let stats = cache.getCacheStats()

      expect(stats.memorySize).toBe(1)

      cache.clearExpiredMemoryCache()
      
      stats = cache.getCacheStats()
      expect(stats.memorySize).toBe(0)
    })
  })

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = FacebookContactCache.getInstance()
      const instance2 = FacebookContactCache.getInstance()
      
      expect(instance1).toBe(instance2)
    })
  })
})
