// Facebook Messenger Message Service
// Handles inbound/outbound message processing following clean code guidelines

import { prisma } from '@/libs/db/prisma'
import type {
  FacebookMessagingEvent,
  FacebookContact,
  FacebookChatRoom,
  FacebookProcessedMessage,
  FacebookUser
} from '@/types/social/facebookTypes'

// Removed unused type imports
import { verifyWebhookSignature, mapMessageType, retryWithBackoff } from '@/utils/social/platformUtils'
import {
  createInternalChatRoom,
  createInternalMessage,
  updateRoomLastActivity,
  createMessageNotification,
  validateMessageContent,
  safeBigIntToString
} from '@/utils/social/messageUtils'

export class FacebookMessageService {
  /**
   * Verify Facebook webhook signature for security
   */
  static async verifyWebhookSignature(domainUuid: string, payload: string, signature: string | null): Promise<boolean> {
    if (!signature) {
      console.error('No signature provided for Facebook webhook')

      return false
    }

    try {
      // Get Facebook configuration for this domain
      const config = await prisma.v_facebook_page_config.findFirst({
        where: {
          domain_uuid: domainUuid,
          is_active: true
        },
        select: { app_secret: true }
      })

      if (!config?.app_secret) {
        console.error('Facebook app secret not found for domain:', domainUuid)

        return false
      }

      return verifyWebhookSignature(payload, signature, config.app_secret, 'sha256')
    } catch (error) {
      console.error('Facebook webhook signature verification failed:', error)

      return false
    }
  }

  /**
   * Process inbound Facebook message and create internal chat message
   */
  static async processInboundMessage(
    domainUuid: string,
    messagingEvent: FacebookMessagingEvent
  ): Promise<FacebookProcessedMessage> {
    console.log('=== FACEBOOK MESSAGE PROCESSING START ===')
    console.log('Domain:', domainUuid)
    console.log('Sender ID:', messagingEvent.sender.id)
    console.log('Message:', messagingEvent.message?.text || '[no text]')

    try {
      // Validate message content
      if (!messagingEvent.message?.text && !messagingEvent.message?.attachments) {
        throw new Error('No message content or attachments')
      }

      // Find or create Facebook contact
      console.log('Step 1: Finding or creating contact...')
      const contact = await this.findOrCreateContact(domainUuid, messagingEvent.sender.id)

      console.log('Contact created/found:', contact.contact_uuid, contact.first_name)

      // Find or create room bridge
      console.log('Step 2: Finding or creating room bridge...')
      const facebookRoom = await this.findOrCreateRoom(domainUuid, contact, messagingEvent.sender.id)

      console.log('Room created/found:', facebookRoom.facebook_room_uuid, facebookRoom.internal_room_uuid)

      // Create internal message
      console.log('Step 3: Creating internal message...')
      const messageContent = messagingEvent.message.text || '[Attachment]'

      const messageType = mapMessageType(messagingEvent.message.attachments ? 'file' : 'text', 'facebook')

      const internalMessage = await createInternalMessage(
        facebookRoom.internal_room_uuid,
        null, // External messages don't have internal author
        messageContent,
        messageType,
        undefined,
        4 // External message flag
      )

      console.log('Internal message created:', internalMessage.message_id.toString())

      // Create message mapping
      console.log('Step 4: Creating message mapping...')
      await this.createMessageMapping(domainUuid, internalMessage.message_id, messagingEvent, 'inbound')
      console.log('Message mapping created successfully')

      // Update room last activity
      await updateRoomLastActivity(facebookRoom.internal_room_uuid)

      // Create notification
      await createMessageNotification(facebookRoom.internal_room_uuid, internalMessage.message_id)

      const result = {
        internal_message_id: safeBigIntToString(internalMessage.message_id) || '0',
        room_uuid: facebookRoom.internal_room_uuid,
        contact_uuid: contact.contact_uuid,
        facebook_message_id: messagingEvent.message.mid,
        delivery_status: 'delivered'
      }

      console.log('=== FACEBOOK MESSAGE PROCESSING COMPLETE ===')
      console.log('Result:', result)
      console.log('============================================')

      return result
    } catch (error) {
      console.error('=== FACEBOOK MESSAGE PROCESSING ERROR ===')
      console.error('Error processing Facebook message:', error)
      console.error('Domain UUID:', domainUuid)
      console.error('Sender ID:', messagingEvent.sender.id)
      throw error
    }
  }

  /**
   * Process outbound message from internal chat to Facebook
   */
  static async processOutboundMessage(
    domainUuid: string,
    roomUuid: string,
    authorUuid: string,
    content: string,
    messageType: string = 'text'
  ): Promise<FacebookProcessedMessage> {
    try {
      // Validate message content
      const validation = validateMessageContent(content)

      if (!validation.valid) {
        throw new Error(validation.error)
      }

      // Find Facebook room bridge
      const facebookRoom = await prisma.v_facebook_chat_rooms.findFirst({
        where: {
          domain_uuid: domainUuid,
          internal_room_uuid: roomUuid
        },
        include: {
          v_facebook_contacts: true
        }
      })

      if (!facebookRoom) {
        throw new Error('Facebook room bridge not found')
      }

      // Create internal message first
      const internalMessage = await createInternalMessage(
        roomUuid,
        authorUuid,
        content,
        mapMessageType(messageType, 'facebook'),
        undefined,
        0 // Regular outbound message
      )

      // Send to Facebook API
      console.log('=== SENDING TO FACEBOOK ===')
      console.log('Contact info:', {
        facebook_user_id: facebookRoom.v_facebook_contacts.facebook_user_id,
        first_name: facebookRoom.v_facebook_contacts.first_name,
        last_name: facebookRoom.v_facebook_contacts.last_name
      })
      console.log('Sending to user_id:', facebookRoom.v_facebook_contacts.facebook_user_id)
      console.log('Message content:', content.trim())

      const facebookResponse = await this.sendToFacebookApi(
        domainUuid,
        facebookRoom.v_facebook_contacts.facebook_user_id,
        content.trim()
      )

      // Create message mapping
      await this.createMessageMapping(
        domainUuid,
        internalMessage.message_id,
        {
          sender: { id: 'agent' },
          recipient: { id: facebookRoom.v_facebook_contacts.facebook_user_id },
          timestamp: Date.now(),
          message: {
            mid: facebookResponse.facebook_message_id || '',
            text: content
          }
        },
        'outbound',
        facebookResponse.success ? 'sent' : 'failed',
        facebookResponse.error
      )

      // Update room last activity
      await updateRoomLastActivity(roomUuid, authorUuid)

      return {
        internal_message_id: safeBigIntToString(internalMessage.message_id) || '0',
        room_uuid: roomUuid,
        contact_uuid: facebookRoom.facebook_contact_uuid,
        facebook_message_id: facebookResponse.facebook_message_id || '',
        delivery_status: facebookResponse.success ? 'sent' : 'failed'
      }
    } catch (error) {
      console.error('Error processing outbound Facebook message:', error)
      throw error
    }
  }

  /**
   * Find or create Facebook contact
   */
  private static async findOrCreateContact(domainUuid: string, facebookUserId: string): Promise<FacebookContact> {
    try {
      // Try to find existing contact
      let contact = await prisma.v_facebook_contacts.findFirst({
        where: {
          domain_uuid: domainUuid,
          facebook_user_id: facebookUserId
        }
      })

      if (!contact) {
        // Get user info from Facebook API
        const userInfo = await this.getFacebookUserInfo(domainUuid, facebookUserId)

        // Create new contact
        contact = await prisma.v_facebook_contacts.create({
          data: {
            domain_uuid: domainUuid,
            facebook_user_id: facebookUserId,
            first_name: userInfo?.first_name || null,
            last_name: userInfo?.last_name || null,
            profile_pic: userInfo?.profile_pic || null,
            locale: userInfo?.locale || null,
            timezone: userInfo?.timezone || null,
            gender: userInfo?.gender || null,
            is_payment_enabled: userInfo?.is_payment_enabled || false,
            is_active: true,
            last_interaction_date: new Date(),
            contact_info: (userInfo || {}) as any,
            insert_date: new Date(),
            update_date: new Date()
          }
        })
      } else {
        // Update last interaction date
        contact = await prisma.v_facebook_contacts.update({
          where: { contact_uuid: contact.contact_uuid },
          data: {
            last_interaction_date: new Date(),
            is_active: true,
            update_date: new Date()
          }
        })
      }

      return contact as FacebookContact
    } catch (error) {
      console.error('Error finding/creating Facebook contact:', error)
      throw error
    }
  }

  /**
   * Find or create Facebook chat room bridge
   */
  private static async findOrCreateRoom(
    domainUuid: string,
    contact: FacebookContact,
    facebookUserId: string
  ): Promise<FacebookChatRoom> {
    try {
      // Try to find existing room
      let facebookRoom = await prisma.v_facebook_chat_rooms.findFirst({
        where: {
          domain_uuid: domainUuid,
          facebook_contact_uuid: contact.contact_uuid
        }
      })

      if (!facebookRoom) {
        // Create internal chat room first
        const internalRoom = await createInternalChatRoom(
          domainUuid,
          `Facebook Chat - ${contact.first_name || facebookUserId}`,
          `Facebook Messenger conversation with ${contact.first_name || facebookUserId}`,
          'facebook',
          facebookUserId
        )

        // Create Facebook chat room bridge
        facebookRoom = await prisma.v_facebook_chat_rooms.create({
          data: {
            domain_uuid: domainUuid,
            internal_room_uuid: internalRoom.room_uuid,
            facebook_contact_uuid: contact.contact_uuid,
            room_status: 'active',
            last_message_at: new Date(),
            conversation_metadata: {
              facebook_user_id: facebookUserId,
              created_from_webhook: true
            },
            insert_date: new Date(),
            update_date: new Date()
          }
        })
      } else {
        // Update last message time
        facebookRoom = await prisma.v_facebook_chat_rooms.update({
          where: { facebook_room_uuid: facebookRoom.facebook_room_uuid },
          data: {
            last_message_at: new Date(),
            update_date: new Date()
          }
        })
      }

      return facebookRoom as FacebookChatRoom
    } catch (error) {
      console.error('Error finding/creating Facebook chat room:', error)
      throw error
    }
  }

  /**
   * Create message mapping for tracking
   */
  private static async createMessageMapping(
    domainUuid: string,
    internalMessageId: bigint,
    messagingEvent: FacebookMessagingEvent,
    direction: 'inbound' | 'outbound',
    deliveryStatus: string = 'pending',
    errorMessage?: string
  ): Promise<void> {
    try {
      await prisma.v_facebook_message_mapping.create({
        data: {
          domain_uuid: domainUuid,
          internal_message_id: internalMessageId,
          facebook_message_id: messagingEvent.message?.mid || null,
          facebook_user_id: messagingEvent.sender.id,
          message_direction: direction,
          facebook_event_type: messagingEvent.message ? 'message' : 'unknown',
          delivery_status: deliveryStatus,
          error_message: errorMessage || null,
          message_metadata: {
            timestamp: messagingEvent.timestamp,
            recipient_id: messagingEvent.recipient.id
          },
          retry_count: 0,
          insert_date: new Date()
        }
      })
    } catch (error) {
      console.error('Error creating Facebook message mapping:', error)
      throw error
    }
  }

  /**
   * Get Facebook user information
   */
  private static async getFacebookUserInfo(domainUuid: string, facebookUserId: string): Promise<FacebookUser | null> {
    try {
      // Use the new caching system with fallback logic
      const { facebookContactCache } = await import('./facebookContactCache')

      return await facebookContactCache.getFacebookUserInfo(domainUuid, facebookUserId)
    } catch (error) {
      console.error('Error fetching Facebook user info:', error)

      return null
    }
  }

  /**
   * Send message to Facebook API
   */
  private static async sendToFacebookApi(
    domainUuid: string,
    facebookUserId: string,
    content: string
  ): Promise<{ success: boolean; facebook_message_id?: string; error?: string }> {
    console.log('=== FACEBOOK API SEND MESSAGE START ===')
    console.log('Domain UUID:', domainUuid)
    console.log('Facebook User ID:', facebookUserId)
    console.log('Content:', content.substring(0, 100) + (content.length > 100 ? '...' : ''))

    try {
      // Get page access token
      const config = await prisma.v_facebook_page_config.findFirst({
        where: {
          domain_uuid: domainUuid,
          is_active: true
        },
        select: { page_access_token: true, page_id: true }
      })

      console.log('Facebook config found:', {
        has_access_token: !!config?.page_access_token,
        page_id: config?.page_id,
        token_length: config?.page_access_token?.length || 0
      })

      if (!config?.page_access_token) {
        console.error('Facebook page access token not found for domain:', domainUuid)
        throw new Error('Facebook page access token not found')
      }

      const payload = {
        recipient: {
          id: facebookUserId
        },
        message: {
          text: content
        }
      }

      console.log('Preparing Facebook API call:', {
        endpoint: 'https://graph.facebook.com/v18.0/me/messages',
        recipient_id: facebookUserId,
        message_length: content.length,
        payload
      })

      const response = await retryWithBackoff(async () => {
        console.log('Making HTTP request to Facebook API...')

        const res = await fetch('https://graph.facebook.com/v18.0/me/messages', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            ...payload,
            access_token: config.page_access_token
          })
        })

        console.log('Facebook API HTTP response status:', res.status, res.statusText)

        if (!res.ok) {
          const errorText = await res.text()
          let errorData

          try {
            errorData = JSON.parse(errorText)
          } catch {
            errorData = { message: errorText }
          }

          console.error('Facebook API HTTP error details:', {
            status: res.status,
            statusText: res.statusText,
            errorData,
            headers: Object.fromEntries(res.headers.entries())
          })

          throw new Error(`Facebook API HTTP error: ${res.status} ${res.statusText} - ${errorData.error?.message || errorText}`)
        }

        const jsonResponse = await res.json()

        console.log('Facebook API JSON response:', jsonResponse)

        return jsonResponse
      })

      const result = {
        success: true,
        facebook_message_id: response.message_id
      }

      console.log('=== FACEBOOK API SEND MESSAGE SUCCESS ===')
      console.log('Success result:', result)

      return result
    } catch (error) {
      console.error('=== FACEBOOK API SEND MESSAGE ERROR ===')
      console.error('Error sending message to Facebook API:', {
        name: (error as Error)?.name,
        message: (error as Error)?.message,
        stack: (error as Error)?.stack,
        domainUuid,
        facebookUserId
      })

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred while sending to Facebook'
      }
    }
  }
}
