// Facebook Integration Service
// Service for managing Facebook chat rooms, contacts, and statistics for internal chat

import { prisma } from '@/libs/db/prisma'
import { RoomType } from '@/types/apps/internal-chat/chatTypes'

export interface FacebookRoomInfo {
  facebook_room_uuid: string
  internal_room_uuid: string
  facebook_contact_uuid: string
  assigned_agent_uuid: string | null
  room_status: string
  last_message_at: Date | null
  conversation_metadata: Record<string, any>
  contact: {
    contact_uuid: string
    facebook_user_id: string
    first_name: string | null
    last_name: string | null
    profile_pic: string | null
    locale: string | null
    timezone: number | null
    is_active: boolean
    last_interaction_date: Date | null
  }
  chat_room: {
    room_uuid: string
    room_name: string | null
    room_type: RoomType
    is_active: boolean
    is_archived: boolean
  }
  is_assigned: boolean
}

export interface FacebookContactInfo {
  contact_uuid: string
  facebook_user_id: string
  first_name: string | null
  last_name: string | null
  profile_pic: string | null
  locale: string | null
  timezone: number | null
  is_active: boolean
  last_interaction_date: Date | null
  contact_info: Record<string, any>
  has_room: boolean
  room_uuid?: string
}

export interface FacebookStats {
  total_contacts: number
  active_rooms: number
  unassigned_rooms: number
  total_messages_sent: number
  total_messages_received: number
  failed_messages: number
  last_activity: Date | null
}

export class FacebookIntegrationService {
  /**
   * Get Facebook rooms for internal chat integration
   */
  static async getFacebookRoomsForDomain(domain_uuid: string): Promise<FacebookRoomInfo[]> {
    const facebookRooms = await prisma.v_facebook_chat_rooms.findMany({
      where: {
        domain_uuid,
        room_status: 'active'
      },
      include: {
        v_facebook_contacts: true,
        v_chat_rooms: {
          select: {
            room_uuid: true,
            room_name: true,
            room_type: true,
            is_active: true,
            is_archived: true
          }
        }
      },
      orderBy: { last_message_at: 'desc' }
    })

    return facebookRooms.map(room => ({
      facebook_room_uuid: room.facebook_room_uuid,
      internal_room_uuid: room.internal_room_uuid,
      facebook_contact_uuid: room.facebook_contact_uuid,
      assigned_agent_uuid: room.assigned_agent_uuid,
      room_status: room.room_status || 'active',
      last_message_at: room.last_message_at,
      conversation_metadata: (room.conversation_metadata as Record<string, any>) || {},
      contact: {
        contact_uuid: room.v_facebook_contacts.contact_uuid,
        facebook_user_id: room.v_facebook_contacts.facebook_user_id,
        first_name: room.v_facebook_contacts.first_name,
        last_name: room.v_facebook_contacts.last_name,
        profile_pic: room.v_facebook_contacts.profile_pic,
        locale: room.v_facebook_contacts.locale,
        timezone: room.v_facebook_contacts.timezone,
        is_active: room.v_facebook_contacts.is_active || false,
        last_interaction_date: room.v_facebook_contacts.last_interaction_date
      },
      chat_room: {
        room_uuid: room.v_chat_rooms.room_uuid,
        room_name: room.v_chat_rooms.room_name,
        room_type: room.v_chat_rooms.room_type as RoomType,
        is_active: room.v_chat_rooms.is_active || false,
        is_archived: room.v_chat_rooms.is_archived || false
      },
      is_assigned: !!room.assigned_agent_uuid
    }))
  }

  /**
   * Get Facebook contacts for domain with optional filtering
   */
  static async getFacebookContactsForDomain(
    domain_uuid: string,
    options: {
      search?: string
      limit?: number
      offset?: number
      is_active?: boolean
    } = {}
  ): Promise<FacebookContactInfo[]> {
    const { search, limit = 50, offset = 0, is_active } = options

    const whereClause: any = {
      domain_uuid
    }

    if (search) {
      whereClause.OR = [
        { first_name: { contains: search, mode: 'insensitive' } },
        { last_name: { contains: search, mode: 'insensitive' } },
        { facebook_user_id: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (typeof is_active === 'boolean') {
      whereClause.is_active = is_active
    }

    const contacts = await prisma.v_facebook_contacts.findMany({
      where: whereClause,
      include: {
        v_facebook_chat_rooms: {
          select: {
            internal_room_uuid: true
          }
        }
      },
      orderBy: [{ last_interaction_date: 'desc' }, { insert_date: 'desc' }],
      take: limit,
      skip: offset
    })

    return contacts.map(contact => ({
      contact_uuid: contact.contact_uuid,
      facebook_user_id: contact.facebook_user_id,
      first_name: contact.first_name,
      last_name: contact.last_name,
      profile_pic: contact.profile_pic,
      locale: contact.locale,
      timezone: contact.timezone,
      is_active: contact.is_active || false,
      last_interaction_date: contact.last_interaction_date,
      contact_info: (contact.contact_info as Record<string, any>) || {},
      has_room: contact.v_facebook_chat_rooms.length > 0,
      room_uuid: contact.v_facebook_chat_rooms[0]?.internal_room_uuid
    }))
  }

  /**
   * Get Facebook integration statistics
   */
  static async getFacebookStats(domain_uuid: string): Promise<FacebookStats> {
    const [
      contactsCount,
      activeRoomsCount,
      unassignedRoomsCount,
      messagesSent,
      messagesReceived,
      failedMessages,
      lastActivity
    ] = await Promise.all([
      // Total contacts
      prisma.v_facebook_contacts.count({
        where: { domain_uuid, is_active: true }
      }),

      // Active rooms
      prisma.v_facebook_chat_rooms.count({
        where: { domain_uuid, room_status: 'active' }
      }),

      // Unassigned rooms (rooms without participants)
      prisma.v_facebook_chat_rooms.count({
        where: {
          domain_uuid,
          room_status: 'active',
          v_chat_rooms: {
            v_chat_room_participants: {
              none: {
                deleted_at: null
              }
            }
          }
        }
      }),

      // Messages sent (outbound)
      prisma.v_facebook_message_mapping.count({
        where: {
          domain_uuid,
          message_direction: 'outbound',
          delivery_status: 'sent'
        }
      }),

      // Messages received (inbound)
      prisma.v_facebook_message_mapping.count({
        where: {
          domain_uuid,
          message_direction: 'inbound'
        }
      }),

      // Failed messages
      prisma.v_facebook_message_mapping.count({
        where: {
          domain_uuid,
          delivery_status: 'failed'
        }
      }),

      // Last activity
      prisma.v_facebook_chat_rooms.findFirst({
        where: { domain_uuid },
        orderBy: { last_message_at: 'desc' },
        select: { last_message_at: true }
      })
    ])

    return {
      total_contacts: contactsCount,
      active_rooms: activeRoomsCount,
      unassigned_rooms: unassignedRoomsCount,
      total_messages_sent: messagesSent,
      total_messages_received: messagesReceived,
      failed_messages: failedMessages,
      last_activity: lastActivity?.last_message_at || null
    }
  }

  /**
   * Create a room for a Facebook contact
   */
  static async createRoomForContact(domain_uuid: string, contact_uuid: string): Promise<string | null> {
    try {
      // Get contact details
      const contact = await prisma.v_facebook_contacts.findFirst({
        where: {
          contact_uuid,
          domain_uuid
        }
      })

      if (!contact) {
        throw new Error('Facebook contact not found')
      }

      // Check if room already exists
      const existingRoom = await prisma.v_facebook_chat_rooms.findFirst({
        where: {
          domain_uuid,
          facebook_contact_uuid: contact_uuid
        }
      })

      if (existingRoom) {
        return existingRoom.internal_room_uuid
      }

      const displayName = [contact.first_name, contact.last_name].filter(Boolean).join(' ') || 
                         `Facebook User ${contact.facebook_user_id}`

      // Create internal room and Facebook bridge in transaction
      const result = await prisma.$transaction(async tx => {
        // Create internal room
        const internalRoom = await tx.v_chat_rooms.create({
          data: {
            domain_uuid,
            room_name: `Facebook: ${displayName}`,
            room_description: `Facebook conversation with ${displayName}`,
            room_type: RoomType.DIRECT,
            created_by_user_uuid: 'system', // System-created room
            room_settings: {
              platform: 'facebook',
              facebook_user_id: contact.facebook_user_id,
              auto_created: false
            },
            insert_date: new Date(),
            insert_user: 'system',
            update_date: new Date(),
            update_user: 'system'
          }
        })

        // Create Facebook chat room bridge
        await tx.v_facebook_chat_rooms.create({
          data: {
            domain_uuid,
            internal_room_uuid: internalRoom.room_uuid,
            facebook_contact_uuid: contact_uuid,
            room_status: 'active',
            last_message_at: new Date(),
            conversation_metadata: {
              facebook_user_id: contact.facebook_user_id,
              created_manually: true
            }
          }
        })

        return internalRoom.room_uuid
      })

      return result
    } catch (error) {
      console.error('Error creating Facebook room:', error)

      return null
    }
  }

  /**
   * Get available agents for Facebook assignment
   */
  static async getAvailableAgents(domain_uuid: string) {
    const agents = await prisma.v_users.findMany({
      where: {
        domain_uuid,
        user_enabled: 'true'

        // Add any additional filters for agents (e.g., specific roles)
      },
      select: {
        user_uuid: true,
        username: true,
        user_email: true,
        contact_uuid: true
      },
      orderBy: [
        { username: 'asc' }
      ]
    })

    return agents.map(agent => ({
      user_uuid: agent.user_uuid,
      username: agent.username || agent.user_uuid,
      display_name: agent.username || agent.user_uuid,
      first_name: null,
      last_name: null
    }))
  }

  /**
   * Assign agent to Facebook room (enhanced version)
   */
  static async assignAgent(
    facebook_room_uuid: string,
    agent_uuid: string,
    assigned_by_user_uuid: string
  ): Promise<{
    success: boolean
    message: string
    agent_name?: string
    contact_name?: string
  }> {
    try {
      // Get the Facebook room with contact and internal room info
      const facebookRoom = await prisma.v_facebook_chat_rooms.findFirst({
        where: {
          facebook_room_uuid
        },
        include: {
          v_facebook_contacts: true,
          v_chat_rooms: true
        }
      })

      if (!facebookRoom || !facebookRoom.v_facebook_contacts || !facebookRoom.v_chat_rooms) {
        return {
          success: false,
          message: 'Facebook room not found'
        }
      }

      // Get agent information
      const agent = await prisma.v_users.findFirst({
        where: {
          user_uuid: agent_uuid
        },
        select: {
          user_uuid: true,
          username: true
        }
      })

      if (!agent) {
        return {
          success: false,
          message: 'Agent not found'
        }
      }

      const agentName = agent.username || agent.user_uuid

      const contactName = [facebookRoom.v_facebook_contacts.first_name, facebookRoom.v_facebook_contacts.last_name]
        .filter(Boolean).join(' ') || `Facebook User ${facebookRoom.v_facebook_contacts.facebook_user_id}`

      // Update assignment in transaction
      await prisma.$transaction(async tx => {
        // Update Facebook room assignment
        await tx.v_facebook_chat_rooms.update({
          where: { facebook_room_uuid },
          data: {
            assigned_agent_uuid: agent_uuid,
            update_date: new Date()
          }
        })

        // Add agent as participant to internal room if not already present
        const existingParticipant = await tx.v_chat_room_participants.findFirst({
          where: {
            room_uuid: facebookRoom.internal_room_uuid,
            user_uuid: agent_uuid,
            deleted_at: null
          }
        })

        if (!existingParticipant) {
          await tx.v_chat_room_participants.create({
            data: {
              room_uuid: facebookRoom.internal_room_uuid,
              user_uuid: agent_uuid,
              participant_role: 'member' as any,
              insert_user: assigned_by_user_uuid,
              update_user: assigned_by_user_uuid,
              notification_settings: {
                mentions: true,
                messages: true,
                reactions: true
              }
            }
          })
        }
      })

      return {
        success: true,
        message: `Agent ${agentName} assigned successfully`,
        agent_name: agentName,
        contact_name: contactName
      }
    } catch (error) {
      console.error('Error assigning agent to Facebook room:', error)

      return {
        success: false,
        message: 'Failed to assign agent'
      }
    }
  }


}
