// Unified Broadcast System
// Consolidates multiple platform-specific broadcasts into a single optimized message
// Fixes Issue 1.1: Zalo Chat Message Display Delays



export interface UnifiedBroadcastMessage {
  type: 'message' | 'notification' | 'status_update'
  room_uuid: string
  message_id: string | bigint
  content: string
  author_uuid: string
  author_name?: string
  message_type: number
  created_at: number
  flags: number
  platform: 'internal' | 'zalo' | 'telegram' | 'facebook'
  platform_message_id?: string
  delivery_status?: 'pending' | 'sent' | 'delivered' | 'failed'
  unread_counts?: Record<string, number>
  metadata?: {
    zalo_message_id?: string
    telegram_message_id?: string
    facebook_message_id?: string
    original_platform?: string
    is_external?: boolean
  }
}

export interface BroadcastOptions {
  includeUnreadCounts?: boolean
  includePlatformSpecific?: boolean
  priority?: 'low' | 'normal' | 'high'
  retryOnFailure?: boolean
}

export class UnifiedBroadcastSystem {
  private static instance: UnifiedBroadcastSystem
  private messageQueue: Map<string, UnifiedBroadcastMessage[]> = new Map()
  private processingQueue = false
  private readonly BATCH_SIZE = 10
  private readonly BATCH_DELAY = 50 // 50ms delay for batching

  static getInstance(): UnifiedBroadcastSystem {
    if (!UnifiedBroadcastSystem.instance) {
      UnifiedBroadcastSystem.instance = new UnifiedBroadcastSystem()
    }

    
return UnifiedBroadcastSystem.instance
  }

  /**
   * Broadcast a message using the unified system
   * Replaces multiple platform-specific broadcasts with a single optimized broadcast
   */
  async broadcastMessage(
    domainUuid: string,
    message: UnifiedBroadcastMessage
  ): Promise<void> {
    try {
      // Add message to queue for batching
      const queueKey = `${domainUuid}_${message.room_uuid}`

      if (!this.messageQueue.has(queueKey)) {
        this.messageQueue.set(queueKey, [])
      }

      this.messageQueue.get(queueKey)!.push(message)

      // Process queue if not already processing
      if (!this.processingQueue) {
        this.processingQueue = true
        setTimeout(() => this.processMessageQueue(), this.BATCH_DELAY)
      }

      return
    } catch (error) {
      console.error('Error in unified broadcast system:', error)
      
      // Fallback to direct broadcast if batching fails
      await this.directBroadcast(domainUuid, message)
    }
  }

  /**
   * Process queued messages in batches for optimal performance
   */
  private async processMessageQueue(): Promise<void> {
    try {
      const queueEntries = Array.from(this.messageQueue.entries())

      // Process each room's messages
      for (const [queueKey, messages] of queueEntries) {
        if (messages.length === 0) continue

        const [domainUuid, roomUuid] = queueKey.split('_room_')

        // Process messages in batches
        for (let i = 0; i < messages.length; i += this.BATCH_SIZE) {
          const batch = messages.slice(i, i + this.BATCH_SIZE)

          await this.processBatch(domainUuid, roomUuid, batch)
        }

        // Clear processed messages
        this.messageQueue.set(queueKey, [])
      }
    } catch (error) {
      console.error('Error processing message queue:', error)
    } finally {
      this.processingQueue = false
      
      // Check if more messages were added during processing
      const hasMessages = Array.from(this.messageQueue.values()).some(queue => queue.length > 0)

      if (hasMessages) {
        this.processingQueue = true
        setTimeout(() => this.processMessageQueue(), this.BATCH_DELAY)
      }
    }
  }

  /**
   * Process a batch of messages for a specific room
   */
  private async processBatch(
    domainUuid: string,
    roomUuid: string,
    messages: UnifiedBroadcastMessage[]
  ): Promise<void> {
    try {
      // Get global socket broadcast function
      const socketBroadcast = (global as any).socketBroadcast
      
      if (!socketBroadcast?.broadcastMessage) {
        console.warn('Socket broadcast not available')
        
return
      }
      
      // Create optimized broadcast payload
      const broadcastPayload = {
        type: 'batch_messages',
        room_uuid: roomUuid,
        messages: messages.map(msg => this.serializeMessage(msg)),
        timestamp: Date.now(),
        batch_size: messages.length
      }
      
      // Single broadcast to room participants
      const roomName = `${domainUuid}_room_${roomUuid}`

      socketBroadcast.broadcastMessage(roomName, broadcastPayload, domainUuid)
      
      // Broadcast to internal chat listeners
      const internalChatRoom = `${domainUuid}_internal_chat`

      socketBroadcast.broadcastMessage(internalChatRoom, broadcastPayload, domainUuid)
      
      console.log(`Unified broadcast: ${messages.length} messages to room ${roomUuid}`)
    } catch (error) {
      console.error('Error processing message batch:', error)
      
      // Fallback to individual broadcasts
      for (const message of messages) {
        await this.directBroadcast(domainUuid, message)
      }
    }
  }

  /**
   * Direct broadcast for fallback scenarios
   */
  private async directBroadcast(
    domainUuid: string,
    message: UnifiedBroadcastMessage
  ): Promise<void> {
    try {
      const socketBroadcast = (global as any).socketBroadcast
      
      if (!socketBroadcast?.broadcastMessage) {
        console.warn('Socket broadcast not available for direct broadcast')
        
return
      }
      
      const serializedMessage = this.serializeMessage(message)
      const roomName = `${domainUuid}_room_${message.room_uuid}`
      
      socketBroadcast.broadcastMessage(roomName, {
        type: 'single_message',
        ...serializedMessage
      }, domainUuid)
      
      console.log(`Direct broadcast: message ${message.message_id} to room ${message.room_uuid}`)
    } catch (error) {
      console.error('Error in direct broadcast:', error)
    }
  }

  /**
   * Serialize message for broadcast, handling BigInt conversion
   */
  private serializeMessage(message: UnifiedBroadcastMessage): any {
    return {
      ...message,
      message_id: typeof message.message_id === 'bigint' 
        ? message.message_id.toString() 
        : message.message_id,
      created_at: typeof message.created_at === 'bigint'
        ? Number(message.created_at)
        : message.created_at
    }
  }

  /**
   * Create unified message from platform-specific data
   */
  static createUnifiedMessage(
    roomUuid: string,
    content: string,
    authorUuid: string,
    platform: 'internal' | 'zalo' | 'telegram' | 'facebook',
    options: {
      messageId?: string | bigint
      authorName?: string
      messageType?: number
      platformMessageId?: string
      deliveryStatus?: 'pending' | 'sent' | 'delivered' | 'failed'
      unreadCounts?: Record<string, number>
      metadata?: any
    } = {}
  ): UnifiedBroadcastMessage {
    return {
      type: 'message',
      room_uuid: roomUuid,
      message_id: options.messageId || Date.now().toString(),
      content: content.trim(),
      author_uuid: authorUuid,
      author_name: options.authorName,
      message_type: options.messageType || 0,
      created_at: Math.floor(Date.now() / 1000),
      flags: 0,
      platform,
      platform_message_id: options.platformMessageId,
      delivery_status: options.deliveryStatus || 'sent',
      unread_counts: options.unreadCounts,
      metadata: {
        ...options.metadata,
        is_external: platform !== 'internal',
        original_platform: platform
      }
    }
  }

  /**
   * Get queue statistics for monitoring
   */
  getQueueStats(): { totalQueued: number; roomCount: number; isProcessing: boolean } {
    const totalQueued = Array.from(this.messageQueue.values())
      .reduce((sum, queue) => sum + queue.length, 0)
    
    return {
      totalQueued,
      roomCount: this.messageQueue.size,
      isProcessing: this.processingQueue
    }
  }

  /**
   * Clear all queued messages (for testing/debugging)
   */
  clearQueue(): void {
    this.messageQueue.clear()
    this.processingQueue = false
  }
}

// Export singleton instance
export const unifiedBroadcast = UnifiedBroadcastSystem.getInstance()
