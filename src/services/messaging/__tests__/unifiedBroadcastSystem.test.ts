// Unified Broadcast System Tests
// Tests for Issue 1.1 fix: Zalo Chat Message Display Delays

import { UnifiedBroadcastSystem } from '../unifiedBroadcastSystem'

// Mock global socket broadcast
const mockBroadcastMessage = jest.fn()

;(global as any).socketBroadcast = {
  broadcastMessage: mockBroadcastMessage
}

describe('UnifiedBroadcastSystem', () => {
  let broadcastSystem: UnifiedBroadcastSystem

  beforeEach(() => {
    broadcastSystem = UnifiedBroadcastSystem.getInstance()
    broadcastSystem.clearQueue()
    mockBroadcastMessage.mockClear()
    jest.clearAllTimers()
    jest.useFakeTimers()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  describe('Message Broadcasting', () => {
    it('should batch multiple messages for the same room', async () => {
      const domainUuid = 'test-domain'
      const roomUuid = 'test-room'

      // Create multiple messages
      const message1 = UnifiedBroadcastSystem.createUnifiedMessage(
        roomUuid,
        'Hello from <PERSON><PERSON>',
        'user1',
        'zalo',
        { messageId: '1', zaloMessageId: 'zalo-1' }
      )

      const message2 = UnifiedBroadcastSystem.createUnifiedMessage(
        roomUuid,
        'Hello from Telegram',
        'user2',
        'telegram',
        { messageId: '2', telegramMessageId: 'tg-1' }
      )

      // Send messages
      await broadcastSystem.broadcastMessage(domainUuid, message1)
      await broadcastSystem.broadcastMessage(domainUuid, message2)

      // Fast-forward timers to trigger batch processing
      jest.advanceTimersByTime(100)

      // Wait for async processing
      await new Promise(resolve => setTimeout(resolve, 0))

      // Should have called broadcast once with batched messages
      expect(mockBroadcastMessage).toHaveBeenCalledTimes(2) // room + internal_chat
      
      const firstCall = mockBroadcastMessage.mock.calls[0]

      expect(firstCall[0]).toBe(`${domainUuid}_room_${roomUuid}`)
      expect(firstCall[1].type).toBe('batch_messages')
      expect(firstCall[1].messages).toHaveLength(2)
      expect(firstCall[1].batch_size).toBe(2)
    })

    it('should handle single message broadcast', async () => {
      const domainUuid = 'test-domain'
      const roomUuid = 'test-room'

      const message = UnifiedBroadcastSystem.createUnifiedMessage(
        roomUuid,
        'Single message',
        'user1',
        'internal'
      )

      await broadcastSystem.broadcastMessage(domainUuid, message)
      
      // Fast-forward timers
      jest.advanceTimersByTime(100)
      await new Promise(resolve => setTimeout(resolve, 0))

      expect(mockBroadcastMessage).toHaveBeenCalled()
      
      const firstCall = mockBroadcastMessage.mock.calls[0]

      expect(firstCall[1].messages).toHaveLength(1)
      expect(firstCall[1].messages[0].content).toBe('Single message')
    })

    it('should serialize BigInt values correctly', async () => {
      const domainUuid = 'test-domain'
      const roomUuid = 'test-room'

      const message = UnifiedBroadcastSystem.createUnifiedMessage(
        roomUuid,
        'Test message',
        'user1',
        'zalo',
        { messageId: BigInt(123456789) }
      )

      await broadcastSystem.broadcastMessage(domainUuid, message)
      
      jest.advanceTimersByTime(100)
      await new Promise(resolve => setTimeout(resolve, 0))

      const firstCall = mockBroadcastMessage.mock.calls[0]
      const broadcastedMessage = firstCall[1].messages[0]
      
      // Should convert BigInt to string
      expect(typeof broadcastedMessage.message_id).toBe('string')
      expect(broadcastedMessage.message_id).toBe('123456789')
    })

    it('should handle broadcast failures gracefully', async () => {
      // Mock broadcast failure
      mockBroadcastMessage.mockImplementation(() => {
        throw new Error('Broadcast failed')
      })

      const domainUuid = 'test-domain'
      const roomUuid = 'test-room'

      const message = UnifiedBroadcastSystem.createUnifiedMessage(
        roomUuid,
        'Test message',
        'user1',
        'zalo'
      )

      // Should not throw error
      await expect(broadcastSystem.broadcastMessage(domainUuid, message)).resolves.toBeUndefined()
    })
  })

  describe('Message Creation', () => {
    it('should create unified message with correct structure', () => {
      const message = UnifiedBroadcastSystem.createUnifiedMessage(
        'room-123',
        'Test content',
        'user-456',
        'zalo',
        {
          messageId: 'msg-789',
          authorName: 'Test User',
          messageType: 1,
          platformMessageId: 'zalo-123',
          deliveryStatus: 'sent',
          unreadCounts: { 'user-1': 2, 'user-2': 0 },
          metadata: { custom: 'data' }
        }
      )

      expect(message).toMatchObject({
        type: 'message',
        room_uuid: 'room-123',
        message_id: 'msg-789',
        content: 'Test content',
        author_uuid: 'user-456',
        author_name: 'Test User',
        message_type: 1,
        platform: 'zalo',
        platform_message_id: 'zalo-123',
        delivery_status: 'sent',
        unread_counts: { 'user-1': 2, 'user-2': 0 },
        metadata: {
          custom: 'data',
          is_external: true,
          original_platform: 'zalo'
        }
      })
    })

    it('should mark internal messages as non-external', () => {
      const message = UnifiedBroadcastSystem.createUnifiedMessage(
        'room-123',
        'Internal message',
        'user-456',
        'internal'
      )

      expect(message.metadata?.is_external).toBe(false)
      expect(message.metadata?.original_platform).toBe('internal')
    })

    it('should trim message content', () => {
      const message = UnifiedBroadcastSystem.createUnifiedMessage(
        'room-123',
        '  Trimmed content  ',
        'user-456',
        'zalo'
      )

      expect(message.content).toBe('Trimmed content')
    })
  })

  describe('Queue Management', () => {
    it('should provide queue statistics', () => {
      const stats = broadcastSystem.getQueueStats()
      
      expect(stats).toHaveProperty('totalQueued')
      expect(stats).toHaveProperty('roomCount')
      expect(stats).toHaveProperty('isProcessing')
      expect(typeof stats.totalQueued).toBe('number')
      expect(typeof stats.roomCount).toBe('number')
      expect(typeof stats.isProcessing).toBe('boolean')
    })

    it('should clear queue when requested', () => {
      const domainUuid = 'test-domain'
      const roomUuid = 'test-room'

      const message = UnifiedBroadcastSystem.createUnifiedMessage(
        roomUuid,
        'Test message',
        'user1',
        'zalo'
      )

      // Add message to queue (don't process yet)
      broadcastSystem.broadcastMessage(domainUuid, message)
      
      let stats = broadcastSystem.getQueueStats()

      expect(stats.totalQueued).toBeGreaterThan(0)

      // Clear queue
      broadcastSystem.clearQueue()
      
      stats = broadcastSystem.getQueueStats()
      expect(stats.totalQueued).toBe(0)
      expect(stats.roomCount).toBe(0)
      expect(stats.isProcessing).toBe(false)
    })
  })

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = UnifiedBroadcastSystem.getInstance()
      const instance2 = UnifiedBroadcastSystem.getInstance()
      
      expect(instance1).toBe(instance2)
    })
  })
})
