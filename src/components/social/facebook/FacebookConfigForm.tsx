// Facebook Page Configuration Component
// Admin interface for managing Facebook Messenger settings per domain

'use client'

import React, { useState, useEffect } from 'react'

import { Save, Eye, EyeOff, AlertCircle, CheckCircle, MessageSquare } from 'lucide-react'

interface PageConfig {
  config_uuid?: string
  domain_uuid: string
  app_id: string
  page_id: string
  webhook_url?: string | null
  is_active: boolean
  allowed_events: string[]
  page_settings: Record<string, any>
  configured: boolean
  insert_date?: string
  update_date?: string
}

interface FacebookConfigFormProps {
  onConfigSaved?: () => void
  className?: string
}

interface FormData {
  app_id: string
  app_secret: string
  page_id: string
  page_access_token: string
  verify_token: string
  webhook_url: string
  allowed_events: string[]
}

export function FacebookConfigForm({ onConfigSaved, className = '' }: FacebookConfigFormProps) {
  const [config, setConfig] = useState<PageConfig | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  const [showSecrets, setShowSecrets] = useState({
    app_secret: false,
    page_access_token: false,
    verify_token: false
  })

  const [formData, setFormData] = useState<FormData>({
    app_id: '',
    app_secret: '',
    page_id: '',
    page_access_token: '',
    verify_token: '',
    webhook_url: '',
    allowed_events: ['messages', 'messaging_postbacks', 'messaging_optins']
  })

  // Load existing configuration
  const loadConfig = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/internal-chat/facebook/page-config')

      if (response.status === 404) {
        // No configuration exists yet
        setConfig(null)

        return
      }

      if (!response.ok) {
        throw new Error('Failed to fetch page configuration')
      }

      const data = await response.json()

      setConfig(data.config)

      // Update form data with existing config
      if (data.config) {
        setFormData({
          app_id: data.config.app_id || '',
          app_secret: '', // Never populate secrets for security
          page_id: data.config.page_id || '',
          page_access_token: '',
          verify_token: '',
          webhook_url: data.config.webhook_url || '',
          allowed_events: data.config.allowed_events || ['messages', 'messaging_postbacks', 'messaging_optins']
        })
      }
    } catch (err) {
      console.error('Error loading config:', err)
      setError(err instanceof Error ? err.message : 'Failed to load configuration')
    } finally {
      setLoading(false)
    }
  }

  // Save configuration
  const handleSave = async () => {
    setSaving(true)
    setError(null)
    setSuccess(null)

    try {
      const payload: any = {
        app_id: formData.app_id.trim(),
        page_id: formData.page_id.trim(),
        webhook_url: formData.webhook_url.trim() || undefined,
        allowed_events: formData.allowed_events
      }

      // Only include secrets if they're provided
      if (formData.app_secret.trim()) {
        payload.app_secret = formData.app_secret.trim()
      }

      if (formData.page_access_token.trim()) {
        payload.page_access_token = formData.page_access_token.trim()
      }

      if (formData.verify_token.trim()) {
        payload.verify_token = formData.verify_token.trim()
      }

      const response = await fetch('/api/internal-chat/facebook/page-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      if (!response.ok) {
        const errorData = await response.json()

        throw new Error(errorData.error || 'Failed to save configuration')
      }

      const result = await response.json()

      setConfig(result.config)
      setSuccess('Configuration saved successfully!')

      // Clear sensitive fields
      setFormData(prev => ({
        ...prev,
        app_secret: '',
        page_access_token: '',
        verify_token: ''
      }))

      // Notify parent component
      onConfigSaved?.()
    } catch (err) {
      console.error('Error saving config:', err)
      setError(err instanceof Error ? err.message : 'Failed to save configuration')
    } finally {
      setSaving(false)
    }
  }

  // Load config on mount
  useEffect(() => {
    loadConfig()
  }, [])

  const toggleSecretVisibility = (field: keyof typeof showSecrets) => {
    setShowSecrets(prev => ({
      ...prev,
      [field]: !prev[field]
    }))
  }

  if (loading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4'></div>
          <p className='text-gray-600'>Loading configuration...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className='flex items-center space-x-3'>
        <div className='p-2 bg-blue-100 rounded-lg'>
          <MessageSquare className='w-6 h-6 text-blue-600' />
        </div>
        <div>
          <h2 className='text-xl font-semibold text-gray-900'>Facebook Messenger Configuration</h2>
          <p className='text-gray-600'>Configure Facebook app and page settings for your domain</p>
        </div>
      </div>

      {/* Status Messages */}
      {error && (
        <div className='bg-red-50 border border-red-200 rounded-lg p-4 flex items-start space-x-3'>
          <AlertCircle className='w-5 h-5 text-red-600 mt-0.5 flex-shrink-0' />
          <div>
            <h4 className='text-sm font-medium text-red-800'>Configuration Error</h4>
            <p className='text-sm text-red-700 mt-1'>{error}</p>
          </div>
        </div>
      )}

      {success && (
        <div className='bg-green-50 border border-green-200 rounded-lg p-4 flex items-start space-x-3'>
          <CheckCircle className='w-5 h-5 text-green-600 mt-0.5 flex-shrink-0' />
          <div>
            <h4 className='text-sm font-medium text-green-800'>Success</h4>
            <p className='text-sm text-green-700 mt-1'>{success}</p>
          </div>
        </div>
      )}

      {/* Current Configuration Status */}
      {config && (
        <div className='bg-blue-50 border border-blue-200 rounded-lg p-4'>
          <h4 className='text-sm font-medium text-blue-900 mb-2'>Current Configuration</h4>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-3 text-sm'>
            <div>
              <span className='text-blue-700 font-medium'>App ID:</span>
              <span className='text-blue-800 ml-2'>{config.app_id}</span>
            </div>
            <div>
              <span className='text-blue-700 font-medium'>Page ID:</span>
              <span className='text-blue-800 ml-2'>{config.page_id}</span>
            </div>
            <div>
              <span className='text-blue-700 font-medium'>Status:</span>
              <span
                className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${
                  config.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}
              >
                {config.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
            <div>
              <span className='text-blue-700 font-medium'>Last Updated:</span>
              <span className='text-blue-800 ml-2'>
                {config.update_date ? new Date(config.update_date).toLocaleDateString() : 'Never'}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Configuration Form */}
      <div className='bg-white border border-gray-200 rounded-lg p-6'>
        <h3 className='text-lg font-medium text-gray-900 mb-4'>
          {config ? 'Update Configuration' : 'Setup Facebook Integration'}
        </h3>

        <div className='space-y-4'>
          {/* App ID */}
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Facebook App ID *</label>
            <input
              type='text'
              value={formData.app_id}
              onChange={e => setFormData(prev => ({ ...prev, app_id: e.target.value }))}
              className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
              placeholder='Enter your Facebook App ID'
              required
            />
            <p className='text-xs text-gray-500 mt-1'>Found in your Facebook App dashboard</p>
          </div>

          {/* App Secret */}
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Facebook App Secret {!config && '*'}</label>
            <div className='relative'>
              <input
                type={showSecrets.app_secret ? 'text' : 'password'}
                value={formData.app_secret}
                onChange={e => setFormData(prev => ({ ...prev, app_secret: e.target.value }))}
                className='w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                placeholder={config ? 'Leave empty to keep current secret' : 'Enter your Facebook App Secret'}
                required={!config}
              />
              <button
                type='button'
                onClick={() => toggleSecretVisibility('app_secret')}
                className='absolute inset-y-0 right-0 pr-3 flex items-center'
              >
                {showSecrets.app_secret ? (
                  <EyeOff className='h-4 w-4 text-gray-400' />
                ) : (
                  <Eye className='h-4 w-4 text-gray-400' />
                )}
              </button>
            </div>
            <p className='text-xs text-gray-500 mt-1'>Found in your Facebook App dashboard (keep this secure)</p>
          </div>

          {/* Page ID */}
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Facebook Page ID *</label>
            <input
              type='text'
              value={formData.page_id}
              onChange={e => setFormData(prev => ({ ...prev, page_id: e.target.value }))}
              className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
              placeholder='Enter your Facebook Page ID'
              required
            />
            <p className='text-xs text-gray-500 mt-1'>Found in your Facebook Page settings</p>
          </div>

          {/* Page Access Token */}
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Page Access Token {!config && '*'}</label>
            <div className='relative'>
              <input
                type={showSecrets.page_access_token ? 'text' : 'password'}
                value={formData.page_access_token}
                onChange={e => setFormData(prev => ({ ...prev, page_access_token: e.target.value }))}
                className='w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                placeholder={config ? 'Leave empty to keep current token' : 'Enter your Page Access Token'}
                required={!config}
              />
              <button
                type='button'
                onClick={() => toggleSecretVisibility('page_access_token')}
                className='absolute inset-y-0 right-0 pr-3 flex items-center'
              >
                {showSecrets.page_access_token ? (
                  <EyeOff className='h-4 w-4 text-gray-400' />
                ) : (
                  <Eye className='h-4 w-4 text-gray-400' />
                )}
              </button>
            </div>
            <p className='text-xs text-gray-500 mt-1'>Generate from Facebook App → Messenger → Settings</p>
          </div>

          {/* Verify Token */}
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>
              Webhook Verify Token {!config && '*'}
            </label>
            <div className='relative'>
              <input
                type={showSecrets.verify_token ? 'text' : 'password'}
                value={formData.verify_token}
                onChange={e => setFormData(prev => ({ ...prev, verify_token: e.target.value }))}
                className='w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                placeholder={config ? 'Leave empty to keep current token' : 'Enter a secure verify token'}
                required={!config}
              />
              <button
                type='button'
                onClick={() => toggleSecretVisibility('verify_token')}
                className='absolute inset-y-0 right-0 pr-3 flex items-center'
              >
                {showSecrets.verify_token ? (
                  <EyeOff className='h-4 w-4 text-gray-400' />
                ) : (
                  <Eye className='h-4 w-4 text-gray-400' />
                )}
              </button>
            </div>
            <p className='text-xs text-gray-500 mt-1'>Create a secure token for webhook verification</p>
          </div>

          {/* Webhook URL */}
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Webhook URL</label>
            <input
              type='url'
              value={formData.webhook_url}
              onChange={e => setFormData(prev => ({ ...prev, webhook_url: e.target.value }))}
              className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
              placeholder='Auto-generated if left empty'
            />
            <p className='text-xs text-gray-500 mt-1'>Leave empty to use auto-generated webhook URL</p>
          </div>

          {/* Allowed Events */}
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-2'>Allowed Events</label>
            <div className='space-y-2'>
              {[
                { value: 'messages', label: 'Messages' },
                { value: 'messaging_postbacks', label: 'Postbacks' },
                { value: 'messaging_optins', label: 'Opt-ins' },
                { value: 'messaging_deliveries', label: 'Delivery Confirmations' },
                { value: 'messaging_reads', label: 'Read Receipts' }
              ].map(event => (
                <label key={event.value} className='flex items-center'>
                  <input
                    type='checkbox'
                    checked={formData.allowed_events.includes(event.value)}
                    onChange={e => {
                      if (e.target.checked) {
                        setFormData(prev => ({
                          ...prev,
                          allowed_events: [...prev.allowed_events, event.value]
                        }))
                      } else {
                        setFormData(prev => ({
                          ...prev,
                          allowed_events: prev.allowed_events.filter(ev => ev !== event.value)
                        }))
                      }
                    }}
                    className='rounded border-gray-300 text-blue-600 focus:ring-blue-500'
                  />
                  <span className='ml-2 text-sm text-gray-700'>{event.label}</span>
                </label>
              ))}
            </div>
          </div>
        </div>

        {/* Save Button */}
        <div className='mt-6 flex justify-end'>
          <button
            onClick={handleSave}
            disabled={saving || !formData.app_id.trim() || !formData.page_id.trim()}
            className='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed'
          >
            {saving ? (
              <>
                <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2'></div>
                Saving...
              </>
            ) : (
              <>
                <Save className='w-4 h-4 mr-2' />
                {config ? 'Update Configuration' : 'Save Configuration'}
              </>
            )}
          </button>
        </div>
      </div>

      {/* Setup Instructions */}
      <div className='bg-gray-50 border border-gray-200 rounded-lg p-6'>
        <h3 className='text-lg font-medium text-gray-900 mb-4'>Setup Instructions</h3>
        <div className='space-y-3 text-sm text-gray-700'>
          <div className='flex items-start space-x-3'>
            <span className='flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium'>
              1
            </span>
            <div>
              <p className='font-medium'>Create Facebook App</p>
              <p className='text-gray-600'>Go to Facebook Developers Console and create a new app</p>
            </div>
          </div>
          <div className='flex items-start space-x-3'>
            <span className='flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium'>
              2
            </span>
            <div>
              <p className='font-medium'>Add Messenger Product</p>
              <p className='text-gray-600'>Add Messenger to your app and configure page access</p>
            </div>
          </div>
          <div className='flex items-start space-x-3'>
            <span className='flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium'>
              3
            </span>
            <div>
              <p className='font-medium'>Configure Webhook</p>
              <p className='text-gray-600'>Set up webhook URL and verify token in Facebook App settings</p>
            </div>
          </div>
          <div className='flex items-start space-x-3'>
            <span className='flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium'>
              4
            </span>
            <div>
              <p className='font-medium'>Test Integration</p>
              <p className='text-gray-600'>Send a test message to verify the integration is working</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
