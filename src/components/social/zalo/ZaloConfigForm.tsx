// ZALO OA Configuration Component
// Admin interface for managing ZALO Official Account settings per domain

'use client'

import React, { useState, useEffect } from 'react'

import { Save, Eye, EyeOff, AlertCircle, CheckCircle, Smartphone, RefreshCw } from 'lucide-react'

interface OaConfig {
  config_uuid?: string
  domain_uuid: string
  app_id: string
  oa_id: string
  webhook_url?: string | null
  is_active: boolean
  allowed_events: string[]
  oa_settings: Record<string, any>
  configured: boolean
  insert_date?: string
  update_date?: string
  token_expired?: boolean
}

interface ZaloConfigFormProps {
  onConfigSaved?: () => void
  className?: string
}

interface FormData {
  app_id: string
  app_secret: string
  oa_id: string
  access_token: string
  refresh_token: string
  webhook_url: string
  allowed_events: string[]
}

export function ZaloConfigForm({ onConfigSaved, className = '' }: ZaloConfigFormProps) {
  const [config, setConfig] = useState<OaConfig | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [refreshingToken, setRefreshingToken] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  const [showSecrets, setShowSecrets] = useState({
    app_secret: false,
    access_token: false,
    refresh_token: false
  })

  const [formData, setFormData] = useState<FormData>({
    app_id: '',
    app_secret: '',
    oa_id: '',
    access_token: '',
    refresh_token: '',
    webhook_url: '',
    allowed_events: ['user_send_text', 'user_send_image', 'user_send_file']
  })

  // Load existing configuration
  const loadConfig = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/internal-chat/zalo/oa-config')

      if (response.status === 404) {
        // No configuration exists yet
        setConfig(null)

        return
      }

      if (!response.ok) {
        throw new Error('Failed to fetch OA configuration')
      }

      const data = await response.json()

      setConfig(data.config)

      // Update form data with existing config
      if (data.config) {
        setFormData({
          app_id: data.config.app_id || '',
          app_secret: '', // Never populate secrets for security
          oa_id: data.config.oa_id || '',
          access_token: '',
          refresh_token: '',
          webhook_url: data.config.webhook_url || '',
          allowed_events: data.config.allowed_events || ['user_send_text', 'user_send_image', 'user_send_file']
        })
      }
    } catch (err) {
      console.error('Error loading config:', err)
      setError(err instanceof Error ? err.message : 'Failed to load configuration')
    } finally {
      setLoading(false)
    }
  }

  // Save configuration
  const handleSave = async () => {
    setSaving(true)
    setError(null)
    setSuccess(null)

    try {
      const payload: any = {
        app_id: formData.app_id.trim(),
        oa_id: formData.oa_id.trim(),
        webhook_url: formData.webhook_url.trim() || undefined,
        allowed_events: formData.allowed_events
      }

      // Only include secrets if they're provided
      if (formData.app_secret.trim()) {
        payload.app_secret = formData.app_secret.trim()
      }

      if (formData.access_token.trim()) {
        payload.access_token = formData.access_token.trim()
      }

      if (formData.refresh_token.trim()) {
        payload.refresh_token = formData.refresh_token.trim()
      }

      const response = await fetch('/api/internal-chat/zalo/oa-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      if (!response.ok) {
        const errorData = await response.json()

        throw new Error(errorData.error || 'Failed to save configuration')
      }

      const result = await response.json()

      setConfig(result.config)
      setSuccess('Configuration saved successfully!')

      // Clear sensitive fields
      setFormData(prev => ({
        ...prev,
        app_secret: '',
        access_token: '',
        refresh_token: ''
      }))

      // Notify parent component
      onConfigSaved?.()
    } catch (err) {
      console.error('Error saving config:', err)
      setError(err instanceof Error ? err.message : 'Failed to save configuration')
    } finally {
      setSaving(false)
    }
  }

  // Refresh access token
  const handleRefreshToken = async () => {
    if (!config) return

    setRefreshingToken(true)
    setError(null)
    setSuccess(null)

    try {
      const response = await fetch('/api/internal-chat/zalo/oa-config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          refresh_token_now: true
        })
      })

      if (!response.ok) {
        const errorData = await response.json()

        throw new Error(errorData.error || 'Failed to refresh token')
      }

      const result = await response.json()

      setConfig(result.config)
      setSuccess('Access token refreshed successfully!')

      // Reload configuration to get updated token info
      await loadConfig()
    } catch (err) {
      console.error('Error refreshing token:', err)
      setError(err instanceof Error ? err.message : 'Failed to refresh token')
    } finally {
      setRefreshingToken(false)
    }
  }

  // Load config on mount
  useEffect(() => {
    loadConfig()
  }, [])

  const toggleSecretVisibility = (field: keyof typeof showSecrets) => {
    setShowSecrets(prev => ({
      ...prev,
      [field]: !prev[field]
    }))
  }

  if (loading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4'></div>
          <p className='text-gray-600'>Loading configuration...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className='flex items-center space-x-3'>
        <div className='p-2 bg-green-100 rounded-lg'>
          <Smartphone className='w-6 h-6 text-green-600' />
        </div>
        <div>
          <h2 className='text-xl font-semibold text-gray-900'>ZALO Official Account Configuration</h2>
          <p className='text-gray-600'>Configure ZALO OA settings for your domain</p>
        </div>
      </div>

      {/* Status Messages */}
      {error && (
        <div className='bg-red-50 border border-red-200 rounded-lg p-4 flex items-start space-x-3'>
          <AlertCircle className='w-5 h-5 text-red-600 mt-0.5 flex-shrink-0' />
          <div>
            <h4 className='text-sm font-medium text-red-800'>Configuration Error</h4>
            <p className='text-sm text-red-700 mt-1'>{error}</p>
          </div>
        </div>
      )}

      {success && (
        <div className='bg-green-50 border border-green-200 rounded-lg p-4 flex items-start space-x-3'>
          <CheckCircle className='w-5 h-5 text-green-600 mt-0.5 flex-shrink-0' />
          <div>
            <h4 className='text-sm font-medium text-green-800'>Success</h4>
            <p className='text-sm text-green-700 mt-1'>{success}</p>
          </div>
        </div>
      )}

      {/* Current Configuration Status */}
      {config && (
        <div className='bg-green-50 border border-green-200 rounded-lg p-4'>
          <div className='flex items-center justify-between mb-2'>
            <h4 className='text-sm font-medium text-green-900'>Current Configuration</h4>
            {config.token_expired && (
              <button
                onClick={handleRefreshToken}
                disabled={refreshingToken}
                className='inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50'
              >
                {refreshingToken ? (
                  <>
                    <div className='animate-spin rounded-full h-3 w-3 border-b-2 border-green-600 mr-1'></div>
                    Refreshing...
                  </>
                ) : (
                  <>
                    <RefreshCw className='w-3 h-3 mr-1' />
                    Refresh Token
                  </>
                )}
              </button>
            )}
          </div>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-3 text-sm'>
            <div>
              <span className='text-green-700 font-medium'>App ID:</span>
              <span className='text-green-800 ml-2'>{config.app_id}</span>
            </div>
            <div>
              <span className='text-green-700 font-medium'>OA ID:</span>
              <span className='text-green-800 ml-2'>{config.oa_id}</span>
            </div>
            <div>
              <span className='text-green-700 font-medium'>Status:</span>
              <span
                className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${
                  config.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}
              >
                {config.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
            <div>
              <span className='text-green-700 font-medium'>Token Status:</span>
              <span
                className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${
                  config.token_expired ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                }`}
              >
                {config.token_expired ? 'Expired' : 'Valid'}
              </span>
            </div>

          </div>
        </div>
      )}

      {/* Configuration Form */}
      <div className='bg-white border border-gray-200 rounded-lg p-6'>
        <h3 className='text-lg font-medium text-gray-900 mb-4'>
          {config ? 'Update Configuration' : 'Setup ZALO Integration'}
        </h3>

        <div className='space-y-4'>
          {/* App ID */}
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>ZALO App ID *</label>
            <input
              type='text'
              value={formData.app_id}
              onChange={e => setFormData(prev => ({ ...prev, app_id: e.target.value }))}
              className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent'
              placeholder='Enter your ZALO App ID'
              required
            />
            <p className='text-xs text-gray-500 mt-1'>Found in your ZALO App dashboard</p>
          </div>

          {/* App Secret */}
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>ZALO App Secret {!config && '*'}</label>
            <div className='relative'>
              <input
                type={showSecrets.app_secret ? 'text' : 'password'}
                value={formData.app_secret}
                onChange={e => setFormData(prev => ({ ...prev, app_secret: e.target.value }))}
                className='w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent'
                placeholder={config ? 'Leave empty to keep current secret' : 'Enter your ZALO App Secret'}
                required={!config}
              />
              <button
                type='button'
                onClick={() => toggleSecretVisibility('app_secret')}
                className='absolute inset-y-0 right-0 pr-3 flex items-center'
              >
                {showSecrets.app_secret ? (
                  <EyeOff className='h-4 w-4 text-gray-400' />
                ) : (
                  <Eye className='h-4 w-4 text-gray-400' />
                )}
              </button>
            </div>
            <div className='mt-1 space-y-1'>
              <p className='text-xs text-gray-500'>Found in your ZALO App dashboard (keep this secure)</p>
              <div className='flex items-start space-x-2 p-2 bg-blue-50 border border-blue-200 rounded-md'>
                <AlertCircle className='h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0' />
                <div className='text-xs text-blue-800'>
                  <p className='font-medium'>Dual Purpose:</p>
                  <p>Used for token refresh and webhook signature verification (if no webhook secret is provided).</p>
                </div>
              </div>
            </div>
          </div>

          {/* OA ID */}
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Official Account ID *</label>
            <input
              type='text'
              value={formData.oa_id}
              onChange={e => setFormData(prev => ({ ...prev, oa_id: e.target.value }))}
              className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent'
              placeholder='Enter your ZALO OA ID'
              required
            />
            <p className='text-xs text-gray-500 mt-1'>Found in your ZALO Official Account settings</p>
          </div>

          {/* Access Token */}
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Access Token {!config && '*'}</label>
            <div className='relative'>
              <input
                type={showSecrets.access_token ? 'text' : 'password'}
                value={formData.access_token}
                onChange={e => setFormData(prev => ({ ...prev, access_token: e.target.value }))}
                className='w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent'
                placeholder={config ? 'Leave empty to keep current token' : 'Enter your Access Token'}
                required={!config}
              />
              <button
                type='button'
                onClick={() => toggleSecretVisibility('access_token')}
                className='absolute inset-y-0 right-0 pr-3 flex items-center'
              >
                {showSecrets.access_token ? (
                  <EyeOff className='h-4 w-4 text-gray-400' />
                ) : (
                  <Eye className='h-4 w-4 text-gray-400' />
                )}
              </button>
            </div>
            <p className='text-xs text-gray-500 mt-1'>Generate from ZALO OA → Settings → API</p>
          </div>

          {/* Refresh Token */}
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Refresh Token</label>
            <div className='relative'>
              <input
                type={showSecrets.refresh_token ? 'text' : 'password'}
                value={formData.refresh_token}
                onChange={e => setFormData(prev => ({ ...prev, refresh_token: e.target.value }))}
                className='w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent'
                placeholder={config ? 'Leave empty to keep current token' : 'Enter your Refresh Token (optional)'}
              />
              <button
                type='button'
                onClick={() => toggleSecretVisibility('refresh_token')}
                className='absolute inset-y-0 right-0 pr-3 flex items-center'
              >
                {showSecrets.refresh_token ? (
                  <EyeOff className='h-4 w-4 text-gray-400' />
                ) : (
                  <Eye className='h-4 w-4 text-gray-400' />
                )}
              </button>
            </div>
            <p className='text-xs text-gray-500 mt-1'>Used to automatically refresh access tokens</p>
          </div>

          {/* Webhook URL */}
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Webhook URL</label>
            <input
              type='url'
              value={formData.webhook_url}
              onChange={e => setFormData(prev => ({ ...prev, webhook_url: e.target.value }))}
              className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent'
              placeholder='Auto-generated if left empty'
            />
            <p className='text-xs text-gray-500 mt-1'>Leave empty to use auto-generated webhook URL</p>
          </div>

          {/* Allowed Events */}
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-2'>Allowed Events</label>
            <div className='space-y-2'>
              {[
                { value: 'user_send_text', label: 'Text Messages' },
                { value: 'user_send_image', label: 'Image Messages' },
                { value: 'user_send_file', label: 'File Messages' },
                { value: 'user_send_audio', label: 'Audio Messages' },
                { value: 'user_send_video', label: 'Video Messages' },
                { value: 'user_send_sticker', label: 'Sticker Messages' },
                { value: 'user_send_location', label: 'Location Messages' },
                { value: 'follow', label: 'Follow Events' },
                { value: 'unfollow', label: 'Unfollow Events' }
              ].map(event => (
                <label key={event.value} className='flex items-center'>
                  <input
                    type='checkbox'
                    checked={formData.allowed_events.includes(event.value)}
                    onChange={e => {
                      if (e.target.checked) {
                        setFormData(prev => ({
                          ...prev,
                          allowed_events: [...prev.allowed_events, event.value]
                        }))
                      } else {
                        setFormData(prev => ({
                          ...prev,
                          allowed_events: prev.allowed_events.filter(ev => ev !== event.value)
                        }))
                      }
                    }}
                    className='rounded border-gray-300 text-green-600 focus:ring-green-500'
                  />
                  <span className='ml-2 text-sm text-gray-700'>{event.label}</span>
                </label>
              ))}
            </div>
          </div>
        </div>

        {/* Save Button */}
        <div className='mt-6 flex justify-end'>
          <button
            onClick={handleSave}
            disabled={saving || !formData.app_id.trim() || !formData.oa_id.trim()}
            className='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed'
          >
            {saving ? (
              <>
                <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2'></div>
                Saving...
              </>
            ) : (
              <>
                <Save className='w-4 h-4 mr-2' />
                {config ? 'Update Configuration' : 'Save Configuration'}
              </>
            )}
          </button>
        </div>
      </div>

      {/* Setup Instructions */}
      <div className='bg-gray-50 border border-gray-200 rounded-lg p-6'>
        <h3 className='text-lg font-medium text-gray-900 mb-4'>Setup Instructions</h3>
        <div className='space-y-3 text-sm text-gray-700'>
          <div className='flex items-start space-x-3'>
            <span className='flex-shrink-0 w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-xs font-medium'>
              1
            </span>
            <div>
              <p className='font-medium'>Create ZALO App</p>
              <p className='text-gray-600'>Go to ZALO Developers Console and create a new app</p>
            </div>
          </div>
          <div className='flex items-start space-x-3'>
            <span className='flex-shrink-0 w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-xs font-medium'>
              2
            </span>
            <div>
              <p className='font-medium'>Setup Official Account</p>
              <p className='text-gray-600'>Create or link your ZALO Official Account to the app</p>
            </div>
          </div>
          <div className='flex items-start space-x-3'>
            <span className='flex-shrink-0 w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-xs font-medium'>
              3
            </span>
            <div>
              <p className='font-medium'>Generate Access Tokens</p>
              <p className='text-gray-600'>Generate access and refresh tokens for API access</p>
            </div>
          </div>
          <div className='flex items-start space-x-3'>
            <span className='flex-shrink-0 w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-xs font-medium'>
              4
            </span>
            <div>
              <p className='font-medium'>Configure Webhook</p>
              <p className='text-gray-600'>Set up webhook URL in ZALO OA settings to receive messages</p>
            </div>
          </div>
          <div className='flex items-start space-x-3'>
            <span className='flex-shrink-0 w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-xs font-medium'>
              5
            </span>
            <div>
              <p className='font-medium'>Test Integration</p>
              <p className='text-gray-600'>Send a test message to verify the integration is working</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
