// Internal Chat Popup Component
// Responsive floating chat window with mobile-first design

'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'

import { motion, AnimatePresence } from 'framer-motion'
import { useSession } from 'next-auth/react'

import { useAppDispatch, useAppSelector } from '@/redux-store/hooks'
import {
  setActiveRoom,
  fetchRoomMessages,
  fetchChatRoom,
  markMessagesAsRead
} from '@/redux-store/slices/internal-chat/internalChatSlice'
import type { ChatRoomParticipant } from '@/types/apps/internal-chat/chatTypes'

// Hooks
import { useChatInitialization } from '@/hooks/chat/useChatInitialization'
import { useChatSocket } from '@/hooks/chat/useChatSocket'
import useMediaQuery from '@/@menu/hooks/useMediaQuery'

// Components
import ChatRoomList from './ChatRoomList'
import MessageList from './MessageList'
import SmartMessageInput from './SmartMessageInput'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

interface ChatPopupProps {
  isOpen: boolean
  onClose: () => void
  onExpandToFullPage?: () => void
  initialRoomId?: string
  position?: 'bottom-right' | 'bottom-left'
}

// Responsive breakpoints following the application's design system
const BREAKPOINTS = {
  sm: '600px',
  md: '900px',
  lg: '1200px',
  xl: '1536px'
} as const

const ChatPopup: React.FC<ChatPopupProps> = ({
  isOpen,
  onClose,
  onExpandToFullPage,
  initialRoomId,
  position = 'bottom-right'
}) => {
  const dispatch = useAppDispatch()
  const { data: session } = useSession()

  const { rooms, activeRoomId, isConnected, connectionStatus, unreadCounts, totalUnreadCount } = useAppSelector(
    state => state.internalChatReducer
  )

  // Responsive states
  const [isMinimized, setIsMinimized] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const [dragPosition, setDragPosition] = useState({ x: 0, y: 0 })
  const popupRef = useRef<HTMLDivElement>(null)

  // Media queries for responsive behavior
  const isMobile = useMediaQuery(BREAKPOINTS.sm)
  const isTablet = useMediaQuery(BREAKPOINTS.md)

  // Initialize chat rooms using centralized hook
  const { initializeChatRooms } = useChatInitialization({
    autoInitialize: false // We'll manually initialize when popup opens
  })

  // Socket functionality for room management
  const { joinRoom, leaveRoom } = useChatSocket()

  // Initialize chat when popup opens
  useEffect(() => {
    if (isOpen) {
      initializeChatRooms()

      // Clear unread count for active room when popup opens
      if (activeRoomId) {
        dispatch(setActiveRoom(activeRoomId))

        // Mark messages as read if there are unread messages
        if (unreadCounts[activeRoomId] > 0) {
          dispatch(markMessagesAsRead({ roomId: activeRoomId }))
        }
      }
    }
  }, [isOpen, initializeChatRooms, activeRoomId, unreadCounts, dispatch])

  // Set initial room if provided
  useEffect(() => {
    if (initialRoomId && activeRoomId !== initialRoomId) {
      // Set active room immediately, even if room data isn't loaded yet
      dispatch(setActiveRoom(initialRoomId))

      // If room data doesn't exist, fetch it
      if (!rooms[initialRoomId]) {
        dispatch(fetchChatRoom(initialRoomId))
      }

      // Mark messages as read when room becomes active
      if (unreadCounts[initialRoomId] > 0) {
        dispatch(markMessagesAsRead({ roomId: initialRoomId }))
      }
    }
  }, [initialRoomId, activeRoomId, rooms, unreadCounts, dispatch])

  // Load messages when room is selected
  useEffect(() => {
    if (activeRoomId && isOpen) {
      dispatch(fetchRoomMessages({ roomId: activeRoomId, limit: 50 }))
    }
  }, [activeRoomId, isOpen, dispatch])

  // Join/leave rooms via socket when active room changes
  useEffect(() => {
    if (activeRoomId && isConnected) {
      joinRoom(activeRoomId)

      // Leave previous room if there was one
      return () => {
        leaveRoom(activeRoomId)
      }
    }
  }, [activeRoomId, isConnected, joinRoom, leaveRoom])

  const handleMinimize = () => {
    setIsMinimized(!isMinimized)
  }

  const handleExpand = () => {
    if (onExpandToFullPage) {
      onExpandToFullPage()
      onClose()
    }
  }

  const handleRoomSelect = (roomId: string) => {
    dispatch(setActiveRoom(roomId))

    // Mark messages as read when room is selected
    if (unreadCounts[roomId] > 0) {
      dispatch(markMessagesAsRead({ roomId }))
    }
  }

  const handleBackToRoomList = () => {
    dispatch(setActiveRoom(null))
  }

  // Drag functionality
  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      setIsDragging(true)
      const rect = popupRef.current?.getBoundingClientRect()

      if (rect) {
        setDragPosition({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        })
      }
    }
  }

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging && popupRef.current) {
        const newX = e.clientX - dragPosition.x
        const newY = e.clientY - dragPosition.y

        // Keep popup within viewport bounds
        const maxX = window.innerWidth - popupRef.current.offsetWidth
        const maxY = window.innerHeight - popupRef.current.offsetHeight

        const boundedX = Math.max(0, Math.min(newX, maxX))
        const boundedY = Math.max(0, Math.min(newY, maxY))

        popupRef.current.style.left = `${boundedX}px`
        popupRef.current.style.top = `${boundedY}px`
        popupRef.current.style.right = 'auto'
        popupRef.current.style.bottom = 'auto'
      }
    }

    const handleMouseUp = () => {
      setIsDragging(false)
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [isDragging, dragPosition])

  // Responsive positioning and sizing
  const getResponsiveStyles = useCallback(() => {
    if (isMobile) {
      // On mobile, use full screen overlay
      return {
        position: 'fixed' as const,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: '100vw',
        height: isMinimized ? '60px' : '100vh',
        maxHeight: '100vh',
        borderRadius: '0px',
        display: 'flex',
        flexDirection: 'column' as const
      }
    } else if (isTablet) {
      // On tablet, use larger popup
      return {
        width: '420px',
        height: isMinimized ? '52px' : '560px',
        maxHeight: 'calc(100vh - 80px)',
        borderRadius: '12px',
        display: 'flex',
        flexDirection: 'column' as const
      }
    } else {
      // Desktop default
      return {
        width: '380px',
        height: isMinimized ? '48px' : '520px',
        maxHeight: 'calc(100vh - 120px)',
        borderRadius: '8px',
        display: 'flex',
        flexDirection: 'column' as const
      }
    }
  }, [isMobile, isTablet, isMinimized])

  const getPositionClasses = useCallback(() => {
    if (isMobile) {
      return '' // Full screen on mobile
    }

    const baseClasses = {
      'bottom-right': 'bottom-4 right-4',
      'bottom-left': 'bottom-4 left-4'
    }

    return baseClasses[position]
  }, [isMobile, position])

  const activeRoom = activeRoomId ? rooms[activeRoomId] : null
  const hasActiveRoomId = Boolean(activeRoomId)
  const responsiveStyles = getResponsiveStyles()
  const positionClasses = getPositionClasses()

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          ref={popupRef}
          initial={isMobile ? { opacity: 0, y: '100%' } : { opacity: 0, scale: 0.8, y: 20 }}
          animate={isMobile ? { opacity: 1, y: 0 } : { opacity: 1, scale: 1, y: 0 }}
          exit={isMobile ? { opacity: 0, y: '100%' } : { opacity: 0, scale: 0.8, y: 20 }}
          transition={{ duration: isMobile ? 0.3 : 0.2, ease: 'easeOut' }}
          className={`fixed z-50 bg-white shadow-2xl border border-gray-200 ${
            isDragging ? 'cursor-grabbing' : 'cursor-auto'
          } ${positionClasses} ${isMobile ? 'rounded-none' : 'rounded-lg'} overflow-hidden`}
          style={{
            ...responsiveStyles,
            userSelect: isDragging ? 'none' : 'auto'
          }}
        >
          {/* Header */}
          <div
            className={`flex items-center justify-between ${isMobile ? 'p-4' : 'p-3'} border-b bg-gray-50 ${
              isMobile ? 'rounded-none' : 'rounded-t-lg'
            } ${!isMinimized && !isMobile ? 'cursor-grab' : ''} flex-shrink-0`}
            onMouseDown={!isMobile ? handleMouseDown : undefined}
          >
            <div className='flex items-center space-x-2 flex-1 min-w-0'>
              {/* Back button when in chat view */}
              {hasActiveRoomId && (
                <button
                  onClick={handleBackToRoomList}
                  className={`${isMobile ? 'p-2' : 'p-1'} hover:bg-gray-200 rounded transition-colors flex-shrink-0`}
                  title='Back to room list'
                >
                  <i className={`ri-arrow-left-line ${isMobile ? 'text-base' : 'text-sm'} text-gray-600`} />
                </button>
              )}

              {/* Connection status */}
              <div
                className={`${isMobile ? 'w-3 h-3' : 'w-2 h-2'} rounded-full flex-shrink-0 ${
                  connectionStatus === 'connected'
                    ? 'bg-green-500'
                    : connectionStatus === 'connecting'
                      ? 'bg-yellow-500'
                      : 'bg-red-500'
                }`}
              />

              {/* Room info */}
              <div className='flex items-center space-x-2 flex-1 min-w-0'>
                {hasActiveRoomId && (
                  <div
                    className={`${
                      isMobile ? 'w-8 h-8' : 'w-6 h-6'
                    } rounded-full bg-gray-200 flex items-center justify-center flex-shrink-0`}
                  >
                    {activeRoom?.room_avatar ? (
                      <img
                        src={activeRoom.room_avatar}
                        alt={activeRoom.room_name || 'Chat'}
                        className='w-full h-full rounded-full object-cover'
                      />
                    ) : (
                      <i className={`ri-user-line ${isMobile ? 'text-sm' : 'text-xs'} text-gray-600`} />
                    )}
                  </div>
                )}

                <div className='min-w-0 flex-1'>
                  <div className='flex items-center space-x-2'>
                    <h3 className={`font-semibold text-gray-800 ${isMobile ? 'text-base' : 'text-sm'} truncate`}>
                      {activeRoom
                        ? activeRoom.room_type === 'direct' && activeRoom.participants && session?.user?.id
                          ? activeRoom.participants.find((p: ChatRoomParticipant) => p.user_uuid !== session.user!.id)
                              ?.user_name ||
                            activeRoom.room_name ||
                            'Chat'
                          : activeRoom.room_name || 'Chat'
                        : hasActiveRoomId
                          ? 'Loading...'
                          : 'Messages'}
                    </h3>
                    {/* Show unread indicator for other rooms only */}
                    {(() => {
                      const currentRoomUnread = activeRoomId ? unreadCounts[activeRoomId] || 0 : 0
                      const otherRoomsUnread = totalUnreadCount - currentRoomUnread

                      return hasActiveRoomId && otherRoomsUnread > 0 ? (
                        <span className='bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[16px] text-center'>
                          {otherRoomsUnread}
                        </span>
                      ) : null
                    })()}
                  </div>
                  {activeRoom && !isMobile && (
                    <span className='text-xs text-gray-500 truncate block'>
                      {activeRoom.room_type === 'direct' ? 'Direct Message' : `${activeRoom.participant_count} members`}
                    </span>
                  )}
                </div>
              </div>
            </div>

            <div className={`flex items-center ${isMobile ? 'space-x-2' : 'space-x-1'} flex-shrink-0`}>
              {/* Expand button - hide on mobile */}
              {onExpandToFullPage && !isMobile && (
                <button
                  onClick={handleExpand}
                  className={`${isMobile ? 'p-2' : 'p-1'} hover:bg-gray-200 rounded transition-colors`}
                  title='Expand to full page'
                >
                  <i className={`ri-external-link-line ${isMobile ? 'text-base' : 'text-sm'} text-gray-600`} />
                </button>
              )}

              {/* Minimize button - hide on mobile */}
              {!isMobile && (
                <button
                  onClick={handleMinimize}
                  className='p-1 hover:bg-gray-200 rounded transition-colors'
                  title={isMinimized ? 'Restore' : 'Minimize'}
                >
                  <i className={`ri-${isMinimized ? 'arrow-up' : 'subtract'}-line text-sm text-gray-600`} />
                </button>
              )}

              {/* Close button */}
              <button
                onClick={onClose}
                className={`${isMobile ? 'p-2' : 'p-1'} hover:bg-gray-200 rounded transition-colors`}
                title='Close'
              >
                <i className={`ri-close-line ${isMobile ? 'text-base' : 'text-sm'} text-gray-600`} />
              </button>
            </div>
          </div>

          {/* Content */}
          {!isMinimized && (
            <div className='flex-1 flex flex-col overflow-hidden'>
              {!hasActiveRoomId ? (

                // Room list view
                <div className='flex flex-col h-full'>
                  {/* Connection status */}
                  {!isConnected && (
                    <div className={`${isMobile ? 'p-4' : 'p-3'} bg-yellow-50 border-b flex-shrink-0`}>
                      <div className='flex items-center space-x-2 text-yellow-700'>
                        <LoadingSpinner size='sm' />
                        <span className={`${isMobile ? 'text-sm' : 'text-xs'}`}>Connecting...</span>
                      </div>
                    </div>
                  )}

                  {/* Room list */}
                  <div className='flex-1 overflow-hidden'>
                    <ChatRoomList onRoomSelect={handleRoomSelect} compact={!isMobile} />
                  </div>
                </div>
              ) : (

                // Chat view
                <div className='flex flex-col h-full'>
                  {/* Connection Status */}
                  {!isConnected && (
                    <div
                      className={`${
                        isMobile ? 'px-4 py-2' : 'px-3 py-1'
                      } bg-yellow-50 border-b border-yellow-200 flex-shrink-0`}
                    >
                      <div className='flex items-center space-x-2 text-yellow-700'>
                        <div
                          className={`animate-spin rounded-full ${
                            isMobile ? 'h-3 w-3 border-b-2' : 'h-2 w-2 border-b-2'
                          } border-yellow-600`}
                        />
                        <span className={`${isMobile ? 'text-sm' : 'text-xs'}`}>
                          {connectionStatus === 'connecting' ? 'Connecting...' : 'Disconnected'}
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Messages */}
                  <div className='flex-1 min-h-0'>
                    <MessageList roomId={activeRoomId!} compact={!isMobile} />
                  </div>

                  {/* Message input */}
                  <div className='border-t bg-white flex-shrink-0'>
                    <SmartMessageInput roomId={activeRoomId!} compact={!isMobile} />
                  </div>
                </div>
              )}
            </div>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default ChatPopup
