// Facebook Contact Selector Component
// Allows agents to select Facebook contacts and start new conversations

'use client'

import React, { useState, useEffect, useCallback } from 'react'

import { MessageSquare, Search, MessageCircle, User, Clock, CheckCircle, AlertCircle } from 'lucide-react'

interface FacebookContact {
  contact_uuid: string
  facebook_user_id: string
  first_name: string | null
  last_name: string | null
  profile_pic: string | null
  locale: string | null
  timezone: number | null
  is_active: boolean
  contact_info: Record<string, any>
  last_interaction_date: string | null
  has_room: boolean
  room_uuid?: string
}

interface FacebookContactSelectorProps {
  onContactSelect: (contact: FacebookContact) => void
  onCreateRoom: (contactUuid: string) => void
  className?: string
}

const FacebookContactSelector: React.FC<FacebookContactSelectorProps> = ({
  onContactSelect,
  onCreateRoom,
  className = ''
}) => {
  const [contacts, setContacts] = useState<FacebookContact[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [creatingRoom, setCreatingRoom] = useState<string | null>(null)
  const [activeFilter, setActiveFilter] = useState<'all' | 'active' | 'inactive'>('all')

  // Fetch Facebook contacts
  const fetchContacts = useCallback(async (search?: string) => {
    setLoading(true)

    setError(null)

    try {
      const params = new URLSearchParams()

      if (search) params.append('search', search)

      if (activeFilter !== 'all') {
        params.append('is_active', activeFilter === 'active' ? 'true' : 'false')
      }

      const response = await fetch(`/api/internal-chat/facebook/contacts?${params}`)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))

        throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch contacts`)
      }

      const data = await response.json()

      if (!data.contacts || !Array.isArray(data.contacts)) {
        throw new Error('Invalid response format from server')
      }

      setContacts(data.contacts)
    } catch (err) {
      console.error('Error fetching Facebook contacts:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to load contacts'

      setError(errorMessage)
      setContacts([])
    } finally {
      setLoading(false)
    }
  }, [activeFilter])

  // Initial load
  useEffect(() => {
    fetchContacts()
  }, [fetchContacts])

  // Search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      fetchContacts(searchTerm)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchTerm, fetchContacts])

  // Handle room creation
  const handleCreateRoom = async (contactUuid: string) => {
    try {
      setCreatingRoom(contactUuid)

      const response = await fetch('/api/internal-chat/facebook/contacts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          contact_uuid: contactUuid
        })
      })

      if (!response.ok) {
        throw new Error('Failed to create room')
      }

      const data = await response.json()

      // Refresh contacts list
      await fetchContacts(searchTerm)

      // Notify parent component
      onCreateRoom(data.room_uuid)
    } catch (err) {
      console.error('Error creating room:', err)
      setError(err instanceof Error ? err.message : 'Failed to create room')
    } finally {
      setCreatingRoom(null)
    }
  }

  // Format last interaction time
  const formatLastInteraction = (timestamp: string | null) => {
    if (!timestamp) return 'No recent interaction'

    const date = new Date(timestamp)
    const now = new Date()
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24))

    if (diffInDays === 0) {
      return 'Today'
    } else if (diffInDays === 1) {
      return 'Yesterday'
    } else if (diffInDays < 7) {
      return `${diffInDays} days ago`
    } else {
      return date.toLocaleDateString()
    }
  }

  // Get contact display name
  const getDisplayName = (contact: FacebookContact) => {
    return [contact.first_name, contact.last_name].filter(Boolean).join(' ') || 
           `Facebook User ${contact.facebook_user_id}`
  }

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Header */}
      <div className='p-4 border-b border-gray-200'>
        <div className='flex items-center space-x-3 mb-4'>
          <div className='p-2 bg-blue-100 rounded-lg'>
            <MessageSquare className='w-5 h-5 text-blue-600' />
          </div>
          <div>
            <h2 className='text-lg font-semibold text-gray-900'>Facebook Contacts</h2>
            <p className='text-sm text-gray-600'>Select a contact to start a conversation</p>
          </div>
        </div>

        {/* Search */}
        <div className='relative mb-3'>
          <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4' />
          <input
            type='text'
            placeholder='Search contacts...'
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className='w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
          />
        </div>

        {/* Filter */}
        <div className='flex space-x-2'>
          {[
            { key: 'all', label: 'All' },
            { key: 'active', label: 'Active' },
            { key: 'inactive', label: 'Inactive' }
          ].map(filter => (
            <button
              key={filter.key}
              onClick={() => setActiveFilter(filter.key as any)}
              className={`px-3 py-1 rounded-full text-xs transition-colors ${
                activeFilter === filter.key
                  ? 'bg-blue-100 text-blue-700'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {filter.label}
            </button>
          ))}
        </div>
      </div>

      {/* Contact List */}
      <div className='flex-1 overflow-y-auto'>
        {loading && (
          <div className='flex items-center justify-center h-32'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600'></div>
          </div>
        )}

        {error && (
          <div className='p-4 text-center'>
            <div className='text-red-600 mb-2'>
              <AlertCircle className='w-8 h-8 mx-auto mb-2' />
              <p className='text-sm'>{error}</p>
            </div>
            <button
              onClick={() => fetchContacts(searchTerm)}
              className='px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors'
            >
              Try Again
            </button>
          </div>
        )}

        {!loading && !error && contacts.length === 0 && (
          <div className='p-6 text-center text-gray-500'>
            <MessageSquare className='w-12 h-12 mx-auto mb-3 text-gray-300' />
            <p className='text-sm'>No Facebook contacts found</p>
            {searchTerm && (
              <p className='text-xs mt-1'>Try adjusting your search terms</p>
            )}
          </div>
        )}

        {!loading && !error && contacts.length > 0 && (
          <div className='divide-y divide-gray-100'>
            {contacts.map(contact => (
              <div
                key={contact.contact_uuid}
                className='p-4 hover:bg-gray-50 transition-colors'
              >
                <div className='flex items-start space-x-3'>
                  {/* Avatar */}
                  <div className='flex-shrink-0'>
                    {contact.profile_pic ? (
                      <img
                        src={contact.profile_pic}
                        alt={getDisplayName(contact)}
                        className='w-10 h-10 rounded-full object-cover'
                      />
                    ) : (
                      <div className='w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center'>
                        <User className='w-5 h-5 text-blue-600' />
                      </div>
                    )}
                  </div>

                  {/* Content */}
                  <div className='flex-1 min-w-0'>
                    <div className='flex items-center justify-between mb-1'>
                      <h3 className='text-sm font-medium text-gray-900 truncate'>
                        {getDisplayName(contact)}
                      </h3>
                      <div className='flex items-center space-x-2'>
                        {contact.is_active ? (
                          <div title='Active'>
                            <CheckCircle className='w-4 h-4 text-green-500' />
                          </div>
                        ) : (
                          <div title='Inactive'>
                            <AlertCircle className='w-4 h-4 text-red-500' />
                          </div>
                        )}
                      </div>
                    </div>

                    <div className='flex items-center space-x-2 text-xs text-gray-500 mb-2'>
                      <span>@{contact.facebook_user_id}</span>
                      {contact.locale && <span>• {contact.locale}</span>}
                    </div>

                    <div className='flex items-center justify-between'>
                      <div className='flex items-center space-x-1 text-xs text-gray-500'>
                        <Clock className='w-3 h-3' />
                        <span>{formatLastInteraction(contact.last_interaction_date)}</span>
                      </div>

                      <div className='flex items-center space-x-2'>
                        {contact.has_room ? (
                          <button
                            onClick={() => onContactSelect(contact)}
                            className='flex items-center space-x-1 px-3 py-1 bg-green-100 text-green-700 text-xs rounded-lg hover:bg-green-200 transition-colors'
                          >
                            <MessageCircle className='w-3 h-3' />
                            <span>Open Chat</span>
                          </button>
                        ) : (
                          <button
                            onClick={() => handleCreateRoom(contact.contact_uuid)}
                            disabled={creatingRoom === contact.contact_uuid}
                            className='flex items-center space-x-1 px-3 py-1 bg-blue-600 text-white text-xs rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50'
                          >
                            {creatingRoom === contact.contact_uuid ? (
                              <div className='animate-spin rounded-full h-3 w-3 border-b-2 border-white'></div>
                            ) : (
                              <MessageCircle className='w-3 h-3' />
                            )}
                            <span>Start Chat</span>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default FacebookContactSelector
