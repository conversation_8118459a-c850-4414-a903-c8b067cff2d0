// Facebook Chat Interface Component
// Specialized chat interface for Facebook Messenger conversations

'use client'

import React, { useState, useEffect, useRef } from 'react'

import { Facebook, Send, Paperclip, Smile, MoreVertical, CheckCircle, Clock, AlertCircle } from 'lucide-react'

interface FacebookMessage {
  message_id: string
  content: string
  author_uuid: string | null
  author_name?: string
  message_type: number
  created_at: number
  flags: number
  delivery_status?: 'sent' | 'delivered' | 'read' | 'failed'
  facebook_message_id?: string
}

interface FacebookContact {
  facebook_user_id: string
  first_name: string | null
  last_name: string | null
  profile_pic: string | null
  is_active: boolean
}

interface FacebookChatInterfaceProps {
  contact: FacebookContact
  messages: FacebookMessage[]
  currentUserId: string
  onSendMessage: (content: string) => Promise<void>
  isLoading?: boolean
  className?: string
}

const FacebookChatInterface: React.FC<FacebookChatInterfaceProps> = ({
  contact,
  messages,
  currentUserId,
  onSendMessage,
  isLoading = false,
  className = ''
}) => {
  const [messageInput, setMessageInput] = useState('')
  const [sending, setSending] = useState(false)
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Handle send message
  const handleSendMessage = async () => {
    if (!messageInput.trim() || sending) return

    const content = messageInput.trim()

    setMessageInput('')
    setSending(true)

    try {
      await onSendMessage(content)
    } catch (error) {
      console.error('Failed to send message:', error)

      // Restore message input on error
      setMessageInput(content)
    } finally {
      setSending(false)
    }
  }

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // Format message timestamp
  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp * 1000)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' })
    }
  }

  // Get contact display name
  const getContactDisplayName = () => {
    return [contact.first_name, contact.last_name].filter(Boolean).join(' ') || 
           `Facebook User ${contact.facebook_user_id}`
  }

  // Get delivery status icon
  const getDeliveryStatusIcon = (status?: string) => {
    switch (status) {
      case 'sent':
        return <CheckCircle className='w-3 h-3 text-gray-400' />
      case 'delivered':
        return <CheckCircle className='w-3 h-3 text-blue-500' />
      case 'read':
        return <CheckCircle className='w-3 h-3 text-green-500' />
      case 'failed':
        return <AlertCircle className='w-3 h-3 text-red-500' />
      default:
        return <Clock className='w-3 h-3 text-gray-400' />
    }
  }

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Header */}
      <div className='flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50'>
        <div className='flex items-center space-x-3'>
          {contact.profile_pic ? (
            <img
              src={contact.profile_pic}
              alt={getContactDisplayName()}
              className='w-10 h-10 rounded-full object-cover'
            />
          ) : (
            <div className='w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center'>
              <Facebook className='w-5 h-5 text-blue-600' />
            </div>
          )}
          <div>
            <h3 className='font-semibold text-gray-900'>{getContactDisplayName()}</h3>
            <div className='flex items-center space-x-2 text-sm text-gray-500'>
              <Facebook className='w-3 h-3' />
              <span>Facebook Messenger</span>
              {contact.is_active && (
                <>
                  <span>•</span>
                  <span className='text-green-600'>Active</span>
                </>
              )}
            </div>
          </div>
        </div>

        <div className='flex items-center space-x-2'>
          <button
            className='p-2 text-gray-600 hover:bg-gray-200 rounded-lg transition-colors'
            title='More options'
          >
            <MoreVertical className='w-5 h-5' />
          </button>
        </div>
      </div>

      {/* Messages */}
      <div className='flex-1 overflow-y-auto p-4 space-y-4'>
        {isLoading && (
          <div className='flex items-center justify-center py-8'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600'></div>
          </div>
        )}

        {!isLoading && messages.length === 0 && (
          <div className='flex flex-col items-center justify-center py-8 text-gray-500'>
            <Facebook className='w-12 h-12 mb-3 text-gray-300' />
            <p className='text-sm'>No messages yet</p>
            <p className='text-xs mt-1'>Start a conversation with {getContactDisplayName()}</p>
          </div>
        )}

        {messages.map((message) => {
          const isFromAgent = message.author_uuid === currentUserId || message.author_uuid !== null
          const isSystemMessage = message.flags === 1

          return (
            <div
              key={message.message_id}
              className={`flex ${isFromAgent ? 'justify-end' : 'justify-start'} ${
                isSystemMessage ? 'justify-center' : ''
              }`}
            >
              {isSystemMessage ? (
                <div className='bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full max-w-xs text-center'>
                  {message.content}
                </div>
              ) : (
                <div className={`max-w-xs lg:max-w-md ${isFromAgent ? 'order-2' : 'order-1'}`}>
                  <div
                    className={`px-4 py-2 rounded-lg ${
                      isFromAgent
                        ? 'bg-blue-600 text-white rounded-br-sm'
                        : 'bg-gray-100 text-gray-900 rounded-bl-sm'
                    }`}
                  >
                    <p className='text-sm whitespace-pre-wrap'>{message.content}</p>
                  </div>

                  <div className={`flex items-center mt-1 space-x-1 text-xs text-gray-500 ${
                    isFromAgent ? 'justify-end' : 'justify-start'
                  }`}>
                    <span>{formatTimestamp(message.created_at)}</span>
                    {isFromAgent && (
                      <div className='flex items-center space-x-1'>
                        {getDeliveryStatusIcon(message.delivery_status)}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )
        })}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className='p-4 border-t border-gray-200 bg-gray-50'>
        <div className='flex items-end space-x-3'>
          <button
            className='p-2 text-gray-600 hover:bg-gray-200 rounded-lg transition-colors'
            title='Attach file'
          >
            <Paperclip className='w-5 h-5' />
          </button>

          <div className='flex-1 relative'>
            <textarea
              value={messageInput}
              onChange={e => setMessageInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={`Message ${getContactDisplayName()}...`}
              className='w-full px-4 py-2 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
              rows={1}
              style={{
                minHeight: '40px',
                maxHeight: '120px',
                height: 'auto'
              }}
              onInput={e => {
                const target = e.target as HTMLTextAreaElement

                target.style.height = 'auto'
                target.style.height = `${Math.min(target.scrollHeight, 120)}px`
              }}
            />
          </div>

          <button
            className='p-2 text-gray-600 hover:bg-gray-200 rounded-lg transition-colors'
            title='Add emoji'
            onClick={() => setShowEmojiPicker(!showEmojiPicker)}
          >
            <Smile className='w-5 h-5' />
          </button>

          <button
            onClick={handleSendMessage}
            disabled={!messageInput.trim() || sending}
            className='p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
            title='Send message'
          >
            {sending ? (
              <div className='animate-spin rounded-full h-5 w-5 border-b-2 border-white'></div>
            ) : (
              <Send className='w-5 h-5' />
            )}
          </button>
        </div>

        {/* Character count */}
        <div className='flex justify-between items-center mt-2 text-xs text-gray-500'>
          <span>Facebook Messenger</span>
          <span className={messageInput.length > 1800 ? 'text-red-500' : ''}>
            {messageInput.length}/2000
          </span>
        </div>
      </div>
    </div>
  )
}

export default FacebookChatInterface
