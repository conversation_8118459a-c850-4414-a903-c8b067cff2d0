// Facebook Room List Component
// Displays Facebook conversations with assignment controls and filtering

'use client'

import React, { useState, useEffect, useCallback } from 'react'

import { MessageSquare, Search, UserCheck, Clock, MessageCircle } from 'lucide-react'

interface FacebookContact {
  contact_uuid: string
  facebook_user_id: string
  first_name: string | null
  last_name: string | null
  profile_pic: string | null
  locale: string | null
  timezone: number | null
  is_active: boolean
  contact_info: Record<string, any>
  last_interaction_date: string | null
}

interface FacebookRoom {
  facebook_room_uuid: string
  internal_room_uuid: string
  facebook_contact_uuid: string
  assigned_agent_uuid: string | null
  assigned_agent: {
    user_uuid: string
    username: string
    display_name: string
    first_name: string | null
    last_name: string | null
  } | null
  room_status: string
  last_message_at: string | null
  conversation_metadata: Record<string, any>
  contact: FacebookContact
  chat_room: {
    room_uuid: string
    room_name: string | null
    room_type: string
    is_active: boolean
    is_archived: boolean
  }
  is_assigned: boolean
  display_name: string
}

interface Agent {
  user_uuid: string
  username: string
  display_name: string
  first_name: string | null
  last_name: string | null
}

interface FacebookRoomListProps {
  onRoomSelect: (roomUuid: string) => void
  selectedRoomId?: string | null
  compact?: boolean
  showAssignmentControls?: boolean
  className?: string
}

const FacebookRoomList: React.FC<FacebookRoomListProps> = ({
  onRoomSelect,
  selectedRoomId,
  showAssignmentControls = false,
  className = ''
}) => {
  const [rooms, setRooms] = useState<FacebookRoom[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [agents, setAgents] = useState<Agent[]>([])
  const [assigningRoom, setAssigningRoom] = useState<string | null>(null)
  const [statusFilter, setStatusFilter] = useState<'all' | 'assigned' | 'unassigned'>('all')

  // Fetch Facebook rooms
  const fetchRooms = useCallback(async (search?: string) => {
    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams()

      if (search) params.append('search', search)

      const response = await fetch(`/api/internal-chat/facebook/chat-rooms?${params}`)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))

        throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch chat rooms`)
      }

      const data = await response.json()

      if (!data.chat_rooms || !Array.isArray(data.chat_rooms)) {
        throw new Error('Invalid response format from server')
      }

      setRooms(data.chat_rooms)
    } catch (err) {
      console.error('Error fetching Facebook rooms:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to load chat rooms'

      setError(errorMessage)
      setRooms([])
    } finally {
      setLoading(false)
    }
  }, [])

  // Fetch available agents
  const fetchAgents = useCallback(async () => {
    try {
      const response = await fetch('/api/internal-chat/facebook/available-agents')

      if (response.ok) {
        const data = await response.json()

        setAgents(data.agents || [])
      }
    } catch (err) {
      console.error('Error fetching agents:', err)
    }
  }, [])

  // Initial load
  useEffect(() => {
    fetchRooms()

    if (showAssignmentControls) {
      fetchAgents()
    }
  }, [fetchRooms, showAssignmentControls, fetchAgents])

  // Search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      fetchRooms(searchTerm)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchTerm, fetchRooms])

  // Filter rooms based on assignment status
  const filteredRooms = rooms.filter(room => {
    if (statusFilter === 'assigned') return room.is_assigned
    if (statusFilter === 'unassigned') return !room.is_assigned
    
return true
  })

  // Handle agent assignment
  const handleAssignAgent = async (roomUuid: string, agentUuid: string) => {
    try {
      setAssigningRoom(roomUuid)

      const response = await fetch('/api/internal-chat/facebook/assign-agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          facebook_room_uuid: roomUuid,
          agent_uuid: agentUuid
        })
      })

      if (!response.ok) {
        throw new Error('Failed to assign agent')
      }

      // Refresh rooms list
      await fetchRooms(searchTerm)
    } catch (err) {
      console.error('Error assigning agent:', err)
      setError(err instanceof Error ? err.message : 'Failed to assign agent')
    } finally {
      setAssigningRoom(null)
    }
  }

  // Format last message time
  const formatLastMessageTime = (timestamp: string | null) => {
    if (!timestamp) return 'No messages'

    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 1) {
      return 'Just now'
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`
    } else {
      return date.toLocaleDateString()
    }
  }

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Search and Filter Header */}
      <div className='p-4 border-b border-gray-200'>
        <div className='relative mb-3'>
          <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4' />
          <input
            type='text'
            placeholder='Search Facebook conversations...'
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className='w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
          />
        </div>

        {/* Status Filter */}
        <div className='flex space-x-2'>
          {[
            { key: 'all', label: 'All', count: rooms.length },
            { key: 'assigned', label: 'Assigned', count: rooms.filter(r => r.is_assigned).length },
            { key: 'unassigned', label: 'Unassigned', count: rooms.filter(r => !r.is_assigned).length }
          ].map(filter => (
            <button
              key={filter.key}
              onClick={() => setStatusFilter(filter.key as any)}
              className={`flex items-center space-x-1 px-3 py-1 rounded-full text-xs transition-colors ${
                statusFilter === filter.key
                  ? 'bg-blue-100 text-blue-700'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              <span>{filter.label}</span>
              <span className='bg-white px-1.5 py-0.5 rounded-full text-xs'>{filter.count}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Room List */}
      <div className='flex-1 overflow-y-auto'>
        {loading && (
          <div className='flex items-center justify-center h-32'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600'></div>
          </div>
        )}

        {error && (
          <div className='p-4 text-center'>
            <div className='text-red-600 mb-2'>
              <MessageCircle className='w-8 h-8 mx-auto mb-2' />
              <p className='text-sm'>{error}</p>
            </div>
            <button
              onClick={() => fetchRooms(searchTerm)}
              className='px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors'
            >
              Try Again
            </button>
          </div>
        )}

        {!loading && !error && filteredRooms.length === 0 && (
          <div className='p-6 text-center text-gray-500'>
            <MessageSquare className='w-12 h-12 mx-auto mb-3 text-gray-300' />
            <p className='text-sm'>No Facebook conversations found</p>
            {searchTerm && (
              <p className='text-xs mt-1'>Try adjusting your search terms</p>
            )}
          </div>
        )}

        {!loading && !error && filteredRooms.length > 0 && (
          <div className='divide-y divide-gray-100'>
            {filteredRooms.map(room => (
              <div
                key={room.facebook_room_uuid}
                onClick={() => onRoomSelect(room.internal_room_uuid)}
                className={`p-4 hover:bg-gray-50 cursor-pointer transition-colors ${
                  selectedRoomId === room.internal_room_uuid ? 'bg-blue-50 border-r-2 border-blue-500' : ''
                }`}
              >
                <div className='flex items-start space-x-3'>
                  {/* Avatar */}
                  <div className='flex-shrink-0'>
                    {room.contact.profile_pic ? (
                      <img
                        src={room.contact.profile_pic}
                        alt={room.display_name}
                        className='w-10 h-10 rounded-full object-cover'
                      />
                    ) : (
                      <div className='w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center'>
                        <MessageSquare className='w-5 h-5 text-blue-600' />
                      </div>
                    )}
                  </div>

                  {/* Content */}
                  <div className='flex-1 min-w-0'>
                    <div className='flex items-center justify-between mb-1'>
                      <h3 className='text-sm font-medium text-gray-900 truncate'>{room.display_name}</h3>
                      <span className='text-xs text-gray-500 flex-shrink-0'>
                        {formatLastMessageTime(room.last_message_at)}
                      </span>
                    </div>

                    <div className='flex items-center space-x-2 text-xs text-gray-500 mb-2'>
                      <span>@{room.contact.facebook_user_id}</span>
                      {room.contact.locale && <span>• {room.contact.locale}</span>}
                      {!room.contact.is_active && (
                        <span className='px-1.5 py-0.5 bg-red-100 text-red-600 rounded'>Inactive</span>
                      )}
                    </div>

                    {/* Assignment Status */}
                    <div className='flex items-center justify-between'>
                      <div className='flex items-center space-x-2'>
                        {room.is_assigned ? (
                          <div className='flex items-center space-x-1 text-xs text-green-600'>
                            <UserCheck className='w-3 h-3' />
                            <span>Assigned</span>
                          </div>
                        ) : (
                          <div className='flex items-center space-x-1 text-xs text-orange-600'>
                            <Clock className='w-3 h-3' />
                            <span>Unassigned</span>
                          </div>
                        )}
                      </div>

                      {/* Assignment Controls */}
                      {showAssignmentControls && (
                        <div className='flex items-center space-x-2'>
                          {assigningRoom === room.facebook_room_uuid ? (
                            <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600'></div>
                          ) : (
                            <>
                              {/* Show assigned agent or assignment dropdown */}
                              {room.assigned_agent ? (
                                <div className='flex items-center text-xs text-green-600'>
                                  <UserCheck className='w-3 h-3 mr-1' />
                                  <span>{room.assigned_agent.display_name}</span>
                                </div>
                              ) : (
                                <select
                                  value={room.assigned_agent_uuid || ''}
                                  onChange={e => {
                                    e.stopPropagation()

                                    if (e.target.value) {
                                      handleAssignAgent(room.facebook_room_uuid, e.target.value)
                                    }
                                  }}
                                  className='text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500'
                                  onClick={e => e.stopPropagation()}
                                >
                                  <option value=''>Assign Agent</option>
                                  {agents.map(agent => (
                                    <option key={agent.user_uuid} value={agent.user_uuid}>
                                      {agent.display_name}
                                    </option>
                                  ))}
                                </select>
                              )}
                            </>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default FacebookRoomList
