// Facebook Agent Assignment Component
// Handles agent assignment for Facebook conversations

'use client'

import React, { useState, useEffect } from 'react'

import { UserCheck, Users, Search, Check, X, AlertCircle } from 'lucide-react'

interface Agent {
  user_uuid: string
  username: string
  first_name: string | null
  last_name: string | null
  is_online?: boolean
  current_conversations?: number
}

interface FacebookAgentAssignmentProps {
  currentAssignedAgent?: string | null
  onAssignAgent: (agentUuid: string) => Promise<void>
  onUnassignAgent: () => Promise<void>
  className?: string
}

const FacebookAgentAssignment: React.FC<FacebookAgentAssignmentProps> = ({
  currentAssignedAgent,
  onAssignAgent,
  onUnassignAgent,
  className = ''
}) => {
  const [agents, setAgents] = useState<Agent[]>([])
  const [loading, setLoading] = useState(false)
  const [assigning, setAssigning] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [showDropdown, setShowDropdown] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch available agents
  const fetchAgents = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/internal-chat/users/search?role=agent')

      if (!response.ok) {
        throw new Error('Failed to fetch agents')
      }

      const data = await response.json()

      setAgents(data.users || [])
    } catch (err) {
      console.error('Error fetching agents:', err)
      setError(err instanceof Error ? err.message : 'Failed to load agents')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAgents()
  }, [])

  // Handle agent assignment
  const handleAssignAgent = async (agentUuid: string) => {
    if (assigning) return

    setAssigning(agentUuid)
    setError(null)

    try {
      await onAssignAgent(agentUuid)
      setShowDropdown(false)
    } catch (err) {
      console.error('Error assigning agent:', err)
      setError(err instanceof Error ? err.message : 'Failed to assign agent')
    } finally {
      setAssigning(null)
    }
  }

  // Handle agent unassignment
  const handleUnassignAgent = async () => {
    if (assigning) return

    setAssigning('unassign')
    setError(null)

    try {
      await onUnassignAgent()
    } catch (err) {
      console.error('Error unassigning agent:', err)
      setError(err instanceof Error ? err.message : 'Failed to unassign agent')
    } finally {
      setAssigning(null)
    }
  }

  // Filter agents based on search term
  const filteredAgents = agents.filter(agent => {
    const fullName = `${agent.first_name || ''} ${agent.last_name || ''}`.trim()
    const searchLower = searchTerm.toLowerCase()

    return (
      agent.username.toLowerCase().includes(searchLower) ||
      fullName.toLowerCase().includes(searchLower)
    )
  })

  // Get agent display name
  const getAgentDisplayName = (agent: Agent) => {
    const fullName = `${agent.first_name || ''} ${agent.last_name || ''}`.trim()

    return fullName || agent.username
  }

  // Get currently assigned agent
  const assignedAgent = agents.find(agent => agent.user_uuid === currentAssignedAgent)

  return (
    <div className={`relative ${className}`}>
      {/* Assignment Status */}
      <div className='flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200'>
        <div className='flex items-center space-x-3'>
          <div className='p-2 bg-blue-100 rounded-lg'>
            <UserCheck className='w-4 h-4 text-blue-600' />
          </div>
          <div>
            <h4 className='text-sm font-medium text-gray-900'>Agent Assignment</h4>
            {assignedAgent ? (
              <p className='text-sm text-gray-600'>
                Assigned to: <span className='font-medium'>{getAgentDisplayName(assignedAgent)}</span>
              </p>
            ) : (
              <p className='text-sm text-orange-600'>No agent assigned</p>
            )}
          </div>
        </div>

        <div className='flex items-center space-x-2'>
          {assignedAgent && (
            <button
              onClick={handleUnassignAgent}
              disabled={assigning === 'unassign'}
              className='p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50'
              title='Unassign agent'
            >
              {assigning === 'unassign' ? (
                <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-red-600'></div>
              ) : (
                <X className='w-4 h-4' />
              )}
            </button>
          )}

          <button
            onClick={() => setShowDropdown(!showDropdown)}
            className='p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors'
            title='Assign agent'
          >
            <Users className='w-4 h-4' />
          </button>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className='mt-2 p-3 bg-red-50 border border-red-200 rounded-lg'>
          <div className='flex items-start space-x-2'>
            <AlertCircle className='w-4 h-4 text-red-600 mt-0.5' />
            <div>
              <p className='text-sm text-red-800'>{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Agent Selection Dropdown */}
      {showDropdown && (
        <div className='absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50'>
          <div className='p-3 border-b border-gray-200'>
            <div className='relative'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4' />
              <input
                type='text'
                placeholder='Search agents...'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className='w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm'
              />
            </div>
          </div>

          <div className='max-h-64 overflow-y-auto'>
            {loading && (
              <div className='flex items-center justify-center py-8'>
                <div className='animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600'></div>
              </div>
            )}

            {!loading && filteredAgents.length === 0 && (
              <div className='p-4 text-center text-gray-500'>
                <Users className='w-8 h-8 mx-auto mb-2 text-gray-300' />
                <p className='text-sm'>No agents found</p>
                {searchTerm && (
                  <p className='text-xs mt-1'>Try adjusting your search terms</p>
                )}
              </div>
            )}

            {!loading && filteredAgents.length > 0 && (
              <div className='divide-y divide-gray-100'>
                {filteredAgents.map(agent => (
                  <button
                    key={agent.user_uuid}
                    onClick={() => handleAssignAgent(agent.user_uuid)}
                    disabled={assigning === agent.user_uuid || agent.user_uuid === currentAssignedAgent}
                    className='w-full p-3 text-left hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
                  >
                    <div className='flex items-center justify-between'>
                      <div className='flex items-center space-x-3'>
                        <div className='w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center'>
                          <span className='text-sm font-medium text-blue-600'>
                            {(agent.first_name?.[0] || agent.username[0]).toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <p className='text-sm font-medium text-gray-900'>
                            {getAgentDisplayName(agent)}
                          </p>
                          <p className='text-xs text-gray-500'>@{agent.username}</p>
                        </div>
                      </div>

                      <div className='flex items-center space-x-2'>
                        {agent.is_online && (
                          <div className='w-2 h-2 bg-green-500 rounded-full' title='Online'></div>
                        )}
                        
                        {agent.current_conversations !== undefined && (
                          <span className='text-xs text-gray-500'>
                            {agent.current_conversations} chats
                          </span>
                        )}

                        {agent.user_uuid === currentAssignedAgent && (
                          <Check className='w-4 h-4 text-green-600' />
                        )}

                        {assigning === agent.user_uuid && (
                          <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600'></div>
                        )}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>

          <div className='p-3 border-t border-gray-200 bg-gray-50'>
            <button
              onClick={() => setShowDropdown(false)}
              className='w-full px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-100 transition-colors text-sm'
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {/* Click outside to close dropdown */}
      {showDropdown && (
        <div
          className='fixed inset-0 z-40'
          onClick={() => setShowDropdown(false)}
        />
      )}
    </div>
  )
}

export default FacebookAgentAssignment
