// Facebook Messenger Configuration Component
// Provides setup instructions and configuration form for Facebook Messenger integration

'use client'

import React, { useState, useEffect } from 'react'

import { MessageCircle, ExternalLink, CheckCircle, AlertCircle, Copy, Eye, EyeOff, RefreshCw } from 'lucide-react'

interface FacebookConfig {
  config_uuid: string
  app_id: string
  page_id: string
  webhook_url: string
  is_active: boolean
  allowed_events: string[]
  page_settings: Record<string, any>
}

interface FormData {
  app_id: string
  app_secret: string
  page_id: string
  page_access_token: string
  verify_token: string
  webhook_url: string
  allowed_events: string[]
}

interface FacebookMessengerConfigProps {
  onConfigSaved?: () => void
  className?: string
}

const FacebookMessengerConfig: React.FC<FacebookMessengerConfigProps> = ({ onConfigSaved, className = '' }) => {
  const [config, setConfig] = useState<FacebookConfig | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [domainUuid, setDomainUuid] = useState<string>('')

  const [showSecrets, setShowSecrets] = useState({
    app_secret: false,
    page_access_token: false,
    verify_token: false
  })

  const [formData, setFormData] = useState<FormData>({
    app_id: '',
    app_secret: '',
    page_id: '',
    page_access_token: '',
    verify_token: '',
    webhook_url: '',
    allowed_events: ['messages', 'messaging_postbacks', 'messaging_optins']
  })

  // Load existing configuration

  // Load domain UUID
  const loadDomainUuid = async () => {
    try {
      const response = await fetch('/api/internal-chat/users/search?role=current')

      if (response.ok) {
        const data = await response.json()

        if (data.user?.domain_uuid) {
          setDomainUuid(data.user.domain_uuid)
        }
      }
    } catch (err) {
      console.error('Error loading domain UUID:', err)
    }
  }

  // Load existing configuration
  const loadConfig = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/internal-chat/facebook/page-config')

      if (response.status === 404) {
        setConfig(null)

        return
      }

      if (!response.ok) {
        throw new Error('Failed to fetch configuration')
      }

      const data = await response.json()

      setConfig(data.config)

      if (data.config) {
        setFormData({
          app_id: data.config.app_id || '',
          app_secret: '',
          page_id: data.config.page_id || '',
          page_access_token: '',
          verify_token: '',
          webhook_url: data.config.webhook_url || '',
          allowed_events: data.config.allowed_events || ['messages', 'messaging_postbacks', 'messaging_optins']
        })
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load configuration')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadDomainUuid()
    loadConfig()
  }, [])

  // Save configuration
  const handleSave = async () => {
    setSaving(true)
    setError(null)
    setSuccess(null)

    try {
      // Validate required fields
      const requiredFields = {
        app_id: 'App ID',
        app_secret: 'App Secret',
        page_id: 'Page ID',
        page_access_token: 'Page Access Token',
        verify_token: 'Verify Token'
      }

      const missingFields = Object.entries(requiredFields)
        .filter(([key]) => {
          const value = formData[key as keyof FormData]

          return !value || (typeof value === 'string' && !value.trim())
        })
        .map(([, label]) => label)

      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(', ')}`)
      }

      const payload = {
        app_id: formData.app_id.trim(),
        app_secret: formData.app_secret.trim(),
        page_id: formData.page_id.trim(),
        page_access_token: formData.page_access_token.trim(),
        verify_token: formData.verify_token.trim(),
        webhook_url: formData.webhook_url.trim() || webhookUrl,
        allowed_events: formData.allowed_events
      }

      const response = await fetch('/api/internal-chat/facebook/page-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      if (!response.ok) {
        const errorData = await response.json()

        throw new Error(errorData.error || 'Failed to save configuration')
      }

      setSuccess('Facebook Messenger configuration saved successfully!')

      await loadConfig()
      onConfigSaved?.()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save configuration')
    } finally {
      setSaving(false)
    }
  }

  // Copy to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  // Generate webhook URL
  const webhookUrl = formData.webhook_url ||
    (domainUuid
      ? `${window.location.origin}/api/internal-chat/facebook/webhook?domain=${domainUuid}`
      : `${window.location.origin}/api/internal-chat/facebook/webhook?domain={YOUR_DOMAIN_UUID}`
    )

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600'></div>
      </div>
    )
  }

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      <div className='flex-1 overflow-y-auto p-6'>
        <div className='max-w-4xl mx-auto'>
          {/* Header */}
          <div className='flex items-center space-x-3 mb-6'>
            <div className='p-2 bg-blue-100 rounded-lg'>
              <MessageCircle className='w-6 h-6 text-blue-600' />
            </div>
            <div>
              <h2 className='text-xl font-bold text-gray-900'>Facebook Messenger Configuration</h2>
              <p className='text-sm text-gray-600'>Set up Facebook Messenger integration for customer conversations</p>
            </div>
          </div>

          {/* Status */}
          {config?.is_active ? (
            <div className='bg-green-50 border border-green-200 rounded-lg p-4 mb-6'>
              <div className='flex items-start space-x-3'>
                <CheckCircle className='w-5 h-5 text-green-600 mt-0.5' />
                <div>
                  <h4 className='text-sm font-medium text-green-900'>Facebook Messenger Active</h4>
                  <p className='text-sm text-green-700 mt-1'>
                    Your Facebook Messenger integration is configured and active. Page: {config.page_settings?.page_name || config.page_id}
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <div className='bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6'>
              <div className='flex items-start space-x-3'>
                <AlertCircle className='w-5 h-5 text-yellow-600 mt-0.5' />
                <div>
                  <h4 className='text-sm font-medium text-yellow-900'>Configuration Required</h4>
                  <p className='text-sm text-yellow-700 mt-1'>
                    Complete the setup below to enable Facebook Messenger conversations.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Setup Instructions */}
          <div className='bg-gray-50 rounded-lg p-6 mb-6'>
            <h3 className='text-lg font-medium text-gray-900 mb-4'>Setup Instructions</h3>
            <div className='space-y-4'>
              <div className='flex items-start space-x-3'>
                <div className='flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium'>
                  1
                </div>
                <div>
                  <h4 className='font-medium text-gray-900'>Create Facebook App</h4>
                  <p className='text-sm text-gray-600 mt-1'>
                    Go to{' '}
                    <a
                      href='https://developers.facebook.com/apps'
                      target='_blank'
                      rel='noopener noreferrer'
                      className='text-blue-600 hover:text-blue-700 inline-flex items-center'
                    >
                      Facebook Developers <ExternalLink className='w-3 h-3 ml-1' />
                    </a>{' '}
                    and create a new app with Messenger product.
                  </p>
                </div>
              </div>

              <div className='flex items-start space-x-3'>
                <div className='flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium'>
                  2
                </div>
                <div>
                  <h4 className='font-medium text-gray-900'>Connect Facebook Page</h4>
                  <p className='text-sm text-gray-600 mt-1'>
                    In your Facebook app, go to Messenger → Settings and connect your Facebook Page. Generate a Page Access Token.
                  </p>
                </div>
              </div>

              <div className='flex items-start space-x-3'>
                <div className='flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium'>
                  3
                </div>
                <div>
                  <h4 className='font-medium text-gray-900'>Configure Webhook</h4>
                  <p className='text-sm text-gray-600 mt-1'>
                    Set up webhook with the URL below and subscribe to messages, messaging_postbacks, and messaging_optins events.
                  </p>
                  <div className='mt-2 space-y-2'>
                    <div className='p-3 bg-gray-100 rounded border text-sm font-mono flex items-center justify-between'>
                      <span className='truncate'>{webhookUrl}</span>
                      <button
                        onClick={() => copyToClipboard(webhookUrl)}
                        className='ml-2 p-1 text-gray-500 hover:text-gray-700'
                        title='Copy webhook URL'
                      >
                        <Copy className='w-4 h-4' />
                      </button>
                    </div>
                    {domainUuid && (
                      <div className='text-xs text-gray-600'>
                        <p className='mb-1'>
                          <strong>Test URL:</strong>{' '}
                          <a
                            href={`${window.location.origin}/api/internal-chat/facebook/webhook-test?domain=${domainUuid}`}
                            target='_blank'
                            rel='noopener noreferrer'
                            className='text-blue-600 hover:text-blue-700 underline'
                          >
                            Test webhook accessibility
                          </a>
                        </p>
                        <p>
                          <strong>Domain UUID:</strong> <code className='bg-gray-200 px-1 rounded'>{domainUuid}</code>
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Configuration Form */}
          <div className='bg-white border border-gray-200 rounded-lg p-6'>
            <h3 className='text-lg font-medium text-gray-900 mb-4'>Configuration</h3>

            {error && (
              <div className='bg-red-50 border border-red-200 rounded-lg p-4 mb-4'>
                <div className='flex items-start space-x-3'>
                  <AlertCircle className='w-5 h-5 text-red-600 mt-0.5' />
                  <div>
                    <h4 className='text-sm font-medium text-red-900'>Configuration Error</h4>
                    <p className='text-sm text-red-700 mt-1'>{error}</p>
                  </div>
                </div>
              </div>
            )}

            {success && (
              <div className='bg-green-50 border border-green-200 rounded-lg p-4 mb-4'>
                <div className='flex items-start space-x-3'>
                  <CheckCircle className='w-5 h-5 text-green-600 mt-0.5' />
                  <div>
                    <h4 className='text-sm font-medium text-green-900'>Success</h4>
                    <p className='text-sm text-green-700 mt-1'>{success}</p>
                  </div>
                </div>
              </div>
            )}

            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              {/* App ID */}
              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Facebook App ID <span className='text-red-500'>*</span>
                </label>
                <input
                  type='text'
                  value={formData.app_id}
                  onChange={e => setFormData({ ...formData, app_id: e.target.value })}
                  className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500'
                  placeholder='Your Facebook App ID'
                />
              </div>

              {/* Page ID */}
              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Facebook Page ID <span className='text-red-500'>*</span>
                </label>
                <input
                  type='text'
                  value={formData.page_id}
                  onChange={e => setFormData({ ...formData, page_id: e.target.value })}
                  className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500'
                  placeholder='Your Facebook Page ID'
                />
              </div>

              {/* App Secret */}
              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  App Secret {!config && <span className='text-red-500'>*</span>}
                </label>
                <div className='relative'>
                  <input
                    type={showSecrets.app_secret ? 'text' : 'password'}
                    value={formData.app_secret}
                    onChange={e => setFormData({ ...formData, app_secret: e.target.value })}
                    className='w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500'
                    placeholder={config ? 'Leave blank to keep current' : 'Your App Secret'}
                  />
                  <button
                    type='button'
                    onClick={() => setShowSecrets({ ...showSecrets, app_secret: !showSecrets.app_secret })}
                    className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600'
                  >
                    {showSecrets.app_secret ? <EyeOff className='w-4 h-4' /> : <Eye className='w-4 h-4' />}
                  </button>
                </div>
              </div>

              {/* Page Access Token */}
              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Page Access Token {!config && <span className='text-red-500'>*</span>}
                </label>
                <div className='relative'>
                  <input
                    type={showSecrets.page_access_token ? 'text' : 'password'}
                    value={formData.page_access_token}
                    onChange={e => setFormData({ ...formData, page_access_token: e.target.value })}
                    className='w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500'
                    placeholder={config ? 'Leave blank to keep current' : 'Your Page Access Token'}
                  />
                  <button
                    type='button'
                    onClick={() => setShowSecrets({ ...showSecrets, page_access_token: !showSecrets.page_access_token })}
                    className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600'
                  >
                    {showSecrets.page_access_token ? <EyeOff className='w-4 h-4' /> : <Eye className='w-4 h-4' />}
                  </button>
                </div>
              </div>

              {/* Verify Token */}
              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Webhook Verify Token {!config && <span className='text-red-500'>*</span>}
                </label>
                <div className='relative'>
                  <input
                    type={showSecrets.verify_token ? 'text' : 'password'}
                    value={formData.verify_token}
                    onChange={e => setFormData({ ...formData, verify_token: e.target.value })}
                    className='w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500'
                    placeholder={config ? 'Leave blank to keep current' : 'Your Webhook Verify Token'}
                  />
                  <button
                    type='button'
                    onClick={() => setShowSecrets({ ...showSecrets, verify_token: !showSecrets.verify_token })}
                    className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600'
                  >
                    {showSecrets.verify_token ? <EyeOff className='w-4 h-4' /> : <Eye className='w-4 h-4' />}
                  </button>
                </div>
              </div>

              {/* Webhook URL */}
              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>Webhook URL (Optional)</label>
                <input
                  type='text'
                  value={formData.webhook_url}
                  onChange={e => setFormData({ ...formData, webhook_url: e.target.value })}
                  className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500'
                  placeholder='Auto-generated if left blank'
                />
              </div>
            </div>

            {/* Actions */}
            <div className='flex items-center justify-between mt-6 pt-4 border-t border-gray-200'>
              <button
                onClick={loadConfig}
                className='flex items-center space-x-2 px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors'
              >
                <RefreshCw className='w-4 h-4' />
                <span>Refresh</span>
              </button>

              <button
                onClick={handleSave}
                disabled={saving}
                className='flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50'
              >
                {saving && <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white'></div>}
                <span>{saving ? 'Saving...' : config ? 'Update Configuration' : 'Save Configuration'}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FacebookMessengerConfig
