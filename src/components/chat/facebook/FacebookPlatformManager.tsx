// Facebook Platform Manager Component
// Main component for managing Facebook contacts, conversations, and agent assignments

'use client'

import React, { useState, useEffect } from 'react'

import { MessageSquare, Users, MessageCircle, Settings, UserCheck, Plus, Filter } from 'lucide-react'

import FacebookRoomList from './FacebookRoomList'
import FacebookContactSelector from './FacebookContactSelector'
import FacebookMessengerConfig from './FacebookMessengerConfig'

interface FacebookStats {
  total_contacts: number
  active_rooms: number
  unassigned_rooms: number
  total_messages: number
}

interface FacebookPlatformManagerProps {
  onRoomSelect: (roomUuid: string) => void
  selectedRoomId?: string | null
  className?: string
}

const FacebookPlatformManager: React.FC<FacebookPlatformManagerProps> = ({
  onRoomSelect,
  selectedRoomId,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState<'conversations' | 'contacts' | 'settings'>('conversations')
  const [stats, setStats] = useState<FacebookStats | null>(null)
  const [showContactSelector, setShowContactSelector] = useState(false)
  const [filterStatus, setFilterStatus] = useState<'all' | 'assigned' | 'unassigned'>('all')

  // Fetch Facebook statistics
  const fetchStats = async () => {
    try {
      const response = await fetch('/api/internal-chat/facebook/stats')

      if (response.ok) {
        const data = await response.json()

        setStats({
          total_contacts: data.stats?.total_contacts || 0,
          active_rooms: data.stats?.active_rooms || 0,
          unassigned_rooms: data.stats?.unassigned_rooms || 0,
          total_messages: (data.stats?.total_messages_sent || 0) + (data.stats?.total_messages_received || 0)
        })
      }
    } catch (error) {
      console.error('Error fetching Facebook stats:', error)
    }
  }

  useEffect(() => {
    fetchStats()
  }, [])

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Header */}
      <div className='p-4 border-b border-gray-200 bg-gray-50'>
        <div className='flex items-center justify-between mb-4'>
          <div className='flex items-center space-x-3'>
            <div className='p-2 bg-blue-100 rounded-lg'>
              <MessageSquare className='w-6 h-6 text-blue-600' />
            </div>
            <div>
              <h1 className='text-xl font-bold text-gray-900'>Facebook Platform</h1>
              <p className='text-sm text-gray-600'>Manage Facebook conversations and agent assignments</p>
            </div>
          </div>

          <div className='flex items-center space-x-2'>
            <button
              onClick={fetchStats}
              className='p-2 text-gray-600 hover:bg-gray-200 rounded-lg transition-colors'
              title='Refresh statistics'
            >
              <Settings className='w-5 h-5' />
            </button>
          </div>
        </div>

        {/* Statistics */}
        {stats && (
          <div className='grid grid-cols-4 gap-4'>
            <div className='bg-white rounded-lg p-3 border border-gray-200'>
              <div className='flex items-center space-x-2'>
                <Users className='w-4 h-4 text-blue-600' />
                <span className='text-sm text-gray-600'>Contacts</span>
              </div>
              <p className='text-lg font-semibold text-gray-900 mt-1'>{stats.total_contacts}</p>
            </div>

            <div className='bg-white rounded-lg p-3 border border-gray-200'>
              <div className='flex items-center space-x-2'>
                <MessageCircle className='w-4 h-4 text-green-600' />
                <span className='text-sm text-gray-600'>Active Rooms</span>
              </div>
              <p className='text-lg font-semibold text-gray-900 mt-1'>{stats.active_rooms}</p>
            </div>

            <div className='bg-white rounded-lg p-3 border border-gray-200'>
              <div className='flex items-center space-x-2'>
                <UserCheck className='w-4 h-4 text-orange-600' />
                <span className='text-sm text-gray-600'>Unassigned</span>
              </div>
              <p className='text-lg font-semibold text-gray-900 mt-1'>{stats.unassigned_rooms}</p>
            </div>

            <div className='bg-white rounded-lg p-3 border border-gray-200'>
              <div className='flex items-center space-x-2'>
                <MessageCircle className='w-4 h-4 text-purple-600' />
                <span className='text-sm text-gray-600'>Messages</span>
              </div>
              <p className='text-lg font-semibold text-gray-900 mt-1'>{stats.total_messages}</p>
            </div>
          </div>
        )}
      </div>

      {/* Navigation Tabs */}
      <div className='border-b border-gray-200'>
        <nav className='flex space-x-8 px-4'>
          <button
            onClick={() => setActiveTab('conversations')}
            className={`py-3 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'conversations'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className='flex items-center space-x-2'>
              <MessageCircle className='w-4 h-4' />
              <span>Conversations</span>
            </div>
          </button>

          <button
            onClick={() => setActiveTab('contacts')}
            className={`py-3 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'contacts'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className='flex items-center space-x-2'>
              <Users className='w-4 h-4' />
              <span>Contacts</span>
            </div>
          </button>

          <button
            onClick={() => setActiveTab('settings')}
            className={`py-3 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'settings'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className='flex items-center space-x-2'>
              <Settings className='w-4 h-4' />
              <span>Configuration</span>
            </div>
          </button>
        </nav>
      </div>

      {/* Action Bar */}
      {activeTab === 'conversations' && (
        <div className='px-4 py-3 bg-gray-50 border-b border-gray-200'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center space-x-3'>
              <button
                onClick={() => setShowContactSelector(true)}
                className='flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors'
              >
                <Plus className='w-4 h-4' />
                <span>Start Conversation</span>
              </button>
            </div>

            <div className='flex items-center space-x-2'>
              <Filter className='w-4 h-4 text-gray-500' />
              <select
                value={filterStatus}
                onChange={e => setFilterStatus(e.target.value as any)}
                className='text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500'
              >
                <option value='all'>All Conversations</option>
                <option value='assigned'>Assigned</option>
                <option value='unassigned'>Unassigned</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Content */}
      <div className='flex-1 overflow-hidden'>
        {activeTab === 'conversations' && (
          <FacebookRoomList
            onRoomSelect={onRoomSelect}
            selectedRoomId={selectedRoomId}
            showAssignmentControls={true}
            className='h-full'
          />
        )}

        {activeTab === 'contacts' && (
          <FacebookContactSelector
            onContactSelect={contact => {
              console.log('Contact selected:', contact)
            }}
            onCreateRoom={contactUuid => {
              console.log('Create room for contact:', contactUuid)
              fetchStats() // Refresh stats
            }}
            className='h-full'
          />
        )}

        {activeTab === 'settings' && (
          <FacebookMessengerConfig onConfigSaved={fetchStats} className='h-full' />
        )}
      </div>

      {/* Contact Selector Modal */}
      {showContactSelector && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
          <div className='bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[80vh] overflow-hidden'>
            <FacebookContactSelector
              onContactSelect={contact => {
                console.log('Contact selected:', contact)
                setShowContactSelector(false)
              }}
              onCreateRoom={contactUuid => {
                console.log('Create room for contact:', contactUuid)
                setShowContactSelector(false)
                fetchStats() // Refresh stats
              }}
              className='h-full'
            />
            <div className='p-4 border-t border-gray-200'>
              <button
                onClick={() => setShowContactSelector(false)}
                className='w-full px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors'
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default FacebookPlatformManager
