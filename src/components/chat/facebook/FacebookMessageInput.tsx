// Facebook Message Input Component
// Specialized message input for Facebook Messenger conversations

'use client'

import React, { useState, useRef, useCallback } from 'react'

import { Send, Paperclip, Smile, AlertCircle } from 'lucide-react'

import { useFacebookIntegration, useFacebookRoomDetection } from '@/hooks/chat/useFacebookIntegration'
import { MessageUtils } from '@/utils/chat/messageUtils'

interface FacebookMessageInputProps {
  roomId: string
  onMessageSent?: (result: any) => void
  compact?: boolean
  className?: string
}

const FacebookMessageInput: React.FC<FacebookMessageInputProps> = ({
  roomId,
  onMessageSent,
  compact = false,
  className = ''
}) => {
  const [message, setMessage] = useState('')
  const [isSending, setIsSending] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const { sendFacebookMessage } = useFacebookIntegration()

  const { isFacebookRoom, facebookInfo, loading: roomLoading } = useFacebookRoomDetection(roomId)

  // Auto-resize textarea
  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current

    if (textarea) {
      textarea.style.height = 'auto'
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`
    }
  }, [])

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value)
    setError(null)
    adjustTextareaHeight()
  }

  // Handle send message
  const handleSendMessage = async () => {
    const messageContent = message.trim()

    if (!messageContent || isSending) return

    // Validate message
    const validation = MessageUtils.validateMessageContent(messageContent)

    if (!validation.isValid) {
      setError(validation.error || 'Invalid message')

      return
    }

    setIsSending(true)
    setError(null)

    // Store the message content before clearing
    const messageToSend = messageContent

    // Clear input immediately to provide instant feedback
    setMessage('')

    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
    }

    try {
      let result

      if (isFacebookRoom) {
        // Send via Facebook integration
        result = await sendFacebookMessage(roomId, messageContent)
      } else {
        // Send via regular internal chat API
        const response = await fetch(`/api/internal-chat/rooms/${roomId}/messages`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            content: messageContent,
            message_type: 0 // TEXT
          })
        })

        if (!response.ok) {
          throw new Error('Failed to send message')
        }

        result = await response.json()
      }

      // Notify parent component
      if (onMessageSent) {
        onMessageSent(result)
      }

      // Show warning if Facebook delivery failed
      if (isFacebookRoom && result.delivery_status === 'failed') {
        setError('Message sent to internal chat but failed to deliver to Facebook')
      }
    } catch (error) {
      console.error('Failed to send message:', error)
      setError(error instanceof Error ? error.message : 'Failed to send message')

      // Restore the message content if sending failed
      setMessage(messageToSend)
      setTimeout(adjustTextareaHeight, 0)
    } finally {
      setIsSending(false)

      // Focus back to textarea
      setTimeout(() => {
        textareaRef.current?.focus()
      }, 0)
    }
  }

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // Show loading state while detecting room type
  if (roomLoading) {
    return (
      <div className={`p-4 border-t border-gray-200 bg-white ${className}`}>
        <div className='flex items-center justify-center'>
          <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600'></div>
        </div>
      </div>
    )
  }

  return (
    <div className={`border-t border-gray-200 bg-white ${className}`}>
      {/* Facebook Room Indicator */}
      {isFacebookRoom && facebookInfo && (
        <div className='px-4 py-2 bg-blue-50 border-b border-blue-100'>
          <div className='flex items-center gap-2 text-sm text-blue-700'>
            <div className='w-2 h-2 bg-blue-500 rounded-full'></div>
            <span>
              Facebook conversation with{' '}
              {[facebookInfo.contact_info.first_name, facebookInfo.contact_info.last_name]
                .filter(Boolean)
                .join(' ') || facebookInfo.contact_info.facebook_user_id}
            </span>
            {!facebookInfo.contact_info.is_active && (
              <span className='text-orange-600 text-xs'>(Inactive)</span>
            )}
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className='px-4 py-2 bg-red-50 border-b border-red-100'>
          <div className='flex items-center gap-2 text-sm text-red-700'>
            <AlertCircle className='w-4 h-4' />
            <span>{error}</span>
          </div>
        </div>
      )}

      {/* Message Input */}
      <div className={`p-4 ${compact ? 'p-3' : 'p-4'}`}>
        <div className='flex items-end gap-3'>
          {/* Attachment Button */}
          <button
            className='p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors'
            title='Attach file'
            disabled={isSending}
          >
            <Paperclip className='w-5 h-5' />
          </button>

          {/* Text Input */}
          <div className='flex-1 relative'>
            <textarea
              ref={textareaRef}
              value={message}
              onChange={handleInputChange}
              onKeyDown={handleKeyPress}
              placeholder={
                isFacebookRoom
                  ? 'Type a message to send via Facebook Messenger...'
                  : 'Type a message...'
              }
              className='w-full px-4 py-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
              style={{ minHeight: '44px', maxHeight: '120px' }}
              disabled={isSending}
              rows={1}
            />
          </div>

          {/* Emoji Button */}
          <button
            className='p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors'
            title='Add emoji'
            disabled={isSending}
          >
            <Smile className='w-5 h-5' />
          </button>

          {/* Send Button */}
          <button
            onClick={handleSendMessage}
            disabled={!message.trim() || isSending}
            className='p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
            title='Send message'
          >
            {isSending ? (
              <div className='animate-spin rounded-full h-5 w-5 border-b-2 border-white'></div>
            ) : (
              <Send className='w-5 h-5' />
            )}
          </button>
        </div>
      </div>
    </div>
  )
}

export default FacebookMessageInput
