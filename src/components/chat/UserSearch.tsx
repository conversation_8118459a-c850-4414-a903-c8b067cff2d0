// User Search Component for Internal Chat
// Allows searching and selecting domain users to start new chats

'use client'

import React, { useState, useEffect, useRef } from 'react'

interface DomainUser {
  user_uuid: string
  username: string
  email: string
  display_name: string
  status: string
  enabled: boolean
}

interface TelegramContact {
  contact_uuid: string
  telegram_user_id: string
  telegram_chat_id: string
  username: string | null
  display_name: string
  phone: string | null
  platform: 'telegram'
}

interface SearchResult {
  type: 'internal_user' | 'telegram_contact' | 'zalo_contact'
  platform: string
  display_name: string

  // Internal user fields
  user_uuid?: string
  username?: string
  email?: string
  status?: string
  enabled?: boolean

  // Telegram contact fields
  contact_uuid?: string
  telegram_user_id?: string
  telegram_chat_id?: string
  phone?: string
}

interface UserSearchProps {
  onUserSelect?: (user: DomainUser) => void
  onTelegramContactSelect?: (contact: TelegramContact) => void
  onZaloContactSelect?: (contact: any) => void
  placeholder?: string
  className?: string
}

const UserSearch: React.FC<UserSearchProps> = ({
  onUserSelect,
  onTelegramContactSelect,
  onZaloContactSelect,
  placeholder = 'Search users and contacts...',
  className = ''
}) => {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [loading, setLoading] = useState(false)
  const [showDropdown, setShowDropdown] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const searchRef = useRef<HTMLInputElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (query.length >= 2) {
        searchUsers(query)
      } else {
        setResults([])
        setShowDropdown(false)
      }
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [query])

  // Handle clicks outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !searchRef.current?.contains(event.target as Node)
      ) {
        setShowDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)

    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const searchUsers = async (searchQuery: string) => {
    setLoading(true)

    try {
      const response = await fetch(`/api/internal-chat/users/search?q=${encodeURIComponent(searchQuery)}&limit=10`)
      const result = await response.json()

      if (result.success) {
        setResults(result.data)
        setShowDropdown(result.data.length > 0)
      } else {
        setResults([])
        setShowDropdown(false)
      }
    } catch (error) {
      console.error('Error searching users:', error)
      setResults([])
      setShowDropdown(false)
    } finally {
      setLoading(false)
    }
  }

  const handleResultSelect = async (result: SearchResult) => {
    try {
      // Clear search
      setQuery('')
      setResults([])
      setShowDropdown(false)

      // Handle different result types
      if (result.type === 'internal_user' && onUserSelect) {
        const user: DomainUser = {
          user_uuid: result.user_uuid!,
          username: result.username!,
          email: result.email!,
          display_name: result.display_name,
          status: result.status!,
          enabled: result.enabled!
        }

        onUserSelect(user)
      } else if (result.type === 'telegram_contact' && onTelegramContactSelect) {
        const contact: TelegramContact = {
          contact_uuid: result.contact_uuid!,
          telegram_user_id: result.telegram_user_id!,
          telegram_chat_id: result.telegram_chat_id!,
          username: result.username || null,
          display_name: result.display_name,
          phone: result.phone || null,
          platform: 'telegram'
        }

        onTelegramContactSelect(contact)
      } else if (result.type === 'zalo_contact' && onZaloContactSelect) {
        // TODO: Handle ZALO contact selection
        onZaloContactSelect(result)
      }
    } catch (error) {
      console.error('Error selecting result:', error)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showDropdown || results.length === 0) return

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => (prev < results.length - 1 ? prev + 1 : 0))
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => (prev > 0 ? prev - 1 : results.length - 1))
        break
      case 'Enter':
        e.preventDefault()

        if (selectedIndex >= 0 && selectedIndex < results.length) {
          handleResultSelect(results[selectedIndex])
        }

        break
      case 'Escape':
        setShowDropdown(false)
        setSelectedIndex(-1)
        break
    }
  }

  return (
    <div className={`relative ${className}`}>
      {/* Search Input */}
      <div className='relative'>
        <input
          ref={searchRef}
          type='text'
          value={query}
          onChange={e => setQuery(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => query.length >= 2 && results.length > 0 && setShowDropdown(true)}
          placeholder={placeholder}
          className='w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
        />
        <div className='absolute left-3 top-2.5'>
          {loading ? (
            <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600' />
          ) : (
            <i className='ri-search-line text-gray-400' />
          )}
        </div>
      </div>

      {/* Dropdown Results */}
      {showDropdown && (
        <div
          ref={dropdownRef}
          className='absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto'
        >
          {results.length === 0 ? (
            <div className='p-3 text-gray-500 text-center'>No results found</div>
          ) : (
            results.map((result, index) => (
              <button
                key={result.type === 'internal_user' ? result.user_uuid : result.contact_uuid}
                onClick={() => handleResultSelect(result)}
                className={`w-full p-3 text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0 transition-colors ${
                  index === selectedIndex ? 'bg-blue-50' : ''
                }`}
              >
                <div className='flex items-center space-x-3'>
                  {/* Platform-specific icon */}
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      result.platform === 'telegram'
                        ? 'bg-blue-100'
                        : result.platform === 'zalo'
                          ? 'bg-green-100'
                          : 'bg-gray-100'
                    }`}
                  >
                    {result.platform === 'telegram' ? (
                      <i className='ri-telegram-line text-blue-600' />
                    ) : result.platform === 'zalo' ? (
                      <i className='ri-message-line text-green-600' />
                    ) : (
                      <span className='text-sm font-medium text-gray-600'>
                        {result.display_name.charAt(0).toUpperCase()}
                      </span>
                    )}
                  </div>

                  <div className='flex-1 min-w-0'>
                    <div className='font-medium text-gray-900 truncate'>{result.display_name}</div>
                    <div className='text-sm text-gray-500 truncate'>
                      {result.type === 'internal_user' ? (
                        `@${result.username} • ${result.email}`
                      ) : result.type === 'telegram_contact' ? (
                        <>
                          {result.username && `@${result.username} • `}
                          Telegram {result.phone && `• ${result.phone}`}
                        </>
                      ) : result.type === 'zalo_contact' ? (
                        'ZALO Contact'
                      ) : (
                        result.platform
                      )}
                    </div>
                  </div>

                  {/* Platform badge */}
                  <div
                    className={`px-2 py-1 rounded-full text-xs font-medium ${
                      result.platform === 'telegram'
                        ? 'bg-blue-100 text-blue-700'
                        : result.platform === 'zalo'
                          ? 'bg-green-100 text-green-700'
                          : 'bg-gray-100 text-gray-700'
                    }`}
                  >
                    {result.platform === 'internal'
                      ? 'Internal'
                      : result.platform === 'telegram'
                        ? 'Telegram'
                        : result.platform === 'zalo'
                          ? 'ZALO'
                          : result.platform}
                  </div>
                </div>
              </button>
            ))
          )}
        </div>
      )}
    </div>
  )
}

export default UserSearch
