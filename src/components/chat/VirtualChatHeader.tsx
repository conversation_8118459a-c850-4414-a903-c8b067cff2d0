// Virtual Chat Header Component
// Header for virtual chat rooms (before first message is sent)

'use client'

import React from 'react'

interface VirtualChatHeaderProps {
  user: any
  onBack?: () => void
  compact?: boolean
  className?: string
}

const VirtualChatHeader: React.FC<VirtualChatHeaderProps> = ({ user, onBack, compact = false, className = '' }) => {
  return (
    <div className={`flex items-center justify-between border-b bg-white ${compact ? 'p-3' : 'p-4'} ${className}`}>
      <div className='flex items-center space-x-3 min-w-0 flex-1'>
        {/* Back button for mobile */}
        {onBack && (
          <button
            onClick={onBack}
            className='p-1 hover:bg-gray-100 rounded transition-colors flex-shrink-0'
            title='Back to conversations'
          >
            <i className='ri-arrow-left-line text-gray-600' />
          </button>
        )}

        {/* User Avatar */}
        <div className='flex-shrink-0'>
          <div
            className={`bg-blue-100 rounded-full flex items-center justify-center ${compact ? 'w-8 h-8' : 'w-10 h-10'}`}
          >
            <span className={`font-medium text-blue-600 ${compact ? 'text-sm' : 'text-base'}`}>
              {user.display_name?.charAt(0)?.toUpperCase() || user.username?.charAt(0)?.toUpperCase() || '?'}
            </span>
          </div>
        </div>

        {/* User Info */}
        <div className='min-w-0 flex-1'>
          <div className='flex items-center space-x-2'>
            <h3 className={`font-semibold text-gray-900 truncate ${compact ? 'text-sm' : 'text-base'}`}>
              {user.display_name || user.username}
            </h3>
            <div className='flex items-center space-x-1'>
              <div className='w-2 h-2 bg-gray-400 rounded-full'></div>
              <span className={`text-gray-500 ${compact ? 'text-xs' : 'text-sm'}`}>New conversation</span>
            </div>
          </div>
          {user.user_email && (
            <p className={`text-gray-500 truncate ${compact ? 'text-xs' : 'text-sm'}`}>{user.user_email}</p>
          )}
        </div>
      </div>

      {/* Actions */}
      <div className='flex items-center space-x-2 flex-shrink-0'>
        <div className={`text-gray-400 ${compact ? 'text-xs' : 'text-sm'}`}>Type a message to start...</div>
      </div>
    </div>
  )
}

export default VirtualChatHeader
