// Smart Message Input Component
// Automatically detects room type and uses appropriate input component

'use client'

import React from 'react'

import MessageInput from './MessageInput'
import TelegramMessageInput from './telegram/TelegramMessageInput'
import ZaloMessageInput from './zalo/ZaloMessageInput'
import FacebookMessageInput from './facebook/FacebookMessageInput'
import { useTelegramRoomDetection } from '@/hooks/chat/useTelegramIntegration'
import { useZaloRoomDetection } from '@/hooks/chat/useZaloIntegration'
import { useFacebookRoomDetection } from '@/hooks/chat/useFacebookIntegration'

interface SmartMessageInputProps {
  roomId: string
  compact?: boolean
  className?: string
}

const SmartMessageInput: React.FC<SmartMessageInputProps> = ({ roomId, compact = false, className = '' }) => {
  const { isTelegramRoom, loading: telegramLoading } = useTelegramRoomDetection(roomId)
  const { isZaloRoom, loading: zaloLoading } = useZaloRoomDetection(roomId)
  const { isFacebookRoom, loading: facebookLoading } = useFacebookRoomDetection(roomId)

  const loading = telegramLoading || zaloLoading || facebookLoading

  // Show loading state while detecting room type
  if (loading) {
    return (
      <div className={`p-4 border-t border-gray-200 bg-white ${className}`}>
        <div className='flex items-center justify-center'>
          <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600'></div>
        </div>
      </div>
    )
  }

  // Use Telegram input for Telegram rooms
  if (isTelegramRoom) {
    return <TelegramMessageInput roomId={roomId} compact={compact} className={className} />
  }

  // Use Zalo input for Zalo rooms
  if (isZaloRoom) {
    return <ZaloMessageInput roomId={roomId} compact={compact} className={className} />
  }

  // Use Facebook input for Facebook rooms
  if (isFacebookRoom) {
    return <FacebookMessageInput roomId={roomId} compact={compact} className={className} />
  }

  // Use regular input for internal chat rooms
  return <MessageInput roomId={roomId} compact={compact} className={className} />
}

export default SmartMessageInput
