// Chat Toggle Button Component
// Floating action button to open chat popup

'use client'

import React from 'react'

import { motion } from 'framer-motion'

import { useAppSelector } from '@/redux-store/hooks'

interface ChatToggleButtonProps {
  onClick: () => void
  position?: 'bottom-right' | 'bottom-left'
  className?: string
}

const ChatToggleButton: React.FC<ChatToggleButtonProps> = ({ onClick, position = 'bottom-right', className = '' }) => {
  const { totalUnreadCount, isConnected, connectionStatus } = useAppSelector(state => state.internalChatReducer)

  const positionClasses = {
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4'
  }

  const getConnectionColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'bg-blue-600 hover:bg-blue-700'
      case 'connecting':
        return 'bg-yellow-500 hover:bg-yellow-600'
      case 'error':
        return 'bg-red-500 hover:bg-red-600'
      default:
        return 'bg-gray-500 hover:bg-gray-600'
    }
  }

  const getConnectionIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'ri-message-3-line'
      case 'connecting':
        return 'ri-loader-4-line animate-spin'
      case 'error':
        return 'ri-error-warning-line'
      default:
        return 'ri-message-3-line'
    }
  }

  return (
    <motion.button
      onClick={onClick}
      className={`
        fixed z-40 text-white rounded-full p-3 shadow-lg transition-all duration-200 
        hover:scale-110 active:scale-95 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
        ${getConnectionColor()}
        ${positionClasses[position]}
        ${className}
      `}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.95 }}
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{
        type: 'spring',
        stiffness: 260,
        damping: 20
      }}
      title={`Open Chat ${!isConnected ? '(Disconnected)' : ''}`}
    >
      {/* Chat icon */}
      <i className={`${getConnectionIcon()} text-xl`} />

      {/* Unread count badge */}
      {totalUnreadCount > 0 && (
        <motion.span
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          exit={{ scale: 0 }}
          className='absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-medium shadow-lg'
        >
          {totalUnreadCount > 99 ? '99+' : totalUnreadCount}
        </motion.span>
      )}

      {/* Connection status indicator */}
      <div
        className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${
          isConnected ? 'bg-green-400' : 'bg-red-400'
        }`}
      />

      {/* Pulse animation for new messages */}
      {totalUnreadCount > 0 && (
        <motion.div
          className='absolute inset-0 rounded-full bg-blue-400 opacity-30'
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.1, 0.3]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        />
      )}
    </motion.button>
  )
}

export default ChatToggleButton
