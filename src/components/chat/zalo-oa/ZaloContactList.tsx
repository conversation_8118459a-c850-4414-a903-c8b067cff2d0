/* eslint-disable */
// ZALO OA Contact List Component
// Displays ZALO contacts in chat sidebar with domain filtering

'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'

// Simple icon components
const Search = ({ className }: { className?: string }) => <span className={className}>🔍</span>
const MessageCircle = ({ className }: { className?: string }) => <span className={className}>💬</span>
const Phone = ({ className }: { className?: string }) => <span className={className}>📞</span>
const UserCheck = ({ className }: { className?: string }) => <span className={className}>✅</span>
const RefreshCw = ({ className }: { className?: string }) => <span className={className}>🔄</span>

// Types
import type { ZaloOaContact, ZaloOaContactListResponse } from '@/types/apps/zalo-oa/zaloOaTypes'

interface ZaloContactListProps {
  onContactSelect?: (contact: ZaloOaContact) => void
  selectedContactId?: string
  className?: string
}

export const ZaloContactList: React.FC<ZaloContactListProps> = ({
  onContactSelect,
  selectedContactId,
  className = ''
}) => {
  const { data: session } = useSession()
  const [contacts, setContacts] = useState<ZaloOaContact[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [filterFollowers, setFilterFollowers] = useState<boolean | null>(null)
  const [syncing, setSyncing] = useState(false)

  // Fetch contacts from API
  const fetchContacts = useCallback(async () => {
    if (!session?.user?.id) return

    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        page: '1',
        limit: '100',
        ...(searchQuery && { search: searchQuery }),
        ...(filterFollowers !== null && { is_follower: filterFollowers.toString() })
      })

      const response = await fetch(`/api/zalo-oa/contacts?${params}`)

      if (!response.ok) {
        throw new Error('Failed to fetch ZALO contacts')
      }

      const data: ZaloOaContactListResponse = await response.json()
      setContacts(data.contacts)
    } catch (err) {
      console.error('Error fetching ZALO contacts:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }, [session, searchQuery, filterFollowers])

  // Sync contacts with ZALO API
  const syncContacts = useCallback(async () => {
    if (!session?.user?.id) return

    try {
      setSyncing(true)

      const response = await fetch('/api/zalo-oa/contacts', {
        method: 'PUT'
      })

      if (!response.ok) {
        throw new Error('Failed to sync ZALO contacts')
      }

      // Refresh the contact list after sync
      await fetchContacts()
    } catch (err) {
      console.error('Error syncing ZALO contacts:', err)
      setError(err instanceof Error ? err.message : 'Sync failed')
    } finally {
      setSyncing(false)
    }
  }, [session, fetchContacts])

  // Initial load and search debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchContacts()
    }, 300) // Debounce search

    return () => clearTimeout(timeoutId)
  }, [fetchContacts])

  // Handle contact selection
  const handleContactSelect = (contact: ZaloOaContact) => {
    onContactSelect?.(contact)
  }

  // Format last interaction time
  const formatLastInteraction = (date?: Date) => {
    if (!date) return 'Never'

    const now = new Date()
    const diff = now.getTime() - new Date(date).getTime()
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(diff / 3600000)
    const days = Math.floor(diff / 86400000)

    if (minutes < 1) return 'Just now'
    if (minutes < 60) return `${minutes}m ago`
    if (hours < 24) return `${hours}h ago`
    return `${days}d ago`
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      <div className='p-4 border-b'>
        <h3 className='text-lg font-semibold mb-3'>ZALO Contacts</h3>

        {/* Search */}
        <div className='relative mb-3'>
          <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
          <input
            type='text'
            placeholder='Search contacts...'
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)}
            className='w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500'
          />
        </div>

        {/* Filters */}
        <div className='flex gap-2 mb-3'>
          <button
            onClick={() => setFilterFollowers(null)}
            className={`px-3 py-1 text-xs rounded ${filterFollowers === null ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
          >
            All
          </button>
          <button
            onClick={() => setFilterFollowers(true)}
            className={`px-3 py-1 text-xs rounded flex items-center gap-1 ${filterFollowers === true ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
          >
            <UserCheck className='h-3 w-3' />
            Followers
          </button>
        </div>

        {/* Sync Button */}
        <button
          onClick={syncContacts}
          disabled={syncing}
          className='w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50'
        >
          <RefreshCw className={`h-4 w-4 inline mr-2 ${syncing ? 'animate-spin' : ''}`} />
          {syncing ? 'Syncing...' : 'Sync Contacts'}
        </button>
      </div>

      {/* Error State */}
      {error && (
        <div className='p-4 text-center text-red-500 text-sm'>
          {error}
          <button onClick={fetchContacts} className='ml-2 text-blue-500 underline'>
            Retry
          </button>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className='p-4 text-center'>
          <div className='animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto'></div>
          <p className='mt-2 text-gray-500'>Loading contacts...</p>
        </div>
      )}

      {/* Contact List */}
      <div className='flex-1 overflow-y-auto'>
        {contacts.length === 0 && !loading ? (
          <div className='text-center py-8 text-gray-500'>
            <MessageCircle className='h-12 w-12 mx-auto mb-3 opacity-50' />
            <p>No ZALO contacts found</p>
          </div>
        ) : (
          <div className='space-y-1 p-2'>
            {contacts.map(contact => (
              <div
                key={contact.contact_uuid}
                className={`
                  flex items-center p-3 rounded-lg cursor-pointer transition-colors
                  hover:bg-gray-100
                  ${selectedContactId === contact.contact_uuid ? 'bg-blue-100' : ''}
                `}
                onClick={() => handleContactSelect(contact)}
              >
                <div className='h-10 w-10 mr-3 bg-gray-300 rounded-full flex items-center justify-center'>
                  {contact.avatar_url ? (
                    <img src={contact.avatar_url} alt='' className='h-10 w-10 rounded-full' />
                  ) : (
                    <span className='text-sm font-medium'>
                      {contact.display_name?.charAt(0) || contact.zalo_user_id.charAt(0)}
                    </span>
                  )}
                </div>

                <div className='flex-1 min-w-0'>
                  <div className='flex items-center justify-between'>
                    <p className='font-medium truncate'>{contact.display_name || `User ${contact.zalo_user_id}`}</p>
                    <div className='flex items-center gap-1'>
                      {contact.is_follower && (
                        <span className='px-2 py-1 text-xs bg-green-100 text-green-800 rounded'>
                          <UserCheck className='h-3 w-3 inline' />
                        </span>
                      )}
                      {contact.phone && (
                        <span className='px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded'>
                          <Phone className='h-3 w-3 inline' />
                        </span>
                      )}
                    </div>
                  </div>

                  <div className='flex items-center justify-between mt-1'>
                    <p className='text-xs text-gray-500 truncate'>ID: {contact.zalo_user_id}</p>
                    <p className='text-xs text-gray-500'>{formatLastInteraction(contact.last_interaction_date)}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default ZaloContactList
