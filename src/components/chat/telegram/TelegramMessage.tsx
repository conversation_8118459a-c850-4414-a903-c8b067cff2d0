// Telegram Message Component
// Displays messages with Telegram-specific formatting and indicators

'use client'

import React from 'react'

import { Check, CheckChe<PERSON>, Clock, AlertCircle, Send } from 'lucide-react'

import type { ChatMessage } from '@/types/apps/internal-chat/chatTypes'

interface TelegramMessageProps {
  message: ChatMessage
  isOwnMessage: boolean
  showAvatar?: boolean
  compact?: boolean
  className?: string
}

const TelegramMessage: React.FC<TelegramMessageProps> = ({
  message,
  isOwnMessage,
  showAvatar = true,
  compact = false,
  className = ''
}) => {
  // Format timestamp
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp * 1000)

    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  // Get delivery status icon for outbound messages
  const getDeliveryStatusIcon = () => {
    if (!isOwnMessage) return null

    // Check if this is a Telegram message by looking for mapping
    // This would be populated by the API when fetching messages
    const deliveryStatus = (message as any).delivery_status

    switch (deliveryStatus) {
      case 'pending':
        return <Clock className='w-3 h-3 text-gray-400' />
      case 'sent':
        return <Check className='w-3 h-3 text-gray-500' />
      case 'delivered':
        return <CheckCheck className='w-3 h-3 text-blue-500' />
      case 'failed':
        return <AlertCircle className='w-3 h-3 text-red-500' />
      default:
        return <Send className='w-3 h-3 text-gray-400' />
    }
  }

  // Check if message is from Telegram user
  const isTelegramUser = message.author_uuid === 'telegram-user'

  // Get message content with Telegram-specific formatting
  const getFormattedContent = () => {
    if (!message.content) return ''

    // Basic HTML formatting support (Telegram supports HTML)
    let content = message.content

    // Convert basic markdown-like formatting to HTML if needed
    // This is a simple example - you might want more sophisticated parsing
    content = content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Bold
      .replace(/\*(.*?)\*/g, '<em>$1</em>') // Italic
      .replace(/`(.*?)`/g, '<code>$1</code>') // Code

    return content
  }

  return (
    <div className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} ${className}`}>
      <div
        className={`flex ${isOwnMessage ? 'flex-row-reverse' : 'flex-row'} items-end space-x-2 max-w-xs lg:max-w-md`}
      >
        {/* Avatar */}
        {showAvatar && !compact && (
          <div className='flex-shrink-0'>
            {isTelegramUser ? (
              <div className='w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center'>
                <Send className='w-4 h-4 text-blue-600' />
              </div>
            ) : (
              <div className='w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center'>
                <div className='w-6 h-6 bg-gray-300 rounded-full'></div>
              </div>
            )}
          </div>
        )}

        {/* Message bubble */}
        <div
          className={`relative px-4 py-2 rounded-lg ${
            isOwnMessage ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-900'
          } ${compact ? 'text-sm' : ''}`}
        >
          {/* Telegram platform indicator */}
          {isTelegramUser && (
            <div className='flex items-center mb-1'>
              <Send className='w-3 h-3 text-blue-400 mr-1' />
              <span className='text-xs text-blue-200'>Telegram</span>
            </div>
          )}

          {/* Message content */}
          <div className='break-words' dangerouslySetInnerHTML={{ __html: getFormattedContent() }} />

          {/* Message metadata */}
          <div
            className={`flex items-center justify-end mt-1 space-x-1 ${
              isOwnMessage ? 'text-blue-100' : 'text-gray-500'
            }`}
          >
            {/* Edited indicator */}
            {message.edited_at && <span className='text-xs opacity-75'>edited</span>}

            {/* Timestamp */}
            <span className='text-xs'>{formatTime(message.created_at)}</span>

            {/* Delivery status for own messages */}
            {isOwnMessage && getDeliveryStatusIcon()}
          </div>

          {/* Message tail */}
          <div
            className={`absolute bottom-0 ${isOwnMessage ? '-right-1' : '-left-1'} w-3 h-3 ${
              isOwnMessage ? 'bg-blue-600' : 'bg-gray-100'
            } transform rotate-45`}
          ></div>
        </div>
      </div>
    </div>
  )
}

// Telegram Message List Component
interface TelegramMessageListProps {
  messages: ChatMessage[]
  currentUserId: string
  compact?: boolean
  className?: string
}

export const TelegramMessageList: React.FC<TelegramMessageListProps> = ({
  messages,
  currentUserId,
  compact = false,
  className = ''
}) => {
  return (
    <div className={`space-y-4 p-4 ${className}`}>
      {messages.map((message, index) => {
        const isOwnMessage = message.author_uuid === currentUserId
        const prevMessage = index > 0 ? messages[index - 1] : null
        const showAvatar = !prevMessage || prevMessage.author_uuid !== message.author_uuid

        return (
          <TelegramMessage
            key={message.message_id}
            message={message}
            isOwnMessage={isOwnMessage}
            showAvatar={showAvatar}
            compact={compact}
          />
        )
      })}
    </div>
  )
}

// Telegram Chat Header Component
interface TelegramChatHeaderProps {
  contact: {
    display_name: string
    username?: string | null
    telegram_user_id: string
    language_code?: string | null
    last_interaction_date?: Date | null
  }
  roomStatus: string
  onBack?: () => void
  compact?: boolean
  className?: string
}

export const TelegramChatHeader: React.FC<TelegramChatHeaderProps> = ({
  contact,
  roomStatus,
  onBack,
  compact = false,
  className = ''
}) => {
  return (
    <div className={`flex items-center p-4 border-b border-gray-200 bg-white ${className}`}>
      {/* Back button */}
      {onBack && (
        <button onClick={onBack} className='mr-3 p-1 hover:bg-gray-100 rounded-full transition-colors'>
          <svg className='w-5 h-5 text-gray-600' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
            <path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M15 19l-7-7 7-7' />
          </svg>
        </button>
      )}

      {/* Contact avatar */}
      <div className='flex-shrink-0 mr-3'>
        <div className='w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center'>
          <Send className='w-5 h-5 text-blue-600' />
        </div>
      </div>

      {/* Contact info */}
      <div className='flex-1 min-w-0'>
        <h3 className={`font-medium text-gray-900 truncate ${compact ? 'text-sm' : ''}`}>{contact.display_name}</h3>

        <div className='flex items-center space-x-2'>
          {contact.username && <span className='text-xs text-gray-500'>@{contact.username}</span>}

          <span
            className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              roomStatus === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
            }`}
          >
            Telegram
          </span>
        </div>
      </div>

      {/* Status indicator */}
      <div className='flex-shrink-0'>
        <div className={`w-3 h-3 rounded-full ${roomStatus === 'active' ? 'bg-green-400' : 'bg-gray-400'}`}></div>
      </div>
    </div>
  )
}

export default TelegramMessage
