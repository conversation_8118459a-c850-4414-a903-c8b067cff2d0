// Telegram Platform Manager Component
// Main component for managing Telegram contacts, conversations, and agent assignments

'use client'

import React, { useState, useEffect } from 'react'

import { Send, Users, MessageCircle, Settings, UserCheck, Plus, Filter } from 'lucide-react'

import TelegramRoomList from './TelegramRoomList'
import TelegramContactSelector from './TelegramContactSelector'

interface TelegramStats {
  total_contacts: number
  active_rooms: number
  unassigned_rooms: number
  total_messages: number
}

interface TelegramPlatformManagerProps {
  onRoomSelect: (roomUuid: string) => void
  selectedRoomId?: string | null
  className?: string
}

const TelegramPlatformManager: React.FC<TelegramPlatformManagerProps> = ({
  onRoomSelect,
  selectedRoomId,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState<'conversations' | 'contacts' | 'settings'>('conversations')
  const [stats, setStats] = useState<TelegramStats | null>(null)
  const [showContactSelector, setShowContactSelector] = useState(false)
  const [filterStatus, setFilterStatus] = useState<'all' | 'assigned' | 'unassigned'>('all')

  // Fetch Telegram statistics
  const fetchStats = async () => {
    try {
      const response = await fetch('/api/internal-chat/telegram/stats')

      if (!response.ok) {
        throw new Error('Failed to fetch Telegram statistics')
      }

      const data = await response.json()

      setStats(data.stats || null)
    } catch (err) {
      console.error('Error fetching Telegram stats:', err)

      // Error handling removed for simplicity
    }
  }

  // Initial load
  useEffect(() => {
    fetchStats()
  }, [])

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Header */}
      <div className='p-4 border-b border-gray-200 bg-gray-50'>
        <div className='flex items-center justify-between mb-4'>
          <div className='flex items-center space-x-3'>
            <div className='p-2 bg-blue-100 rounded-lg'>
              <Send className='w-6 h-6 text-blue-600' />
            </div>
            <div>
              <h1 className='text-xl font-bold text-gray-900'>Telegram Platform</h1>
              <p className='text-sm text-gray-600'>Manage Telegram conversations and agent assignments</p>
            </div>
          </div>

          <div className='flex items-center space-x-2'>
            <button
              onClick={() => setShowContactSelector(true)}
              className='px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2'
            >
              <Plus className='w-4 h-4' />
              <span>New Chat</span>
            </button>

            {/* Removed global assignment button - settings available in tab */}
          </div>
        </div>

        {/* Statistics */}
        {stats && (
          <div className='grid grid-cols-4 gap-4'>
            <div className='bg-white rounded-lg p-3 border border-gray-200'>
              <div className='flex items-center space-x-2'>
                <Users className='w-4 h-4 text-blue-600' />
                <span className='text-sm text-gray-600'>Contacts</span>
              </div>
              <p className='text-lg font-semibold text-gray-900 mt-1'>{stats.total_contacts}</p>
            </div>

            <div className='bg-white rounded-lg p-3 border border-gray-200'>
              <div className='flex items-center space-x-2'>
                <MessageCircle className='w-4 h-4 text-green-600' />
                <span className='text-sm text-gray-600'>Active Rooms</span>
              </div>
              <p className='text-lg font-semibold text-gray-900 mt-1'>{stats.active_rooms}</p>
            </div>

            <div className='bg-white rounded-lg p-3 border border-gray-200'>
              <div className='flex items-center space-x-2'>
                <UserCheck className='w-4 h-4 text-orange-600' />
                <span className='text-sm text-gray-600'>Unassigned</span>
              </div>
              <p className='text-lg font-semibold text-gray-900 mt-1'>{stats.unassigned_rooms}</p>
            </div>

            <div className='bg-white rounded-lg p-3 border border-gray-200'>
              <div className='flex items-center space-x-2'>
                <Send className='w-4 h-4 text-purple-600' />
                <span className='text-sm text-gray-600'>Messages</span>
              </div>
              <p className='text-lg font-semibold text-gray-900 mt-1'>{stats.total_messages}</p>
            </div>
          </div>
        )}
      </div>

      {/* Navigation Tabs */}
      <div className='border-b border-gray-200'>
        <nav className='flex space-x-8 px-4'>
          <button
            onClick={() => setActiveTab('conversations')}
            className={`py-3 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'conversations'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className='flex items-center space-x-2'>
              <MessageCircle className='w-4 h-4' />
              <span>Conversations</span>
            </div>
          </button>

          <button
            onClick={() => setActiveTab('contacts')}
            className={`py-3 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'contacts'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className='flex items-center space-x-2'>
              <Users className='w-4 h-4' />
              <span>Contacts</span>
            </div>
          </button>

          <button
            onClick={() => setActiveTab('settings')}
            className={`py-3 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'settings'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className='flex items-center space-x-2'>
              <Settings className='w-4 h-4' />
              <span>Assignment Settings</span>
            </div>
          </button>
        </nav>
      </div>

      {/* Filters for conversations */}
      {activeTab === 'conversations' && (
        <div className='px-4 py-3 border-b border-gray-200 bg-gray-50'>
          <div className='flex items-center space-x-4'>
            <div className='flex items-center space-x-2'>
              <Filter className='w-4 h-4 text-gray-500' />
              <span className='text-sm font-medium text-gray-700'>Filter:</span>
            </div>

            <div className='flex space-x-2'>
              <button
                onClick={() => setFilterStatus('all')}
                className={`px-3 py-1 text-xs rounded-full transition-colors ${
                  filterStatus === 'all' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                All
              </button>

              <button
                onClick={() => setFilterStatus('assigned')}
                className={`px-3 py-1 text-xs rounded-full transition-colors ${
                  filterStatus === 'assigned'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                Assigned
              </button>

              <button
                onClick={() => setFilterStatus('unassigned')}
                className={`px-3 py-1 text-xs rounded-full transition-colors ${
                  filterStatus === 'unassigned'
                    ? 'bg-orange-100 text-orange-800'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                Unassigned
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Content */}
      <div className='flex-1 overflow-hidden'>
        {activeTab === 'conversations' && (
          <TelegramRoomList
            onRoomSelect={onRoomSelect}
            selectedRoomId={selectedRoomId}
            showAssignmentControls={true}
            className='h-full'
          />
        )}

        {activeTab === 'contacts' && (
          <TelegramContactSelector
            onContactSelect={contact => {
              console.log('Contact selected:', contact)
            }}
            onCreateRoom={contactUuid => {
              console.log('Create room for contact:', contactUuid)
              fetchStats() // Refresh stats
            }}
            className='h-full'
          />
        )}

        {activeTab === 'settings' && (
          <div className='p-6'>
            <div className='max-w-2xl'>
              <h3 className='text-lg font-semibold text-gray-900 mb-4'>Agent Assignment</h3>
              <p className='text-gray-600 mb-6'>
                Assign agents to Telegram conversations by adding them as participants to the chat rooms.
              </p>

              <div className='bg-white rounded-lg border border-gray-200 p-4'>
                <p className='text-sm text-gray-600'>To assign agents to Telegram conversations:</p>
                <ol className='mt-2 text-sm text-gray-600 list-decimal list-inside space-y-1'>
                  <li>Select a conversation from the Conversations tab</li>
                  <li>Use the assignment controls to add agents as participants</li>
                  <li>Agents will receive messages and can respond to customers</li>
                </ol>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Contact Selector Modal */}
      {showContactSelector && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
          <div className='bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[80vh] overflow-hidden'>
            <TelegramContactSelector
              onContactSelect={contact => {
                console.log('Contact selected:', contact)
                setShowContactSelector(false)
              }}
              onCreateRoom={contactUuid => {
                console.log('Create room for contact:', contactUuid)
                setShowContactSelector(false)
                fetchStats() // Refresh stats
              }}
              className='h-full'
            />

            <div className='p-4 border-t border-gray-200'>
              <button
                onClick={() => setShowContactSelector(false)}
                className='w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors'
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default TelegramPlatformManager
