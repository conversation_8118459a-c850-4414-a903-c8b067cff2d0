// Telegram Contact Selector Component
// Allows users to browse and select Telegram contacts to start conversations

'use client'

import React, { useState, useEffect } from 'react'

import { Search, MessageCircle, User, Clock, Phone, AlertCircle, Loader2, Users } from 'lucide-react'

interface TelegramContact {
  contact_uuid: string
  telegram_user_id: string
  telegram_chat_id: string
  username: string | null
  first_name: string | null
  last_name: string | null
  display_name: string
  phone: string | null
  is_bot: boolean
  language_code: string | null
  contact_info: any
  last_interaction_date: Date | null
  chat_room: {
    room_uuid: string
    room_name: string
    room_type: string
    is_active: boolean
  } | null
  room_status: string | null
}

interface TelegramContactSelectorProps {
  onContactSelect: (contact: TelegramContact) => void
  onCreateRoom: (contactUuid: string) => void
  className?: string
}

const TelegramContactSelector: React.FC<TelegramContactSelectorProps> = ({
  onContactSelect,
  onCreateRoom,
  className = ''
}) => {
  const [contacts, setContacts] = useState<TelegramContact[]>([])
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [error, setError] = useState<string | null>(null)

  // Fetch Telegram contacts
  const fetchContacts = async (search: string = '') => {
    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams()

      if (search.trim()) params.append('search', search.trim())
      params.append('limit', '50')

      const response = await fetch(`/api/internal-chat/telegram/contacts?${params}`)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))

        throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch contacts`)
      }

      const data = await response.json()

      if (!data.contacts || !Array.isArray(data.contacts)) {
        throw new Error('Invalid response format from server')
      }

      setContacts(data.contacts)
    } catch (err) {
      console.error('Error fetching Telegram contacts:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to load contacts'

      setError(errorMessage)
      setContacts([]) // Clear contacts on error
    } finally {
      setLoading(false)
    }
  }

  // Initial load
  useEffect(() => {
    fetchContacts()
  }, [])

  // Search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      fetchContacts(searchTerm)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchTerm])

  const handleContactClick = (contact: TelegramContact) => {
    if (contact.chat_room) {
      // Room exists, select it
      onContactSelect(contact)
    } else {
      // No room exists, create one
      onCreateRoom(contact.contact_uuid)
    }
  }

  const formatLastInteraction = (date: Date | null) => {
    if (!date) return 'Never'

    const now = new Date()
    const diffMs = now.getTime() - new Date(date).getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffDays === 0) return 'Today'
    if (diffDays === 1) return 'Yesterday'
    if (diffDays < 7) return `${diffDays} days ago`
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`

    return `${Math.floor(diffDays / 30)} months ago`
  }

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Header */}
      <div className='p-4 border-b border-gray-200'>
        <h3 className='text-lg font-semibold text-gray-900 mb-3'>Telegram Contacts</h3>

        {/* Search */}
        <div className='relative'>
          <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4' />
          <input
            type='text'
            placeholder='Search contacts...'
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className='w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
          />
        </div>
      </div>

      {/* Content */}
      <div className='flex-1 overflow-y-auto'>
        {loading && (
          <div className='flex items-center justify-center py-8'>
            <div className='text-center'>
              <Loader2 className='w-8 h-8 animate-spin text-blue-600 mx-auto mb-2' />
              <p className='text-sm text-gray-500'>Loading contacts...</p>
            </div>
          </div>
        )}

        {error && (
          <div className='p-4 text-center'>
            <div className='text-red-500 mb-2'>
              <AlertCircle className='w-8 h-8 mx-auto' />
            </div>
            <p className='text-sm text-red-600 mb-3'>{error}</p>
            <button
              onClick={() => fetchContacts(searchTerm)}
              className='px-3 py-1 bg-red-100 text-red-700 text-sm rounded hover:bg-red-200 transition-colors'
            >
              Try Again
            </button>
          </div>
        )}

        {!loading && !error && contacts.length === 0 && (
          <div className='p-6 text-center'>
            <div className='text-gray-400 mb-3'>
              <Users className='w-12 h-12 mx-auto' />
            </div>
            <h3 className='text-lg font-medium text-gray-900 mb-2'>No contacts found</h3>
            <p className='text-sm text-gray-500'>
              {searchTerm ? 'Try adjusting your search terms.' : 'No Telegram contacts available yet.'}
            </p>
          </div>
        )}

        {!loading && !error && contacts.length > 0 && (
          <div className='divide-y divide-gray-100'>
            {contacts.map(contact => (
              <div
                key={contact.contact_uuid}
                onClick={() => handleContactClick(contact)}
                className='p-4 hover:bg-gray-50 cursor-pointer transition-colors'
              >
                <div className='flex items-start space-x-3'>
                  {/* Avatar */}
                  <div className='flex-shrink-0'>
                    <div className='w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center'>
                      <User className='w-5 h-5 text-blue-600' />
                    </div>
                  </div>

                  {/* Contact Info */}
                  <div className='flex-1 min-w-0'>
                    <div className='flex items-center justify-between'>
                      <div className='flex items-center space-x-2'>
                        <h4 className='text-sm font-medium text-gray-900 truncate'>{contact.display_name}</h4>
                        {contact.is_bot && (
                          <span className='inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800'>
                            Bot
                          </span>
                        )}
                      </div>

                      {/* Status indicator */}
                      <div className='flex items-center space-x-1'>
                        {contact.chat_room ? (
                          <div className='flex items-center text-xs text-green-600'>
                            <MessageCircle className='w-4 h-4 mr-1' />
                            <span className='font-medium'>Active</span>
                          </div>
                        ) : (
                          <div className='flex items-center text-xs text-gray-500'>
                            <MessageCircle className='w-4 h-4 mr-1' />
                            <span>No chat</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Username */}
                    {contact.username && <p className='text-xs text-gray-500 mt-1'>@{contact.username}</p>}

                    {/* Phone */}
                    {contact.phone && (
                      <div className='flex items-center mt-1'>
                        <Phone className='w-3 h-3 text-gray-400 mr-1' />
                        <p className='text-xs text-gray-500'>{contact.phone}</p>
                      </div>
                    )}

                    {/* Last interaction */}
                    <div className='flex items-center mt-2'>
                      <Clock className='w-3 h-3 text-gray-400 mr-1' />
                      <p className='text-xs text-gray-500'>{formatLastInteraction(contact.last_interaction_date)}</p>
                    </div>

                    {/* Room status */}
                    {contact.chat_room && (
                      <div className='mt-2'>
                        <span
                          className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            contact.room_status === 'active'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {contact.room_status === 'active' ? 'Active Chat' : 'Inactive'}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className='p-4 border-t border-gray-200 bg-gray-50'>
        <p className='text-xs text-gray-500 text-center'>
          {contacts.length} contact{contacts.length !== 1 ? 's' : ''} found
        </p>
      </div>
    </div>
  )
}

export default TelegramContactSelector
