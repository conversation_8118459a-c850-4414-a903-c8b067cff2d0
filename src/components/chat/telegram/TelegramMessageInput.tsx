// Telegram Message Input Component
// Enhanced message input for Telegram conversations with platform-specific features

'use client'

import React, { useState, useRef, useEffect } from 'react'

import { Send, Paperclip, Smile, AlertCircle } from 'lucide-react'

import { useTelegramIntegration, useTelegramRoomDetection } from '@/hooks/chat/useTelegramIntegration'
import { useTelegramRealtime, useTelegramTyping, useTelegramDeliveryTracking } from '@/hooks/chat/useTelegramRealtime'

interface TelegramMessageInputProps {
  roomId: string
  onMessageSent?: (message: any) => void
  compact?: boolean
  className?: string
}

const TelegramMessageInput: React.FC<TelegramMessageInputProps> = ({
  roomId,
  onMessageSent,
  compact = false,
  className = ''
}) => {
  const [message, setMessage] = useState('')
  const [sending, setSending] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const { sendTelegramMessage } = useTelegramIntegration()
  const { isTelegramRoom, telegramInfo } = useTelegramRoomDetection(roomId)

  // Real-time features
  const { sendTypingIndicator, stopTypingIndicator } = useTelegramTyping(roomId)

  useTelegramDeliveryTracking(roomId) // Track delivery status

  // Real-time event handlers
  useTelegramRealtime({
    roomId,
    onDeliveryStatusUpdate: (_, status, error) => {
      if (status === 'failed' && error) {
        setError(`Message delivery failed: ${error}`)
      }
    }
  })

  // Typing indicator timer
  const typingTimerRef = useRef<NodeJS.Timeout | null>(null)

  // Handle typing indicators
  const handleTyping = () => {
    if (isTelegramRoom) {
      sendTypingIndicator()

      // Clear existing timer
      if (typingTimerRef.current) {
        clearTimeout(typingTimerRef.current)
      }

      // Stop typing after 3 seconds of inactivity
      typingTimerRef.current = setTimeout(() => {
        stopTypingIndicator()
      }, 3000)
    }
  }

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current

    if (textarea) {
      textarea.style.height = 'auto'
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`
    }
  }, [message])

  // Cleanup typing timer
  useEffect(() => {
    return () => {
      if (typingTimerRef.current) {
        clearTimeout(typingTimerRef.current)
      }
    }
  }, [])

  // Handle message submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!message.trim() || sending) return

    const messageContent = message.trim()

    setMessage('')
    setSending(true)
    setError(null)

    try {
      let result

      if (isTelegramRoom) {
        // Send via Telegram integration
        result = await sendTelegramMessage(roomId, messageContent)
      } else {
        // Send via regular internal chat API
        const response = await fetch(`/api/internal-chat/rooms/${roomId}/messages`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            content: messageContent,
            message_type: 0 // TEXT
          })
        })

        if (!response.ok) {
          throw new Error('Failed to send message')
        }

        result = await response.json()
      }

      // Notify parent component
      if (onMessageSent) {
        onMessageSent(result)
      }

      // Show warning if Telegram delivery failed
      if (isTelegramRoom && result.delivery_status === 'failed') {
        setError('Message sent to internal chat but failed to deliver to Telegram')
      }
    } catch (err) {
      console.error('Error sending message:', err)
      setError(err instanceof Error ? err.message : 'Failed to send message')
      setMessage(messageContent) // Restore message on error
    } finally {
      setSending(false)
    }
  }

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  // Check if room is inactive
  const isRoomInactive = isTelegramRoom && telegramInfo?.room_status !== 'active'

  return (
    <div className={`border-t border-gray-200 bg-white ${className}`}>
      {/* Telegram room indicator */}
      {isTelegramRoom && telegramInfo && (
        <div className='px-4 py-2 bg-blue-50 border-b border-blue-100'>
          <div className='flex items-center space-x-2'>
            <Send className='w-4 h-4 text-blue-600' />
            <span className='text-sm text-blue-700'>
              Telegram conversation with {telegramInfo.contact.display_name}
            </span>
            {telegramInfo.contact.username && (
              <span className='text-xs text-blue-500'>@{telegramInfo.contact.username}</span>
            )}
          </div>
        </div>
      )}

      {/* Error display */}
      {error && (
        <div className='px-4 py-2 bg-red-50 border-b border-red-100'>
          <div className='flex items-center space-x-2'>
            <AlertCircle className='w-4 h-4 text-red-600' />
            <span className='text-sm text-red-700'>{error}</span>
            <button onClick={() => setError(null)} className='text-red-600 hover:text-red-700 text-xs ml-auto'>
              Dismiss
            </button>
          </div>
        </div>
      )}

      {/* Inactive room warning */}
      {isRoomInactive && (
        <div className='px-4 py-2 bg-yellow-50 border-b border-yellow-100'>
          <div className='flex items-center space-x-2'>
            <AlertCircle className='w-4 h-4 text-yellow-600' />
            <span className='text-sm text-yellow-700'>
              This Telegram conversation is inactive. Messages may not be delivered.
            </span>
          </div>
        </div>
      )}

      {/* Message input form */}
      <form onSubmit={handleSubmit} className='p-4'>
        <div className='flex items-end space-x-3'>
          {/* Attachment button (placeholder) */}
          <button
            type='button'
            className='flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 transition-colors'
            disabled={sending || isRoomInactive}
            title='Attach file'
          >
            <Paperclip className='w-5 h-5' />
          </button>

          {/* Message input */}
          <div className='flex-1 relative'>
            <textarea
              ref={textareaRef}
              value={message}
              onChange={e => {
                setMessage(e.target.value)
                handleTyping() // Trigger typing indicator
              }}
              onKeyDown={handleKeyDown}
              onBlur={() => {
                // Stop typing when input loses focus
                if (isTelegramRoom) {
                  stopTypingIndicator()
                }
              }}
              placeholder={
                isRoomInactive
                  ? 'Room is inactive...'
                  : isTelegramRoom
                    ? 'Type a message to send via Telegram...'
                    : 'Type a message...'
              }
              disabled={sending || isRoomInactive}
              className={`w-full resize-none border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed ${
                compact ? 'text-sm' : ''
              }`}
              rows={1}
              style={{ minHeight: '40px', maxHeight: '120px' }}
            />

            {/* Character count for long messages */}
            {message.length > 3000 && (
              <div className='absolute -top-6 right-0 text-xs text-gray-500'>{message.length}/4000</div>
            )}
          </div>

          {/* Emoji button (placeholder) */}
          <button
            type='button'
            className='flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 transition-colors'
            disabled={sending || isRoomInactive}
            title='Add emoji'
          >
            <Smile className='w-5 h-5' />
          </button>

          {/* Send button */}
          <button
            type='submit'
            disabled={!message.trim() || sending || isRoomInactive}
            className={`flex-shrink-0 p-2 rounded-lg transition-colors ${
              message.trim() && !sending && !isRoomInactive
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
            }`}
            title={isRoomInactive ? 'Room is inactive' : isTelegramRoom ? 'Send message via Telegram' : 'Send message'}
          >
            {sending ? (
              <div className='animate-spin rounded-full h-5 w-5 border-b-2 border-current'></div>
            ) : (
              <Send className='w-5 h-5' />
            )}
          </button>
        </div>

        {/* Telegram-specific hints */}
        {isTelegramRoom && !isRoomInactive && (
          <div className='mt-2 text-xs text-gray-500'>
            Messages will be sent to Telegram user {telegramInfo?.contact.display_name}
            {message.length > 4000 && <span className='text-red-500 ml-2'>Message too long (max 4000 characters)</span>}
          </div>
        )}
      </form>
    </div>
  )
}

export default TelegramMessageInput
