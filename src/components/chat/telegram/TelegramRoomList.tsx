// Telegram Room List Component
// Displays list of Telegram conversations integrated with internal chat

'use client'

import React, { useState, useEffect } from 'react'

import { Send, Search, Plus, MessageCircle, User, AlertCircle, Loader2, Users, UserCheck } from 'lucide-react'

import TelegramContactSelector from './TelegramContactSelector'

interface Agent {
  user_uuid: string
  username: string
  display_name: string
}

interface TelegramRoom {
  telegram_room_uuid: string
  internal_room_uuid: string
  room_status: string
  last_message_at: Date | null
  conversation_metadata: any
  assigned_agent_uuid?: string | null
  assigned_agent?: {
    user_uuid: string
    username: string
    display_name: string
    first_name: string | null
    last_name: string | null
  } | null
  room: {
    room_uuid: string
    room_name: string
    room_description: string
    room_type: string
    room_avatar?: string | null
    is_active: boolean
    is_archived: boolean
    room_settings?: any
    insert_date: Date
    update_date: Date
  } | null
  contact: {
    contact_uuid: string
    telegram_user_id: string
    telegram_chat_id: string
    username: string | null
    first_name: string | null
    last_name: string | null
    display_name: string
    phone: string | null
    language_code: string | null
    contact_info: any
    last_interaction_date: Date | null
  } | null
}

interface TelegramRoomListProps {
  onRoomSelect: (roomUuid: string) => void
  selectedRoomId?: string | null
  compact?: boolean
  showAssignmentControls?: boolean
  className?: string
}

const TelegramRoomList: React.FC<TelegramRoomListProps> = ({
  onRoomSelect,
  selectedRoomId,
  compact = false,
  showAssignmentControls = false,
  className = ''
}) => {
  const [rooms, setRooms] = useState<TelegramRoom[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showContactSelector, setShowContactSelector] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [agents, setAgents] = useState<Agent[]>([])
  const [assigningRoom, setAssigningRoom] = useState<string | null>(null)

  // Fetch Telegram rooms
  const fetchRooms = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/internal-chat/telegram/chat-rooms')

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))

        throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch chat rooms`)
      }

      const data = await response.json()

      if (!data.chat_rooms || !Array.isArray(data.chat_rooms)) {
        throw new Error('Invalid response format from server')
      }

      setRooms(data.chat_rooms)
    } catch (err) {
      console.error('Error fetching Telegram rooms:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to load chat rooms'

      setError(errorMessage)
      setRooms([]) // Clear rooms on error
    } finally {
      setLoading(false)
    }
  }



  // Fetch available agents
  const fetchAgents = async () => {
    try {
      const response = await fetch('/api/internal-chat/agents')

      if (!response.ok) {
        throw new Error('Failed to fetch agents')
      }

      const data = await response.json()

      setAgents(data.agents || [])
    } catch (err) {
      console.error('Error fetching agents:', err)
    }
  }

  // Handle agent assignment
  const handleAssignAgent = async (roomUuid: string, agentUuid: string) => {
    try {
      setAssigningRoom(roomUuid)

      const response = await fetch('/api/internal-chat/telegram/assign-agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          telegram_room_uuid: roomUuid,
          agent_uuid: agentUuid
        })
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))

        throw new Error(errorData.error || 'Failed to assign agent')
      }

      // Refresh rooms to show updated assignment
      await fetchRooms()
    } catch (err) {
      console.error('Error assigning agent:', err)
      setError(err instanceof Error ? err.message : 'Failed to assign agent')
    } finally {
      setAssigningRoom(null)
    }
  }



  // Initial load
  useEffect(() => {
    fetchRooms()

    if (showAssignmentControls) {
      fetchAgents()
    }
  }, [showAssignmentControls])

  // Filter rooms based on search
  const filteredRooms = rooms.filter(room => {
    if (!searchTerm) return true

    const searchLower = searchTerm.toLowerCase()

    return (
      room.contact?.display_name.toLowerCase().includes(searchLower) ||
      room.contact?.username?.toLowerCase().includes(searchLower) ||
      room.room?.room_name.toLowerCase().includes(searchLower)
    )
  })

  // Handle contact selection from selector
  const handleContactSelect = (contact: any) => {
    if (contact.chat_room) {
      onRoomSelect(contact.chat_room.room_uuid)
      setShowContactSelector(false)
    }
  }

  // Handle room creation
  const handleCreateRoom = async (contactUuid: string) => {
    try {
      const response = await fetch('/api/internal-chat/telegram/chat-rooms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          telegram_contact_uuid: contactUuid
        })
      })

      if (!response.ok) {
        throw new Error('Failed to create room')
      }

      const data = await response.json()

      // Refresh rooms list
      await fetchRooms()

      // Select the new room
      onRoomSelect(data.internal_room_uuid)
      setShowContactSelector(false)
    } catch (err) {
      console.error('Error creating room:', err)
      setError(err instanceof Error ? err.message : 'Failed to create room')
    }
  }

  // Format last message time
  const formatLastMessageTime = (date: Date | null) => {
    if (!date) return ''

    const now = new Date()
    const messageDate = new Date(date)
    const diffMs = now.getTime() - messageDate.getTime()
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMinutes < 1) return 'now'
    if (diffMinutes < 60) return `${diffMinutes}m`
    if (diffHours < 24) return `${diffHours}h`
    if (diffDays < 7) return `${diffDays}d`

    return messageDate.toLocaleDateString()
  }

  if (showContactSelector) {
    return (
      <TelegramContactSelector
        onContactSelect={handleContactSelect}
        onCreateRoom={handleCreateRoom}
        className={className}
      />
    )
  }

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Header */}
      <div className='p-4 border-b border-gray-200'>
        <div className='flex items-center justify-between mb-3'>
          <h3 className={`font-semibold text-gray-900 ${compact ? 'text-base' : 'text-lg'}`}>Telegram Chats</h3>

          <button
            onClick={() => setShowContactSelector(true)}
            className='p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors'
            title='Start new Telegram conversation'
          >
            <Plus className='w-5 h-5' />
          </button>
        </div>

        {/* Search */}
        <div className='relative'>
          <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4' />
          <input
            type='text'
            placeholder='Search Telegram chats...'
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className='w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
          />
        </div>
      </div>

      {/* Content */}
      <div className='flex-1 overflow-y-auto'>
        {loading && (
          <div className='flex items-center justify-center py-8'>
            <div className='text-center'>
              <Loader2 className='w-8 h-8 animate-spin text-blue-600 mx-auto mb-2' />
              <p className='text-sm text-gray-500'>Loading conversations...</p>
            </div>
          </div>
        )}

        {error && (
          <div className='p-4 text-center'>
            <div className='text-red-500 mb-2'>
              <AlertCircle className='w-8 h-8 mx-auto' />
            </div>
            <p className='text-sm text-red-600 mb-3'>{error}</p>
            <button
              onClick={fetchRooms}
              className='px-3 py-1 bg-red-100 text-red-700 text-sm rounded hover:bg-red-200 transition-colors'
            >
              Try Again
            </button>
          </div>
        )}

        {!loading && !error && filteredRooms.length === 0 && (
          <div className='p-6 text-center'>
            {searchTerm ? (
              <div>
                <div className='text-gray-400 mb-3'>
                  <MessageCircle className='w-12 h-12 mx-auto' />
                </div>
                <h3 className='text-lg font-medium text-gray-900 mb-2'>No conversations found</h3>
                <p className='text-sm text-gray-500'>Try adjusting your search terms.</p>
              </div>
            ) : (
              <div>
                <div className='text-gray-400 mb-3'>
                  <Users className='w-12 h-12 mx-auto' />
                </div>
                <h3 className='text-lg font-medium text-gray-900 mb-2'>No Telegram conversations yet</h3>
                <p className='text-sm text-gray-500 mb-4'>Start chatting with your Telegram contacts.</p>
                <button
                  onClick={() => setShowContactSelector(true)}
                  className='px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors'
                >
                  Start a conversation
                </button>
              </div>
            )}
          </div>
        )}

        {!loading && !error && filteredRooms.length > 0 && (
          <div className='divide-y divide-gray-100'>
            {filteredRooms.map(room => (
              <div
                key={room.telegram_room_uuid}
                onClick={() => onRoomSelect(room.internal_room_uuid)}
                className={`p-4 hover:bg-gray-50 cursor-pointer transition-colors ${
                  selectedRoomId === room.internal_room_uuid ? 'bg-blue-50 border-r-2 border-blue-500' : ''
                }`}
              >
                <div className='flex items-start space-x-3'>
                  {/* Avatar */}
                  <div className='flex-shrink-0'>
                    <div className='w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center'>
                      <Send className='w-5 h-5 text-blue-600' />
                    </div>
                  </div>

                  {/* Room Info */}
                  <div className='flex-1 min-w-0'>
                    <div className='flex items-center justify-between'>
                      <h4 className={`font-medium text-gray-900 truncate ${compact ? 'text-sm' : ''}`}>
                        {room.contact?.display_name || room.room?.room_name || 'Unknown Contact'}
                      </h4>

                      <div className='flex items-center space-x-2'>
                        {/* Assignment controls or status */}
                        {showAssignmentControls ? (
                          <>
                            {assigningRoom === room.telegram_room_uuid ? (
                              <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600'></div>
                            ) : (
                              <>
                                {/* Show assigned agent or assignment dropdown */}
                                {room.assigned_agent ? (
                                  <div className='flex items-center text-xs text-green-600'>
                                    <UserCheck className='w-3 h-3 mr-1' />
                                    <span>{room.assigned_agent.display_name}</span>
                                  </div>
                                ) : (
                                  <select
                                    value={room.assigned_agent_uuid || ''}
                                    onChange={e => {
                                      e.stopPropagation()

                                      if (e.target.value) {
                                        handleAssignAgent(room.telegram_room_uuid, e.target.value)
                                      }
                                    }}
                                    className='text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500'
                                    onClick={e => e.stopPropagation()}
                                  >
                                    <option value=''>Assign Agent</option>
                                    {agents.map(agent => (
                                      <option key={agent.user_uuid} value={agent.user_uuid}>
                                        {agent.display_name}
                                      </option>
                                    ))}
                                  </select>
                                )}
                              </>
                            )}
                          </>
                        ) : (
                          <>
                            {/* Assignment status */}
                            {room.assigned_agent_uuid ? (
                              <div className='flex items-center text-xs text-green-600'>
                                <UserCheck className='w-3 h-3 mr-1' />
                                <span>Assigned</span>
                              </div>
                            ) : (
                              <div className='flex items-center text-xs text-orange-600'>
                                <User className='w-3 h-3 mr-1' />
                                <span>Unassigned</span>
                              </div>
                            )}
                          </>
                        )}

                        {/* Last message time */}
                        <span className='text-xs text-gray-500'>{formatLastMessageTime(room.last_message_at)}</span>
                      </div>
                    </div>

                    {/* Username and contact info */}
                    <div className='flex items-center space-x-3 mt-1'>
                      {room.contact?.username && (
                        <span className='text-xs text-gray-500 font-mono'>@{room.contact.username}</span>
                      )}

                      {room.contact?.phone && <span className='text-xs text-gray-500'>{room.contact.phone}</span>}

                      {room.contact?.language_code && (
                        <span className='text-xs text-gray-500 uppercase font-mono'>{room.contact.language_code}</span>
                      )}
                    </div>

                    {/* Room status and metadata */}
                    <div className='flex items-center justify-between mt-2'>
                      <p className='text-xs text-gray-500 truncate'>
                        {room.room?.room_description || 'Telegram conversation'}
                      </p>

                      <div className='flex items-center space-x-2'>
                        {/* Status indicator */}
                        <div
                          className={`w-2 h-2 rounded-full ${
                            room.room_status === 'active' ? 'bg-green-400' : 'bg-gray-400'
                          }`}
                        ></div>

                        <span
                          className={`text-xs px-2 py-0.5 rounded-full ${
                            room.room_status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {room.room_status}
                        </span>
                      </div>
                    </div>

                    {/* Status badge */}
                    <div className='flex items-center mt-2 space-x-2'>
                      <span className='inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800'>
                        <Send className='w-3 h-3 mr-1' />
                        Telegram
                      </span>

                      {room.room_status !== 'active' && (
                        <span className='inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800'>
                          {room.room_status}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className='p-4 border-t border-gray-200 bg-gray-50'>
        <p className='text-xs text-gray-500 text-center'>
          {filteredRooms.length} Telegram chat{filteredRooms.length !== 1 ? 's' : ''}
        </p>
      </div>
    </div>
  )
}

export default TelegramRoomList
