// Delete Conversation Confirmation Dialog
// Modal dialog to confirm conversation deletion

'use client'

import React from 'react'

import { useSession } from 'next-auth/react'

import type { ChatRoom } from '@/types/apps/internal-chat/chatTypes'
import { RoomType } from '@/types/apps/internal-chat/chatTypes'

interface DeleteConversationDialogProps {
  isOpen: boolean
  room: ChatRoom | null
  onConfirm: () => void
  onCancel: () => void
  isDeleting?: boolean
}

const DeleteConversationDialog: React.FC<DeleteConversationDialogProps> = ({
  isOpen,
  room,
  onConfirm,
  onCancel,
  isDeleting = false
}) => {
  const { data: session } = useSession()

  if (!isOpen || !room) return null

  const getRoomDisplayName = () => {
    if (room.room_type === RoomType.DIRECT && room.participants && session?.user?.id) {
      // For direct messages, show the other participant's name
      const otherParticipant = room.participants.find(p => p.user_uuid !== session.user!.id)

      return otherParticipant?.user_name || room.room_name || 'Direct Message'
    }

    return room.room_name || 'Unnamed Room'
  }

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && !isDeleting) {
      onCancel()
    }
  }

  return (
    <div
      className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'
      onClick={handleBackdropClick}
    >
      <div className='bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 animate-in fade-in-0 zoom-in-95 duration-200'>
        {/* Header */}
        <div className='px-6 py-4 border-b border-gray-200 dark:border-gray-700'>
          <div className='flex items-center space-x-3'>
            <div className='flex-shrink-0 w-10 h-10 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center'>
              <i className='ri-delete-bin-line text-red-600 dark:text-red-400 text-lg' />
            </div>
            <div>
              <h3 className='text-lg font-semibold text-gray-900 dark:text-gray-100'>Delete Conversation</h3>
              <p className='text-sm text-gray-500 dark:text-gray-400'>This action cannot be undone</p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className='px-6 py-4'>
          <p className='text-gray-700 dark:text-gray-300 mb-4'>
            Are you sure you want to delete the conversation{' '}
            <span className='font-semibold text-gray-900 dark:text-gray-100'>&ldquo;{getRoomDisplayName()}&rdquo;</span>
            ?
          </p>
          <div className='bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3'>
            <div className='flex items-start space-x-2'>
              <i className='ri-warning-line text-yellow-600 dark:text-yellow-400 text-sm mt-0.5 flex-shrink-0' />
              <div className='text-sm text-yellow-800 dark:text-yellow-200'>
                <p className='font-medium mb-1'>This will permanently delete:</p>
                <ul className='list-disc list-inside space-y-0.5 text-xs'>
                  <li>All messages in this conversation</li>
                  <li>All file attachments</li>
                  <li>All notifications</li>
                  <li>Conversation history for all participants</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className='px-6 py-4 bg-gray-50 dark:bg-gray-700/50 rounded-b-lg flex justify-end space-x-3'>
          <button
            onClick={onCancel}
            disabled={isDeleting}
            className='px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed'
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            disabled={isDeleting}
            className='px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1 rounded-lg transition-colors duration-150 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2'
          >
            {isDeleting && <i className='ri-loader-4-line animate-spin text-sm' />}
            <span>{isDeleting ? 'Deleting...' : 'Delete Conversation'}</span>
          </button>
        </div>
      </div>
    </div>
  )
}

export default DeleteConversationDialog
