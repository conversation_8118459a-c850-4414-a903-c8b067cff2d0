// Message Input Component
// Input field for typing and sending messages

'use client'

import React, { useState, useRef, useEffect } from 'react'

import { useAppDispatch, useAppSelector } from '@/redux-store/hooks'
import { sendMessage, addOptimisticMessage } from '@/redux-store/slices/internal-chat/internalChatSlice'
import { MessageType } from '@/types/apps/internal-chat/chatTypes'
import { MessageUtils } from '@/utils/chat/messageUtils'

interface MessageInputProps {
  roomId: string
  compact?: boolean
  className?: string
}

const MessageInput: React.FC<MessageInputProps> = ({ roomId, compact = false, className = '' }) => {
  const dispatch = useAppDispatch()
  const { currentUser } = useAppSelector(state => state.internalChatReducer)
  const [message, setMessage] = useState('')
  const [isUploading, setIsUploading] = useState(false)
  const [isSending, setIsSending] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }, [message])

  const handleSendMessage = async () => {
    const trimmedMessage = message.trim()

    if (!trimmedMessage || !currentUser || isSending) return

    setIsSending(true)

    // Validate message
    const validation = MessageUtils.validateMessageContent(trimmedMessage)

    if (!validation.isValid) {
      alert(validation.error)
      setIsSending(false)

      return
    }

    // Store the message content before clearing
    const messageToSend = trimmedMessage

    // Clear input immediately to provide instant feedback
    setMessage('')

    // Focus back to textarea for better UX
    setTimeout(() => {
      textareaRef.current?.focus()
    }, 0)

    // Create optimistic message
    const tempId = MessageUtils.generateTempId()

    const optimisticMessage = {
      message_id: tempId,
      room_uuid: roomId,
      author_uuid: currentUser?.id || 'current-user',
      content: messageToSend,
      message_type: MessageType.TEXT,
      reply_to: null,
      edited_at: null,
      created_at: Math.floor(Date.now() / 1000),
      flags: 0,
      author_name: currentUser?.username || 'You',
      author_email: currentUser?.email || '',
      is_deleted: false,
      is_edited: false,
      is_pinned: false,
      is_system: false,
      is_urgent: false,
      attachments: [],
      reactions: [],
      reply_parent: undefined
    }

    // Add optimistic message
    dispatch(addOptimisticMessage({ roomId, message: optimisticMessage }))

    try {
      // Send actual message
      await dispatch(
        sendMessage({
          roomId,
          messageData: {
            content: messageToSend,
            message_type: MessageType.TEXT
          }
        })
      ).unwrap()
    } catch (error) {
      console.error('Failed to send message:', error)

      // Restore the message content if sending failed
      setMessage(messageToSend)
    } finally {
      setIsSending(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const handleFileUpload = () => {
    if (!isUploading) {
      fileInputRef.current?.click()
    }
  }

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files

    if (files && files.length > 0) {
      setIsUploading(true)

      try {
        // Handle file upload
        console.log('Files selected:', files)

        // TODO: Implement file upload

        // Reset file input
        if (fileInputRef.current) {
          fileInputRef.current.value = ''
        }
      } catch (error) {
        console.error('File upload failed:', error)
      } finally {
        setIsUploading(false)
      }
    }
  }

  return (
    <div className={`border-t border-gray-200 bg-white shadow-sm ${className}`}>
      {/* Input area */}
      <div className={compact ? 'p-2' : 'p-3'}>
        <div className={`flex items-center ${compact ? 'gap-1' : 'gap-2'}`}>
          {/* File upload button */}
          <button
            onClick={handleFileUpload}
            disabled={isUploading}
            className={`${
              compact ? 'p-2' : 'p-2.5'
            } rounded-lg transition-all duration-200 flex-shrink-0 group relative ${
              isUploading
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'hover:bg-blue-50 hover:text-blue-600 text-gray-500'
            }`}
            title={isUploading ? 'Uploading...' : 'Attach file'}
          >
            {isUploading ? (
              <i className={`ri-loader-4-line animate-spin ${compact ? 'text-base' : 'text-lg'}`} />
            ) : (
              <i className={`ri-attachment-line ${compact ? 'text-base' : 'text-lg'}`} />
            )}
          </button>

          {/* Message input */}
          <div className='flex items-center flex-1 relative'>
            <textarea
              ref={textareaRef}
              value={message}
              onChange={e => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder='Type a message...'
              className={`w-full resize-none border-2 rounded-xl transition-all duration-200 ${
                compact ? 'px-3 py-2 text-sm' : 'px-4 py-3'
              } ${
                message.trim()
                  ? 'border-blue-200 bg-blue-50/30 focus:border-blue-400 focus:bg-blue-50/50'
                  : 'border-gray-200 bg-gray-50/50 focus:border-blue-300 focus:bg-white'
              } focus:outline-none focus:ring-0 placeholder-gray-400`}
              style={{
                minHeight: compact ? '40px' : '44px',
                maxHeight: compact ? '100px' : '140px'
              }}
              rows={1}
            />

            {/* Character count */}
            {message.length > 3500 && (
              <div
                className={`absolute ${compact ? 'bottom-1.5 right-2' : 'bottom-2 right-3'} text-xs font-medium ${
                  message.length > 4000
                    ? 'text-red-500 bg-red-50 px-1.5 py-0.5 rounded'
                    : message.length > 3800
                      ? 'text-orange-500 bg-orange-50 px-1.5 py-0.5 rounded'
                      : 'text-gray-500'
                }`}
              >
                {message.length}/4000
              </div>
            )}
          </div>

          {/* Send button */}
          <button
            onClick={handleSendMessage}
            disabled={!message.trim() || isSending}
            className={`${
              compact ? 'p-2' : 'p-2.5'
            } rounded-lg transition-all duration-200 flex-shrink-0 group relative ${
              message.trim() && !isSending
                ? 'bg-blue-600 text-white hover:bg-blue-700 hover:scale-105 shadow-md hover:shadow-lg'
                : 'bg-gray-200 text-gray-400 cursor-not-allowed'
            }`}
            title={isSending ? 'Sending...' : 'Send message'}
          >
            {isSending ? (
              <i className={`ri-loader-4-line animate-spin ${compact ? 'text-base' : 'text-lg'}`} />
            ) : (
              <i className={`ri-send-plane-line ${compact ? 'text-base' : 'text-lg'}`} />
            )}
          </button>
        </div>
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type='file'
        multiple
        className='hidden'
        onChange={handleFileSelect}
        accept='image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.zip,.rar'
      />
    </div>
  )
}

export default MessageInput
