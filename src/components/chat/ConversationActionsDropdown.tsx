// Conversation Actions Dropdown Component
// Three-dot menu for conversation actions (delete, etc.)

'use client'

import React, { useState, useRef, useEffect } from 'react'

import type { ChatRoom } from '@/types/apps/internal-chat/chatTypes'
import { ParticipantRole } from '@/types/apps/internal-chat/chatTypes'
import { useAppSelector } from '@/redux-store/hooks'

interface ConversationActionsDropdownProps {
  room: ChatRoom
  onDelete: (roomId: string) => void
  compact?: boolean
  className?: string
}

const ConversationActionsDropdown: React.FC<ConversationActionsDropdownProps> = ({
  room,
  onDelete,
  compact = false,
  className = ''
}) => {
  const { currentUser } = useAppSelector(state => state.internalChatReducer)
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  // Check if current user can delete the room
  const canDelete = () => {
    if (!currentUser) return false

    // Check if user is room owner
    const userParticipant = room.participants?.find(p => p.user_uuid === currentUser.user_uuid)

    return userParticipant?.participant_role === ParticipantRole.OWNER
  }

  const handleToggleDropdown = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsOpen(!isOpen)
  }

  const handleDelete = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsOpen(false)
    onDelete(room.room_uuid)
  }

  // Don't render if user has no permissions
  if (!canDelete()) {
    return null
  }

  return (
    <div className={`relative ${className}`}>
      {/* Three-dot button */}
      <button
        ref={buttonRef}
        onClick={handleToggleDropdown}
        className={`
          flex items-center justify-center rounded-full transition-all duration-200 relative z-30
          hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1
          ${compact ? 'w-6 h-6' : 'w-8 h-8'}
        `}
        title='More actions'
      >
        <i className={`ri-more-2-fill text-gray-500 dark:text-gray-400 ${compact ? 'text-sm' : 'text-base'}`} />
      </button>

      {/* Dropdown menu */}
      {isOpen && (
        <div
          ref={dropdownRef}
          className={`
            absolute right-0 mt-1 py-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700
            min-w-[160px] z-[100] animate-in fade-in-0 zoom-in-95 duration-100
            ${compact ? 'text-sm' : ''}
          `}
        >
          {/* Delete action */}
          <button
            onClick={handleDelete}
            className={`
              w-full px-3 py-2 text-left flex items-center space-x-2 transition-colors duration-150
              hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400
              ${compact ? 'text-sm' : ''}
            `}
          >
            <i className='ri-delete-bin-line flex-shrink-0' />
            <span>Delete conversation</span>
          </button>

          {/* Placeholder for future actions */}
          <div className='border-t border-gray-200 dark:border-gray-600 my-1' />
          <div className='px-3 py-2 text-xs text-gray-400 dark:text-gray-500'>More actions coming soon...</div>
        </div>
      )}
    </div>
  )
}

export default ConversationActionsDropdown
