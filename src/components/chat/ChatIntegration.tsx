// Chat Integration Component
// Main component that manages chat popup, notifications, and full-page integration

'use client'

import React, { useState, useEffect, useMemo } from 'react'

import { useRouter, useParams } from 'next/navigation'

import { useSession } from 'next-auth/react'

import { useAppDispatch } from '@/redux-store/hooks'
import type { ChatMessage, UserPresence, ChatNotification, ChatRoom } from '@/types/apps/internal-chat/chatTypes'
import {
  setConnectionStatus,
  setCurrentUser,
  addMessage,
  updateUserPresence,
  addNotification,
  incrementUnreadCount,
  updateUnreadCount,
  addRoom
} from '@/redux-store/slices/internal-chat/internalChatSlice'

// Components
import ChatToggleButton from './ChatToggleButton'
import ChatPopup from './ChatPopup'

// Hooks
import { useChatNotifications } from '@/hooks/chat/useChatNotifications'
import { useChatSocket } from '@/hooks/chat/useChatSocket'
import { useChatInitialization } from '@/hooks/chat/useChatInitialization'

interface ChatIntegrationProps {
  position?: 'bottom-right' | 'bottom-left'
  className?: string
}

const ChatIntegration: React.FC<ChatIntegrationProps> = ({ position = 'bottom-right', className = '' }) => {
  const dispatch = useAppDispatch()
  const { data: session } = useSession()
  const router = useRouter()
  const params = useParams()

  // const { isConnected, connectionStatus, currentUser } = useAppSelector(state => state.internalChatReducer)

  // UI State
  const [showPopup, setShowPopup] = useState(false)
  const [popupRoomId, setPopupRoomId] = useState<string>()

  // Custom hooks
  const { requestNotificationPermission, showBrowserNotification } = useChatNotifications()

  // Initialize chat rooms to load unread counts
  useChatInitialization({
    autoInitialize: true // Auto-initialize to load unread counts for the toggle button
  })

  // Memoize socket options to prevent unnecessary reconnections
  const socketOptions = useMemo(
    () => ({
      onMessage: (message: ChatMessage) => {
        console.log('ChatIntegration - Received message via socket:', {
          messageId: message.message_id,
          content: message.content?.substring(0, 50),
          author: message.author_name,
          authorUuid: message.author_uuid,
          currentUserUuid: session?.user?.id,
          isFromCurrentUser: message.author_uuid === session?.user?.id,
          timestamp: message.created_at,
          roomId: message.room_uuid
        })

        dispatch(addMessage({ roomId: message.room_uuid, message }))

        // Skip notifications for messages from current user
        if (message.author_uuid === session?.user?.id) {
          console.log('Skipping notification for own message')

          return
        }

        // Update unread count from the socket message if available
        // This ensures the count is synchronized with the database
        if (message.unread_counts && session?.user?.id) {
          const userUnreadCount = message.unread_counts[session.user.id]

          if (typeof userUnreadCount === 'number') {
            dispatch(updateUnreadCount({ roomId: message.room_uuid, count: userUnreadCount }))
          }
        } else {
          // Fallback: increment unread count for messages not from current user
          // Only skip increment if popup is open AND showing the same room AND message is not from current user
          const isFromCurrentUser = message.author_uuid === session?.user?.id
          const shouldIncrementUnread = !isFromCurrentUser && !(showPopup && popupRoomId === message.room_uuid)

          if (shouldIncrementUnread) {
            dispatch(incrementUnreadCount({ roomId: message.room_uuid }))
          }
        }

        // Only show browser notification - don't auto-open popup
        // Let users decide when to open the chat
        showBrowserNotification({
          title: message.author_name || 'New Message',
          body: message.content || 'Sent an attachment',
          icon: '/icons/chat-notification.png',
          onClick: () => {
            setPopupRoomId(message.room_uuid)
            setShowPopup(true)
          }
        })
      },
      onPresenceUpdate: (presence: UserPresence) => {
        dispatch(updateUserPresence(presence))
      },
      onNotification: (notification: ChatNotification) => {
        console.log('Received notification:', notification)
        dispatch(addNotification(notification))

        // For room invites, show browser notification
        if (notification.notification_type === 'room_invite') {
          showBrowserNotification({
            title: 'New Chat Invitation',
            body: notification.message_content || 'You have been added to a new conversation',
            icon: '/icons/chat-notification.png',
            onClick: () => {
              setPopupRoomId(notification.room_uuid)
              setShowPopup(true)
            }
          })
        }
      },
      onNewRoom: (room: ChatRoom & { unread_counts?: Record<string, number> }) => {
        console.log('Received new room:', room)

        // Extract unread count for current user if available
        let roomWithUnreadCount = room

        if (room.unread_counts && session?.user?.id) {
          const userUnreadCount = room.unread_counts[session.user.id] || 0

          roomWithUnreadCount = {
            ...room,
            unread_count: userUnreadCount
          }
          console.log(`New room ${room.room_uuid} has unread count: ${userUnreadCount} for user ${session.user.id}`)
        }

        dispatch(addRoom(roomWithUnreadCount))

        // Don't increment unread count here - it will be handled when messages are received
        // The room should start with the unread count from the database
      },
      onConnectionChange: (status: 'connecting' | 'connected' | 'disconnected' | 'error') => {
        dispatch(setConnectionStatus(status))
      },
      domain: session?.user?.domain || 'default'
    }),
    [dispatch, session?.user?.id, session?.user?.domain, showBrowserNotification, showPopup, popupRoomId]
  )

  // Socket connection with memoized options
  const { connect, disconnect, isSocketConnected } = useChatSocket(socketOptions)

  // Initialize chat when user session is available
  useEffect(() => {
    if (session?.user) {
      dispatch(setCurrentUser(session.user))

      // Request notification permission
      requestNotificationPermission()

      // Connect to chat socket (async)
      connect(session.user.id).catch(error => {
        console.error('Failed to connect to chat socket:', error)
      })
    }

    return () => {
      if (isSocketConnected) {
        disconnect().catch(error => {
          console.error('Failed to disconnect from chat socket:', error)
        })
      }
    }
  }, [
    session?.user?.id,
    dispatch,
    connect,
    disconnect,
    requestNotificationPermission,
    isSocketConnected,
    session?.user
  ])

  // Handle popup toggle
  const handleTogglePopup = () => {
    setShowPopup(!showPopup)
  }

  // Handle expand to full page
  const handleExpandToFullPage = () => {
    setShowPopup(false)
    const locale = params.lang || 'en'

    // Navigate to internal chat page
    if (popupRoomId) {
      router.push(`/${locale}/apps/internal-chat/room/${popupRoomId}`)
    } else {
      router.push(`/${locale}/apps/internal-chat`)
    }
  }

  // Handle close popup
  const handleClosePopup = () => {
    setShowPopup(false)
    setPopupRoomId(undefined)
  }

  // Don't render if user is not authenticated
  if (!session?.user) {
    return null
  }

  return (
    <>
      {/* Chat Toggle Button */}
      <ChatToggleButton onClick={handleTogglePopup} position={position} className={className} />

      {/* Chat Popup */}
      <ChatPopup
        isOpen={showPopup}
        onClose={handleClosePopup}
        onExpandToFullPage={handleExpandToFullPage}
        initialRoomId={popupRoomId}
        position={position}
      />
    </>
  )
}

export default ChatIntegration
