// ZALO Contact Selector Component
// Allows users to select ZALO contacts and create new conversations

'use client'

import React, { useState, useEffect, useCallback } from 'react'

import { Search, MessageCircle, Users, Smartphone, UserCheck, Phone, RefreshCw } from 'lucide-react'

import type { ZaloContactWithRoom } from '@/types/social/zaloTypes'

interface ZaloContactSelectorProps {
  onContactSelect: (contact: ZaloContactWithRoom) => void
  onCreateRoom: (contactUuid: string) => void
  compact?: boolean
  className?: string
}

const ZaloContactSelector: React.FC<ZaloContactSelectorProps> = ({
  onContactSelect,
  onCreateRoom,
  compact = false,
  className = ''
}) => {
  const [contacts, setContacts] = useState<ZaloContactWithRoom[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [followerFilter, setFollowerFilter] = useState<'all' | 'followers' | 'non-followers'>('all')

  // Fetch contacts from API with useCallback to prevent unnecessary re-renders
  const fetchContacts = useCallback(async (search?: string) => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams()
      const searchQuery = search !== undefined ? search : searchTerm

      if (searchQuery?.trim()) params.append('search', searchQuery.trim())
      params.append('limit', '50')

      if (followerFilter === 'followers') params.append('is_follower', 'true')
      if (followerFilter === 'non-followers') params.append('is_follower', 'false')

      const response = await fetch(`/api/internal-chat/zalo/contacts?${params}`)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))

        throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch contacts`)
      }

      const data = await response.json()

      if (!data.contacts || !Array.isArray(data.contacts)) {
        throw new Error('Invalid response format from server')
      }

      setContacts(data.contacts)
    } catch (err) {
      console.error('Error fetching ZALO contacts:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to load contacts'

      setError(errorMessage)
      setContacts([])
    } finally {
      setLoading(false)
    }
  }, [searchTerm, followerFilter])

  // Initial load
  useEffect(() => {
    fetchContacts()
  }, [fetchContacts])

  // Search with debounce - only trigger when searchTerm changes
  useEffect(() => {
    const timer = setTimeout(() => {
      fetchContacts(searchTerm)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchTerm, fetchContacts])

  const handleContactClick = (contact: ZaloContactWithRoom) => {
    if (contact.chat_room) {
      // Room exists, select it
      onContactSelect(contact)
    } else {
      // No room exists, create one
      onCreateRoom(contact.contact_uuid)
    }
  }

  // handleCreateRoom function removed as it's not used - room creation is handled by parent component

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Header */}
      <div className='p-4 border-b border-gray-200'>
        <div className='flex items-center justify-between mb-3'>
          <h3 className={`font-semibold text-gray-900 ${compact ? 'text-base' : 'text-lg'}`}>ZALO Contacts</h3>

          <button
            onClick={() => fetchContacts(searchTerm)}
            className='p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors'
            title='Refresh contacts'
          >
            <RefreshCw className='w-4 h-4' />
          </button>
        </div>

        {/* Search */}
        <div className='relative mb-3'>
          <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400' />
          <input
            type='text'
            placeholder='Search contacts...'
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className='w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm'
          />
        </div>

        {/* Filter buttons */}
        <div className='flex space-x-2'>
          <button
            onClick={() => setFollowerFilter('all')}
            className={`px-3 py-1 text-xs rounded-full transition-colors ${
              followerFilter === 'all' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            All
          </button>
          <button
            onClick={() => setFollowerFilter('followers')}
            className={`px-3 py-1 text-xs rounded-full transition-colors ${
              followerFilter === 'followers'
                ? 'bg-green-100 text-green-700'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            Followers
          </button>
          <button
            onClick={() => setFollowerFilter('non-followers')}
            className={`px-3 py-1 text-xs rounded-full transition-colors ${
              followerFilter === 'non-followers'
                ? 'bg-gray-100 text-gray-700'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            Non-followers
          </button>
        </div>
      </div>

      {/* Content */}
      <div className='flex-1 overflow-y-auto'>
        {loading && (
          <div className='p-6 text-center'>
            <div className='animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-3'></div>
            <p className='text-sm text-gray-500'>Loading contacts...</p>
          </div>
        )}

        {error && (
          <div className='p-6 text-center'>
            <div className='text-red-400 mb-3'>
              <Users className='w-12 h-12 mx-auto' />
            </div>
            <h3 className='text-lg font-medium text-gray-900 mb-2'>Error loading contacts</h3>
            <p className='text-sm text-gray-500 mb-4'>{error}</p>
            <button
              onClick={() => fetchContacts()}
              className='px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors'
            >
              Try again
            </button>
          </div>
        )}

        {!loading && !error && contacts.length === 0 && (
          <div className='p-6 text-center'>
            <div className='text-gray-400 mb-3'>
              <Users className='w-12 h-12 mx-auto' />
            </div>
            <h3 className='text-lg font-medium text-gray-900 mb-2'>No contacts found</h3>
            <p className='text-sm text-gray-500'>
              {searchTerm ? 'Try adjusting your search terms.' : 'No ZALO contacts available.'}
            </p>
          </div>
        )}

        {!loading && !error && contacts.length > 0 && (
          <div className='divide-y divide-gray-100'>
            {contacts.map(contact => (
              <div
                key={contact.contact_uuid}
                onClick={() => handleContactClick(contact)}
                className='p-4 hover:bg-gray-50 cursor-pointer transition-colors'
              >
                <div className='flex items-start space-x-3'>
                  {/* Avatar */}
                  <div className='flex-shrink-0'>
                    {contact.avatar_url ? (
                      <img
                        src={contact.avatar_url}
                        alt={contact.display_name}
                        className='w-10 h-10 rounded-full object-cover'
                      />
                    ) : (
                      <div className='w-10 h-10 bg-green-100 rounded-full flex items-center justify-center'>
                        <Smartphone className='w-5 h-5 text-green-600' />
                      </div>
                    )}
                  </div>

                  {/* Content */}
                  <div className='flex-1 min-w-0'>
                    <div className='flex items-center justify-between mb-1'>
                      <h4 className='text-sm font-medium text-gray-900 truncate'>{contact.display_name}</h4>
                      <div className='flex items-center space-x-1'>
                        {contact.is_follower && <UserCheck className='w-3 h-3 text-green-500' />}
                        {contact.phone && <Phone className='w-3 h-3 text-blue-500' />}
                        {contact.chat_room && <MessageCircle className='w-3 h-3 text-blue-500' />}
                      </div>
                    </div>

                    <div className='flex items-center justify-between'>
                      <p className='text-xs text-gray-500 truncate'>ID: {contact.zalo_user_id}</p>
                      {contact.chat_room ? (
                        <span className='text-xs text-blue-600 font-medium'>Open chat</span>
                      ) : (
                        <span className='text-xs text-green-600 font-medium'>Start chat</span>
                      )}
                    </div>

                    {contact.username && <p className='text-xs text-gray-400 mt-1'>@{contact.username}</p>}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default ZaloContactSelector
