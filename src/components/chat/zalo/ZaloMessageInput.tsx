// Zalo Message Input Component
// Enhanced message input for Zalo conversations with platform-specific features

'use client'

import React, { useState, useRef, useEffect } from 'react'

import { Send, Paperclip, Smile, AlertCircle } from 'lucide-react'

import { useZaloIntegration, useZaloRoomDetection } from '@/hooks/chat/useZaloIntegration'
import { useZaloTyping, useZaloDeliveryTracking } from '@/hooks/chat/useZaloRealtime'

interface ZaloMessageInputProps {
  roomId: string
  onMessageSent?: (message: any) => void
  compact?: boolean
  className?: string
}

const ZaloMessageInput: React.FC<ZaloMessageInputProps> = ({
  roomId,
  onMessageSent,
  compact = false,
  className = ''
}) => {
  const [message, setMessage] = useState('')
  const [sending, setSending] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const { sendZaloMessage } = useZaloIntegration()
  const { isZaloRoom, zaloInfo } = useZaloRoomDetection(roomId)

  // Real-time features
  const { sendTypingIndicator, stopTypingIndicator } = useZaloTyping(roomId)

  useZaloDeliveryTracking(roomId) // Track delivery status

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current

    if (textarea) {
      textarea.style.height = 'auto'
      textarea.style.height = `${Math.min(textarea.scrollHeight, compact ? 100 : 140)}px`
    }
  }, [message, compact])

  // Handle typing indicators
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value

    setMessage(value)

    // Clear error when user starts typing
    if (error) setError(null)

    // Send typing indicator
    if (value.trim() && isZaloRoom) {
      sendTypingIndicator()
    } else {
      stopTypingIndicator()
    }
  }

  // Handle key press
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // Handle blur (stop typing indicator)
  const handleBlur = () => {
    stopTypingIndicator()
  }

  // Send message
  const handleSendMessage = async () => {
    const messageContent = message.trim()

    if (!messageContent || sending) return

    setSending(true)
    setError(null)

    try {
      let result

      if (isZaloRoom) {
        // Send via Zalo integration
        result = await sendZaloMessage(roomId, messageContent)
      } else {
        // Send via regular internal chat API
        const response = await fetch(`/api/internal-chat/rooms/${roomId}/messages`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            content: messageContent,
            message_type: 0 // TEXT
          })
        })

        if (!response.ok) {
          throw new Error('Failed to send message')
        }

        result = await response.json()
      }

      // Notify parent component
      if (onMessageSent) {
        onMessageSent(result)
      }

      // Show warning if Zalo delivery failed
      if (isZaloRoom && result.delivery_status === 'failed') {
        setError('Message sent to internal chat but failed to deliver to Zalo')
      }

      // Clear message input
      setMessage('')
      stopTypingIndicator()

      // Focus back to input
      textareaRef.current?.focus()
    } catch (err) {
      console.error('Error sending message:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to send message'

      setError(errorMessage)
    } finally {
      setSending(false)
    }
  }

  // Handle file attachment (placeholder)
  const handleAttachment = () => {
    // TODO: Implement file attachment for Zalo
    console.log('File attachment not yet implemented for Zalo')
  }

  // Handle emoji picker (placeholder)
  const handleEmoji = () => {
    // TODO: Implement emoji picker
    console.log('Emoji picker not yet implemented')
  }

  return (
    <div className={`border-t border-gray-200 bg-white ${className}`}>
      {/* Error message */}
      {error && (
        <div className='px-4 py-2 bg-red-50 border-b border-red-200'>
          <div className='flex items-center space-x-2 text-red-700'>
            <AlertCircle className='w-4 h-4 flex-shrink-0' />
            <span className='text-sm'>{error}</span>
            <button
              onClick={() => setError(null)}
              className='ml-auto text-red-500 hover:text-red-700'
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Zalo room indicator */}
      {isZaloRoom && zaloInfo && (
        <div className='px-4 py-2 bg-green-50 border-b border-green-200'>
          <div className='flex items-center space-x-2 text-green-700'>
            <div className='w-2 h-2 bg-green-500 rounded-full'></div>
            <span className='text-xs font-medium'>
              Zalo conversation with {zaloInfo.contact.display_name}
            </span>
          </div>
        </div>
      )}

      {/* Input area */}
      <div className={`flex items-end space-x-2 ${compact ? 'p-3' : 'p-4'}`}>
        {/* Attachment button */}
        <button
          onClick={handleAttachment}
          disabled={sending}
          className={`flex-shrink-0 p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors ${
            sending ? 'opacity-50 cursor-not-allowed' : ''
          }`}
          title='Attach file'
        >
          {sending ? (
            <div className='animate-spin rounded-full h-5 w-5 border-b-2 border-gray-600'></div>
          ) : (
            <Paperclip className={`${compact ? 'w-4 h-4' : 'w-5 h-5'}`} />
          )}
        </button>

        {/* Message input */}
        <div className='flex items-center flex-1 relative'>
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onBlur={handleBlur}
            placeholder={isZaloRoom ? 'Type a message to Zalo...' : 'Type a message...'}
            disabled={sending}
            className={`w-full resize-none border-2 rounded-xl transition-all duration-200 ${
              compact ? 'px-3 py-2 text-sm' : 'px-4 py-3'
            } ${
              message.trim()
                ? 'border-green-200 bg-green-50/30 focus:border-green-400 focus:bg-green-50/50'
                : 'border-gray-200 bg-gray-50/50 focus:border-green-300 focus:bg-white'
            } focus:outline-none focus:ring-0 placeholder-gray-400 ${
              sending ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            style={{
              minHeight: compact ? '40px' : '44px',
              maxHeight: compact ? '100px' : '140px'
            }}
            rows={1}
          />
        </div>

        {/* Emoji button */}
        <button
          onClick={handleEmoji}
          disabled={sending}
          className={`flex-shrink-0 p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors ${
            sending ? 'opacity-50 cursor-not-allowed' : ''
          }`}
          title='Add emoji'
        >
          <Smile className={`${compact ? 'w-4 h-4' : 'w-5 h-5'}`} />
        </button>

        {/* Send button */}
        <button
          onClick={handleSendMessage}
          disabled={!message.trim() || sending}
          className={`flex-shrink-0 p-2 rounded-lg transition-all duration-200 ${
            message.trim() && !sending
              ? 'bg-green-500 text-white hover:bg-green-600 shadow-md hover:shadow-lg'
              : 'bg-gray-200 text-gray-400 cursor-not-allowed'
          }`}
          title='Send message'
        >
          <Send className={`${compact ? 'w-4 h-4' : 'w-5 h-5'}`} />
        </button>
      </div>
    </div>
  )
}

export default ZaloMessageInput
