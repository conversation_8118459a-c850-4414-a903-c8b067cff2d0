// ZALO Room List Component
// Displays ZALO chat rooms with search, filtering, and assignment controls

'use client'

import React, { useState, useEffect, useCallback } from 'react'

import { Search, Plus, MessageCircle, Users, Smartphone, UserCheck, Clock } from 'lucide-react'

import type { ZaloRoomInfo, ZaloContactWithRoom } from '@/types/social/zaloTypes'
import ZaloContactSelector from './ZaloContactSelector'

interface Agent {
  user_uuid: string
  username: string
  display_name: string
}

interface ZaloRoomListProps {
  onRoomSelect: (roomUuid: string) => void
  selectedRoomId?: string | null
  showAssignmentControls?: boolean
  compact?: boolean
  className?: string
}

const ZaloRoomList: React.FC<ZaloRoomListProps> = ({
  onRoomSelect,
  selectedRoomId,
  showAssignmentControls = false,
  compact = false,
  className = ''
}) => {
  const [rooms, setRooms] = useState<ZaloRoomInfo[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'assigned' | 'unassigned'>('all')
  const [showContactSelector, setShowContactSelector] = useState(false)
  const [agents, setAgents] = useState<Agent[]>([])
  const [assigningRoom, setAssigningRoom] = useState<string | null>(null)

  // Fetch rooms from API with useCallback to prevent unnecessary re-renders
  const fetchRooms = useCallback(async (search?: string) => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams()
      const searchQuery = search !== undefined ? search : searchTerm

      if (searchQuery.trim()) params.append('search', searchQuery.trim())
      params.append('status', 'active')

      const response = await fetch(`/api/internal-chat/zalo/rooms?${params}`)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))

        throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch rooms`)
      }

      const data = await response.json()

      if (!data.rooms || !Array.isArray(data.rooms)) {
        throw new Error('Invalid response format from server')
      }

      setRooms(data.rooms)
    } catch (err) {
      console.error('Error fetching ZALO rooms:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to load rooms'

      setError(errorMessage)
      setRooms([])
    } finally {
      setLoading(false)
    }
  }, [searchTerm])

  // Fetch available agents
  const fetchAgents = async () => {
    try {
      const response = await fetch('/api/internal-chat/agents')

      if (!response.ok) {
        throw new Error('Failed to fetch agents')
      }

      const data = await response.json()

      setAgents(data.agents || [])
    } catch (err) {
      console.error('Error fetching agents:', err)
    }
  }

  // Handle agent assignment
  const handleAssignAgent = async (roomUuid: string, agentUuid: string) => {
    try {
      setAssigningRoom(roomUuid)

      const response = await fetch('/api/internal-chat/zalo/assign-agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          zalo_room_uuid: roomUuid,
          agent_uuid: agentUuid
        })
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))

        throw new Error(errorData.error || 'Failed to assign agent')
      }

      // Refresh rooms to show updated assignment
      await fetchRooms()
    } catch (err) {
      console.error('Error assigning agent:', err)
      setError(err instanceof Error ? err.message : 'Failed to assign agent')
    } finally {
      setAssigningRoom(null)
    }
  }

  // Initial load
  useEffect(() => {
    fetchRooms()

    if (showAssignmentControls) {
      fetchAgents()
    }
  }, [fetchRooms, showAssignmentControls])

  // Search with debounce - only trigger when searchTerm changes
  useEffect(() => {
    const timer = setTimeout(() => {
      fetchRooms(searchTerm)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchTerm, fetchRooms])

  // Filter rooms based on assignment status
  const filteredRooms = rooms.filter(room => {
    if (statusFilter === 'assigned') return room.is_assigned
    if (statusFilter === 'unassigned') return !room.is_assigned
    
return true
  })

  const formatLastMessageTime = (date: Date | null) => {
    if (!date) return ''

    const now = new Date()
    const messageDate = new Date(date)
    const diffInHours = (now.getTime() - messageDate.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60)

      
return diffInMinutes <= 1 ? 'Just now' : `${diffInMinutes}m ago`
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)

      
return diffInDays === 1 ? '1 day ago' : `${diffInDays} days ago`
    }
  }

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Header */}
      <div className='p-4 border-b border-gray-200'>
        <div className='flex items-center justify-between mb-3'>
          <h3 className={`font-semibold text-gray-900 ${compact ? 'text-base' : 'text-lg'}`}>ZALO Chats</h3>

          <button
            onClick={() => setShowContactSelector(true)}
            className='p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors'
            title='Start new ZALO conversation'
          >
            <Plus className='w-5 h-5' />
          </button>
        </div>

        {/* Search */}
        <div className='relative mb-3'>
          <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400' />
          <input
            type='text'
            placeholder='Search conversations...'
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className='w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm'
          />
        </div>

        {/* Filter buttons */}
        <div className='flex space-x-2'>
          <button
            onClick={() => setStatusFilter('all')}
            className={`px-3 py-1 text-xs rounded-full transition-colors ${
              statusFilter === 'all' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            All
          </button>
          <button
            onClick={() => setStatusFilter('assigned')}
            className={`px-3 py-1 text-xs rounded-full transition-colors ${
              statusFilter === 'assigned'
                ? 'bg-green-100 text-green-700'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            Assigned
          </button>
          <button
            onClick={() => setStatusFilter('unassigned')}
            className={`px-3 py-1 text-xs rounded-full transition-colors ${
              statusFilter === 'unassigned'
                ? 'bg-orange-100 text-orange-700'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            Unassigned
          </button>
        </div>
      </div>

      {/* Content */}
      <div className='flex-1 overflow-y-auto'>
        {loading && (
          <div className='p-6 text-center'>
            <div className='animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-3'></div>
            <p className='text-sm text-gray-500'>Loading conversations...</p>
          </div>
        )}

        {error && (
          <div className='p-6 text-center'>
            <div className='text-red-400 mb-3'>
              <MessageCircle className='w-12 h-12 mx-auto' />
            </div>
            <h3 className='text-lg font-medium text-gray-900 mb-2'>Error loading conversations</h3>
            <p className='text-sm text-gray-500 mb-4'>{error}</p>
            <button
              onClick={() => fetchRooms()}
              className='px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors'
            >
              Try again
            </button>
          </div>
        )}

        {!loading && !error && filteredRooms.length === 0 && (
          <div className='p-6 text-center'>
            {searchTerm ? (
              <div>
                <div className='text-gray-400 mb-3'>
                  <MessageCircle className='w-12 h-12 mx-auto' />
                </div>
                <h3 className='text-lg font-medium text-gray-900 mb-2'>No conversations found</h3>
                <p className='text-sm text-gray-500'>Try adjusting your search terms.</p>
              </div>
            ) : (
              <div>
                <div className='text-gray-400 mb-3'>
                  <Users className='w-12 h-12 mx-auto' />
                </div>
                <h3 className='text-lg font-medium text-gray-900 mb-2'>No ZALO conversations yet</h3>
                <p className='text-sm text-gray-500 mb-4'>Start chatting with your ZALO contacts.</p>
                <button
                  onClick={() => setShowContactSelector(true)}
                  className='px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors'
                >
                  Start a conversation
                </button>
              </div>
            )}
          </div>
        )}

        {!loading && !error && filteredRooms.length > 0 && (
          <div className='divide-y divide-gray-100'>
            {filteredRooms.map(room => (
              <div
                key={room.zalo_room_uuid}
                onClick={() => onRoomSelect(room.internal_room_uuid)}
                className={`p-4 hover:bg-gray-50 cursor-pointer transition-colors ${
                  selectedRoomId === room.internal_room_uuid ? 'bg-blue-50 border-r-2 border-blue-500' : ''
                }`}
              >
                <div className='flex items-start space-x-3'>
                  {/* Avatar */}
                  <div className='flex-shrink-0'>
                    <div className='w-10 h-10 bg-green-100 rounded-full flex items-center justify-center'>
                      <Smartphone className='w-5 h-5 text-green-600' />
                    </div>
                  </div>

                  {/* Content */}
                  <div className='flex-1 min-w-0'>
                    <div className='flex items-center justify-between mb-1'>
                      <h4 className='text-sm font-medium text-gray-900 truncate'>{room.contact.display_name}</h4>
                      <div className='flex items-center space-x-1'>
                        {room.contact.is_follower && <UserCheck className='w-3 h-3 text-green-500' />}
                        {room.is_assigned && <div className='w-2 h-2 bg-green-500 rounded-full' />}
                      </div>
                    </div>

                    <div className='flex items-center justify-between'>
                      <p className='text-xs text-gray-500 truncate'>ID: {room.contact.zalo_user_id}</p>
                      {room.last_message_at && (
                        <div className='flex items-center text-xs text-gray-400'>
                          <Clock className='w-3 h-3 mr-1' />
                          {formatLastMessageTime(room.last_message_at)}
                        </div>
                      )}
                    </div>

                    {/* Assignment controls */}
                    {showAssignmentControls && (
                      <div className='mt-2 flex items-center justify-between'>
                        <div className='flex items-center space-x-2'>
                          {assigningRoom === room.zalo_room_uuid ? (
                            <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600'></div>
                          ) : (
                            <>
                              {/* Show assigned agent or assignment dropdown */}
                              {room.assigned_agent ? (
                                <div className='flex items-center text-xs text-green-600'>
                                  <UserCheck className='w-3 h-3 mr-1' />
                                  <span>{room.assigned_agent.display_name}</span>
                                </div>
                              ) : (
                                <select
                                  value={room.assigned_agent_uuid || ''}
                                  onChange={e => {
                                    e.stopPropagation()

                                    if (e.target.value) {
                                      handleAssignAgent(room.zalo_room_uuid, e.target.value)
                                    }
                                  }}
                                  className='text-xs border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500'
                                  onClick={e => e.stopPropagation()}
                                >
                                  <option value=''>Assign Agent</option>
                                  {agents.map(agent => (
                                    <option key={agent.user_uuid} value={agent.user_uuid}>
                                      {agent.display_name}
                                    </option>
                                  ))}
                                </select>
                              )}
                            </>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Contact Selector Modal */}
      {showContactSelector && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
          <div className='bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[80vh] flex flex-col'>
            {/* Modal Header */}
            <div className='p-4 border-b border-gray-200 flex items-center justify-between'>
              <h3 className='text-lg font-semibold text-gray-900'>Start New ZALO Conversation</h3>
              <button
                onClick={() => setShowContactSelector(false)}
                className='p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors'
              >
                <i className='ri-close-line text-xl' />
              </button>
            </div>

            {/* Modal Content */}
            <div className='flex-1 min-h-0'>
              <ZaloContactSelector
                onContactSelect={(contact: ZaloContactWithRoom) => {
                  if (contact.chat_room) {
                    // Room exists, select it and close modal
                    onRoomSelect(contact.chat_room.internal_room_uuid)
                    setShowContactSelector(false)
                  }
                }}
                onCreateRoom={async (contactUuid: string) => {
                  try {
                    // Create room for the contact
                    const response = await fetch('/api/internal-chat/zalo/contacts', {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({ contact_uuid: contactUuid })
                    })

                    if (response.ok) {
                      const data = await response.json()

                      // Select the newly created room
                      onRoomSelect(data.room_uuid)

                      // Refresh the room list
                      fetchRooms()

                      // Close the modal
                      setShowContactSelector(false)
                    } else {
                      console.error('Failed to create ZALO room')
                    }
                  } catch (error) {
                    console.error('Error creating ZALO room:', error)
                  }
                }}
                className='h-full'
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ZaloRoomList
