// ZALO Platform Manager Component
// Main component for managing ZALO contacts, conversations, and agent assignments

'use client'

import React, { useState, useEffect } from 'react'

import { Smartphone, Users, MessageCircle, Settings } from 'lucide-react'

import ZaloRoomList from './ZaloRoomList'
import ZaloContactSelector from './ZaloContactSelector'
import type { ZaloStats } from '@/types/social/zaloTypes'

interface ZaloPlatformManagerProps {
  onRoomSelect: (roomUuid: string) => void
  selectedRoomId?: string | null
  className?: string
}

const ZaloPlatformManager: React.FC<ZaloPlatformManagerProps> = ({ onRoomSelect, selectedRoomId, className = '' }) => {
  const [activeTab, setActiveTab] = useState<'conversations' | 'contacts'>('conversations')
  const [stats, setStats] = useState<ZaloStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch ZALO statistics
  const fetchStats = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/internal-chat/zalo/stats')

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))

        throw new Error(errorData.error || 'Failed to fetch statistics')
      }

      const data = await response.json()

      setStats(data.stats)
    } catch (err) {
      console.error('Error fetching ZALO stats:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to load statistics'

      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // Initial load
  useEffect(() => {
    fetchStats()
  }, [])

  const tabs = [
    {
      id: 'conversations' as const,
      label: 'Conversations',
      icon: MessageCircle,
      count: stats?.active_rooms || 0
    },
    {
      id: 'contacts' as const,
      label: 'Contacts',
      icon: Users,
      count: stats?.total_contacts || 0
    }
  ]

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Header */}
      <div className='p-4 border-b border-gray-200'>
        <div className='flex items-center justify-between mb-4'>
          <div className='flex items-center space-x-3'>
            <div className='p-2 bg-green-100 rounded-lg'>
              <Smartphone className='w-5 h-5 text-green-600' />
            </div>
            <div>
              <h2 className='text-lg font-semibold text-gray-900'>ZALO Integration</h2>
              <p className='text-sm text-gray-600'>Manage ZALO conversations and contacts</p>
            </div>
          </div>

          <button
            onClick={fetchStats}
            className='p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors'
            title='Refresh statistics'
          >
            <Settings className='w-5 h-5' />
          </button>
        </div>

        {/* Statistics */}
        {stats && (
          <div className='grid grid-cols-4 gap-4 mb-4'>
            <div className='text-center'>
              <div className='text-lg font-semibold text-gray-900'>{stats.total_contacts}</div>
              <div className='text-xs text-gray-500'>Contacts</div>
            </div>
            <div className='text-center'>
              <div className='text-lg font-semibold text-gray-900'>{stats.active_rooms}</div>
              <div className='text-xs text-gray-500'>Active Chats</div>
            </div>
            <div className='text-center'>
              <div className='text-lg font-semibold text-orange-600'>{stats.unassigned_rooms}</div>
              <div className='text-xs text-gray-500'>Unassigned</div>
            </div>
            <div className='text-center'>
              <div className='text-lg font-semibold text-green-600'>{stats.total_messages_sent}</div>
              <div className='text-xs text-gray-500'>Messages Sent</div>
            </div>
          </div>
        )}

        {/* Error display */}
        {error && (
          <div className='mb-4 p-3 bg-red-50 border border-red-200 rounded-lg'>
            <p className='text-sm text-red-600'>{error}</p>
          </div>
        )}

        {/* Tab Navigation */}
        <div className='flex space-x-1 bg-gray-100 rounded-lg p-1'>
          {tabs.map(tab => {
            const Icon = tab.icon

            
return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex-1 flex items-center justify-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === tab.id ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Icon className='w-4 h-4' />
                <span>{tab.label}</span>
                {tab.count > 0 && (
                  <span
                    className={`px-2 py-0.5 text-xs rounded-full ${
                      activeTab === tab.id ? 'bg-gray-100 text-gray-600' : 'bg-gray-200 text-gray-600'
                    }`}
                  >
                    {tab.count}
                  </span>
                )}
              </button>
            )
          })}
        </div>
      </div>

      {/* Content */}
      <div className='flex-1 overflow-hidden'>
        {activeTab === 'conversations' && (
          <ZaloRoomList
            onRoomSelect={onRoomSelect}
            selectedRoomId={selectedRoomId}
            showAssignmentControls={true}
            className='h-full'
          />
        )}

        {activeTab === 'contacts' && (
          <ZaloContactSelector
            onContactSelect={contact => {
              console.log('Contact selected:', contact)
            }}
            onCreateRoom={contactUuid => {
              console.log('Create room for contact:', contactUuid)
              fetchStats() // Refresh stats
            }}
            className='h-full'
          />
        )}
      </div>

      {/* Loading overlay */}
      {loading && (
        <div className='absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center'>
          <div className='flex items-center space-x-2'>
            <div className='animate-spin w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full'></div>
            <span className='text-sm text-gray-600'>Loading...</span>
          </div>
        </div>
      )}
    </div>
  )
}

export default ZaloPlatformManager
