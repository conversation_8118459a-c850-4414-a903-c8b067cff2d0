// Chat Room List Component
// Displays list of chat rooms with search and filtering

'use client'

import React, { useState, useRef, useEffect, useMemo } from 'react'

import { useSession } from 'next-auth/react'

import { useAppSelector, useAppDispatch } from '@/redux-store/hooks'
import { deleteChatRoom, createVirtualRoom } from '@/redux-store/slices/internal-chat/internalChatSlice'
import type { ChatRoom } from '@/types/apps/internal-chat/chatTypes'
import { RoomType } from '@/types/apps/internal-chat/chatTypes'
import { MessageUtils } from '@/utils/chat/messageUtils'
import UserSearch from './UserSearch'
import SimpleCreateGroupDialog from './SimpleCreateGroupDialog'
import DeleteConversationDialog from './DeleteConversationDialog'
import TelegramContactSelector from './telegram/TelegramContactSelector'
import ZaloContactSelector from './zalo/ZaloContactSelector'
import FacebookContactSelector from './facebook/FacebookContactSelector'

interface ChatRoomListProps {
  onRoomSelect: (roomId: string) => void
  compact?: boolean
  className?: string
}

const ChatRoomList: React.FC<ChatRoomListProps> = ({ onRoomSelect, compact = false, className = '' }) => {
  const dispatch = useAppDispatch()
  const { data: session } = useSession()
  const { rooms, roomsLoading, unreadCounts } = useAppSelector(state => state.internalChatReducer)

  const [selectedFilter, setSelectedFilter] = useState<RoomType | 'all' | 'social'>('all')
  const [socialSubFilter, setSocialSubFilter] = useState<'all' | 'telegram' | 'zalo' | 'facebook'>('all')
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [roomToDelete, setRoomToDelete] = useState<ChatRoom | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null)
  const [createGroupDialogOpen, setCreateGroupDialogOpen] = useState(false)
  const [showTelegramContacts, setShowTelegramContacts] = useState(false)
  const [showZaloContacts, setShowZaloContacts] = useState(false)
  const [showFacebookContacts, setShowFacebookContacts] = useState(false)

  // Removed debug refresh functionality

  const dropdownRef = useRef<HTMLDivElement>(null)

  // Handle clicks outside dropdown to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node

      // Check if click is inside any dropdown
      const isInsideDropdown = dropdownRef.current && dropdownRef.current.contains(target)

      // Check if click is on a three-dot button (to avoid closing when opening)
      const isThreeDotButton = (target as Element)?.closest?.('[data-dropdown-trigger]')

      if (!isInsideDropdown && !isThreeDotButton && openDropdownId) {
        setOpenDropdownId(null)
      }
    }

    if (openDropdownId) {
      // Use a small delay to avoid immediate closure
      const timeoutId = setTimeout(() => {
        document.addEventListener('mousedown', handleClickOutside)
      }, 150)

      return () => {
        clearTimeout(timeoutId)
        document.removeEventListener('mousedown', handleClickOutside)
      }
    }
  }, [openDropdownId])

  // Memoize filtered and sorted rooms to prevent unnecessary re-computations
  const filteredRooms = useMemo(() => {
    // Convert rooms object to array
    const roomsArray = Object.values(rooms) as ChatRoom[]

    // Filter rooms by type and platform
    return roomsArray
      .filter(room => {
        // First, exclude hidden/deleted rooms
        if ((room as any).is_hidden || (room as any).deleted_at) {
          return false
        }

        // Filter by type/platform
        if (selectedFilter === 'social') {
          // Show only external platform rooms (Telegram, ZALO, etc.)
          return room.platform && room.platform !== 'internal'
        } else if (selectedFilter !== 'all') {
          // Filter by room type for internal rooms
          return room.room_type === selectedFilter
        }

        return true
      })
      .filter(room => {
        // Apply social sub-filter if in social mode
        if (selectedFilter === 'social' && socialSubFilter !== 'all') {
          return room.platform === socialSubFilter
        }

        return true
      })
      .sort((a, b) => {
        // Sort by unread count first, then by last activity
        const aUnread = unreadCounts[a.room_uuid] || 0
        const bUnread = unreadCounts[b.room_uuid] || 0

        if (aUnread !== bUnread) {
          return bUnread - aUnread // Rooms with unread messages first
        }

        // Then by last activity
        return new Date(b.update_date).getTime() - new Date(a.update_date).getTime()
      })
  }, [rooms, selectedFilter, socialSubFilter, unreadCounts])

  const getRoomIcon = (room: any) => {
    // Platform-specific icons
    if (room.platform === 'telegram') {
      return 'ri-telegram-line'
    }

    if (room.platform === 'zalo') {
      return 'ri-message-line' // Using message icon for ZALO since there's no specific ZALO icon
    }

    if (room.platform === 'facebook') {
      return 'ri-facebook-line'
    }

    // Default internal chat icons
    switch (room.room_type) {
      case RoomType.DIRECT:
        return 'ri-user-line'
      case RoomType.GROUP:
        return 'ri-group-line'
      case RoomType.DEPARTMENT:
        return 'ri-building-line'
      case RoomType.BROADCAST:
        return 'ri-broadcast-line'
      default:
        return 'ri-chat-3-line'
    }
  }

  const getRoomDisplayName = (room: any) => {
    // Handle Telegram rooms
    if (room.platform === 'telegram') {
      return room.telegram_contact?.display_name || room.room_name || 'Telegram User'
    }

    if (room.room_type === RoomType.DIRECT && room.participants && session?.user?.id) {
      // For direct messages, show the other participant's name
      const otherParticipant = room.participants.find((p: any) => p.user_uuid !== session.user!.id)

      return otherParticipant?.user_name || room.room_name || 'Direct Message'
    }

    return room.room_name || 'Unnamed Room'
  }

  const getLastMessagePreview = (room: any) => {
    // Handle Telegram rooms
    if (room.platform === 'telegram') {
      return 'Telegram conversation'
    }

    // Debug logging to understand where room data is coming from
    console.log(`getLastMessagePreview for room ${room.room_uuid}:`, {
      hasLastMessage: !!room.last_message,
      lastMessage: room.last_message,
      roomName: room.room_name,
      updateDate: room.update_date,
      roomSource: 'investigating...'
    })

    if (!room.last_message) {
      return ''
    }

    const message = room.last_message

    // Check if message is deleted (using the is_deleted flag from API)
    if ((message as any).is_deleted) {
      return 'Message deleted'
    }

    // Handle different message types
    switch (message.message_type) {
      case 1:
        return '📷 Image'
      case 2:
        return '📎 File'
      case 3:
        return '🔔 System message'
      default:
        return MessageUtils.truncateContent(message.content || '', 50)
    }
  }

  // Check if user is online (currently unused but kept for future use)
  // const isUserOnline = (userId: string) => {
  //   return onlineUsers.includes(userId)
  // }

  // Delete handlers
  const handleDeleteRequest = (roomId: string) => {
    const room = rooms[roomId]

    if (room) {
      setRoomToDelete(room)
      setDeleteDialogOpen(true)
      setOpenDropdownId(null) // Close dropdown when opening delete dialog
    }
  }

  const toggleDropdown = (roomUuid: string, event: React.MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()

    // If clicking the same dropdown that's already open, close it
    if (openDropdownId === roomUuid) {
      setOpenDropdownId(null)
    } else {
      setOpenDropdownId(roomUuid)
    }
  }

  const handleDeleteConfirm = async () => {
    if (!roomToDelete) return

    setIsDeleting(true)

    try {
      await dispatch(deleteChatRoom(roomToDelete.room_uuid)).unwrap()
      setDeleteDialogOpen(false)
      setRoomToDelete(null)
    } catch (error) {
      console.error('Failed to delete conversation:', error)

      // Error is handled by Redux state
    } finally {
      setIsDeleting(false)
    }
  }

  const handleDeleteCancel = () => {
    if (!isDeleting) {
      setDeleteDialogOpen(false)
      setRoomToDelete(null)
    }
  }

  // Removed debug refresh handler

  const handleUserSelect = (user: any) => {
    // Create a virtual room ID for the selected user
    const virtualRoomId = `new_chat_${user.user_uuid}`

    // Create virtual room in Redux store
    dispatch(createVirtualRoom({ roomId: virtualRoomId, user }))

    // Select the virtual room
    onRoomSelect(virtualRoomId)
  }

  const handleGroupCreated = (roomId: string) => {
    // Navigate to the newly created group
    onRoomSelect(roomId)
    setCreateGroupDialogOpen(false)
  }

  // Handle Telegram contact selection
  const handleTelegramContactSelect = (contact: any) => {
    if (contact.chat_room) {
      onRoomSelect(contact.chat_room.room_uuid)
    }

    setShowTelegramContacts(false)
  }

  // Handle Facebook contact selection
  const handleFacebookContactSelect = (contact: any) => {
    if (contact.room_uuid) {
      onRoomSelect(contact.room_uuid)
    }

    setShowFacebookContacts(false)
  }

  // Handle Telegram room creation
  const handleTelegramRoomCreate = async (contactUuid: string) => {
    try {
      const response = await fetch('/api/internal-chat/telegram/chat-rooms', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ telegram_contact_uuid: contactUuid })
      })

      if (response.ok) {
        const data = await response.json()

        onRoomSelect(data.internal_room_uuid)

        // Room will be automatically included in the main room list
      }
    } catch (error) {
      console.error('Error creating Telegram room:', error)
    }

    setShowTelegramContacts(false)
  }

  // Handle Zalo contact selection
  const handleZaloContactSelect = (contact: any) => {
    if (contact.chat_room) {
      onRoomSelect(contact.chat_room.internal_room_uuid)
    }

    setShowZaloContacts(false)
  }

  // Handle Zalo room creation
  const handleZaloRoomCreate = async (contactUuid: string) => {
    try {
      const response = await fetch('/api/internal-chat/zalo/contacts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ contact_uuid: contactUuid })
      })

      if (response.ok) {
        const data = await response.json()

        onRoomSelect(data.room_uuid)

        // Room will be automatically included in the main room list
      }
    } catch (error) {
      console.error('Error creating Zalo room:', error)
    }

    setShowZaloContacts(false)
  }

  // Handle Facebook room creation
  const handleFacebookRoomCreate = async (contactUuid: string) => {
    try {
      const response = await fetch('/api/internal-chat/facebook/contacts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ contact_uuid: contactUuid })
      })

      if (response.ok) {
        const data = await response.json()

        onRoomSelect(data.room_uuid)

        // Room will be automatically included in the main room list
      }
    } catch (error) {
      console.error('Error creating Facebook room:', error)
    }

    setShowFacebookContacts(false)
  }

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Header */}
      <div className='p-3 border-b'>
        <div className='flex items-center justify-between mb-3'>
          <h2 className={`font-semibold text-gray-800 ${compact ? 'text-sm' : 'text-lg'}`}>Messages</h2>
          {/* Create Group Button */}
          <button
            onClick={() => setCreateGroupDialogOpen(true)}
            className='flex items-center space-x-1 px-2 py-1 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-xs'
            title='Create Group'
          >
            <i className='ri-group-line' />
            {!compact && <span>Group</span>}
          </button>
          {/* Removed debug refresh button */}
        </div>

        {/* User Search - Now includes social contacts */}
        <div className='space-y-2'>
          <UserSearch
            placeholder='Search users and contacts...'
            onUserSelect={handleUserSelect}
            onTelegramContactSelect={handleTelegramContactSelect}
            onZaloContactSelect={handleZaloContactSelect}
          />
        </div>

        {/* Filter tabs */}
        {!compact && (
          <div className='flex space-x-1 mt-2'>
            {[
              { key: 'all', label: 'All', icon: 'ri-chat-3-line' },
              { key: RoomType.DIRECT, label: 'Direct', icon: 'ri-user-line' },
              { key: RoomType.GROUP, label: 'Groups', icon: 'ri-group-line' },

              // { key: RoomType.DEPARTMENT, label: 'Teams', icon: 'ri-building-line' },
              { key: 'social', label: 'Social', icon: 'ri-global-line' }
            ].map(filter => (
              <button
                key={filter.key}
                onClick={() => setSelectedFilter(filter.key as any)}
                className={`flex items-center space-x-1 px-2 py-1 rounded text-xs transition-colors ${
                  selectedFilter === filter.key ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                <i className={filter.icon} />
                <span>{filter.label}</span>
              </button>
            ))}
          </div>
        )}

        {/* Social sub-filters */}
        {!compact && selectedFilter === 'social' && (
          <div className='flex space-x-1 mt-2 ml-2'>
            {[
              { key: 'all', label: 'All Social', icon: 'ri-global-line' },
              { key: 'telegram', label: 'Telegram', icon: 'ri-telegram-line' },
              { key: 'zalo', label: 'ZALO', icon: 'ri-message-line' },
              { key: 'facebook', label: 'Facebook', icon: 'ri-facebook-line' }
            ].map(subFilter => (
              <button
                key={subFilter.key}
                onClick={() => setSocialSubFilter(subFilter.key as any)}
                className={`flex items-center space-x-1 px-2 py-1 rounded text-xs transition-colors ${
                  socialSubFilter === subFilter.key ? 'bg-green-100 text-green-700' : 'text-gray-500 hover:bg-gray-100'
                }`}
              >
                <i className={subFilter.icon} />
                <span>{subFilter.label}</span>
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Room List */}
      <div className='flex-1 overflow-y-auto'>
        {roomsLoading ? (
          <div className={`flex items-center justify-center ${compact ? 'h-24' : 'h-32'}`}>
            <div
              className={`animate-spin rounded-full ${compact ? 'h-4 w-4' : 'h-6 w-6'} border-b-2 border-blue-600`}
            />
          </div>
        ) : filteredRooms.length === 0 ? (
          <div className={`flex flex-col items-center justify-center ${compact ? 'h-24' : 'h-32'} text-gray-500`}>
            <i
              className={`${selectedFilter === 'social' ? 'ri-global-line' : 'ri-chat-3-line'} ${compact ? 'text-xl' : 'text-2xl'} mb-2`}
            />
            <p className={`${compact ? 'text-xs' : 'text-sm'} mb-2`}>
              {selectedFilter === 'social' ? 'No social conversations' : 'No conversations found'}
            </p>
            {selectedFilter === 'social' && socialSubFilter === 'telegram' && (
              <button
                onClick={() => setShowTelegramContacts(true)}
                className='bg-inherit text-blue-600 hover:text-blue-700 text-sm'
              >
                Browse Telegram contacts
              </button>
            )}
            {selectedFilter === 'social' && socialSubFilter === 'zalo' && (
              <button
                onClick={() => setShowZaloContacts(true)}
                className='bg-inherit text-green-600 hover:text-green-700 text-sm'
              >
                Browse ZALO contacts
              </button>
            )}
            {selectedFilter === 'social' && socialSubFilter === 'facebook' && (
              <button
                onClick={() => setShowFacebookContacts(true)}
                className='bg-inherit text-blue-600 hover:text-blue-700 text-sm'
              >
                Browse Facebook contacts
              </button>
            )}
            {selectedFilter === 'social' && socialSubFilter === 'all' && (
              <div className='space-y-2'>
                <button
                  onClick={() => setShowTelegramContacts(true)}
                  className='bg-inherit block text-blue-600 hover:text-blue-700 text-sm'
                >
                  Browse Telegram contacts
                </button>
                <button
                  onClick={() => setShowZaloContacts(true)}
                  className='bg-inherit block text-green-600 hover:text-green-700 text-sm'
                >
                  Browse ZALO contacts
                </button>
                <button
                  onClick={() => setShowFacebookContacts(true)}
                  className='bg-inherit block text-blue-600 hover:text-blue-700 text-sm'
                >
                  Browse Facebook contacts
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className={`${compact ? 'space-y-0.5 p-1' : 'space-y-1 p-2'}`}>
            {filteredRooms.map(room => {
              const unreadCount = unreadCounts[room.room_uuid] || 0
              const displayName = getRoomDisplayName(room)
              const lastMessagePreview = getLastMessagePreview(room)

              const lastMessageTime = room.last_message
                ? MessageUtils.getRelativeTime(parseInt(room.last_message.created_at.toString()))
                : MessageUtils.getRelativeTime(Math.floor(new Date(room.insert_date).getTime() / 1000))

              return (
                <div
                  key={room.room_uuid}
                  className={`w-full ${
                    compact ? 'p-2' : 'p-3'
                  } rounded-lg transition-colors hover:bg-gray-50 dark:hover:bg-gray-700 group relative cursor-pointer`}
                  onClick={() => onRoomSelect(room.room_uuid)}
                >
                  <div className={`flex items-start ${compact ? 'space-x-2' : 'space-x-3'} relative`}>
                    {/* Room icon/avatar */}
                    <div
                      className={`flex-shrink-0 ${
                        compact ? 'w-8 h-8' : 'w-10 h-10'
                      } rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center`}
                    >
                      {room.room_avatar ? (
                        <img
                          src={room.room_avatar}
                          alt={displayName}
                          className='w-full h-full rounded-full object-cover'
                        />
                      ) : (
                        <i
                          className={`${getRoomIcon(room)} text-gray-600 dark:text-gray-300 ${compact ? 'text-sm' : ''}`}
                        />
                      )}
                    </div>

                    {/* Room info */}
                    <div className='flex-1 min-w-0'>
                      <div className='flex items-center justify-between'>
                        <h3
                          className={`font-medium text-gray-900 dark:text-gray-100 truncate ${compact ? 'text-sm' : ''}`}
                        >
                          {displayName}
                        </h3>
                        <div className={`flex items-center ${compact ? 'space-x-0.5' : 'space-x-1'}`}>
                          {unreadCount > 0 && (
                            <span
                              className={`bg-blue-600 text-white rounded-full text-center ${
                                compact ? 'text-xs px-1.5 py-0.5 min-w-[16px]' : 'text-xs px-2 py-0.5 min-w-[20px]'
                              }`}
                            >
                              {unreadCount > 99 ? '99+' : unreadCount}
                            </span>
                          )}
                          <span className={`text-gray-500 dark:text-gray-400 ${compact ? 'text-xs' : 'text-sm'}`}>
                            {lastMessageTime}
                          </span>
                        </div>
                      </div>

                      {!compact && (
                        <p className='text-gray-600 dark:text-gray-300 truncate mt-1 text-sm'>{lastMessagePreview}</p>
                      )}

                      {/* Online status for direct messages */}
                      {/* {room.room_type === RoomType.DIRECT && room.participants && (
                        <div className='flex items-center mt-1'>
                          {room.participants.map(participant => {
                            if (participant.user_uuid === room.created_by_user_uuid) return null
                            const isOnline = isUserOnline(participant.user_uuid)

                            return (
                              <div key={participant.user_uuid} className='flex items-center space-x-1'>
                                <div className={`w-2 h-2 rounded-full ${isOnline ? 'bg-green-400' : 'bg-gray-300'}`} />
                                <span className='text-xs text-gray-500 dark:text-gray-400'>{isOnline ? 'Online' : 'Offline'}</span>
                              </div>
                            )
                          })}
                        </div>
                      )} */}
                    </div>

                    {/* Action dropdown - positioned absolutely to avoid interfering with click */}
                    <div
                      className='relative z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-150'
                      ref={dropdownRef}
                    >
                      <button
                        onClick={e => toggleDropdown(room.room_uuid, e)}
                        className={`${compact ? 'w-6 h-6' : 'w-8 h-8'} rounded-full flex items-center justify-center hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors`}
                        title='More actions'
                        data-dropdown-trigger='true'
                      >
                        <i
                          className={`ri-more-2-fill text-gray-500 dark:text-gray-400 ${compact ? 'text-sm' : 'text-base'}`}
                        />
                      </button>

                      {/* Dropdown menu */}
                      {openDropdownId === room.room_uuid && (
                        <div
                          className='absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg py-1 z-50'
                          onClick={e => e.stopPropagation()}
                        >
                          <button
                            onMouseDown={e => {
                              e.preventDefault()
                              e.stopPropagation()

                              // Close dropdown and trigger delete immediately
                              setOpenDropdownId(null)
                              handleDeleteRequest(room.room_uuid)
                            }}
                            className='w-full px-4 py-2 text-left text-sm text-red-600 dark:text-red-400 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2 cursor-pointer'
                          >
                            <i className='ri-delete-bin-line' />
                            Delete Conversation
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </div>

      {/* Delete Conversation Dialog */}
      <DeleteConversationDialog
        isOpen={deleteDialogOpen}
        room={roomToDelete}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
        isDeleting={isDeleting}
      />

      {/* Create Group Dialog */}
      <SimpleCreateGroupDialog
        isOpen={createGroupDialogOpen}
        onClose={() => setCreateGroupDialogOpen(false)}
        onGroupCreated={handleGroupCreated}
      />

      {/* Telegram Contact Selector Modal */}
      {showTelegramContacts && (
        <div className='fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4'>
          <div className='bg-white rounded-lg w-full max-w-md h-96 flex flex-col'>
            <div className='p-4 border-b flex items-center justify-between'>
              <h3 className='text-lg font-semibold'>Select Telegram Contact</h3>
              <button onClick={() => setShowTelegramContacts(false)} className='text-gray-400 hover:text-gray-600'>
                <i className='ri-close-line text-xl' />
              </button>
            </div>
            <div className='flex-1 overflow-hidden'>
              <TelegramContactSelector
                onContactSelect={handleTelegramContactSelect}
                onCreateRoom={handleTelegramRoomCreate}
                className='h-full'
              />
            </div>
          </div>
        </div>
      )}

      {/* Zalo Contact Selector Modal */}
      {showZaloContacts && (
        <div className='fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4'>
          <div className='bg-white rounded-lg w-full max-w-md h-96 flex flex-col'>
            <div className='p-4 border-b flex items-center justify-between'>
              <h3 className='text-lg font-semibold'>Select ZALO Contact</h3>
              <button onClick={() => setShowZaloContacts(false)} className='text-gray-400 hover:text-gray-600'>
                <i className='ri-close-line text-xl' />
              </button>
            </div>
            <div className='flex-1 overflow-hidden'>
              <ZaloContactSelector
                onContactSelect={handleZaloContactSelect}
                onCreateRoom={handleZaloRoomCreate}
                className='h-full'
              />
            </div>
          </div>
        </div>
      )}

      {/* Facebook Contact Selector Modal */}
      {showFacebookContacts && (
        <div className='fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4'>
          <div className='bg-white rounded-lg w-full max-w-md h-96 flex flex-col'>
            <div className='p-4 border-b flex items-center justify-between'>
              <h3 className='text-lg font-semibold'>Select Facebook Contact</h3>
              <button onClick={() => setShowFacebookContacts(false)} className='text-gray-400 hover:text-gray-600'>
                <i className='ri-close-line text-xl' />
              </button>
            </div>
            <div className='flex-1 overflow-hidden'>
              <FacebookContactSelector
                onContactSelect={handleFacebookContactSelect}
                onCreateRoom={handleFacebookRoomCreate}
                className='h-full'
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ChatRoomList
