// Message List Component
// Displays chat messages with virtual scrolling and lazy loading

'use client'

import React, { useEffect, useRef, useState, useMemo } from 'react'

import { useAppDispatch, useAppSelector } from '@/redux-store/hooks'
import { fetchRoomMessages, markMessagesAsRead } from '@/redux-store/slices/internal-chat/internalChatSlice'
import type { ChatMessage } from '@/types/apps/internal-chat/chatTypes'
import { MessageUtils } from '@/utils/chat/messageUtils'

interface MessageListProps {
  roomId: string
  compact?: boolean
  className?: string
}

const MessageList: React.FC<MessageListProps> = ({ roomId, compact = false, className = '' }) => {
  const dispatch = useAppDispatch()

  const { messages, messagesLoading, hasMoreMessages, currentUser, unreadCounts } = useAppSelector(
    state => state.internalChatReducer
  )

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const messagesContainerRef = useRef<HTMLDivElement>(null)
  const [isLoadingMore, setIsLoadingMore] = useState(false)

  const roomMessages = useMemo(() => messages[roomId] || [], [messages, roomId])
  const isLoading = messagesLoading[roomId] || false
  const hasMore = hasMoreMessages[roomId] || false

  // Mark messages as read when room becomes active and has unread messages
  useEffect(() => {
    if (roomId && unreadCounts[roomId] > 0) {
      // Get the latest message ID to mark as read
      const latestMessage = roomMessages[roomMessages.length - 1]
      const lastReadMessageId = latestMessage?.message_id

      dispatch(
        markMessagesAsRead({
          roomId,
          lastReadMessageId
        })
      )
    }
  }, [roomId, roomMessages, unreadCounts, dispatch])

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current && roomMessages.length > 0) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }, [roomMessages.length])

  // Load more messages when scrolling to top
  const handleScroll = async () => {
    const container = messagesContainerRef.current

    if (!container || isLoadingMore || !hasMore) return

    if (container.scrollTop === 0) {
      setIsLoadingMore(true)
      const oldestMessage = roomMessages[0]

      if (oldestMessage) {
        await dispatch(
          fetchRoomMessages({
            roomId,
            before: oldestMessage.message_id,
            limit: 50
          })
        )
      }

      setIsLoadingMore(false)
    }
  }

  const renderMessage = (message: ChatMessage, index: number) => {
    const isOwn = message.author_uuid === currentUser?.id
    const previousMessage = index > 0 ? roomMessages[index - 1] : null
    const shouldGroup = MessageUtils.shouldGroupWithPrevious(message, previousMessage)

    const showDateSeparator =
      !previousMessage ||
      !MessageUtils.isFromToday(previousMessage.created_at) !== !MessageUtils.isFromToday(message.created_at)

    return (
      <div key={message.message_id}>
        {/* Date separator */}
        {showDateSeparator && (
          <div className='flex items-center justify-center my-4'>
            <div className='bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full'>
              {MessageUtils.getDateSeparatorText(message.created_at)}
            </div>
          </div>
        )}

        {/* Message */}
        <div className={`flex ${isOwn ? 'justify-end' : 'justify-start'} ${shouldGroup ? 'mt-1' : 'mt-4'}`}>
          <div className={`flex ${isOwn ? 'flex-row-reverse' : 'flex-row'} items-end space-x-2 max-w-[80%]`}>
            {/* Avatar - only show for non-grouped messages from others */}
            {!shouldGroup && !isOwn && (
              <div
                className={`flex-shrink-0 ${compact ? 'w-6 h-6' : 'w-8 h-8'} rounded-full bg-gray-300 flex items-center justify-center`}
              >
                <span className={`text-white font-medium ${compact ? 'text-xs' : 'text-sm'}`}>
                  {message.author_name?.charAt(0).toUpperCase() || '?'}
                </span>
              </div>
            )}

            {/* Message bubble */}
            <div className={`${shouldGroup && !isOwn ? (compact ? 'ml-8' : 'ml-10') : ''}`}>
              {/* Author name - only show for non-grouped messages from others */}
              {!shouldGroup && !isOwn && message.author_name && (
                <div className={`text-gray-600 mb-1 ${compact ? 'text-xs' : 'text-sm'}`}>{message.author_name}</div>
              )}

              {/* Message content */}
              <div
                className={`rounded-lg px-3 py-2 ${
                  isOwn ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-900'
                } ${compact ? 'text-sm' : ''}`}
              >
                {/* Reply indicator */}
                {message.reply_parent && (
                  <div className={`border-l-2 pl-2 mb-2 opacity-75 ${isOwn ? 'border-blue-300' : 'border-gray-400'}`}>
                    <div className='text-xs font-medium'>{message.reply_parent.author_name}</div>
                    <div className='text-xs truncate'>
                      {MessageUtils.truncateContent(message.reply_parent.content || '', 50)}
                    </div>
                  </div>
                )}

                {/* Message text */}
                {message.content && (
                  <div
                    className='break-words'
                    dangerouslySetInnerHTML={{
                      __html: MessageUtils.formatMessageContent(message.content)
                    }}
                  />
                )}

                {/* Attachments */}
                {message.attachments && message.attachments.length > 0 && (
                  <div className='mt-2 space-y-2'>
                    {message.attachments.map(attachment => (
                      <div key={attachment.attachment_id} className='flex items-center space-x-2'>
                        <i className={`${MessageUtils.getFileIcon(attachment.content_type || '')} text-lg`} />
                        <div className='flex-1 min-w-0'>
                          <div className='text-sm font-medium truncate'>{attachment.filename}</div>
                          <div className='text-xs opacity-75'>
                            {MessageUtils.formatFileSize(attachment.file_size || 0)}
                          </div>
                        </div>
                        <button className='p-1 hover:bg-black hover:bg-opacity-10 rounded'>
                          <i className='ri-download-line text-sm' />
                        </button>
                      </div>
                    ))}
                  </div>
                )}

                {/* Message status */}
                {isOwn && (
                  <div className='flex items-center justify-end mt-1 space-x-1'>
                    <span className='text-xs opacity-75'>{MessageUtils.getRelativeTime(message.created_at)}</span>
                    {message.is_edited && <span className='text-xs opacity-75'>(edited)</span>}
                    <i
                      className={`text-xs ${
                        MessageUtils.getMessageStatus(message) === 'sent' ? 'ri-check-double-line' : 'ri-check-line'
                      }`}
                    />
                  </div>
                )}
              </div>

              {/* Reactions */}
              {message.reactions && message.reactions.length > 0 && (
                <div className='flex flex-wrap gap-1 mt-1'>
                  {Object.entries(MessageUtils.getReactionSummary(message.reactions)).map(([emoji, data]) => (
                    <button
                      key={emoji}
                      className='flex items-center space-x-1 bg-gray-100 hover:bg-gray-200 rounded-full px-2 py-1 text-xs transition-colors'
                    >
                      <span>{emoji}</span>
                      <span>{data.count}</span>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (isLoading && roomMessages.length === 0) {
    return (
      <div className='flex items-center justify-center h-full'>
        <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600' />
      </div>
    )
  }

  return (
    <div
      ref={messagesContainerRef}
      className={`flex-1 overflow-y-auto h-full ${compact ? 'p-3' : 'p-4'} ${className}`}
      onScroll={handleScroll}
    >
      {/* Load more indicator */}
      {isLoadingMore && (
        <div className='flex justify-center py-2'>
          <div className={`animate-spin rounded-full ${compact ? 'h-3 w-3' : 'h-4 w-4'} border-b-2 border-blue-600`} />
        </div>
      )}

      {/* Messages */}
      {roomMessages.length === 0 ? (
        <div className='flex flex-col items-center justify-center h-full text-gray-500'>
          <i className={`ri-chat-3-line ${compact ? 'text-3xl' : 'text-4xl'} mb-4`} />
          <p className={`${compact ? 'text-base' : 'text-lg'} font-medium mb-2`}>No messages yet</p>
          <p className={`${compact ? 'text-xs' : 'text-sm'}`}>Start the conversation!</p>
        </div>
      ) : (
        <div className={compact ? 'space-y-0.5' : 'space-y-1'}>
          {roomMessages
            .filter((message: ChatMessage, index: number, array: ChatMessage[]) => {
              // Additional client-side deduplication filter
              // Remove any messages with duplicate IDs (shouldn't happen but safety check)
              return array.findIndex((m: ChatMessage) => m.message_id === message.message_id) === index
            })
            .map((message: ChatMessage, index: number) => renderMessage(message, index))}
        </div>
      )}

      {/* Scroll anchor */}
      <div ref={messagesEndRef} />
    </div>
  )
}

export default MessageList
