// Chat Header Component
// Header for chat room with room info and actions

'use client'

import React, { useState } from 'react'

import { useSession } from 'next-auth/react'

import type { ChatRoom } from '@/types/apps/internal-chat/chatTypes'
import { RoomType } from '@/types/apps/internal-chat/chatTypes'
import { useAppSelector } from '@/redux-store/hooks'
import GroupMemberList from './GroupMemberList'

interface ChatHeaderProps {
  room: ChatRoom
  onBack?: () => void
  compact?: boolean
  className?: string
}

const ChatHeader: React.FC<ChatHeaderProps> = ({ room, onBack, compact = false, className = '' }) => {
  const { data: session } = useSession()
  const { onlineUsers } = useAppSelector(state => state.internalChatReducer)
  const [showGroupMembers, setShowGroupMembers] = useState(false)

  const getRoomIcon = (roomType: RoomType) => {
    switch (roomType) {
      case RoomType.DIRECT:
        return 'ri-user-line'
      case RoomType.GROUP:
        return 'ri-group-line'
      case RoomType.DEPARTMENT:
        return 'ri-building-line'
      case RoomType.BROADCAST:
        return 'ri-broadcast-line'
      default:
        return 'ri-chat-3-line'
    }
  }

  const getRoomDisplayName = () => {
    if (room.room_type === RoomType.DIRECT && room.participants && session?.user?.id) {
      // For direct messages, show the other participant's name
      const otherParticipant = room.participants.find(p => p.user_uuid !== session.user!.id)

      return otherParticipant?.user_name || room.room_name || 'Direct Message'
    }

    return room.room_name || 'Unnamed Room'
  }

  const getOnlineCount = () => {
    if (!room.participants) return 0

    return room.participants.filter(p => onlineUsers.includes(p.user_uuid)).length
  }

  const getStatusText = () => {
    if (room.room_type === RoomType.DIRECT && room.participants && session?.user?.id) {
      const otherParticipant = room.participants.find(p => p.user_uuid !== session.user!.id)

      if (otherParticipant && onlineUsers.includes(otherParticipant.user_uuid)) {
        return 'Online'
      }

      return 'Offline'
    }

    const onlineCount = getOnlineCount()
    const totalCount = room.participant_count || 0

    if (room.room_type === RoomType.GROUP) {
      return `${totalCount} member${totalCount !== 1 ? 's' : ''} • ${onlineCount} online`
    }

    return `${onlineCount}/${totalCount} online`
  }

  const handleInfoClick = () => {
    if (room.room_type === RoomType.GROUP) {
      setShowGroupMembers(true)
    }

    // For other room types, could show different info panels
  }

  return (
    <div className={`flex items-center justify-between p-3 border-b bg-white ${className}`}>
      <div className='flex items-center space-x-3 flex-1 min-w-0'>
        {/* Back button for compact mode */}
        {compact && onBack && (
          <button onClick={onBack} className='p-1 hover:bg-gray-100 rounded transition-colors'>
            <i className='ri-arrow-left-line text-gray-600' />
          </button>
        )}

        {/* Room avatar/icon */}
        <div
          className={`flex-shrink-0 ${compact ? 'w-8 h-8' : 'w-10 h-10'} rounded-full bg-gray-200 flex items-center justify-center`}
        >
          {room.room_avatar ? (
            <img
              src={room.room_avatar}
              alt={getRoomDisplayName()}
              className='w-full h-full rounded-full object-cover'
            />
          ) : (
            <i className={`${getRoomIcon(room.room_type)} text-gray-600 ${compact ? 'text-sm' : ''}`} />
          )}
        </div>

        {/* Room info */}
        <div className='flex-1 min-w-0'>
          <h3 className={`font-semibold text-gray-900 truncate ${compact ? 'text-sm' : ''}`}>{getRoomDisplayName()}</h3>
          <p className={`text-gray-500 truncate ${compact ? 'text-xs' : 'text-sm'}`}>{getStatusText()}</p>
        </div>
      </div>

      {/* Actions */}
      <div className='flex items-center space-x-1 relative'>
        {!compact && (
          <>
            {/* Video call button */}
            <button className='p-2 hover:bg-gray-100 rounded transition-colors' title='Start video call'>
              <i className='ri-video-line text-gray-600' />
            </button>

            {/* Voice call button */}
            <button className='p-2 hover:bg-gray-100 rounded transition-colors' title='Start voice call'>
              <i className='ri-phone-line text-gray-600' />
            </button>

            {/* Room info button */}
            <button
              onClick={handleInfoClick}
              className='p-2 hover:bg-gray-100 rounded transition-colors'
              title={room.room_type === RoomType.GROUP ? 'Group members' : 'Room information'}
            >
              <i
                className={
                  room.room_type === RoomType.GROUP
                    ? 'ri-group-line text-gray-600'
                    : 'ri-information-line text-gray-600'
                }
              />
            </button>
          </>
        )}

        {/* More options */}
        <button className='p-2 hover:bg-gray-100 rounded transition-colors' title='More options'>
          <i className='ri-more-2-line text-gray-600' />
        </button>

        {/* Group Member List */}
        {room.room_type === RoomType.GROUP && (
          <GroupMemberList room={room} isOpen={showGroupMembers} onClose={() => setShowGroupMembers(false)} />
        )}
      </div>
    </div>
  )
}

export default ChatHeader
