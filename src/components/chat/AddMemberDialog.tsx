// Simple Add Member Dialog
// Add members to existing group

'use client'

import React, { useState, useEffect, useCallback } from 'react'

import { useSession } from 'next-auth/react'

import type { DomainUser } from '@/types/apps/internal-chat/chatTypes'

interface AddMemberDialogProps {
  isOpen: boolean
  onClose: () => void
  roomId: string
  onMemberAdded?: () => void
}

const AddMemberDialog: React.FC<AddMemberDialogProps> = ({ isOpen, onClose, roomId, onMemberAdded }) => {
  const { data: session } = useSession()

  const [isAdding, setIsAdding] = useState(false)
  const [selectedMembers, setSelectedMembers] = useState<DomainUser[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<DomainUser[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [existingMembers, setExistingMembers] = useState<string[]>([])

  // Fetch existing members to filter them out from search
  const fetchExistingMembers = useCallback(async () => {
    try {
      const response = await fetch(`/api/internal-chat/rooms/${roomId}/members`)
      const result = await response.json()

      if (result.success) {
        setExistingMembers(result.data.members.map((m: any) => m.user_uuid))
      }
    } catch (error) {
      console.error('Error fetching existing members:', error)
    }
  }, [roomId])

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (isOpen) {
      setSelectedMembers([])
      setSearchQuery('')
      setSearchResults([])
      fetchExistingMembers()
    }
  }, [isOpen, fetchExistingMembers])

  const searchUsers = useCallback(
    async (query: string) => {
      setIsSearching(true)

      try {
        const response = await fetch(`/api/internal-chat/users/search?q=${encodeURIComponent(query)}&limit=20`)
        const result = await response.json()

        if (result.success) {
          // Filter out already selected users, current user, and existing members
          const filteredUsers = result.data.filter(
            (user: DomainUser) =>
              user.user_uuid !== session?.user?.id &&
              !selectedMembers.some(member => member.user_uuid === user.user_uuid) &&
              !existingMembers.includes(user.user_uuid)
          )

          setSearchResults(filteredUsers)
        }
      } catch (error) {
        console.error('Error searching users:', error)
        setSearchResults([])
      } finally {
        setIsSearching(false)
      }
    },
    [session?.user?.id, selectedMembers, existingMembers]
  )

  // Search users with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchQuery.length >= 2) {
        searchUsers(searchQuery)
      } else {
        setSearchResults([])
      }
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [searchQuery, searchUsers])

  const addMember = (user: DomainUser) => {
    setSelectedMembers(prev => [...prev, user])
    setSearchQuery('')
    setSearchResults([])
  }

  const removeMember = (userUuid: string) => {
    setSelectedMembers(prev => prev.filter(member => member.user_uuid !== userUuid))
  }

  const handleAddMembers = async () => {
    if (!session?.user?.id || selectedMembers.length === 0) return

    setIsAdding(true)

    try {
      const response = await fetch(`/api/internal-chat/rooms/${roomId}/members`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          user_uuids: selectedMembers.map(member => member.user_uuid),
          role: 'member',
          send_notification: true
        })
      })

      const result = await response.json()

      if (result.success) {
        const addedCount = result.data.added_members?.length || 0
        const skippedCount = result.data.skipped_existing?.length || 0

        if (addedCount > 0) {
          // Show success message
          console.log(`Successfully added ${addedCount} member${addedCount !== 1 ? 's' : ''}`)
        }

        if (skippedCount > 0) {
          console.log(`${skippedCount} user${skippedCount !== 1 ? 's were' : ' was'} already in the group`)
        }

        onMemberAdded?.()
        onClose()
      } else {
        alert(result.error || 'Failed to add members')
      }
    } catch (error) {
      console.error('Error adding members:', error)
      alert('Failed to add members. Please try again.')
    } finally {
      setIsAdding(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
      <div className='bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-hidden'>
        {/* Header */}
        <div className='flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700'>
          <h2 className='text-lg font-semibold text-gray-900 dark:text-white'>Add Members</h2>
          <button
            onClick={onClose}
            className='text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
            disabled={isAdding}
          >
            <i className='ri-close-line text-xl' />
          </button>
        </div>

        {/* Content */}
        <div className='p-4 space-y-4 max-h-[60vh] overflow-y-auto'>
          {/* Search Users */}
          <div>
            <label className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>Search Users</label>
            <div className='relative'>
              <input
                type='text'
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                placeholder='Search users to add...'
                className='w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white'
              />
              <div className='absolute left-3 top-2.5'>
                {isSearching ? (
                  <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600' />
                ) : (
                  <i className='ri-search-line text-gray-400' />
                )}
              </div>
            </div>
          </div>

          {/* Search Results */}
          {searchResults.length > 0 && (
            <div className='border border-gray-200 dark:border-gray-600 rounded-lg max-h-32 overflow-y-auto'>
              {searchResults.map(user => (
                <div
                  key={user.user_uuid}
                  className='flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-100 dark:border-gray-600 last:border-b-0'
                >
                  <div className='flex items-center space-x-2'>
                    <div className='w-6 h-6 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center'>
                      <i className='ri-user-line text-xs text-gray-500' />
                    </div>
                    <div>
                      <p className='text-sm font-medium text-gray-900 dark:text-white'>{user.username}</p>
                    </div>
                  </div>
                  <button
                    onClick={() => addMember(user)}
                    className='px-2 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700'
                  >
                    Add
                  </button>
                </div>
              ))}
            </div>
          )}

          {/* Selected Members */}
          {selectedMembers.length > 0 && (
            <div>
              <h4 className='text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>
                Selected Members ({selectedMembers.length})
              </h4>
              <div className='space-y-1 max-h-32 overflow-y-auto'>
                {selectedMembers.map(member => (
                  <div
                    key={member.user_uuid}
                    className='flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded'
                  >
                    <div className='flex items-center space-x-2'>
                      <div className='w-6 h-6 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center'>
                        <i className='ri-user-line text-xs text-gray-500' />
                      </div>
                      <p className='text-sm text-gray-900 dark:text-white'>{member.username}</p>
                    </div>
                    <button
                      onClick={() => removeMember(member.user_uuid)}
                      className='text-red-500 hover:text-red-700 p-1'
                    >
                      <i className='ri-close-line text-sm' />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className='flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-700'>
          <button
            onClick={onClose}
            className='px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
            disabled={isAdding}
          >
            Cancel
          </button>

          <button
            onClick={handleAddMembers}
            disabled={selectedMembers.length === 0 || isAdding}
            className='px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2'
          >
            {isAdding && <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white' />}
            <span>
              {isAdding
                ? 'Adding...'
                : `Add ${selectedMembers.length} Member${selectedMembers.length !== 1 ? 's' : ''}`}
            </span>
          </button>
        </div>
      </div>
    </div>
  )
}

export default AddMemberDialog
