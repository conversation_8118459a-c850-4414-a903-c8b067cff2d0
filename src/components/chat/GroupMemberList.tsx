// Simple Group Member List Component
// Shows group members in a simple dropdown

'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'

import { useSession } from 'next-auth/react'

import type { ChatRoom } from '@/types/apps/internal-chat/chatTypes'
import { ParticipantRole } from '@/types/apps/internal-chat/chatTypes'
import AddMemberDialog from './AddMemberDialog'

interface GroupMember {
  participant_uuid: string
  user_uuid: string
  username: string
  user_email: string
  participant_role: ParticipantRole
  joined_date: string
  presence?: {
    status: 'online' | 'away' | 'busy' | 'offline'
    last_seen: string | null
  }
}

interface GroupMemberListProps {
  room: ChatRoom
  isOpen: boolean
  onClose: () => void
}

const GroupMemberList: React.FC<GroupMemberListProps> = ({ room, isOpen, onClose }) => {
  const { data: session } = useSession()
  const [members, setMembers] = useState<GroupMember[]>([])
  const [loading, setLoading] = useState(false)
  const [currentUserRole, setCurrentUserRole] = useState<ParticipantRole>(ParticipantRole.MEMBER)
  const [showAddMember, setShowAddMember] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen, onClose])

  const fetchGroupMembers = useCallback(async () => {
    setLoading(true)

    try {
      const response = await fetch(`/api/internal-chat/rooms/${room.room_uuid}/members`)
      const result = await response.json()

      if (result.success) {
        setMembers(result.data.members)
        setCurrentUserRole(result.data.current_user_role)
      }
    } catch (error) {
      console.error('Error fetching group members:', error)
    } finally {
      setLoading(false)
    }
  }, [room.room_uuid])

  // Fetch group members
  useEffect(() => {
    if (isOpen && room.room_type === 'group') {
      fetchGroupMembers()
    }
  }, [isOpen, room.room_type, fetchGroupMembers])

  const [memberToRemove, setMemberToRemove] = useState<{ userId: string; username: string } | null>(null)
  const [isRemoving, setIsRemoving] = useState(false)
  const [removeError, setRemoveError] = useState<string | null>(null)

  const handleRemoveMember = async (userId: string, username: string) => {
    setMemberToRemove({ userId, username })
  }

  const confirmRemoveMember = async () => {
    if (!memberToRemove) return

    setIsRemoving(true)
    setRemoveError(null)

    try {
      const response = await fetch(`/api/internal-chat/rooms/${room.room_uuid}/members/${memberToRemove.userId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        // Refresh members list
        fetchGroupMembers()
        setMemberToRemove(null)
      } else {
        const errorData = await response.json()

        setRemoveError(errorData.error || 'Failed to remove member')
      }
    } catch (error) {
      console.error('Error removing member:', error)
      setRemoveError('Network error. Please try again.')
    } finally {
      setIsRemoving(false)
    }
  }

  const canRemoveMember = (memberRole: ParticipantRole) => {
    if (currentUserRole === 'owner') return memberRole !== 'owner'
    if (currentUserRole === 'admin') return memberRole === 'member' || memberRole === 'moderator'

    return false
  }

  const getRoleColor = (role: ParticipantRole) => {
    switch (role) {
      case ParticipantRole.OWNER:
        return 'text-purple-600 bg-purple-100'
      case ParticipantRole.ADMIN:
        return 'text-blue-600 bg-blue-100'
      case ParticipantRole.MODERATOR:
        return 'text-green-600 bg-green-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getPresenceColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-500'
      case 'away':
        return 'bg-yellow-500'
      case 'busy':
        return 'bg-red-500'
      default:
        return 'bg-gray-400'
    }
  }

  if (!isOpen) return null

  return (
    <div
      ref={dropdownRef}
      className='absolute right-0 top-full mt-2 w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg py-2 z-50 max-h-96 overflow-y-auto'
    >
      {/* Header */}
      <div className='px-4 py-2 border-b border-gray-200 dark:border-gray-700'>
        <div className='flex items-center justify-between'>
          <h3 className='font-medium text-gray-900 dark:text-white'>Group Members ({members.length})</h3>
          <button onClick={onClose} className='text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'>
            <i className='ri-close-line' />
          </button>
        </div>
      </div>

      {/* Members List */}
      <div className='py-2'>
        {loading ? (
          <div className='flex items-center justify-center py-4'>
            <div className='animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600' />
          </div>
        ) : (
          <div className='space-y-1'>
            {members.map(member => (
              <div
                key={member.participant_uuid}
                className='group flex items-center justify-between px-4 py-2 hover:bg-gray-50 dark:hover:bg-gray-700'
              >
                <div className='flex items-center space-x-3 flex-1 min-w-0'>
                  {/* Avatar with presence indicator */}
                  <div className='relative flex-shrink-0'>
                    <div className='w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center'>
                      <i className='ri-user-line text-sm text-gray-500' />
                    </div>
                    {member.presence && (
                      <div
                        className={`absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 rounded-full border border-white ${getPresenceColor(member.presence.status)}`}
                      />
                    )}
                  </div>

                  <div className='flex-1 min-w-0'>
                    <div className='flex items-center space-x-2'>
                      <p className='text-sm font-medium text-gray-900 dark:text-white truncate'>
                        {member.username}
                        {member.user_uuid === session?.user?.id && (
                          <span className='text-gray-500 text-xs ml-1'>(You)</span>
                        )}
                      </p>
                      <span
                        className={`px-1.5 py-0.5 rounded text-xs font-medium ${getRoleColor(member.participant_role)}`}
                      >
                        {member.participant_role}
                      </span>
                    </div>
                    <p className='text-xs text-gray-500 dark:text-gray-400 truncate'>
                      {member.presence?.status === 'online'
                        ? 'Online'
                        : member.presence?.status === 'away'
                          ? 'Away'
                          : member.presence?.status === 'busy'
                            ? 'Busy'
                            : 'Offline'}
                    </p>
                  </div>
                </div>

                {/* Actions */}
                {canRemoveMember(member.participant_role) && member.user_uuid !== session?.user?.id && (
                  <button
                    onClick={() => handleRemoveMember(member.user_uuid, member.username)}
                    className='text-red-500 hover:text-red-700 p-1 opacity-0 group-hover:opacity-100 transition-opacity'
                    title='Remove member'
                  >
                    <i className='ri-user-unfollow-line text-sm' />
                  </button>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Add Member Button */}
      {(currentUserRole === 'owner' || currentUserRole === 'admin') && (
        <div className='px-4 py-2 border-t border-gray-200 dark:border-gray-700'>
          <button
            onClick={() => setShowAddMember(true)}
            className='w-full flex items-center justify-center space-x-2 px-3 py-2 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors text-sm'
          >
            <i className='ri-user-add-line' />
            <span>Add Member</span>
          </button>
        </div>
      )}

      {/* Add Member Dialog */}
      <AddMemberDialog
        isOpen={showAddMember}
        onClose={() => setShowAddMember(false)}
        roomId={room.room_uuid}
        onMemberAdded={() => {
          fetchGroupMembers()
          setShowAddMember(false)
        }}
      />

      {/* Leave Group */}
      <div className='px-4 py-2 border-t border-gray-200 dark:border-gray-700'>
        <button
          onClick={() => handleRemoveMember(session?.user?.id || '', 'You')}
          className='w-full flex items-center justify-center space-x-2 px-3 py-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors text-sm'
        >
          <i className='ri-logout-box-line' />
          <span>Leave Group</span>
        </button>
      </div>

      {/* Remove Member Confirmation Dialog */}
      {memberToRemove && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
          <div className='bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md overflow-hidden'>
            {/* Header */}
            <div className='flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700'>
              <h2 className='text-lg font-semibold text-gray-900 dark:text-white'>
                {memberToRemove.userId === session?.user?.id ? 'Leave Group' : 'Remove Member'}
              </h2>
              <button
                onClick={() => setMemberToRemove(null)}
                className='text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
                disabled={isRemoving}
              >
                <i className='ri-close-line text-xl' />
              </button>
            </div>

            {/* Content */}
            <div className='p-4'>
              <p className='text-gray-700 dark:text-gray-300 mb-4'>
                {memberToRemove.userId === session?.user?.id ? (
                  <>Are you sure you want to leave this group?</>
                ) : (
                  <>
                    Are you sure you want to remove <span className='font-semibold'>{memberToRemove.username}</span>{' '}
                    from this group?
                  </>
                )}
              </p>

              {removeError && (
                <div className='bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-3 rounded-md mb-4'>
                  {removeError}
                </div>
              )}

              <div className='flex justify-end space-x-3'>
                <button
                  onClick={() => setMemberToRemove(null)}
                  className='px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors'
                  disabled={isRemoving}
                >
                  Cancel
                </button>
                <button
                  onClick={confirmRemoveMember}
                  className='px-4 py-2 text-white bg-red-600 rounded-md hover:bg-red-700 transition-colors flex items-center space-x-2'
                  disabled={isRemoving}
                >
                  {isRemoving ? (
                    <>
                      <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white' />
                      <span>{memberToRemove.userId === session?.user?.id ? 'Leaving...' : 'Removing...'}</span>
                    </>
                  ) : (
                    <span>{memberToRemove.userId === session?.user?.id ? 'Leave' : 'Remove'}</span>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default GroupMemberList
