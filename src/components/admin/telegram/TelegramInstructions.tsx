// Telegram Setup Instructions Component
// Comprehensive guide for setting up Telegram bot integration

/* eslint-disable react/no-unescaped-entities */
'use client'

import React from 'react'

import { Send, ExternalLink, Copy, CheckCircle, AlertCircle, Info } from 'lucide-react'

interface TelegramInstructionsProps {
  className?: string
}

const TelegramInstructions: React.FC<TelegramInstructionsProps> = ({ className = '' }) => {
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const webhookUrl = `${process.env.NEXT_PUBLIC_API_URL || 'https://yourdomain.com'}/api/internal-chat/telegram/webhook`

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className='flex items-center space-x-3'>
        <div className='p-2 bg-blue-100 rounded-lg'>
          <Send className='w-6 h-6 text-blue-600' />
        </div>
        <div>
          <h2 className='text-xl font-semibold text-gray-900'>Telegram Bot Setup Instructions</h2>
          <p className='text-gray-600'>Complete guide to configure Telegram bot integration</p>
        </div>
      </div>

      {/* Overview */}
      <div className='bg-blue-50 border border-blue-200 rounded-lg p-6'>
        <div className='flex items-start space-x-3'>
          <Info className='w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0' />
          <div>
            <h3 className='text-sm font-medium text-blue-900'>What You'll Need</h3>
            <ul className='text-sm text-blue-800 mt-2 space-y-1'>
              <li>A Telegram account</li>
              <li>Access to @BotFather on Telegram</li>
              <li>Admin access to this system</li>
              <li>A public domain for webhook (HTTPS required)</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Step-by-step Instructions */}
      <div className='space-y-6'>
        {/* Step 1 */}
        <div className='bg-white border border-gray-200 rounded-lg p-6'>
          <div className='flex items-start space-x-4'>
            <div className='flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium'>
              1
            </div>
            <div className='flex-1'>
              <h3 className='text-lg font-medium text-gray-900 mb-3'>Create a New Telegram Bot</h3>

              <div className='space-y-4'>
                <div>
                  <p className='text-sm text-gray-700 mb-2'>
                    Open Telegram and search for <strong>@BotFather</strong>, then start a conversation.
                  </p>
                  <a
                    href='https://t.me/botfather'
                    target='_blank'
                    rel='noopener noreferrer'
                    className='inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                  >
                    <ExternalLink className='w-4 h-4 mr-2' />
                    Open @BotFather
                  </a>
                </div>

                <div>
                  <p className='text-sm text-gray-700 mb-2'>Send the following command to create a new bot:</p>
                  <div className='bg-gray-100 rounded-md p-3 font-mono text-sm flex items-center justify-between'>
                    <span>/newbot</span>
                    <button onClick={() => copyToClipboard('/newbot')} className='text-gray-500 hover:text-gray-700'>
                      <Copy className='w-4 h-4' />
                    </button>
                  </div>
                </div>

                <div>
                  <p className='text-sm text-gray-700'>
                    Follow BotFather's instructions to choose a name and username for your bot. The username must end
                    with "bot" (e.g., "MyCompanyBot").
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Step 2 */}
        <div className='bg-white border border-gray-200 rounded-lg p-6'>
          <div className='flex items-start space-x-4'>
            <div className='flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium'>
              2
            </div>
            <div className='flex-1'>
              <h3 className='text-lg font-medium text-gray-900 mb-3'>Get Your Bot Token</h3>

              <div className='space-y-4'>
                <div>
                  <p className='text-sm text-gray-700'>
                    After creating the bot, BotFather will provide you with a bot token. It looks like this:
                  </p>
                  <div className='bg-gray-100 rounded-md p-3 font-mono text-sm mt-2'>
                    *********:ABCdefGHIjklMNOpqrsTUVwxyz
                  </div>
                </div>

                <div className='bg-yellow-50 border border-yellow-200 rounded-lg p-4'>
                  <div className='flex items-start space-x-3'>
                    <AlertCircle className='w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0' />
                    <div>
                      <h4 className='text-sm font-medium text-yellow-800'>Important Security Note</h4>
                      <p className='text-sm text-yellow-700 mt-1'>
                        Keep your bot token secure! Anyone with this token can control your bot. Never share it publicly
                        or commit it to version control.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Step 3 */}
        <div className='bg-white border border-gray-200 rounded-lg p-6'>
          <div className='flex items-start space-x-4'>
            <div className='flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium'>
              3
            </div>
            <div className='flex-1'>
              <h3 className='text-lg font-medium text-gray-900 mb-3'>Configure Bot Settings (Optional)</h3>

              <div className='space-y-4'>
                <div>
                  <p className='text-sm text-gray-700 mb-3'>
                    You can customize your bot's behavior using these BotFather commands:
                  </p>

                  <div className='space-y-3'>
                    <div>
                      <p className='text-sm font-medium text-gray-900'>Set bot description:</p>
                      <div className='bg-gray-100 rounded-md p-3 font-mono text-sm flex items-center justify-between mt-1'>
                        <span>/setdescription</span>
                        <button
                          onClick={() => copyToClipboard('/setdescription')}
                          className='text-gray-500 hover:text-gray-700'
                        >
                          <Copy className='w-4 h-4' />
                        </button>
                      </div>
                    </div>

                    <div>
                      <p className='text-sm font-medium text-gray-900'>Set bot profile photo:</p>
                      <div className='bg-gray-100 rounded-md p-3 font-mono text-sm flex items-center justify-between mt-1'>
                        <span>/setuserpic</span>
                        <button
                          onClick={() => copyToClipboard('/setuserpic')}
                          className='text-gray-500 hover:text-gray-700'
                        >
                          <Copy className='w-4 h-4' />
                        </button>
                      </div>
                    </div>

                    <div>
                      <p className='text-sm font-medium text-gray-900'>Set bot commands menu:</p>
                      <div className='bg-gray-100 rounded-md p-3 font-mono text-sm flex items-center justify-between mt-1'>
                        <span>/setcommands</span>
                        <button
                          onClick={() => copyToClipboard('/setcommands')}
                          className='text-gray-500 hover:text-gray-700'
                        >
                          <Copy className='w-4 h-4' />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Step 4 */}
        <div className='bg-white border border-gray-200 rounded-lg p-6'>
          <div className='flex items-start space-x-4'>
            <div className='flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium'>
              4
            </div>
            <div className='flex-1'>
              <h3 className='text-lg font-medium text-gray-900 mb-3'>Configure Integration in System</h3>

              <div className='space-y-4'>
                <div>
                  <p className='text-sm text-gray-700 mb-3'>
                    Go to the <strong>Configuration</strong> tab and enter your bot details:
                  </p>

                  <div className='space-y-3'>
                    <div>
                      <p className='text-sm font-medium text-gray-900'>Bot Token (Required):</p>
                      <p className='text-sm text-gray-600'>Paste the token you received from BotFather</p>
                    </div>

                    <div>
                      <p className='text-sm font-medium text-gray-900'>Bot Username (Optional):</p>
                      <p className='text-sm text-gray-600'>Your bot's username (without @)</p>
                    </div>

                    <div>
                      <p className='text-sm font-medium text-gray-900'>Webhook URL (Auto-generated):</p>
                      <div className='bg-gray-100 rounded-md p-3 font-mono text-sm flex items-center justify-between mt-1'>
                        <span className='truncate'>{webhookUrl}</span>
                        <button
                          onClick={() => copyToClipboard(webhookUrl)}
                          className='text-gray-500 hover:text-gray-700 ml-2'
                        >
                          <Copy className='w-4 h-4' />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Step 5 */}
        <div className='bg-white border border-gray-200 rounded-lg p-6'>
          <div className='flex items-start space-x-4'>
            <div className='flex-shrink-0 w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-medium'>
              5
            </div>
            <div className='flex-1'>
              <h3 className='text-lg font-medium text-gray-900 mb-3'>Test Your Integration</h3>

              <div className='space-y-4'>
                <div>
                  <p className='text-sm text-gray-700 mb-3'>After saving your configuration:</p>

                  <ol className='text-sm text-gray-700 space-y-2 list-decimal list-inside'>
                    <li>Find your bot on Telegram by searching for its username</li>
                    <li>Start a conversation by clicking "Start" or sending any message</li>
                    <li>Check the "Conversations" tab to see if the message appears</li>
                    <li>Reply from the system to test two-way communication</li>
                  </ol>
                </div>

                <div className='bg-green-50 border border-green-200 rounded-lg p-4'>
                  <div className='flex items-start space-x-3'>
                    <CheckCircle className='w-5 h-5 text-green-600 mt-0.5 flex-shrink-0' />
                    <div>
                      <h4 className='text-sm font-medium text-green-800'>Success!</h4>
                      <p className='text-sm text-green-700 mt-1'>
                        If messages appear in both directions, your Telegram integration is working correctly.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Troubleshooting */}
      <div className='bg-gray-50 border border-gray-200 rounded-lg p-6'>
        <h3 className='text-lg font-medium text-gray-900 mb-4'>Troubleshooting</h3>

        <div className='space-y-4'>
          <div>
            <h4 className='text-sm font-medium text-gray-900'>Bot not receiving messages?</h4>
            <ul className='text-sm text-gray-700 mt-1 space-y-1 list-disc list-inside ml-4'>
              <li>Verify your bot token is correct</li>
              <li>Ensure your webhook URL is accessible via HTTPS</li>
              <li>Check that your domain has a valid SSL certificate</li>
              <li>Make sure the bot is not already connected to another webhook</li>
            </ul>
          </div>

          <div>
            <h4 className='text-sm font-medium text-gray-900'>Messages not sending from system?</h4>
            <ul className='text-sm text-gray-700 mt-1 space-y-1 list-disc list-inside ml-4'>
              <li>Check the bot token permissions</li>
              <li>Verify the user has started a conversation with the bot</li>
              <li>Review system logs for API errors</li>
            </ul>
          </div>

          <div>
            <h4 className='text-sm font-medium text-gray-900'>Need help?</h4>
            <p className='text-sm text-gray-700 mt-1'>
              Check the system logs or contact your administrator for additional support.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TelegramInstructions
