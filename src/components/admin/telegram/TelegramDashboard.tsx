// Telegram Integration Dashboard
// Simple overview and configuration for Telegram integration

'use client'

import React, { useState, useEffect } from 'react'

import { Send, Users, MessageCircle, UserCheck } from 'lucide-react'

import TelegramBotConfig from './TelegramBotConfig'
import TelegramRoomList from '@/components/chat/telegram/TelegramRoomList'
import TelegramContactSelector from '@/components/chat/telegram/TelegramContactSelector'

interface TelegramStats {
  total_contacts: number
  active_rooms: number
  unassigned_rooms: number
  total_messages_sent: number
  total_messages_received: number
  failed_messages: number
}

interface TelegramDashboardProps {
  className?: string
}

const TelegramDashboard: React.FC<TelegramDashboardProps> = ({ className = '' }) => {
  const [stats, setStats] = useState<TelegramStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [configured, setConfigured] = useState<boolean>(true)
  const [activeTab, setActiveTab] = useState<'overview' | 'conversations' | 'contacts' | 'config'>('overview')

  // Fetch basic statistics
  const fetchStats = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/internal-chat/telegram/stats')
      const data = await response.json()

      if (response.ok) {
        setStats(data.stats)
        setConfigured(data.configured !== false)
      } else {
        // Handle configuration errors
        setError(data.message || data.error || 'Failed to fetch statistics')
        setConfigured(data.configured !== false)
        setStats(null)
      }
    } catch (err) {
      console.error('Error fetching stats:', err)
      setError('Failed to connect to server')
      setConfigured(true) // Assume configured if we can't determine
    } finally {
      setLoading(false)
    }
  }

  // Load stats on mount
  useEffect(() => {
    if (activeTab === 'overview') {
      fetchStats()
    }
  }, [activeTab])

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className='bg-white rounded-lg shadow-sm border border-gray-200 p-6'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center space-x-3'>
            <div className='p-2 bg-blue-100 rounded-lg'>
              <Send className='w-6 h-6 text-blue-600' />
            </div>
            <div>
              <h1 className='text-2xl font-bold text-gray-900'>Telegram Integration</h1>
              <p className='text-gray-600'>Configure Telegram bot and view basic statistics</p>
            </div>
          </div>

          {/* Simple tab navigation */}
          <div className='flex space-x-1 bg-gray-100 rounded-lg p-1'>
            <button
              onClick={() => setActiveTab('overview')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'overview' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Overview
            </button>
            <button
              onClick={() => setActiveTab('conversations')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'conversations' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Conversations
            </button>
            <button
              onClick={() => setActiveTab('contacts')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'contacts' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Contacts
            </button>
            <button
              onClick={() => setActiveTab('config')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'config' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Configuration
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      {activeTab === 'overview' && (
        <div className='space-y-6'>
          {/* Configuration Status */}
          {error && (
            <div className='bg-white rounded-lg shadow-sm border border-gray-200 p-6'>
              <div className='flex items-start space-x-3'>
                <div className='flex-shrink-0'>
                  <div className='w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center'>
                    <Send className='w-4 h-4 text-yellow-600' />
                  </div>
                </div>
                <div className='flex-1'>
                  <h3 className='text-sm font-medium text-gray-900'>
                    {configured ? 'Configuration Issue' : 'Bot Not Configured'}
                  </h3>
                  <p className='mt-1 text-sm text-gray-600'>{error}</p>
                  <div className='mt-3'>
                    <button
                      onClick={() => setActiveTab('config')}
                      className='inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                    >
                      Go to Configuration
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Basic stats */}
          <div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
            <div className='bg-white rounded-lg shadow-sm border border-gray-200 p-6'>
              <div className='flex items-center justify-between'>
                <div>
                  <p className='text-sm font-medium text-gray-600'>Contacts</p>
                  <p className='text-2xl font-bold text-gray-900'>{loading ? '...' : stats?.total_contacts || 0}</p>
                </div>
                <Users className='w-8 h-8 text-blue-600' />
              </div>
              <div className='mt-4'>
                <button
                  onClick={() => setActiveTab('contacts')}
                  className='text-xs text-blue-600 hover:text-blue-700 underline'
                >
                  View Contacts
                </button>
              </div>
            </div>

            <div className='bg-white rounded-lg shadow-sm border border-gray-200 p-6'>
              <div className='flex items-center justify-between'>
                <div>
                  <p className='text-sm font-medium text-gray-600'>Active Chats</p>
                  <p className='text-2xl font-bold text-gray-900'>{loading ? '...' : stats?.active_rooms || 0}</p>
                </div>
                <MessageCircle className='w-8 h-8 text-green-600' />
              </div>
              <div className='mt-4'>
                <button
                  onClick={() => setActiveTab('conversations')}
                  className='text-xs text-green-600 hover:text-green-700 underline'
                >
                  View Conversations
                </button>
              </div>
            </div>

            <div className='bg-white rounded-lg shadow-sm border border-gray-200 p-6'>
              <div className='flex items-center justify-between'>
                <div>
                  <p className='text-sm font-medium text-gray-600'>Unassigned</p>
                  <p className='text-2xl font-bold text-gray-900'>{loading ? '...' : stats?.unassigned_rooms || 0}</p>
                </div>
                <UserCheck className='w-8 h-8 text-orange-600' />
              </div>
              <div className='mt-4'>
                {(stats?.unassigned_rooms || 0) > 0 ? (
                  <button
                    onClick={() => setActiveTab('conversations')}
                    className='text-xs text-orange-600 hover:text-orange-700 underline'
                  >
                    Assign Agents
                  </button>
                ) : (
                  <span className='text-xs text-gray-500'>All assigned</span>
                )}
              </div>
            </div>

            <div className='bg-white rounded-lg shadow-sm border border-gray-200 p-6'>
              <div className='flex items-center justify-between'>
                <div>
                  <p className='text-sm font-medium text-gray-600'>Messages</p>
                  <p className='text-2xl font-bold text-gray-900'>
                    {loading ? '...' : (stats?.total_messages_sent || 0) + (stats?.total_messages_received || 0)}
                  </p>
                </div>
                <Send className='w-8 h-8 text-purple-600' />
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'conversations' && (
        <div className='bg-white rounded-lg shadow-sm border border-gray-200'>
          <div className='p-4 border-b border-gray-200'>
            <h3 className='text-lg font-semibold text-gray-900'>Telegram Conversations</h3>
            <p className='text-sm text-gray-600 mt-1'>Manage active Telegram conversations and assign agents</p>
          </div>
          <div className='h-96'>
            <TelegramRoomList
              onRoomSelect={roomUuid => {
                console.log('Room selected:', roomUuid)
              }}
              selectedRoomId={null}
              showAssignmentControls={true}
              className='h-full'
            />
          </div>
        </div>
      )}

      {activeTab === 'contacts' && (
        <div className='bg-white rounded-lg shadow-sm border border-gray-200'>
          <div className='p-4 border-b border-gray-200'>
            <h3 className='text-lg font-semibold text-gray-900'>Telegram Contacts</h3>
            <p className='text-sm text-gray-600 mt-1'>View and manage Telegram contacts</p>
          </div>
          <div className='h-96'>
            <TelegramContactSelector
              onContactSelect={contact => {
                console.log('Contact selected:', contact)
              }}
              onCreateRoom={contactUuid => {
                console.log('Create room for contact:', contactUuid)
                fetchStats() // Refresh stats
                setActiveTab('conversations') // Switch to conversations tab
              }}
              className='h-full'
            />
          </div>
        </div>
      )}

      {/* Assignment tab removed - assignment now handled directly in conversations */}

      {activeTab === 'config' && <TelegramBotConfig />}
    </div>
  )
}

export default TelegramDashboard
