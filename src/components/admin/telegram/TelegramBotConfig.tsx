// Telegram Bot Configuration Component
// Admin interface for managing Telegram bot settings per domain

/* eslint-disable react/no-unescaped-entities */
'use client'

import React, { useState, useEffect } from 'react'

import { Save, Eye, EyeOff, AlertCircle, CheckCircle, Bot } from 'lucide-react'

import CollapsibleHelp, {
  HelpStep,
  CopyableCode,
  ExternalLinkButton,
  InfoBox,
  HelpSection
} from '@/components/common/CollapsibleHelp'

interface BotConfig {
  config_uuid?: string
  domain_uuid: string
  bot_username?: string | null
  webhook_url?: string | null
  is_active: boolean
  allowed_updates: string[]
  bot_settings: Record<string, any>
  bot_token_masked?: string
  configured: boolean
  insert_date?: string
  update_date?: string
}

interface TelegramBotConfigProps {
  className?: string
}

const TelegramBotConfig: React.FC<TelegramBotConfigProps> = ({ className = '' }) => {
  const [config, setConfig] = useState<BotConfig | null>(null)
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [showToken, setShowToken] = useState(false)

  // Form state
  const [formData, setFormData] = useState({
    bot_token: '',
    bot_username: '',
    webhook_url: '',
    webhook_secret: '',
    is_active: true,
    allowed_updates: ['message', 'edited_message', 'callback_query']
  })

  // Available update types
  const updateTypes = [
    { value: 'message', label: 'Messages' },
    { value: 'edited_message', label: 'Edited Messages' },
    { value: 'callback_query', label: 'Callback Queries' },
    { value: 'inline_query', label: 'Inline Queries' },
    { value: 'chosen_inline_result', label: 'Chosen Inline Results' },
    { value: 'channel_post', label: 'Channel Posts' },
    { value: 'edited_channel_post', label: 'Edited Channel Posts' }
  ]

  // Fetch current configuration
  const fetchConfig = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/internal-chat/telegram/bot-config')

      if (response.status === 404) {
        // No configuration exists yet
        setConfig(null)

        return
      }

      if (!response.ok) {
        throw new Error('Failed to fetch bot configuration')
      }

      const data = await response.json()

      setConfig(data.bot_config)

      // Update form data with existing config
      if (data.bot_config) {
        setFormData({
          bot_token: '', // Never populate token for security
          bot_username: data.bot_config.bot_username || '',
          webhook_url: data.bot_config.webhook_url || '',
          webhook_secret: '',
          is_active: data.bot_config.is_active,
          allowed_updates: data.bot_config.allowed_updates || ['message', 'edited_message', 'callback_query']
        })
      }
    } catch (err) {
      console.error('Error fetching bot configuration:', err)
      setError(err instanceof Error ? err.message : 'Failed to load configuration')
    } finally {
      setLoading(false)
    }
  }

  // Save configuration
  const saveConfig = async () => {
    // Only require token for new configurations
    if (!formData.bot_token.trim() && !config) {
      setError('Bot token is required when creating a new configuration. Please enter your Telegram bot token.')

      return
    }

    setSaving(true)
    setError(null)
    setSuccess(null)

    try {
      const payload: any = {
        bot_username: formData.bot_username.trim() || undefined,
        webhook_url: formData.webhook_url.trim() || undefined,
        webhook_secret: formData.webhook_secret.trim() || undefined,
        allowed_updates: formData.allowed_updates,
        bot_settings: {}
      }

      // Only include bot_token if it's provided
      if (formData.bot_token.trim()) {
        payload.bot_token = formData.bot_token.trim()
      }

      const response = await fetch('/api/internal-chat/telegram/bot-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      if (!response.ok) {
        const errorData = await response.json()

        throw new Error(errorData.error || 'Failed to save configuration')
      }

      const data = await response.json()

      setConfig(data.bot_config)

      // Show success message and any warnings
      let message = data.message || 'Configuration saved successfully'

      if (data.warnings && data.warnings.length > 0) {
        message += '\n\nWarnings:\n' + data.warnings.join('\n')
      }

      setSuccess(message)

      // Clear sensitive fields only after successful save
      setFormData(prev => ({ ...prev, bot_token: '', webhook_secret: '' }))

      // Refresh configuration
      await fetchConfig()
    } catch (err) {
      console.error('Error saving bot configuration:', err)
      setError(err instanceof Error ? err.message : 'Failed to save configuration')
    } finally {
      setSaving(false)
    }
  }

  // Toggle bot active status
  const toggleBotStatus = async () => {
    if (!config) return

    try {
      const response = await fetch('/api/internal-chat/telegram/bot-config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          is_active: !config.is_active
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update bot status')
      }

      await fetchConfig()
      setSuccess(`Bot ${!config.is_active ? 'activated' : 'deactivated'} successfully`)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update bot status')
    }
  }

  // Handle form input changes
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  // Handle allowed updates change
  const handleUpdateTypeChange = (updateType: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      allowed_updates: checked
        ? [...prev.allowed_updates, updateType]
        : prev.allowed_updates.filter(type => type !== updateType)
    }))
  }

  // Initial load
  useEffect(() => {
    fetchConfig()
  }, [])

  // Clear messages after delay
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => setSuccess(null), 5000)

      return () => clearTimeout(timer)
    }
  }, [success])

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className='p-6 border-b border-gray-200'>
        <div className='flex items-center space-x-3'>
          <div className='p-2 bg-blue-100 rounded-lg'>
            <Bot className='w-6 h-6 text-blue-600' />
          </div>
          <div>
            <h2 className='text-xl font-semibold text-gray-900'>Telegram Bot Configuration</h2>
            <p className='text-sm text-gray-600'>Configure your Telegram bot for internal chat integration</p>
          </div>
        </div>
      </div>

      {/* Setup Instructions */}
      <div className='p-6 border-b border-gray-200'>
        <CollapsibleHelp title='Telegram Bot Setup Instructions' variant='compact'>
          <div className='space-y-6'>
            <InfoBox type='info' title="What You'll Need">
              <ul className='text-sm space-y-1 mt-2'>
                <li>A Telegram account</li>
                <li>Access to @BotFather on Telegram</li>
                <li>A public domain for webhook (HTTPS required)</li>
              </ul>
            </InfoBox>

            <HelpStep stepNumber={1} title='Create a New Telegram Bot' color='blue'>
              <p className='text-sm text-gray-700 mb-2'>
                Open Telegram and search for <strong>@BotFather</strong>, then start a conversation.
              </p>
              <ExternalLinkButton href='https://t.me/botfather' color='blue'>
                Open @BotFather
              </ExternalLinkButton>
              <div className='mt-3'>
                <CopyableCode code='/newbot' label='Send this command:' />
              </div>
              <p className='text-sm text-gray-700 mt-2'>
                Follow BotFather's instructions to choose a name and username for your bot.
              </p>
            </HelpStep>

            <HelpStep stepNumber={2} title='Get Your Bot Token' color='blue'>
              <p className='text-sm text-gray-700 mb-2'>
                After creating the bot, BotFather will provide you with a bot token:
              </p>
              <div className='bg-gray-100 rounded-md p-3 font-mono text-sm mt-2'>
                123456789:ABCdefGHIjklMNOpqrsTUVwxyz
              </div>
              <InfoBox type='warning' title='Security Note'>
                Keep your bot token secure! Never share it publicly or commit it to version control.
              </InfoBox>
            </HelpStep>

            <HelpStep stepNumber={3} title='Configure Integration' color='blue'>
              <p className='text-sm text-gray-700 mb-2'>
                Enter your bot token in the form below and save the configuration.
              </p>
              <ul className='text-sm text-gray-700 space-y-1 list-disc list-inside ml-4'>
                <li>Bot Token: Paste the token from BotFather</li>
                <li>Bot Username: Your bot's username (optional)</li>
                <li>Webhook URL: Auto-generated for your domain</li>
              </ul>
            </HelpStep>

            <HelpStep stepNumber={4} title='Test Integration' color='blue'>
              <p className='text-sm text-gray-700 mb-2'>After saving your configuration:</p>
              <ol className='text-sm text-gray-700 space-y-1 list-decimal list-inside ml-4'>
                <li>Find your bot on Telegram by searching for its username</li>
                <li>Start a conversation by clicking "Start" or sending any message</li>
                <li>Check the "Conversations" tab to see if the message appears</li>
                <li>Reply from the system to test two-way communication</li>
              </ol>
            </HelpStep>

            <HelpSection title='Troubleshooting'>
              <div className='space-y-3'>
                <div>
                  <h4 className='text-sm font-medium text-gray-900'>Bot not receiving messages?</h4>
                  <ul className='text-sm text-gray-700 mt-1 space-y-1 list-disc list-inside ml-4'>
                    <li>Verify your bot token is correct</li>
                    <li>Ensure your webhook URL is accessible via HTTPS</li>
                    <li>Check that your domain has a valid SSL certificate</li>
                  </ul>
                </div>
                <div>
                  <h4 className='text-sm font-medium text-gray-900'>Messages not sending from system?</h4>
                  <ul className='text-sm text-gray-700 mt-1 space-y-1 list-disc list-inside ml-4'>
                    <li>Check the bot token permissions</li>
                    <li>Verify the user has started a conversation with the bot</li>
                    <li>Review system logs for API errors</li>
                  </ul>
                </div>
              </div>
            </HelpSection>
          </div>
        </CollapsibleHelp>
      </div>

      {/* Content */}
      <div className='p-6'>
        {loading && (
          <div className='flex items-center justify-center py-8'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600'></div>
          </div>
        )}

        {/* Error display */}
        {error && (
          <div className='mb-6 p-4 bg-red-50 border border-red-200 rounded-lg'>
            <div className='flex items-center space-x-2'>
              <AlertCircle className='w-5 h-5 text-red-600' />
              <span className='text-red-700'>{error}</span>
            </div>
          </div>
        )}

        {/* Success display */}
        {success && (
          <div className='mb-6 p-4 bg-green-50 border border-green-200 rounded-lg'>
            <div className='flex items-start space-x-2'>
              <CheckCircle className='w-5 h-5 text-green-600 mt-0.5 flex-shrink-0' />
              <div className='text-green-700'>
                {success.split('\n').map((line, index) => (
                  <div key={index} className={index > 0 ? 'mt-1' : ''}>
                    {line}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {!loading && (
          <div className='space-y-6'>
            {/* Current status */}
            {config && (
              <div className='p-4 bg-gray-50 rounded-lg'>
                <div className='flex items-center justify-between'>
                  <div>
                    <h3 className='font-medium text-gray-900'>Current Status</h3>
                    <p className='text-sm text-gray-600'>
                      Bot is {config.is_active ? 'active' : 'inactive'}
                      {config.bot_username && ` (@${config.bot_username})`}
                    </p>
                  </div>
                  <button
                    onClick={toggleBotStatus}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      config.is_active
                        ? 'bg-red-100 text-red-700 hover:bg-red-200'
                        : 'bg-green-100 text-green-700 hover:bg-green-200'
                    }`}
                  >
                    {config.is_active ? 'Deactivate' : 'Activate'}
                  </button>
                </div>
              </div>
            )}

            {/* Bot token */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-2'>
                Bot Token {!config && '*'}
                {config && (
                  <span className='text-xs text-gray-500 font-normal ml-1'>
                    (optional - leave empty to keep current)
                  </span>
                )}
              </label>
              <div className='relative'>
                <input
                  type={showToken ? 'text' : 'password'}
                  value={formData.bot_token}
                  onChange={e => handleInputChange('bot_token', e.target.value)}
                  placeholder={config ? 'Enter new token to update (optional)' : 'Enter your Telegram bot token'}
                  className='w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-10'
                />
                <button
                  type='button'
                  onClick={() => setShowToken(!showToken)}
                  className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600'
                >
                  {showToken ? <EyeOff className='w-4 h-4' /> : <Eye className='w-4 h-4' />}
                </button>
              </div>
              {config?.bot_token_masked && (
                <p className='text-xs text-gray-500 mt-1'>Current token: {config.bot_token_masked}</p>
              )}
              {config && (
                <p className='text-xs text-green-600 mt-1'>
                  ✓ Token is already configured. Leave this field empty to keep the existing token.
                </p>
              )}
            </div>

            {/* Bot username */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-2'>Bot Username</label>
              <input
                type='text'
                value={formData.bot_username}
                onChange={e => handleInputChange('bot_username', e.target.value)}
                placeholder='@your_bot_username'
                className='w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
              />
            </div>

            {/* Webhook URL */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-2'>Webhook URL</label>
              <input
                type='url'
                value={formData.webhook_url}
                onChange={e => handleInputChange('webhook_url', e.target.value)}
                placeholder='https://your-domain.com/api/internal-chat/telegram/webhook'
                className='w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
              />
              <p className='text-xs text-gray-500 mt-1'>
                URL where Telegram will send webhook events. The webhook will be automatically configured with Telegram
                when you save.
              </p>
            </div>

            {/* Webhook secret */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-2'>Webhook Secret</label>
              <input
                type='password'
                value={formData.webhook_secret}
                onChange={e => handleInputChange('webhook_secret', e.target.value)}
                placeholder='Optional webhook secret for security'
                className='w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
              />
            </div>

            {/* Allowed updates */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-3'>Allowed Update Types</label>
              <div className='grid grid-cols-2 gap-3'>
                {updateTypes.map(updateType => (
                  <label key={updateType.value} className='flex items-center space-x-2'>
                    <input
                      type='checkbox'
                      checked={formData.allowed_updates.includes(updateType.value)}
                      onChange={e => handleUpdateTypeChange(updateType.value, e.target.checked)}
                      className='rounded border-gray-300 text-blue-600 focus:ring-blue-500'
                    />
                    <span className='text-sm text-gray-700'>{updateType.label}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Save button */}
            <div className='flex justify-end'>
              <button
                onClick={saveConfig}
                disabled={saving}
                className='px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2'
              >
                {saving ? (
                  <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white'></div>
                ) : (
                  <Save className='w-4 h-4' />
                )}
                <span>{saving ? 'Saving...' : 'Save Configuration'}</span>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default TelegramBotConfig
