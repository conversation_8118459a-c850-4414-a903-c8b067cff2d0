// Facebook Setup Instructions Component
// Comprehensive guide for setting up Facebook Messenger integration

/* eslint-disable react/no-unescaped-entities */
'use client'

import React from 'react'

import { MessageSquare, ExternalLink, Copy, CheckCircle, AlertCircle, Info } from 'lucide-react'

interface FacebookInstructionsProps {
  className?: string
}

const FacebookInstructions: React.FC<FacebookInstructionsProps> = ({ className = '' }) => {
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const webhookUrl = `${process.env.NEXT_PUBLIC_API_URL || 'https://yourdomain.com'}/api/internal-chat/facebook/webhook`

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className='flex items-center space-x-3'>
        <div className='p-2 bg-blue-100 rounded-lg'>
          <MessageSquare className='w-6 h-6 text-blue-600' />
        </div>
        <div>
          <h2 className='text-xl font-semibold text-gray-900'>Facebook Messenger Setup Instructions</h2>
          <p className='text-gray-600'>Complete guide to configure Facebook Messenger integration</p>
        </div>
      </div>

      {/* Overview */}
      <div className='bg-blue-50 border border-blue-200 rounded-lg p-6'>
        <div className='flex items-start space-x-3'>
          <Info className='w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0' />
          <div>
            <h3 className='text-sm font-medium text-blue-900'>What You'll Need</h3>
            <ul className='text-sm text-blue-800 mt-2 space-y-1'>
              <li>A Facebook account with developer access</li>
              <li>A Facebook Page for your business</li>
              <li>Admin access to this system</li>
              <li>A public domain for webhook (HTTPS required)</li>
              <li>Facebook App with Messenger product enabled</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Step-by-step Instructions */}
      <div className='space-y-6'>
        {/* Step 1 */}
        <div className='bg-white border border-gray-200 rounded-lg p-6'>
          <div className='flex items-start space-x-4'>
            <div className='flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium'>
              1
            </div>
            <div className='flex-1'>
              <h3 className='text-lg font-medium text-gray-900 mb-3'>Create a Facebook App</h3>

              <div className='space-y-4'>
                <div>
                  <p className='text-sm text-gray-700 mb-2'>
                    Go to the Facebook Developers Console and create a new app.
                  </p>
                  <a
                    href='https://developers.facebook.com/apps/'
                    target='_blank'
                    rel='noopener noreferrer'
                    className='inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                  >
                    <ExternalLink className='w-4 h-4 mr-2' />
                    Open Facebook Developers
                  </a>
                </div>

                <div>
                  <p className='text-sm text-gray-700'>
                    <strong>Steps:</strong>
                  </p>
                  <ol className='text-sm text-gray-700 mt-2 space-y-1 list-decimal list-inside ml-4'>
                    <li>Click "Create App"</li>
                    <li>Select "Business" as the app type</li>
                    <li>Fill in your app details (name, contact email, etc.)</li>
                    <li>Complete the security check</li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Step 2 */}
        <div className='bg-white border border-gray-200 rounded-lg p-6'>
          <div className='flex items-start space-x-4'>
            <div className='flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium'>
              2
            </div>
            <div className='flex-1'>
              <h3 className='text-lg font-medium text-gray-900 mb-3'>Add Messenger Product</h3>

              <div className='space-y-4'>
                <div>
                  <p className='text-sm text-gray-700'>In your Facebook App dashboard:</p>
                  <ol className='text-sm text-gray-700 mt-2 space-y-1 list-decimal list-inside ml-4'>
                    <li>Go to "Add Products to Your App"</li>
                    <li>Find "Messenger" and click "Set Up"</li>
                    <li>This will add Messenger to your app's product list</li>
                  </ol>
                </div>

                <div className='bg-yellow-50 border border-yellow-200 rounded-lg p-4'>
                  <div className='flex items-start space-x-3'>
                    <AlertCircle className='w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0' />
                    <div>
                      <h4 className='text-sm font-medium text-yellow-800'>Important</h4>
                      <p className='text-sm text-yellow-700 mt-1'>
                        Make sure you have a Facebook Page created before proceeding. You'll need to connect your app to
                        a specific page.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Step 3 */}
        <div className='bg-white border border-gray-200 rounded-lg p-6'>
          <div className='flex items-start space-x-4'>
            <div className='flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium'>
              3
            </div>
            <div className='flex-1'>
              <h3 className='text-lg font-medium text-gray-900 mb-3'>Generate Page Access Token</h3>

              <div className='space-y-4'>
                <div>
                  <p className='text-sm text-gray-700'>In the Messenger settings:</p>
                  <ol className='text-sm text-gray-700 mt-2 space-y-1 list-decimal list-inside ml-4'>
                    <li>Go to "Messenger" → "Settings"</li>
                    <li>Find "Access Tokens" section</li>
                    <li>Click "Add or Remove Pages"</li>
                    <li>Select your Facebook Page and grant necessary permissions</li>
                    <li>Copy the generated Page Access Token</li>
                  </ol>
                </div>

                <div>
                  <p className='text-sm text-gray-700'>The token will look like this:</p>
                  <div className='bg-gray-100 rounded-md p-3 font-mono text-sm mt-2'>
                    EAABwzLixnjYBAOZBxZCvZCxZCvZCxZCvZCxZCvZCxZCvZCxZCvZCx...
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Step 4 */}
        <div className='bg-white border border-gray-200 rounded-lg p-6'>
          <div className='flex items-start space-x-4'>
            <div className='flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium'>
              4
            </div>
            <div className='flex-1'>
              <h3 className='text-lg font-medium text-gray-900 mb-3'>Set Up Webhook</h3>

              <div className='space-y-4'>
                <div>
                  <p className='text-sm text-gray-700 mb-3'>Configure the webhook to receive messages:</p>

                  <div className='space-y-3'>
                    <div>
                      <p className='text-sm font-medium text-gray-900'>Webhook URL:</p>
                      <div className='bg-gray-100 rounded-md p-3 font-mono text-sm flex items-center justify-between mt-1'>
                        <span className='truncate'>{webhookUrl}</span>
                        <button
                          onClick={() => copyToClipboard(webhookUrl)}
                          className='text-gray-500 hover:text-gray-700 ml-2'
                        >
                          <Copy className='w-4 h-4' />
                        </button>
                      </div>
                    </div>

                    <div>
                      <p className='text-sm font-medium text-gray-900'>Verify Token:</p>
                      <p className='text-sm text-gray-600'>
                        Create a secure random string (you'll enter this in both Facebook and our system)
                      </p>
                      <div className='bg-gray-100 rounded-md p-3 font-mono text-sm mt-1'>
                        my_secure_verify_token_123
                      </div>
                    </div>

                    <div>
                      <p className='text-sm font-medium text-gray-900'>Webhook Fields:</p>
                      <p className='text-sm text-gray-600'>Select these subscription fields:</p>
                      <ul className='text-sm text-gray-700 mt-1 space-y-1 list-disc list-inside ml-4'>
                        <li>messages</li>
                        <li>messaging_postbacks</li>
                        <li>messaging_optins</li>
                        <li>messaging_deliveries (optional)</li>
                        <li>messaging_reads (optional)</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Step 5 */}
        <div className='bg-white border border-gray-200 rounded-lg p-6'>
          <div className='flex items-start space-x-4'>
            <div className='flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium'>
              5
            </div>
            <div className='flex-1'>
              <h3 className='text-lg font-medium text-gray-900 mb-3'>Configure Integration in System</h3>

              <div className='space-y-4'>
                <div>
                  <p className='text-sm text-gray-700 mb-3'>
                    Go to the <strong>Configuration</strong> tab and enter your Facebook details:
                  </p>

                  <div className='space-y-3'>
                    <div>
                      <p className='text-sm font-medium text-gray-900'>App ID (Required):</p>
                      <p className='text-sm text-gray-600'>Found in your Facebook App dashboard → Settings → Basic</p>
                    </div>

                    <div>
                      <p className='text-sm font-medium text-gray-900'>App Secret (Required):</p>
                      <p className='text-sm text-gray-600'>
                        Found in your Facebook App dashboard → Settings → Basic (click "Show")
                      </p>
                    </div>

                    <div>
                      <p className='text-sm font-medium text-gray-900'>Page ID (Required):</p>
                      <p className='text-sm text-gray-600'>Found in your Facebook Page settings → About</p>
                    </div>

                    <div>
                      <p className='text-sm font-medium text-gray-900'>Page Access Token (Required):</p>
                      <p className='text-sm text-gray-600'>The token you generated in Step 3</p>
                    </div>

                    <div>
                      <p className='text-sm font-medium text-gray-900'>Verify Token (Required):</p>
                      <p className='text-sm text-gray-600'>The same token you used in Facebook webhook setup</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Step 6 */}
        <div className='bg-white border border-gray-200 rounded-lg p-6'>
          <div className='flex items-start space-x-4'>
            <div className='flex-shrink-0 w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-medium'>
              6
            </div>
            <div className='flex-1'>
              <h3 className='text-lg font-medium text-gray-900 mb-3'>Test Your Integration</h3>

              <div className='space-y-4'>
                <div>
                  <p className='text-sm text-gray-700 mb-3'>After saving your configuration:</p>

                  <ol className='text-sm text-gray-700 space-y-2 list-decimal list-inside'>
                    <li>Go to your Facebook Page</li>
                    <li>Send a message to your page from a different Facebook account</li>
                    <li>Check the "Conversations" tab to see if the message appears</li>
                    <li>Reply from the system to test two-way communication</li>
                  </ol>
                </div>

                <div className='bg-green-50 border border-green-200 rounded-lg p-4'>
                  <div className='flex items-start space-x-3'>
                    <CheckCircle className='w-5 h-5 text-green-600 mt-0.5 flex-shrink-0' />
                    <div>
                      <h4 className='text-sm font-medium text-green-800'>Success!</h4>
                      <p className='text-sm text-green-700 mt-1'>
                        If messages appear in both directions, your Facebook Messenger integration is working correctly.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* App Review Process */}
      <div className='bg-orange-50 border border-orange-200 rounded-lg p-6'>
        <h3 className='text-lg font-medium text-gray-900 mb-4'>Facebook App Review (For Production)</h3>

        <div className='space-y-4'>
          <div>
            <p className='text-sm text-gray-700'>
              For production use, you'll need to submit your app for Facebook review to access advanced messaging
              features:
            </p>
            <ul className='text-sm text-gray-700 mt-2 space-y-1 list-disc list-inside ml-4'>
              <li>
                <strong>pages_messaging</strong> - Required for sending messages
              </li>
              <li>
                <strong>pages_manage_metadata</strong> - For page information access
              </li>
              <li>
                <strong>pages_read_engagement</strong> - For reading page interactions
              </li>
            </ul>
          </div>

          <div>
            <p className='text-sm text-gray-700'>
              During development, you can test with admin, developer, and tester roles without app review.
            </p>
          </div>
        </div>
      </div>

      {/* Troubleshooting */}
      <div className='bg-gray-50 border border-gray-200 rounded-lg p-6'>
        <h3 className='text-lg font-medium text-gray-900 mb-4'>Troubleshooting</h3>

        <div className='space-y-4'>
          <div>
            <h4 className='text-sm font-medium text-gray-900'>Webhook verification failed?</h4>
            <ul className='text-sm text-gray-700 mt-1 space-y-1 list-disc list-inside ml-4'>
              <li>Ensure your webhook URL is accessible via HTTPS</li>
              <li>Verify the verify token matches exactly</li>
              <li>Check that your domain has a valid SSL certificate</li>
              <li>Make sure the webhook endpoint is responding correctly</li>
            </ul>
          </div>

          <div>
            <h4 className='text-sm font-medium text-gray-900'>Not receiving messages?</h4>
            <ul className='text-sm text-gray-700 mt-1 space-y-1 list-disc list-inside ml-4'>
              <li>Verify your Page Access Token is valid and has correct permissions</li>
              <li>Check that webhook subscription fields include "messages"</li>
              <li>Ensure your app is connected to the correct Facebook Page</li>
              <li>Review Facebook webhook logs for errors</li>
            </ul>
          </div>

          <div>
            <h4 className='text-sm font-medium text-gray-900'>Can't send messages?</h4>
            <ul className='text-sm text-gray-700 mt-1 space-y-1 list-disc list-inside ml-4'>
              <li>Check that the user has initiated conversation with your page</li>
              <li>Verify your Page Access Token permissions</li>
              <li>Ensure your app has the necessary Facebook permissions</li>
              <li>Review system logs for API errors</li>
            </ul>
          </div>

          <div>
            <h4 className='text-sm font-medium text-gray-900'>Need help?</h4>
            <p className='text-sm text-gray-700 mt-1'>
              Check the system logs, Facebook's webhook debugger, or contact your administrator for additional support.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FacebookInstructions
