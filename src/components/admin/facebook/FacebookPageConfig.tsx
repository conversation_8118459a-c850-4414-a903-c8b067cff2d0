// Facebook Page Configuration Component
// Admin interface for managing Facebook Messenger settings per domain

/* eslint-disable react/no-unescaped-entities */
'use client'

import React, { useState, useEffect } from 'react'

import { Save, Eye, EyeOff, AlertCircle, CheckCircle, MessageSquare } from 'lucide-react'

import <PERSON>lap<PERSON><PERSON><PERSON><PERSON>, {
  HelpStep,
  ExternalLinkButton,
  InfoBox,
  HelpSection
} from '@/components/common/CollapsibleHelp'

interface PageConfig {
  config_uuid?: string
  domain_uuid: string
  app_id: string
  page_id: string
  webhook_url?: string | null
  is_active: boolean
  allowed_events: string[]
  page_settings: Record<string, any>
  configured: boolean
  insert_date?: string
  update_date?: string
}

interface FacebookPageConfigProps {
  className?: string
}

interface FormData {
  app_id: string
  app_secret: string
  page_id: string
  page_access_token: string
  verify_token: string
  webhook_url: string
  allowed_events: string[]
}

const FacebookPageConfig: React.FC<FacebookPageConfigProps> = ({ className = '' }) => {
  const [config, setConfig] = useState<PageConfig | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  const [showSecrets, setShowSecrets] = useState({
    app_secret: false,
    page_access_token: false,
    verify_token: false
  })

  const [formData, setFormData] = useState<FormData>({
    app_id: '',
    app_secret: '',
    page_id: '',
    page_access_token: '',
    verify_token: '',
    webhook_url: '',
    allowed_events: ['messages', 'messaging_postbacks', 'messaging_optins']
  })

  // Load existing configuration
  const loadConfig = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/internal-chat/facebook/page-config')

      if (response.status === 404) {
        // No configuration exists yet
        setConfig(null)

        return
      }

      if (!response.ok) {
        throw new Error('Failed to fetch page configuration')
      }

      const data = await response.json()

      setConfig(data.config)

      // Update form data with existing config
      if (data.config) {
        setFormData({
          app_id: data.config.app_id || '',
          app_secret: '', // Never populate secrets for security
          page_id: data.config.page_id || '',
          page_access_token: '',
          verify_token: '',
          webhook_url: data.config.webhook_url || '',
          allowed_events: data.config.allowed_events || ['messages', 'messaging_postbacks', 'messaging_optins']
        })
      }
    } catch (err) {
      console.error('Error loading config:', err)
      setError(err instanceof Error ? err.message : 'Failed to load configuration')
    } finally {
      setLoading(false)
    }
  }

  // Save configuration
  const handleSave = async () => {
    setSaving(true)
    setError(null)
    setSuccess(null)

    try {
      const payload: any = {
        app_id: formData.app_id.trim(),
        page_id: formData.page_id.trim(),
        webhook_url: formData.webhook_url.trim() || undefined,
        allowed_events: formData.allowed_events
      }

      // Only include secrets if they're provided
      if (formData.app_secret.trim()) {
        payload.app_secret = formData.app_secret.trim()
      }

      if (formData.page_access_token.trim()) {
        payload.page_access_token = formData.page_access_token.trim()
      }

      if (formData.verify_token.trim()) {
        payload.verify_token = formData.verify_token.trim()
      }

      const response = await fetch('/api/internal-chat/facebook/page-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      if (!response.ok) {
        const errorData = await response.json()

        throw new Error(errorData.error || 'Failed to save configuration')
      }

      const result = await response.json()

      setConfig(result.config)
      setSuccess('Configuration saved successfully!')

      // Clear sensitive fields
      setFormData(prev => ({
        ...prev,
        app_secret: '',
        page_access_token: '',
        verify_token: ''
      }))
    } catch (err) {
      console.error('Error saving config:', err)
      setError(err instanceof Error ? err.message : 'Failed to save configuration')
    } finally {
      setSaving(false)
    }
  }

  // Load config on mount
  useEffect(() => {
    loadConfig()
  }, [])

  const toggleSecretVisibility = (field: keyof typeof showSecrets) => {
    setShowSecrets(prev => ({
      ...prev,
      [field]: !prev[field]
    }))
  }

  if (loading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4'></div>
          <p className='text-gray-600'>Loading configuration...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className='p-6 border-b border-gray-200'>
        <div className='flex items-center space-x-3'>
          <div className='p-2 bg-blue-100 rounded-lg'>
            <MessageSquare className='w-6 h-6 text-blue-600' />
          </div>
          <div>
            <h2 className='text-xl font-semibold text-gray-900'>Facebook Messenger Configuration</h2>
            <p className='text-sm text-gray-600'>Configure Facebook app and page settings for your domain</p>
          </div>
        </div>
      </div>

      {/* Setup Instructions */}
      <div className='p-6 border-b border-gray-200'>
        <CollapsibleHelp title='Facebook Messenger Setup Instructions' variant='compact'>
          <div className='space-y-6'>
            <InfoBox type='info' title="What You'll Need">
              <ul className='text-sm space-y-1 mt-2'>
                <li>A Facebook account with developer access</li>
                <li>A Facebook Page for your business</li>
                <li>A public domain for webhook (HTTPS required)</li>
                <li>Facebook App with Messenger product enabled</li>
              </ul>
            </InfoBox>

            <HelpStep stepNumber={1} title='Create a Facebook App' color='blue'>
              <p className='text-sm text-gray-700 mb-2'>Go to the Facebook Developers Console and create a new app.</p>
              <ExternalLinkButton href='https://developers.facebook.com/apps/' color='blue'>
                Open Facebook Developers
              </ExternalLinkButton>
              <div className='mt-3'>
                <p className='text-sm text-gray-700'>
                  <strong>Steps:</strong>
                </p>
                <ol className='text-sm text-gray-700 mt-2 space-y-1 list-decimal list-inside ml-4'>
                  <li>Click &quot;Create App&quot;</li>
                  <li>Select &quot;Business&quot; as the app type</li>
                  <li>Fill in your app details</li>
                  <li>Complete the security check</li>
                </ol>
              </div>
            </HelpStep>

            <HelpStep stepNumber={2} title='Add Messenger Product' color='blue'>
              <p className='text-sm text-gray-700 mb-2'>In your Facebook App dashboard:</p>
              <ol className='text-sm text-gray-700 space-y-1 list-decimal list-inside ml-4'>
                <li>Go to "Add Products to Your App"</li>
                <li>Find "Messenger" and click "Set Up"</li>
                <li>This will add Messenger to your app's product list</li>
              </ol>
              <InfoBox type='warning' title='Important'>
                Make sure you have a Facebook Page created before proceeding. You'll need to connect your app to a
                specific page.
              </InfoBox>
            </HelpStep>

            <HelpStep stepNumber={3} title='Generate Page Access Token' color='blue'>
              <p className='text-sm text-gray-700 mb-2'>In the Messenger settings:</p>
              <ol className='text-sm text-gray-700 space-y-1 list-decimal list-inside ml-4'>
                <li>Go to "Messenger" → "Settings"</li>
                <li>Find "Access Tokens" section</li>
                <li>Click "Add or Remove Pages"</li>
                <li>Select your Facebook Page and grant permissions</li>
                <li>Copy the generated Page Access Token</li>
              </ol>
            </HelpStep>

            <HelpStep stepNumber={4} title='Configure Integration' color='blue'>
              <p className='text-sm text-gray-700 mb-2'>Enter your Facebook details in the form below:</p>
              <ul className='text-sm text-gray-700 space-y-1 list-disc list-inside ml-4'>
                <li>
                  <strong>App ID:</strong> Found in App Settings → Basic
                </li>
                <li>
                  <strong>App Secret:</strong> Found in App Settings → Basic (click "Show")
                </li>
                <li>
                  <strong>Page ID:</strong> Found in your Facebook Page settings
                </li>
                <li>
                  <strong>Page Access Token:</strong> Generated in Step 3
                </li>
                <li>
                  <strong>Verify Token:</strong> Create a secure random string
                </li>
              </ul>
            </HelpStep>

            <HelpSection title='Troubleshooting'>
              <div className='space-y-3'>
                <div>
                  <h4 className='text-sm font-medium text-gray-900'>Webhook verification failed?</h4>
                  <ul className='text-sm text-gray-700 mt-1 space-y-1 list-disc list-inside ml-4'>
                    <li>Ensure your webhook URL is accessible via HTTPS</li>
                    <li>Verify the verify token matches exactly</li>
                    <li>Check that your domain has a valid SSL certificate</li>
                  </ul>
                </div>
                <div>
                  <h4 className='text-sm font-medium text-gray-900'>Not receiving messages?</h4>
                  <ul className='text-sm text-gray-700 mt-1 space-y-1 list-disc list-inside ml-4'>
                    <li>Verify your Page Access Token is valid</li>
                    <li>Check webhook subscription fields include "messages"</li>
                    <li>Ensure your app is connected to the correct Facebook Page</li>
                  </ul>
                </div>
              </div>
            </HelpSection>
          </div>
        </CollapsibleHelp>
      </div>

      {/* Content */}
      <div className='p-6'>
        {/* Status Messages */}
        {error && (
          <div className='bg-red-50 border border-red-200 rounded-lg p-4 flex items-start space-x-3'>
            <AlertCircle className='w-5 h-5 text-red-600 mt-0.5 flex-shrink-0' />
            <div>
              <h4 className='text-sm font-medium text-red-800'>Configuration Error</h4>
              <p className='text-sm text-red-700 mt-1'>{error}</p>
            </div>
          </div>
        )}

        {success && (
          <div className='bg-green-50 border border-green-200 rounded-lg p-4 flex items-start space-x-3'>
            <CheckCircle className='w-5 h-5 text-green-600 mt-0.5 flex-shrink-0' />
            <div>
              <h4 className='text-sm font-medium text-green-800'>Success</h4>
              <p className='text-sm text-green-700 mt-1'>{success}</p>
            </div>
          </div>
        )}

        {/* Current Configuration Status */}
        {config && (
          <div className='bg-blue-50 border border-blue-200 rounded-lg p-4'>
            <h4 className='text-sm font-medium text-blue-900 mb-2'>Current Configuration</h4>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-3 text-sm'>
              <div>
                <span className='text-blue-700 font-medium'>App ID:</span>
                <span className='text-blue-800 ml-2'>{config.app_id}</span>
              </div>
              <div>
                <span className='text-blue-700 font-medium'>Page ID:</span>
                <span className='text-blue-800 ml-2'>{config.page_id}</span>
              </div>
              <div>
                <span className='text-blue-700 font-medium'>Status:</span>
                <span
                  className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${
                    config.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}
                >
                  {config.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>
              <div>
                <span className='text-blue-700 font-medium'>Last Updated:</span>
                <span className='text-blue-800 ml-2'>
                  {config.update_date ? new Date(config.update_date).toLocaleDateString() : 'Never'}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Configuration Form */}
        <div className='bg-white border border-gray-200 rounded-lg p-6'>
          <h3 className='text-lg font-medium text-gray-900 mb-4'>
            {config ? 'Update Configuration' : 'Setup Facebook Integration'}
          </h3>

          <div className='space-y-4'>
            {/* App ID */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-1'>Facebook App ID *</label>
              <input
                type='text'
                value={formData.app_id}
                onChange={e => setFormData(prev => ({ ...prev, app_id: e.target.value }))}
                className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                placeholder='Enter your Facebook App ID'
                required
              />
              <p className='text-xs text-gray-500 mt-1'>Found in your Facebook App dashboard</p>
            </div>

            {/* App Secret */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-1'>
                Facebook App Secret {!config && '*'}
              </label>
              <div className='relative'>
                <input
                  type={showSecrets.app_secret ? 'text' : 'password'}
                  value={formData.app_secret}
                  onChange={e => setFormData(prev => ({ ...prev, app_secret: e.target.value }))}
                  className='w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                  placeholder={config ? 'Leave empty to keep current secret' : 'Enter your Facebook App Secret'}
                  required={!config}
                />
                <button
                  type='button'
                  onClick={() => toggleSecretVisibility('app_secret')}
                  className='absolute inset-y-0 right-0 pr-3 flex items-center'
                >
                  {showSecrets.app_secret ? (
                    <EyeOff className='h-4 w-4 text-gray-400' />
                  ) : (
                    <Eye className='h-4 w-4 text-gray-400' />
                  )}
                </button>
              </div>
              <p className='text-xs text-gray-500 mt-1'>Found in your Facebook App dashboard (keep this secure)</p>
            </div>

            {/* Page ID */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-1'>Facebook Page ID *</label>
              <input
                type='text'
                value={formData.page_id}
                onChange={e => setFormData(prev => ({ ...prev, page_id: e.target.value }))}
                className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                placeholder='Enter your Facebook Page ID'
                required
              />
              <p className='text-xs text-gray-500 mt-1'>Found in your Facebook Page settings</p>
            </div>

            {/* Page Access Token */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-1'>Page Access Token {!config && '*'}</label>
              <div className='relative'>
                <input
                  type={showSecrets.page_access_token ? 'text' : 'password'}
                  value={formData.page_access_token}
                  onChange={e => setFormData(prev => ({ ...prev, page_access_token: e.target.value }))}
                  className='w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                  placeholder={config ? 'Leave empty to keep current token' : 'Enter your Page Access Token'}
                  required={!config}
                />
                <button
                  type='button'
                  onClick={() => toggleSecretVisibility('page_access_token')}
                  className='absolute inset-y-0 right-0 pr-3 flex items-center'
                >
                  {showSecrets.page_access_token ? (
                    <EyeOff className='h-4 w-4 text-gray-400' />
                  ) : (
                    <Eye className='h-4 w-4 text-gray-400' />
                  )}
                </button>
              </div>
              <p className='text-xs text-gray-500 mt-1'>Generate from Facebook App → Messenger → Settings</p>
            </div>

            {/* Verify Token */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-1'>
                Webhook Verify Token {!config && '*'}
              </label>
              <div className='relative'>
                <input
                  type={showSecrets.verify_token ? 'text' : 'password'}
                  value={formData.verify_token}
                  onChange={e => setFormData(prev => ({ ...prev, verify_token: e.target.value }))}
                  className='w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                  placeholder={config ? 'Leave empty to keep current token' : 'Enter a secure verify token'}
                  required={!config}
                />
                <button
                  type='button'
                  onClick={() => toggleSecretVisibility('verify_token')}
                  className='absolute inset-y-0 right-0 pr-3 flex items-center'
                >
                  {showSecrets.verify_token ? (
                    <EyeOff className='h-4 w-4 text-gray-400' />
                  ) : (
                    <Eye className='h-4 w-4 text-gray-400' />
                  )}
                </button>
              </div>
              <p className='text-xs text-gray-500 mt-1'>Create a secure token for webhook verification</p>
            </div>

            {/* Webhook URL */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-1'>Webhook URL</label>
              <input
                type='url'
                value={formData.webhook_url}
                onChange={e => setFormData(prev => ({ ...prev, webhook_url: e.target.value }))}
                className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                placeholder='Auto-generated if left empty'
              />
              <p className='text-xs text-gray-500 mt-1'>Leave empty to use auto-generated webhook URL</p>
            </div>

            {/* Allowed Events */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-2'>Allowed Events</label>
              <div className='space-y-2'>
                {[
                  { value: 'messages', label: 'Messages' },
                  { value: 'messaging_postbacks', label: 'Postbacks' },
                  { value: 'messaging_optins', label: 'Opt-ins' },
                  { value: 'messaging_deliveries', label: 'Delivery Confirmations' },
                  { value: 'messaging_reads', label: 'Read Receipts' }
                ].map(event => (
                  <label key={event.value} className='flex items-center'>
                    <input
                      type='checkbox'
                      checked={formData.allowed_events.includes(event.value)}
                      onChange={e => {
                        if (e.target.checked) {
                          setFormData(prev => ({
                            ...prev,
                            allowed_events: [...prev.allowed_events, event.value]
                          }))
                        } else {
                          setFormData(prev => ({
                            ...prev,
                            allowed_events: prev.allowed_events.filter(ev => ev !== event.value)
                          }))
                        }
                      }}
                      className='rounded border-gray-300 text-blue-600 focus:ring-blue-500'
                    />
                    <span className='ml-2 text-sm text-gray-700'>{event.label}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className='mt-6 flex justify-end'>
            <button
              onClick={handleSave}
              disabled={saving || !formData.app_id.trim() || !formData.page_id.trim()}
              className='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed'
            >
              {saving ? (
                <>
                  <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2'></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className='w-4 h-4 mr-2' />
                  {config ? 'Update Configuration' : 'Save Configuration'}
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FacebookPageConfig
