// ZALO OA Configuration Component
// Admin interface for managing ZALO Official Account settings per domain

/* eslint-disable react/no-unescaped-entities */
'use client'

import React, { useState, useEffect } from 'react'

import { Save, Eye, EyeOff, AlertCircle, CheckCircle, Smartphone, RefreshCw } from 'lucide-react'

import CollapsibleHelp, {
  HelpStep,
  ExternalLinkButton,
  InfoBox,
  HelpSection
} from '@/components/common/CollapsibleHelp'

interface OaConfig {
  config_uuid?: string
  domain_uuid: string
  app_id: string
  oa_id: string
  webhook_url?: string | null
  is_active: boolean

  allowed_events: string[]
  oa_settings: Record<string, any>
  configured: boolean
  insert_date?: string
  update_date?: string
  token_expired?: boolean
}

interface ZaloOaConfigProps {
  className?: string
}

interface FormData {
  app_id: string
  app_secret: string
  oa_id: string
  access_token: string
  refresh_token: string
  allowed_events: string[]
}

const ZaloOaConfig: React.FC<ZaloOaConfigProps> = ({ className = '' }) => {
  const [config, setConfig] = useState<OaConfig | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [refreshingToken, setRefreshingToken] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  const [showSecrets, setShowSecrets] = useState({
    app_secret: false,
    access_token: false,
    refresh_token: false
  })



  const [formData, setFormData] = useState<FormData>({
    app_id: '',
    app_secret: '',
    oa_id: '',
    access_token: '',
    refresh_token: '',
    allowed_events: ['user_send_text', 'user_send_image', 'user_send_file']
  })

  // Load existing configuration
  const loadConfig = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/internal-chat/zalo/oa-config')

      if (response.status === 404) {
        // No configuration exists yet
        setConfig(null)

        return
      }

      if (!response.ok) {
        throw new Error('Failed to fetch OA configuration')
      }

      const data = await response.json()

      setConfig(data.config)

      // Update form data with existing config
      if (data.config) {
        setFormData({
          app_id: data.config.app_id || '',
          app_secret: '', // Never populate secrets for security
          oa_id: data.config.oa_id || '',
          access_token: '',
          refresh_token: '',
          allowed_events: data.config.allowed_events || ['user_send_text', 'user_send_image', 'user_send_file']
        })
      }
    } catch (err) {
      console.error('Error loading config:', err)
      setError(err instanceof Error ? err.message : 'Failed to load configuration')
    } finally {
      setLoading(false)
    }
  }

  // Save configuration
  const handleSave = async () => {
    setSaving(true)
    setError(null)
    setSuccess(null)

    try {
      const payload: any = {
        app_id: formData.app_id.trim(),
        oa_id: formData.oa_id.trim(),
        allowed_events: formData.allowed_events
      }

      // Only include secrets if they're provided
      if (formData.app_secret.trim()) {
        payload.app_secret = formData.app_secret.trim()
      }

      if (formData.access_token.trim()) {
        payload.access_token = formData.access_token.trim()
      }

      if (formData.refresh_token.trim()) {
        payload.refresh_token = formData.refresh_token.trim()
      }

      const response = await fetch('/api/internal-chat/zalo/oa-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      })

      if (!response.ok) {
        const errorData = await response.json()

        throw new Error(errorData.error || 'Failed to save configuration')
      }

      const result = await response.json()

      setConfig(result.config)
      setSuccess('Configuration saved successfully!')

      // Clear sensitive fields
      setFormData(prev => ({
        ...prev,
        app_secret: '',
        access_token: '',
        refresh_token: ''
      }))
    } catch (err) {
      console.error('Error saving config:', err)
      setError(err instanceof Error ? err.message : 'Failed to save configuration')
    } finally {
      setSaving(false)
    }
  }

  // Refresh access token using centralized token service
  const handleRefreshToken = async () => {
    if (!config) return

    setRefreshingToken(true)
    setError(null)
    setSuccess(null)

    try {
      // Use the API endpoint which internally uses ZaloTokenService
      const response = await fetch('/api/internal-chat/zalo/oa-config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          refresh_token_now: true
        })
      })

      if (!response.ok) {
        const errorData = await response.json()

        throw new Error(errorData.error || 'Failed to refresh token')
      }

      const result = await response.json()

      setConfig(result.config)
      setSuccess('Access token refreshed successfully!')

      // Reload configuration to get updated token info
      await loadConfig()
    } catch (err) {
      console.error('Error refreshing token:', err)
      setError(err instanceof Error ? err.message : 'Failed to refresh token')
    } finally {
      setRefreshingToken(false)
    }
  }







  // Load config on mount
  useEffect(() => {
    loadConfig()
  }, [])



  const toggleSecretVisibility = (field: keyof typeof showSecrets) => {
    setShowSecrets(prev => ({
      ...prev,
      [field]: !prev[field]
    }))
  }

  if (loading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4'></div>
          <p className='text-gray-600'>Loading configuration...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className='p-6 border-b border-gray-200'>
        <div className='flex items-center space-x-3'>
          <div className='p-2 bg-green-100 rounded-lg'>
            <Smartphone className='w-6 h-6 text-green-600' />
          </div>
          <div>
            <h2 className='text-xl font-semibold text-gray-900'>ZALO Official Account Configuration</h2>
            <p className='text-sm text-gray-600'>Configure ZALO OA settings for your domain</p>
          </div>
        </div>
      </div>

      {/* Setup Instructions */}
      <div className='p-6 border-b border-gray-200'>
        <CollapsibleHelp title='ZALO Official Account Setup Instructions' variant='compact'>
          <div className='space-y-6'>
            <InfoBox type='info' title="What You'll Need">
              <ul className='text-sm space-y-1 mt-2'>
                <li>A ZALO account with developer access</li>
                <li>A ZALO Official Account (OA) for your business</li>
                <li>A public domain for webhook (HTTPS required)</li>
                <li>ZALO App with OA API access</li>
              </ul>
            </InfoBox>

            <HelpStep stepNumber={1} title='Create ZALO Official Account' color='green'>
              <p className='text-sm text-gray-700 mb-2'>First, create a ZALO Official Account for your business.</p>
              <ExternalLinkButton href='https://oa.zalo.me/' color='green'>
                Open ZALO OA Portal
              </ExternalLinkButton>
              <div className='mt-3'>
                <p className='text-sm text-gray-700'>
                  <strong>Steps:</strong>
                </p>
                <ol className='text-sm text-gray-700 mt-2 space-y-1 list-decimal list-inside ml-4'>
                  <li>Sign up or log in with your ZALO account</li>
                  <li>Click "Create Official Account"</li>
                  <li>Fill in your business information</li>
                  <li>Upload required documents for verification</li>
                  <li>Wait for approval (usually 1-3 business days)</li>
                </ol>
              </div>
            </HelpStep>

            <HelpStep stepNumber={2} title='Create ZALO App' color='green'>
              <p className='text-sm text-gray-700 mb-2'>Create a ZALO App to access the API.</p>
              <ExternalLinkButton href='https://developers.zalo.me/' color='green'>
                Open ZALO Developers
              </ExternalLinkButton>
              <div className='mt-3'>
                <ol className='text-sm text-gray-700 space-y-1 list-decimal list-inside ml-4'>
                  <li>Click &quot;Create App&quot;</li>
                  <li>Choose &quot;Official Account API&quot; as the app type</li>
                  <li>Fill in your app details</li>
                  <li>Link your Official Account to the app</li>
                  <li>Complete the app verification process</li>
                </ol>
              </div>
              <InfoBox type='warning' title='Important'>
                Your Official Account must be approved before you can create an app. Make sure to complete the OA
                verification process first.
              </InfoBox>
            </HelpStep>

            <HelpStep stepNumber={3} title='Configure Webhook URL' color='green'>
              <p className='text-sm text-gray-700 mb-2'>Set up webhook URL in your ZALO App dashboard:</p>
              <ol className='text-sm text-gray-700 space-y-1 list-decimal list-inside ml-4'>
                <li>Go to your ZALO App dashboard</li>
                <li>Navigate to "Official Account API" → "Webhook Settings"</li>
                <li>Add this webhook URL: <code className='bg-gray-100 px-1 rounded text-xs'>{typeof window !== 'undefined' ? `${window.location.origin}/api/internal-chat/zalo/webhook?domain=your_domain_uuid` : 'https://yourdomain.com/api/internal-chat/zalo/webhook?domain=your_domain_uuid'}</code></li>
                <li>Replace "your_domain_uuid" with your actual domain UUID</li>
                <li>Save the configuration</li>
              </ol>
              <InfoBox type='warning' title='Important'>
                The webhook URL must be accessible via HTTPS and respond to ZALO's verification requests.
              </InfoBox>
            </HelpStep>

            <HelpStep stepNumber={4} title='Get API Credentials' color='green'>
              <p className='text-sm text-gray-700 mb-2'>In your ZALO App dashboard:</p>
              <ol className='text-sm text-gray-700 space-y-1 list-decimal list-inside ml-4'>
                <li>Go to "App Information" section</li>
                <li>Copy your App ID and App Secret</li>
                <li>Go to "Official Account API" section</li>
                <li>Note down your OA ID (Official Account ID)</li>
              </ol>
            </HelpStep>

            <HelpStep stepNumber={5} title='Configure Integration' color='green'>
              <p className='text-sm text-gray-700 mb-2'>Enter your ZALO details in the form below:</p>
              <ul className='text-sm text-gray-700 space-y-1 list-disc list-inside ml-4'>
                <li>
                  <strong>App ID:</strong> From your ZALO App dashboard
                </li>
                <li>
                  <strong>App Secret:</strong> From your ZALO App dashboard (keep secure)
                </li>
                <li>
                  <strong>OA ID:</strong> Your Official Account ID
                </li>
                <li>
                  <strong>Access Token:</strong> Generate from ZALO OA → Settings → API
                </li>
                <li>
                  <strong>Refresh Token:</strong> Optional, for automatic token renewal
                </li>
                <li>
                  <strong>Webhook Secret:</strong> Optional, for webhook signature verification
                </li>
              </ul>
              <InfoBox type='info' title='Token Management'>
                <p className='text-sm'>
                  Access tokens expire after 25 hours. You can manually generate new tokens from your ZALO OA dashboard,
                  or provide a refresh token for automatic renewal.
                </p>
              </InfoBox>
            </HelpStep>

            <InfoBox type='info' title='Token Management'>
              <div className='space-y-2'>
                <p className='text-sm'>ZALO access tokens have expiration dates. When your token expires:</p>
                <ol className='text-sm space-y-1 list-decimal list-inside ml-4'>
                  <li>You'll see a "Token Expired" warning in the Overview tab</li>
                  <li>Click "Refresh Token" if you have a refresh token</li>
                  <li>Or manually generate a new access token from ZALO Developers</li>
                </ol>
              </div>
            </InfoBox>

            <HelpSection title='Troubleshooting'>
              <div className='space-y-3'>
                <div>
                  <h4 className='text-sm font-medium text-gray-900'>Invalid Access Token Error?</h4>
                  <ul className='text-sm text-gray-700 mt-1 space-y-1 list-disc list-inside ml-4'>
                    <li>Generate a new access token from ZALO OA → Settings → API</li>
                    <li>Ensure your App ID and App Secret are correct</li>
                    <li>Check that your app is approved and active in ZALO Developer Console</li>
                    <li>Verify your Official Account is linked to the app</li>
                  </ul>
                </div>
                <div>
                  <h4 className='text-sm font-medium text-gray-900'>Webhook Signature Verification Failed?</h4>
                  <ul className='text-sm text-gray-700 mt-1 space-y-1 list-disc list-inside ml-4'>
                    <li>Ensure your App Secret is correct (used for signature verification)</li>
                    <li>Check that the webhook URL is accessible via HTTPS</li>
                    <li>Verify the webhook secret matches what's configured in ZALO</li>
                  </ul>
                </div>
                <div>
                  <h4 className='text-sm font-medium text-gray-900'>OA not receiving messages?</h4>
                  <ul className='text-sm text-gray-700 mt-1 space-y-1 list-disc list-inside ml-4'>
                    <li>Verify your webhook URL is accessible via HTTPS</li>
                    <li>Check that your access token is valid and not expired</li>
                    <li>Ensure your app is properly linked to your Official Account</li>
                  </ul>
                </div>
                <div>
                  <h4 className='text-sm font-medium text-gray-900'>Can't send messages?</h4>
                  <ul className='text-sm text-gray-700 mt-1 space-y-1 list-disc list-inside ml-4'>
                    <li>Check that the user has followed your Official Account</li>
                    <li>Verify your access token has send message permissions</li>
                    <li>Ensure your OA is approved and active</li>
                  </ul>
                </div>
                <div>
                  <h4 className='text-sm font-medium text-gray-900'>Token expired issues?</h4>
                  <ul className='text-sm text-gray-700 mt-1 space-y-1 list-disc list-inside ml-4'>
                    <li>Generate a new access token from ZALO Developers</li>
                    <li>Set up refresh token for automatic renewal</li>
                    <li>Monitor token expiry dates in the system</li>
                  </ul>
                </div>
              </div>
            </HelpSection>
          </div>
        </CollapsibleHelp>
      </div>

      {/* Content */}
      <div className='p-6'>
        {/* Status Messages */}
        {error && (
          <div className='bg-red-50 border border-red-200 rounded-lg p-4 flex items-start space-x-3'>
            <AlertCircle className='w-5 h-5 text-red-600 mt-0.5 flex-shrink-0' />
            <div>
              <h4 className='text-sm font-medium text-red-800'>Configuration Error</h4>
              <p className='text-sm text-red-700 mt-1'>{error}</p>
            </div>
          </div>
        )}

        {success && (
          <div className='bg-green-50 border border-green-200 rounded-lg p-4 flex items-start space-x-3'>
            <CheckCircle className='w-5 h-5 text-green-600 mt-0.5 flex-shrink-0' />
            <div>
              <h4 className='text-sm font-medium text-green-800'>Success</h4>
              <p className='text-sm text-green-700 mt-1'>{success}</p>
            </div>
          </div>
        )}

        {/* Current Configuration Status */}
        {config && (
          <div className='bg-green-50 border border-green-200 rounded-lg p-4'>
            <div className='flex items-center justify-between mb-2'>
              <h4 className='text-sm font-medium text-green-900'>Current Configuration</h4>
              {config.token_expired && (
                <button
                  onClick={handleRefreshToken}
                  disabled={refreshingToken}
                  className='inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50'
                >
                  {refreshingToken ? (
                    <>
                      <div className='animate-spin rounded-full h-3 w-3 border-b-2 border-green-600 mr-1'></div>
                      Refreshing...
                    </>
                  ) : (
                    <>
                      <RefreshCw className='w-3 h-3 mr-1' />
                      Refresh Token
                    </>
                  )}
                </button>
              )}
            </div>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-3 text-sm'>
              <div>
                <span className='text-green-700 font-medium'>App ID:</span>
                <span className='text-green-800 ml-2'>{config.app_id}</span>
              </div>
              <div>
                <span className='text-green-700 font-medium'>OA ID:</span>
                <span className='text-green-800 ml-2'>{config.oa_id}</span>
              </div>
              <div>
                <span className='text-green-700 font-medium'>Status:</span>
                <span
                  className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${
                    config.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}
                >
                  {config.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>
              <div>
                <span className='text-green-700 font-medium'>Token Status:</span>
                <span
                  className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${
                    config.token_expired ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                  }`}
                >
                  {config.token_expired ? 'Expired' : 'Valid'}
                </span>
              </div>

            </div>
          </div>
        )}

        {/* Configuration Form */}
        <div className='bg-white border border-gray-200 rounded-lg p-6'>
          <h3 className='text-lg font-medium text-gray-900 mb-4'>
            {config ? 'Update Configuration' : 'Setup ZALO Integration'}
          </h3>



          <div className='space-y-4'>
            {/* App ID */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-1'>ZALO App ID *</label>
              <input
                type='text'
                value={formData.app_id}
                onChange={e => setFormData(prev => ({ ...prev, app_id: e.target.value }))}
                className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent'
                placeholder='Enter your ZALO App ID'
                required
              />
              <p className='text-xs text-gray-500 mt-1'>Found in your ZALO App dashboard</p>
            </div>

            {/* App Secret */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-1'>ZALO App Secret {!config && '*'}</label>
              <div className='relative'>
                <input
                  type={showSecrets.app_secret ? 'text' : 'password'}
                  value={formData.app_secret}
                  onChange={e => setFormData(prev => ({ ...prev, app_secret: e.target.value }))}
                  className='w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent'
                  placeholder={config ? 'Leave empty to keep current secret' : 'Enter your ZALO App Secret'}
                  required={!config}
                />
                <button
                  type='button'
                  onClick={() => toggleSecretVisibility('app_secret')}
                  className='absolute inset-y-0 right-0 pr-3 flex items-center'
                >
                  {showSecrets.app_secret ? (
                    <EyeOff className='h-4 w-4 text-gray-400' />
                  ) : (
                    <Eye className='h-4 w-4 text-gray-400' />
                  )}
                </button>
              </div>
              <p className='text-xs text-gray-500 mt-1'>Found in your ZALO App dashboard (keep this secure)</p>
            </div>

            {/* OA ID */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-1'>Official Account ID *</label>
              <input
                type='text'
                value={formData.oa_id}
                onChange={e => setFormData(prev => ({ ...prev, oa_id: e.target.value }))}
                className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent'
                placeholder='Enter your ZALO OA ID'
                required
              />
              <p className='text-xs text-gray-500 mt-1'>Found in your ZALO Official Account settings</p>
            </div>

            {/* Access Token */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-1'>Access Token {!config && '*'}</label>
              <div className='relative'>
                <input
                  type={showSecrets.access_token ? 'text' : 'password'}
                  value={formData.access_token}
                  onChange={e => setFormData(prev => ({ ...prev, access_token: e.target.value }))}
                  className='w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent'
                  placeholder={config ? 'Leave empty to keep current token' : 'Enter your Access Token'}
                  required={!config}
                />
                <button
                  type='button'
                  onClick={() => toggleSecretVisibility('access_token')}
                  className='absolute inset-y-0 right-0 pr-3 flex items-center'
                >
                  {showSecrets.access_token ? (
                    <EyeOff className='h-4 w-4 text-gray-400' />
                  ) : (
                    <Eye className='h-4 w-4 text-gray-400' />
                  )}
                </button>
              </div>
              <p className='text-xs text-gray-500 mt-1'>Generate from ZALO OA → Settings → API</p>


            </div>

            {/* Refresh Token */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-1'>Refresh Token</label>
              <div className='relative'>
                <input
                  type={showSecrets.refresh_token ? 'text' : 'password'}
                  value={formData.refresh_token}
                  onChange={e => setFormData(prev => ({ ...prev, refresh_token: e.target.value }))}
                  className='w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent'
                  placeholder='Refresh token (optional)'
                />
                <button
                  type='button'
                  onClick={() => toggleSecretVisibility('refresh_token')}
                  className='absolute inset-y-0 right-0 pr-3 flex items-center'
                >
                  {showSecrets.refresh_token ? (
                    <EyeOff className='h-4 w-4 text-gray-400' />
                  ) : (
                    <Eye className='h-4 w-4 text-gray-400' />
                  )}
                </button>
              </div>
              <p className='text-xs text-gray-500 mt-1'>Used for automatic token renewal (obtained with access token)</p>
            </div>

            {/* Save Button */}
            <div className='mt-6 flex justify-end'>
              <button
                onClick={handleSave}
                disabled={saving || !formData.app_id.trim() || !formData.oa_id.trim()}
                className='inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed'
              >
                {saving ? (
                  <>
                    <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2'></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className='w-4 h-4 mr-2' />
                    {config ? 'Update Configuration' : 'Save Configuration'}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ZaloOaConfig
