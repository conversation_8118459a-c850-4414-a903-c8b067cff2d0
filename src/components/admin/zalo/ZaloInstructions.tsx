// ZALO Setup Instructions Component
// Comprehensive guide for setting up ZALO Official Account integration

/* eslint-disable react/no-unescaped-entities */
'use client'

import React from 'react'

import { Smartphone, ExternalLink, Copy, CheckCircle, AlertCircle, Info, RefreshCw } from 'lucide-react'

interface ZaloInstructionsProps {
  className?: string
}

const ZaloInstructions: React.FC<ZaloInstructionsProps> = ({ className = '' }) => {
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const webhookUrl = `${process.env.NEXT_PUBLIC_API_URL || 'https://yourdomain.com'}/api/internal-chat/zalo/webhook`

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className='flex items-center space-x-3'>
        <div className='p-2 bg-green-100 rounded-lg'>
          <Smartphone className='w-6 h-6 text-green-600' />
        </div>
        <div>
          <h2 className='text-xl font-semibold text-gray-900'>ZALO Official Account Setup Instructions</h2>
          <p className='text-gray-600'>Complete guide to configure ZALO OA integration</p>
        </div>
      </div>

      {/* Overview */}
      <div className='bg-green-50 border border-green-200 rounded-lg p-6'>
        <div className='flex items-start space-x-3'>
          <Info className='w-5 h-5 text-green-600 mt-0.5 flex-shrink-0' />
          <div>
            <h3 className='text-sm font-medium text-green-900'>What You'll Need</h3>
            <ul className='text-sm text-green-800 mt-2 space-y-1'>
              <li>A ZALO account with developer access</li>
              <li>A ZALO Official Account (OA) for your business</li>
              <li>Admin access to this system</li>
              <li>A public domain for webhook (HTTPS required)</li>
              <li>ZALO App with OA API access</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Step-by-step Instructions */}
      <div className='space-y-6'>
        {/* Step 1 */}
        <div className='bg-white border border-gray-200 rounded-lg p-6'>
          <div className='flex items-start space-x-4'>
            <div className='flex-shrink-0 w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-medium'>
              1
            </div>
            <div className='flex-1'>
              <h3 className='text-lg font-medium text-gray-900 mb-3'>Create ZALO Official Account</h3>

              <div className='space-y-4'>
                <div>
                  <p className='text-sm text-gray-700 mb-2'>First, create a ZALO Official Account for your business.</p>
                  <a
                    href='https://oa.zalo.me/'
                    target='_blank'
                    rel='noopener noreferrer'
                    className='inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500'
                  >
                    <ExternalLink className='w-4 h-4 mr-2' />
                    Open ZALO OA Portal
                  </a>
                </div>

                <div>
                  <p className='text-sm text-gray-700'>
                    <strong>Steps:</strong>
                  </p>
                  <ol className='text-sm text-gray-700 mt-2 space-y-1 list-decimal list-inside ml-4'>
                    <li>Sign up or log in with your ZALO account</li>
                    <li>Click "Create Official Account"</li>
                    <li>Fill in your business information</li>
                    <li>Upload required documents for verification</li>
                    <li>Wait for approval (usually 1-3 business days)</li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Step 2 */}
        <div className='bg-white border border-gray-200 rounded-lg p-6'>
          <div className='flex items-start space-x-4'>
            <div className='flex-shrink-0 w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-medium'>
              2
            </div>
            <div className='flex-1'>
              <h3 className='text-lg font-medium text-gray-900 mb-3'>Create ZALO App</h3>

              <div className='space-y-4'>
                <div>
                  <p className='text-sm text-gray-700 mb-2'>Create a ZALO App to access the API.</p>
                  <a
                    href='https://developers.zalo.me/'
                    target='_blank'
                    rel='noopener noreferrer'
                    className='inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500'
                  >
                    <ExternalLink className='w-4 h-4 mr-2' />
                    Open ZALO Developers
                  </a>
                </div>

                <div>
                  <p className='text-sm text-gray-700'>In the ZALO Developers Console:</p>
                  <ol className='text-sm text-gray-700 mt-2 space-y-1 list-decimal list-inside ml-4'>
                    <li>Click "Create App"</li>
                    <li>Choose "Official Account API" as the app type</li>
                    <li>Fill in your app details (name, description, etc.)</li>
                    <li>Link your Official Account to the app</li>
                    <li>Complete the app verification process</li>
                  </ol>
                </div>

                <div className='bg-yellow-50 border border-yellow-200 rounded-lg p-4'>
                  <div className='flex items-start space-x-3'>
                    <AlertCircle className='w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0' />
                    <div>
                      <h4 className='text-sm font-medium text-yellow-800'>Important</h4>
                      <p className='text-sm text-yellow-700 mt-1'>
                        Your Official Account must be approved before you can create an app. Make sure to complete the
                        OA verification process first.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Step 3 */}
        <div className='bg-white border border-gray-200 rounded-lg p-6'>
          <div className='flex items-start space-x-4'>
            <div className='flex-shrink-0 w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-medium'>
              3
            </div>
            <div className='flex-1'>
              <h3 className='text-lg font-medium text-gray-900 mb-3'>Get API Credentials</h3>

              <div className='space-y-4'>
                <div>
                  <p className='text-sm text-gray-700'>In your ZALO App dashboard:</p>
                  <ol className='text-sm text-gray-700 mt-2 space-y-1 list-decimal list-inside ml-4'>
                    <li>Go to "App Information" section</li>
                    <li>Copy your App ID and App Secret</li>
                    <li>Go to "Official Account API" section</li>
                    <li>Generate or copy your Access Token</li>
                    <li>Note down your OA ID (Official Account ID)</li>
                  </ol>
                </div>

                <div>
                  <p className='text-sm text-gray-700'>Your credentials will look like this:</p>
                  <div className='space-y-2 mt-2'>
                    <div>
                      <p className='text-xs font-medium text-gray-900'>App ID:</p>
                      <div className='bg-gray-100 rounded-md p-2 font-mono text-sm'>1234567890123456789</div>
                    </div>
                    <div>
                      <p className='text-xs font-medium text-gray-900'>OA ID:</p>
                      <div className='bg-gray-100 rounded-md p-2 font-mono text-sm'>9876543210987654321</div>
                    </div>
                    <div>
                      <p className='text-xs font-medium text-gray-900'>Access Token:</p>
                      <div className='bg-gray-100 rounded-md p-2 font-mono text-sm'>
                        abcdef123456789abcdef123456789abcdef123456789...
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Step 4 */}
        <div className='bg-white border border-gray-200 rounded-lg p-6'>
          <div className='flex items-start space-x-4'>
            <div className='flex-shrink-0 w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-medium'>
              4
            </div>
            <div className='flex-1'>
              <h3 className='text-lg font-medium text-gray-900 mb-3'>Configure Webhook</h3>

              <div className='space-y-4'>
                <div>
                  <p className='text-sm text-gray-700 mb-3'>Set up webhook to receive messages from ZALO:</p>

                  <div className='space-y-3'>
                    <div>
                      <p className='text-sm font-medium text-gray-900'>Webhook URL:</p>
                      <div className='bg-gray-100 rounded-md p-3 font-mono text-sm flex items-center justify-between mt-1'>
                        <span className='truncate'>{webhookUrl}</span>
                        <button
                          onClick={() => copyToClipboard(webhookUrl)}
                          className='text-gray-500 hover:text-gray-700 ml-2'
                        >
                          <Copy className='w-4 h-4' />
                        </button>
                      </div>
                    </div>

                    <div>
                      <p className='text-sm font-medium text-gray-900'>Webhook Secret (Optional):</p>
                      <p className='text-sm text-gray-600'>Create a secure random string for webhook verification</p>
                      <div className='bg-gray-100 rounded-md p-3 font-mono text-sm mt-1'>
                        my_secure_zalo_webhook_secret_123
                      </div>
                    </div>

                    <div>
                      <p className='text-sm font-medium text-gray-900'>Event Types:</p>
                      <p className='text-sm text-gray-600'>Subscribe to these events:</p>
                      <ul className='text-sm text-gray-700 mt-1 space-y-1 list-disc list-inside ml-4'>
                        <li>user_send_text</li>
                        <li>user_send_image</li>
                        <li>user_send_file</li>
                        <li>user_send_audio</li>
                        <li>user_send_video</li>
                        <li>follow/unfollow (optional)</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Step 5 */}
        <div className='bg-white border border-gray-200 rounded-lg p-6'>
          <div className='flex items-start space-x-4'>
            <div className='flex-shrink-0 w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-medium'>
              5
            </div>
            <div className='flex-1'>
              <h3 className='text-lg font-medium text-gray-900 mb-3'>Configure Integration in System</h3>

              <div className='space-y-4'>
                <div>
                  <p className='text-sm text-gray-700 mb-3'>
                    Go to the <strong>Configuration</strong> tab and enter your ZALO details:
                  </p>

                  <div className='space-y-3'>
                    <div>
                      <p className='text-sm font-medium text-gray-900'>App ID (Required):</p>
                      <p className='text-sm text-gray-600'>From your ZALO App dashboard</p>
                    </div>

                    <div>
                      <p className='text-sm font-medium text-gray-900'>App Secret (Required):</p>
                      <p className='text-sm text-gray-600'>From your ZALO App dashboard (keep secure)</p>
                    </div>

                    <div>
                      <p className='text-sm font-medium text-gray-900'>OA ID (Required):</p>
                      <p className='text-sm text-gray-600'>Your Official Account ID</p>
                    </div>

                    <div>
                      <p className='text-sm font-medium text-gray-900'>Access Token (Required):</p>
                      <p className='text-sm text-gray-600'>Generated from your ZALO App</p>
                    </div>

                    <div>
                      <p className='text-sm font-medium text-gray-900'>Refresh Token (Optional):</p>
                      <p className='text-sm text-gray-600'>For automatic token refresh</p>
                    </div>

                    <div>
                      <p className='text-sm font-medium text-gray-900'>Token Expires At (Optional):</p>
                      <p className='text-sm text-gray-600'>When your access token expires</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Step 6 */}
        <div className='bg-white border border-gray-200 rounded-lg p-6'>
          <div className='flex items-start space-x-4'>
            <div className='flex-shrink-0 w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm font-medium'>
              6
            </div>
            <div className='flex-1'>
              <h3 className='text-lg font-medium text-gray-900 mb-3'>Test Your Integration</h3>

              <div className='space-y-4'>
                <div>
                  <p className='text-sm text-gray-700 mb-3'>After saving your configuration:</p>

                  <ol className='text-sm text-gray-700 space-y-2 list-decimal list-inside'>
                    <li>Find your Official Account on ZALO</li>
                    <li>Follow your OA and send a test message</li>
                    <li>Check the "Conversations" tab to see if the message appears</li>
                    <li>Reply from the system to test two-way communication</li>
                  </ol>
                </div>

                <div className='bg-green-50 border border-green-200 rounded-lg p-4'>
                  <div className='flex items-start space-x-3'>
                    <CheckCircle className='w-5 h-5 text-green-600 mt-0.5 flex-shrink-0' />
                    <div>
                      <h4 className='text-sm font-medium text-green-800'>Success!</h4>
                      <p className='text-sm text-green-700 mt-1'>
                        If messages appear in both directions, your ZALO integration is working correctly.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Token Management */}
      <div className='bg-blue-50 border border-blue-200 rounded-lg p-6'>
        <div className='flex items-start space-x-3'>
          <RefreshCw className='w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0' />
          <div>
            <h3 className='text-lg font-medium text-gray-900 mb-3'>Token Management</h3>

            <div className='space-y-3'>
              <div>
                <p className='text-sm text-gray-700'>
                  ZALO access tokens have expiration dates. When your token expires:
                </p>
                <ol className='text-sm text-gray-700 mt-2 space-y-1 list-decimal list-inside ml-4'>
                  <li>You'll see a "Token Expired" warning in the Overview tab</li>
                  <li>Go to the Configuration tab</li>
                  <li>Click "Refresh Token" if you have a refresh token</li>
                  <li>Or manually generate a new access token from ZALO Developers</li>
                </ol>
              </div>

              <div>
                <p className='text-sm text-gray-700'>
                  <strong>Pro tip:</strong> Set up refresh tokens to automatically renew access tokens before they
                  expire.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Troubleshooting */}
      <div className='bg-gray-50 border border-gray-200 rounded-lg p-6'>
        <h3 className='text-lg font-medium text-gray-900 mb-4'>Troubleshooting</h3>

        <div className='space-y-4'>
          <div>
            <h4 className='text-sm font-medium text-gray-900'>OA not receiving messages?</h4>
            <ul className='text-sm text-gray-700 mt-1 space-y-1 list-disc list-inside ml-4'>
              <li>Verify your webhook URL is accessible via HTTPS</li>
              <li>Check that your access token is valid and not expired</li>
              <li>Ensure your app is properly linked to your Official Account</li>
              <li>Verify webhook event subscriptions are configured</li>
            </ul>
          </div>

          <div>
            <h4 className='text-sm font-medium text-gray-900'>Can't send messages?</h4>
            <ul className='text-sm text-gray-700 mt-1 space-y-1 list-disc list-inside ml-4'>
              <li>Check that the user has followed your Official Account</li>
              <li>Verify your access token has send message permissions</li>
              <li>Ensure your OA is approved and active</li>
              <li>Review ZALO API rate limits and quotas</li>
            </ul>
          </div>

          <div>
            <h4 className='text-sm font-medium text-gray-900'>Token expired issues?</h4>
            <ul className='text-sm text-gray-700 mt-1 space-y-1 list-disc list-inside ml-4'>
              <li>Generate a new access token from ZALO Developers</li>
              <li>Set up refresh token for automatic renewal</li>
              <li>Monitor token expiry dates in the system</li>
              <li>Update token expiry information when refreshing</li>
            </ul>
          </div>

          <div>
            <h4 className='text-sm font-medium text-gray-900'>Need help?</h4>
            <p className='text-sm text-gray-700 mt-1'>
              Check ZALO's official documentation, system logs, or contact your administrator for additional support.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ZaloInstructions
