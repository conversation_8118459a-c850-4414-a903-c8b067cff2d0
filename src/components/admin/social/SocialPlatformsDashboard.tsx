// Social Platforms Dashboard
// Unified dashboard for managing all social platform integrations

'use client'

import React, { useState } from 'react'

import { Send, MessageCircle, Globe, MessageSquare, Smartphone } from 'lucide-react'

import TelegramDashboard from '../telegram/TelegramDashboard'
import FacebookDashboard from '../facebook/FacebookDashboard'
import ZaloDashboard from '../zalo/ZaloDashboard'

interface SocialPlatformsDashboardProps {
  className?: string
}

const SocialPlatformsDashboard: React.FC<SocialPlatformsDashboardProps> = ({ className = '' }) => {
  const [activePlatform, setActivePlatform] = useState<'telegram' | 'zalo' | 'facebook' | 'overview'>('overview')

  const platforms = [
    {
      key: 'telegram',
      name: 'Telegram',
      icon: Send,
      color: 'blue',
      description: 'Configure Telegram bot integration',
      status: 'available'
    },
    {
      key: 'facebook',
      name: 'Facebook Messenger',
      icon: MessageSquare,
      color: 'blue',
      description: 'Configure Facebook Messenger integration',
      status: 'available'
    },
    {
      key: 'zalo',
      name: 'ZALO',
      icon: Smartphone,
      color: 'green',
      description: 'Configure ZALO OA integration',
      status: 'available'
    }
  ]

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Platform Overview */}
      {activePlatform === 'overview' && (
        <>
          {/* Header */}
          <div className='bg-white rounded-lg shadow-sm border border-gray-200 p-6'>
            <div className='flex items-center space-x-3 mb-4'>
              <div className='p-2 bg-blue-100 rounded-lg'>
                <Globe className='w-6 h-6 text-blue-600' />
              </div>
              <div>
                <h2 className='text-xl font-semibold text-gray-900'>Social Platform Integrations</h2>
                <p className='text-gray-600'>Manage integrations with social messaging platforms</p>
              </div>
            </div>
          </div>

          {/* Platform Cards */}
          <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            {platforms.map(platform => {
              const IconComponent = platform.icon
              const isAvailable = platform.status === 'available'

              return (
                <div
                  key={platform.key}
                  className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 transition-all ${
                    isAvailable ? 'hover:shadow-md cursor-pointer' : 'opacity-60'
                  }`}
                  onClick={() => isAvailable && setActivePlatform(platform.key as any)}
                >
                  <div className='flex items-start justify-between'>
                    <div className='flex items-center space-x-3'>
                      <div className={`p-3 bg-${platform.color}-100 rounded-lg`}>
                        <IconComponent className={`w-6 h-6 text-${platform.color}-600`} />
                      </div>
                      <div>
                        <h3 className='text-lg font-semibold text-gray-900'>{platform.name}</h3>
                        <p className='text-sm text-gray-600 mt-1'>{platform.description}</p>
                      </div>
                    </div>

                    <div className='flex flex-col items-end space-y-2'>
                      <span
                        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          platform.status === 'available'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}
                      >
                        {platform.status === 'available' ? 'Available' : 'Coming Soon'}
                      </span>

                      {isAvailable && (
                        <button className='bg-inherit text-blue-600 hover:text-blue-700 text-sm font-medium'>
                          Configure →
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Quick Setup Guide */}
                  {platform.key === 'telegram' && isAvailable && (
                    <div className='mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg'>
                      <h4 className='text-sm font-medium text-blue-900 mb-2'>Quick Setup:</h4>
                      <ol className='text-xs text-blue-800 space-y-1 px-4'>
                        <li>Message @BotFather on Telegram → /newbot</li>
                        <li>Copy bot token and configure below</li>
                        <li>Start receiving messages in Internal Chat</li>
                      </ol>
                    </div>
                  )}

                  {platform.key === 'facebook' && isAvailable && (
                    <div className='mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg'>
                      <h4 className='text-sm font-medium text-blue-900 mb-2'>Quick Setup:</h4>
                      <ol className='text-xs text-blue-800 space-y-1 px-4'>
                        <li>Create Facebook App in Developers Console</li>
                        <li>Add Messenger product and get Page Access Token</li>
                        <li>Configure webhook and start receiving messages</li>
                      </ol>
                    </div>
                  )}

                  {platform.key === 'zalo' && isAvailable && (
                    <div className='mt-4 p-3 bg-green-50 border border-green-200 rounded-lg'>
                      <h4 className='text-sm font-medium text-green-900 mb-2'>Quick Setup:</h4>
                      <ol className='text-xs text-green-800 space-y-1 px-4'>
                        <li>Create ZALO App and Official Account</li>
                        <li>Generate access tokens for your OA</li>
                        <li>Configure webhook and start receiving messages</li>
                      </ol>
                    </div>
                  )}
                </div>
              )
            })}
          </div>

          {/* Integration Benefits */}
          <div className='bg-white rounded-lg shadow-sm border border-gray-200 p-6'>
            <h3 className='text-lg font-semibold text-gray-900 mb-4'>Benefits of Social Platform Integration</h3>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
              <div className='text-center'>
                <div className='w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3'>
                  <MessageCircle className='w-6 h-6 text-blue-600' />
                </div>
                <h4 className='font-medium text-gray-900 mb-2'>Unified Messaging</h4>
                <p className='text-sm text-gray-600'>
                  Handle all customer conversations from different platforms in one interface
                </p>
              </div>
              <div className='text-center'>
                <div className='w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3'>
                  <Send className='w-6 h-6 text-green-600' />
                </div>
                <h4 className='font-medium text-gray-900 mb-2'>Real-time Sync</h4>
                <p className='text-sm text-gray-600'>
                  Messages sync instantly between platforms and your internal chat system
                </p>
              </div>
              <div className='text-center'>
                <div className='w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3'>
                  <Globe className='w-6 h-6 text-purple-600' />
                </div>
                <h4 className='font-medium text-gray-900 mb-2'>Multi-Platform</h4>
                <p className='text-sm text-gray-600'>
                  Support for multiple social platforms with consistent user experience
                </p>
              </div>
            </div>
          </div>
        </>
      )}

      {/* Platform-specific Configuration */}
      {activePlatform === 'telegram' && (
        <div>
          <div className='mb-4'>
            <button
              onClick={() => setActivePlatform('overview')}
              className='bg-inherit text-blue-600 hover:text-blue-700 text-sm font-medium'
            >
              ← Back to Social Platforms
            </button>
          </div>
          <TelegramDashboard />
        </div>
      )}

      {activePlatform === 'facebook' && (
        <div>
          <div className='mb-4'>
            <button
              onClick={() => setActivePlatform('overview')}
              className='bg-inherit text-blue-600 hover:text-blue-700 text-sm font-medium'
            >
              ← Back to Social Platforms
            </button>
          </div>
          <FacebookDashboard />
        </div>
      )}

      {activePlatform === 'zalo' && (
        <div>
          <div className='mb-4'>
            <button
              onClick={() => setActivePlatform('overview')}
              className='bg-inherit text-blue-600 hover:text-blue-700 text-sm font-medium'
            >
              ← Back to Social Platforms
            </button>
          </div>
          <ZaloDashboard />
        </div>
      )}
    </div>
  )
}

export default SocialPlatformsDashboard
