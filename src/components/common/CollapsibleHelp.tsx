// Collapsible Help Component
// Reusable component for showing/hiding help content in configuration forms

'use client'

import React, { useState } from 'react'

import { ChevronDown, ChevronUp, HelpCircle, Copy, ExternalLink } from 'lucide-react'

interface CollapsibleHelpProps {
  title?: string
  children: React.ReactNode
  defaultOpen?: boolean
  className?: string
  variant?: 'default' | 'compact'
}

interface HelpStepProps {
  stepNumber: number
  title: string
  children: React.ReactNode
  color?: 'blue' | 'green' | 'purple'
}

interface CopyableCodeProps {
  code: string
  label?: string
}

interface ExternalLinkButtonProps {
  href: string
  children: React.ReactNode
  color?: 'blue' | 'green' | 'purple'
}

// Main collapsible help component
const CollapsibleHelp: React.FC<CollapsibleHelpProps> = ({
  title = 'Setup Instructions',
  children,
  defaultOpen = false,
  className = '',
  variant = 'default'
}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen)

  const toggleOpen = () => setIsOpen(!isOpen)

  if (variant === 'compact') {
    return (
      <div className={`border border-gray-200 rounded-lg ${className}`}>
        <button
          onClick={toggleOpen}
          className='w-full px-4 py-3 flex items-center justify-between text-left hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset rounded-lg'
        >
          <div className='flex items-center space-x-2'>
            <HelpCircle className='w-4 h-4 text-gray-500' />
            <span className='text-sm font-medium text-gray-700'>{title}</span>
          </div>
          {isOpen ? <ChevronUp className='w-4 h-4 text-gray-500' /> : <ChevronDown className='w-4 h-4 text-gray-500' />}
        </button>

        {isOpen && (
          <div className='px-4 pb-4 border-t border-gray-200 bg-gray-50'>
            <div className='pt-4'>{children}</div>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg ${className}`}>
      <button
        onClick={toggleOpen}
        className='w-full px-6 py-4 flex items-center justify-between text-left hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset rounded-lg'
      >
        <div className='flex items-center space-x-3'>
          <HelpCircle className='w-5 h-5 text-blue-600' />
          <span className='text-lg font-medium text-blue-900'>{title}</span>
        </div>
        {isOpen ? <ChevronUp className='w-5 h-5 text-blue-600' /> : <ChevronDown className='w-5 h-5 text-blue-600' />}
      </button>

      {isOpen && (
        <div className='px-6 pb-6 border-t border-blue-200'>
          <div className='pt-6'>{children}</div>
        </div>
      )}
    </div>
  )
}

// Help step component for numbered instructions
export const HelpStep: React.FC<HelpStepProps> = ({ stepNumber, title, children, color = 'blue' }) => {
  const colorClasses = {
    blue: 'bg-blue-100 text-blue-600',
    green: 'bg-green-100 text-green-600',
    purple: 'bg-purple-100 text-purple-600'
  }

  return (
    <div className='flex items-start space-x-4 mb-6'>
      <div
        className={`flex-shrink-0 w-8 h-8 ${colorClasses[color]} rounded-full flex items-center justify-center text-sm font-medium`}
      >
        {stepNumber}
      </div>
      <div className='flex-1'>
        <h3 className='text-lg font-medium text-gray-900 mb-3'>{title}</h3>
        <div className='space-y-3'>{children}</div>
      </div>
    </div>
  )
}

// Copyable code component
export const CopyableCode: React.FC<CopyableCodeProps> = ({ code, label }) => {
  const copyToClipboard = () => {
    navigator.clipboard.writeText(code)
  }

  return (
    <div>
      {label && <p className='text-sm font-medium text-gray-900 mb-1'>{label}</p>}
      <div className='bg-gray-100 rounded-md p-3 font-mono text-sm flex items-center justify-between'>
        <span className='truncate'>{code}</span>
        <button
          onClick={copyToClipboard}
          className='text-gray-500 hover:text-gray-700 ml-2 flex-shrink-0'
          title='Copy to clipboard'
        >
          <Copy className='w-4 h-4' />
        </button>
      </div>
    </div>
  )
}

// External link button component
export const ExternalLinkButton: React.FC<ExternalLinkButtonProps> = ({ href, children, color = 'blue' }) => {
  const colorClasses = {
    blue: 'text-blue-700 bg-blue-100 hover:bg-blue-200 focus:ring-blue-500',
    green: 'text-green-700 bg-green-100 hover:bg-green-200 focus:ring-green-500',
    purple: 'text-purple-700 bg-purple-100 hover:bg-purple-200 focus:ring-purple-500'
  }

  return (
    <a
      href={href}
      target='_blank'
      rel='noopener noreferrer'
      className={`inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md ${colorClasses[color]} focus:outline-none focus:ring-2 focus:ring-offset-2`}
    >
      <ExternalLink className='w-4 h-4 mr-2' />
      {children}
    </a>
  )
}

// Info box component for warnings and notes
export const InfoBox: React.FC<{
  type: 'warning' | 'info' | 'success'
  title: string
  children: React.ReactNode
}> = ({ type, title, children }) => {
  const typeClasses = {
    warning: {
      container: 'bg-yellow-50 border-yellow-200',
      icon: 'text-yellow-600',
      title: 'text-yellow-800',
      text: 'text-yellow-700'
    },
    info: {
      container: 'bg-blue-50 border-blue-200',
      icon: 'text-blue-600',
      title: 'text-blue-800',
      text: 'text-blue-700'
    },
    success: {
      container: 'bg-green-50 border-green-200',
      icon: 'text-green-600',
      title: 'text-green-800',
      text: 'text-green-700'
    }
  }

  const classes = typeClasses[type]

  return (
    <div className={`${classes.container} border rounded-lg p-4`}>
      <div className='flex items-start space-x-3'>
        <HelpCircle className={`w-5 h-5 ${classes.icon} mt-0.5 flex-shrink-0`} />
        <div>
          <h4 className={`text-sm font-medium ${classes.title}`}>{title}</h4>
          <div className={`text-sm ${classes.text} mt-1`}>{children}</div>
        </div>
      </div>
    </div>
  )
}

// Help section component for grouping related content
export const HelpSection: React.FC<{
  title: string
  children: React.ReactNode
}> = ({ title, children }) => {
  return (
    <div className='mb-6'>
      <h3 className='text-lg font-medium text-gray-900 mb-4'>{title}</h3>
      <div className='space-y-4'>{children}</div>
    </div>
  )
}

export default CollapsibleHelp
