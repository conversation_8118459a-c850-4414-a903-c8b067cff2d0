'use client'

// React Imports
import { useEffect, useState } from 'react'

const useMediaQuery = (breakpoint?: string): boolean => {
  // Initialize state based on breakpoint
  const [matches, setMatches] = useState(() => {
    if (typeof window === 'undefined') return false
    if (breakpoint === 'always') return true
    if (!breakpoint) return false

    return window.matchMedia(`(max-width: ${breakpoint})`).matches
  })

  useEffect(() => {
    if (!breakpoint || breakpoint === 'always' || typeof window === 'undefined') {
      return
    }

    const media = window.matchMedia(`(max-width: ${breakpoint})`)

    // Set initial state
    setMatches(media.matches)

    const listener = (event: MediaQueryListEvent) => {
      setMatches(event.matches)
    }

    // Use the modern addEventListener API
    media.addEventListener('change', listener)

    return () => {
      media.removeEventListener('change', listener)
    }
  }, [breakpoint])

  return matches
}

export default useMediaQuery
