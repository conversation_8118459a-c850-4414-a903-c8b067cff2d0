SELECT
  'zalo' :: text AS platform,
  v_zalo_oa_chat_rooms.zalo_room_uuid AS platform_room_uuid,
  v_zalo_oa_chat_rooms.domain_uuid,
  v_zalo_oa_chat_rooms.internal_room_uuid,
  v_zalo_oa_chat_rooms.assigned_agent_uuid,
  v_zalo_oa_chat_rooms.room_status,
  v_zalo_oa_chat_rooms.last_message_at,
  v_zalo_oa_chat_rooms.insert_date,
  v_zalo_oa_chat_rooms.update_date
FROM
  v_zalo_oa_chat_rooms
UNION
ALL
SELECT
  'telegram' :: text AS platform,
  v_telegram_chat_rooms.telegram_room_uuid AS platform_room_uuid,
  v_telegram_chat_rooms.domain_uuid,
  v_telegram_chat_rooms.internal_room_uuid,
  v_telegram_chat_rooms.assigned_agent_uuid,
  v_telegram_chat_rooms.room_status,
  v_telegram_chat_rooms.last_message_at,
  v_telegram_chat_rooms.insert_date,
  v_telegram_chat_rooms.update_date
FROM
  v_telegram_chat_rooms;