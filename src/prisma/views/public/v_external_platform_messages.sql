SELECT
  'zalo' :: text AS platform,
  v_zalo_message_mapping.mapping_uuid,
  v_zalo_message_mapping.domain_uuid,
  v_zalo_message_mapping.internal_message_id,
  v_zalo_message_mapping.zalo_message_id AS external_message_id,
  v_zalo_message_mapping.zalo_user_id AS external_user_id,
  v_zalo_message_mapping.message_direction,
  v_zalo_message_mapping.delivery_status,
  v_zalo_message_mapping.error_message,
  v_zalo_message_mapping.insert_date
FROM
  v_zalo_message_mapping
UNION
ALL
SELECT
  'telegram' :: text AS platform,
  v_telegram_message_mapping.mapping_uuid,
  v_telegram_message_mapping.domain_uuid,
  v_telegram_message_mapping.internal_message_id,
  (v_telegram_message_mapping.telegram_message_id) :: text AS external_message_id,
  (v_telegram_message_mapping.telegram_user_id) :: text AS external_user_id,
  v_telegram_message_mapping.message_direction,
  v_telegram_message_mapping.delivery_status,
  v_telegram_message_mapping.error_message,
  v_telegram_message_mapping.insert_date
FROM
  v_telegram_message_mapping;