SELECT
  'zalo' :: text AS platform,
  v_zalo_oa_contacts.contact_uuid,
  v_zalo_oa_contacts.domain_uuid,
  v_zalo_oa_contacts.zalo_user_id AS external_user_id,
  v_zalo_oa_contacts.display_name,
  v_zalo_oa_contacts.avatar_url,
  v_zalo_oa_contacts.phone,
  v_zalo_oa_contacts.last_interaction_date,
  v_zalo_oa_contacts.insert_date,
  v_zalo_oa_contacts.update_date
FROM
  v_zalo_oa_contacts
UNION
ALL
SELECT
  'telegram' :: text AS platform,
  v_telegram_contacts.contact_uuid,
  v_telegram_contacts.domain_uuid,
  (v_telegram_contacts.telegram_user_id) :: text AS external_user_id,
  COALESCE(
    (
      (
        (v_telegram_contacts.first_name) :: text || ' ' :: text
      ) || (v_telegram_contacts.last_name) :: text
    ),
    (v_telegram_contacts.username) :: text,
    (v_telegram_contacts.first_name) :: text
  ) AS display_name,
  NULL :: text AS avatar_url,
  v_telegram_contacts.phone,
  v_telegram_contacts.last_interaction_date,
  v_telegram_contacts.insert_date,
  v_telegram_contacts.update_date
FROM
  v_telegram_contacts;