// Internal Chat Redux Slice
// Simplified state management following clean architecture principles

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import type { PayloadAction } from '@reduxjs/toolkit'

import type {
  ChatState,
  ChatRoom,
  ChatMessage,
  UserPresence,
  ChatNotification,
  CreateRoomRequest,
  SendMessageRequest,
  EditMessageRequest
} from '@/types/apps/internal-chat/chatTypes'

// =====================================================
// ASYNC THUNKS
// =====================================================

// Fetch user's chat rooms
export const fetchChatRooms = createAsyncThunk(
  'internalChat/fetchRooms',
  async (params: { type?: string; page?: number; limit?: number } = {}) => {
    const searchParams = new URLSearchParams()

    if (params.type) searchParams.append('type', params.type)
    if (params.page) searchParams.append('page', params.page.toString())
    if (params.limit) searchParams.append('limit', params.limit.toString())

    const response = await fetch(`/api/internal-chat/rooms?${searchParams}`)

    if (!response.ok) {
      throw new Error('Failed to fetch rooms')
    }

    return response.json()
  }
)

// Fetch individual room data
export const fetchChatRoom = createAsyncThunk('internalChat/fetchRoom', async (roomId: string) => {
  const response = await fetch(`/api/internal-chat/rooms/${roomId}`)

  if (!response.ok) {
    throw new Error('Failed to fetch room')
  }

  return response.json()
})

// Create new chat room
export const createChatRoom = createAsyncThunk('internalChat/createRoom', async (roomData: CreateRoomRequest) => {
  const response = await fetch('/api/internal-chat/rooms', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(roomData)
  })

  if (!response.ok) {
    throw new Error('Failed to create room')
  }

  return response.json()
})

// Fetch messages for a room
export const fetchRoomMessages = createAsyncThunk(
  'internalChat/fetchMessages',
  async (params: { roomId: string; before?: string; after?: string; limit?: number }) => {
    const searchParams = new URLSearchParams()

    if (params.before) searchParams.append('before', params.before)
    if (params.after) searchParams.append('after', params.after)
    if (params.limit) searchParams.append('limit', params.limit.toString())

    const response = await fetch(`/api/internal-chat/rooms/${params.roomId}/messages?${searchParams}`)

    if (!response.ok) {
      throw new Error('Failed to fetch messages')
    }

    const result = await response.json()

    return { roomId: params.roomId, ...result }
  }
)

// Send new message
export const sendMessage = createAsyncThunk(
  'internalChat/sendMessage',
  async (params: { roomId: string; messageData: SendMessageRequest }, { getState, dispatch }) => {
    const state = getState() as { internalChatReducer: ChatState }
    const { virtualRooms } = state.internalChatReducer

    let actualRoomId = params.roomId

    // Check if this is a virtual room (new conversation)
    if (virtualRooms[params.roomId]) {
      const virtualRoom = virtualRooms[params.roomId]

      // Create the actual room first
      const createRoomResponse = await fetch('/api/internal-chat/rooms', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          room_name: virtualRoom.user.display_name,
          room_type: 'direct',
          participant_uuids: [virtualRoom.user.user_uuid]
        })
      })

      if (!createRoomResponse.ok) {
        throw new Error('Failed to create room')
      }

      const createRoomResult = await createRoomResponse.json()

      if (!createRoomResult.success) {
        throw new Error('Failed to create room')
      }

      actualRoomId = createRoomResult.data.room_uuid

      // Remove the virtual room and add the real room
      dispatch(internalChatSlice.actions.removeVirtualRoom(params.roomId))
      dispatch(internalChatSlice.actions.addRoom(createRoomResult.data))
      dispatch(internalChatSlice.actions.setActiveRoom(actualRoomId))
    }

    // Send the message to the actual room
    const response = await fetch(`/api/internal-chat/rooms/${actualRoomId}/messages`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(params.messageData)
    })

    if (!response.ok) {
      throw new Error('Failed to send message')
    }

    const result = await response.json()

    return { roomId: actualRoomId, originalRoomId: params.roomId, ...result }
  }
)

// Mark messages as read
export const markMessagesAsRead = createAsyncThunk(
  'internalChat/markMessagesAsRead',
  async (params: { roomId: string; lastReadMessageId?: string }) => {
    const response = await fetch(`/api/internal-chat/rooms/${params.roomId}/read`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ last_read_message_id: params.lastReadMessageId })
    })

    if (!response.ok) {
      throw new Error('Failed to mark messages as read')
    }

    const result = await response.json()

    return { roomId: params.roomId, ...result }
  }
)

// Edit message
export const editMessage = createAsyncThunk(
  'internalChat/editMessage',
  async (params: { messageId: string; messageData: EditMessageRequest }) => {
    const response = await fetch(`/api/internal-chat/messages/${params.messageId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(params.messageData)
    })

    if (!response.ok) {
      throw new Error('Failed to edit message')
    }

    return response.json()
  }
)

// Delete message
export const deleteMessage = createAsyncThunk('internalChat/deleteMessage', async (messageId: string) => {
  const response = await fetch(`/api/internal-chat/messages/${messageId}`, {
    method: 'DELETE'
  })

  if (!response.ok) {
    throw new Error('Failed to delete message')
  }

  return { messageId }
})

// Delete chat room
export const deleteChatRoom = createAsyncThunk('internalChat/deleteRoom', async (roomId: string) => {
  const response = await fetch(`/api/internal-chat/rooms/${roomId}`, {
    method: 'DELETE'
  })

  if (!response.ok) {
    const errorData = await response.json()

    throw new Error(errorData.error || 'Failed to delete room')
  }

  return { roomId }
})

// =====================================================
// INITIAL STATE
// =====================================================

// =====================================================
// IMPROVED STATE STRUCTURE (Using existing ChatState but cleaner)
// =====================================================

const initialState: ChatState = {
  // Connection state (simplified)
  isConnected: false,
  connectionStatus: 'disconnected',

  // Current user
  currentUser: null,

  // Rooms (keep existing structure but cleaner)
  rooms: {},
  activeRoomId: null,
  roomsLoading: false,
  roomsInitialized: false,

  // Messages (keep existing structure)
  messages: {},
  messagesLoading: {},
  hasMoreMessages: {},

  // Users & Presence (simplified)
  domainUsers: {},
  userPresence: {},
  onlineUsers: [],

  // UI State (separated concerns)
  sidebarOpen: true,
  selectedThread: null,
  typingUsers: {},

  // Notifications (simplified)
  unreadCounts: {},
  notifications: [],
  totalUnreadCount: 0,

  // File uploads (keep minimal)
  uploadProgress: {},

  // Error state (simplified)
  error: null,

  // Virtual rooms (keep for backward compatibility)
  virtualRooms: {}
}

// =====================================================
// SLICE DEFINITION
// =====================================================

const internalChatSlice = createSlice({
  name: 'internalChat',
  initialState,
  reducers: {
    // Connection management
    setConnectionStatus: (state, action: PayloadAction<ChatState['connectionStatus']>) => {
      state.connectionStatus = action.payload
      state.isConnected = action.payload === 'connected'
    },

    // Current user
    setCurrentUser: (state, action: PayloadAction<any>) => {
      state.currentUser = action.payload
    },

    // Room management
    setActiveRoom: (state, action: PayloadAction<string | null>) => {
      state.activeRoomId = action.payload

      // Mark room as read when activated (local state update)
      // The actual API call should be made separately using markMessagesAsRead
      if (action.payload && state.unreadCounts[action.payload]) {
        state.unreadCounts[action.payload] = 0
        state.totalUnreadCount = Object.values(state.unreadCounts).reduce((sum, count) => sum + count, 0)
      }
    },

    // Virtual room management
    createVirtualRoom: (state, action: PayloadAction<{ roomId: string; user: any }>) => {
      const { roomId, user } = action.payload

      state.virtualRooms[roomId] = {
        user,
        created_at: new Date().toISOString()
      }

      // Initialize empty messages array for the virtual room
      state.messages[roomId] = []
    },

    removeVirtualRoom: (state, action: PayloadAction<string>) => {
      const roomId = action.payload

      delete state.virtualRooms[roomId]
    },

    addRoom: (state, action: PayloadAction<ChatRoom>) => {
      const existingRoom = state.rooms[action.payload.room_uuid]

      if (existingRoom) {
        // Room already exists, merge data preserving important fields
        const mergedRoom = { ...existingRoom, ...action.payload }

        // Preserve last_message if the new data doesn't have it but existing room does
        if (!action.payload.last_message && existingRoom.last_message) {
          mergedRoom.last_message = existingRoom.last_message
        }

        // Preserve participants if the new data doesn't have them but existing room does
        if (!action.payload.participants && existingRoom.participants) {
          mergedRoom.participants = existingRoom.participants
        }

        state.rooms[action.payload.room_uuid] = mergedRoom
      } else {
        // New room, add as-is
        state.rooms[action.payload.room_uuid] = action.payload
      }

      // Initialize unread count if not already set
      if (!state.unreadCounts[action.payload.room_uuid]) {
        state.unreadCounts[action.payload.room_uuid] = action.payload.unread_count || 0
        state.totalUnreadCount = Object.values(state.unreadCounts).reduce((sum, count) => sum + count, 0)
      }
    },

    updateRoom: (state, action: PayloadAction<ChatRoom>) => {
      if (state.rooms[action.payload.room_uuid]) {
        // Preserve existing participants and last_message if not provided in the update
        const existingRoom = state.rooms[action.payload.room_uuid]
        const updatedRoom = { ...existingRoom, ...action.payload }

        // If the update doesn't include participants, preserve the existing ones
        if (!action.payload.participants && existingRoom.participants) {
          updatedRoom.participants = existingRoom.participants
        }

        // If the update doesn't include last_message, preserve the existing one
        if (!action.payload.last_message && existingRoom.last_message) {
          updatedRoom.last_message = existingRoom.last_message
        }

        state.rooms[action.payload.room_uuid] = updatedRoom
      }
    },

    removeRoom: (state, action: PayloadAction<string>) => {
      delete state.rooms[action.payload]
      delete state.messages[action.payload]
      delete state.messagesLoading[action.payload]
      delete state.hasMoreMessages[action.payload]
      delete state.unreadCounts[action.payload]
      delete state.typingUsers[action.payload]

      if (state.activeRoomId === action.payload) {
        state.activeRoomId = null
      }
    },

    // Message management
    addMessage: (state, action: PayloadAction<{ roomId: string; message: ChatMessage }>) => {
      const { roomId, message } = action.payload

      if (!state.messages[roomId]) {
        state.messages[roomId] = []
      }

      // Check if message already exists by ID (most reliable)
      const existingIndex = state.messages[roomId].findIndex(m => m.message_id === message.message_id)

      if (existingIndex === -1) {
        // Enhanced duplicate detection for socket broadcast messages
        const isFromCurrentUser = message.author_uuid === state.currentUser?.id

        // More comprehensive duplicate detection
        const duplicateExists = state.messages[roomId].some(m => {
          // Check for exact ID match first
          if (m.message_id === message.message_id) {
            return true
          }

          // Check for content-based duplicates with stricter criteria
          const sameAuthor = m.author_uuid === message.author_uuid
          const sameContent = m.content === message.content && message.content && message.content.trim().length > 0
          const similarTime = Math.abs(m.created_at - message.created_at) < 5 // Within 5 seconds

          // For current user messages, be more strict about duplicates
          if (isFromCurrentUser) {
            const notOptimistic = !m.isOptimistic
            const notTempId = !m.message_id.startsWith('temp_')

            return sameAuthor && sameContent && similarTime && notOptimistic && notTempId
          }

          // For other users, check for exact content and timing duplicates
          return sameAuthor && sameContent && similarTime && m.message_id !== message.message_id
        })

        if (duplicateExists) {
          return
        }

        // Add the message
        state.messages[roomId].push(message)

        // Sort messages by created_at to maintain chronological order
        state.messages[roomId].sort((a, b) => a.created_at - b.created_at)

        // Update room's last activity - only if this message is newer than current last_message
        if (state.rooms[roomId]) {
          const currentLastMessage = state.rooms[roomId].last_message

          const shouldUpdateLastMessage =
            !currentLastMessage ||
            message.created_at > currentLastMessage.created_at ||
            (message.created_at === currentLastMessage.created_at && message.message_id > currentLastMessage.message_id)

          if (shouldUpdateLastMessage) {
            state.rooms[roomId] = {
              ...state.rooms[roomId],
              update_date: new Date().toISOString(),
              last_message: {
                message_id: message.message_id,
                room_uuid: message.room_uuid,
                author_uuid: message.author_uuid,
                content: message.content,
                message_type: message.message_type,
                reply_to: message.reply_to,
                edited_at: message.edited_at,
                created_at: message.created_at,
                flags: message.flags,
                author_name: message.author_name
              }
            }
          }
        }
      } else {
        // Update existing message if it's an optimistic message being confirmed
        const existingMessage = state.messages[roomId][existingIndex]

        if (existingMessage.isOptimistic && !message.isOptimistic) {
          state.messages[roomId][existingIndex] = { ...message, isOptimistic: false, isSending: false }

          // Update last_message if this was the most recent message
          if (state.rooms[roomId]) {
            const currentLastMessage = state.rooms[roomId].last_message

            if (currentLastMessage && currentLastMessage.message_id === existingMessage.message_id) {
              state.rooms[roomId].last_message = {
                message_id: message.message_id,
                room_uuid: message.room_uuid,
                author_uuid: message.author_uuid,
                content: message.content,
                message_type: message.message_type,
                reply_to: message.reply_to,
                edited_at: message.edited_at,
                created_at: message.created_at,
                flags: message.flags,
                author_name: message.author_name
              }
            }
          }
        }
      }
    },

    updateMessage: (state, action: PayloadAction<ChatMessage>) => {
      const message = action.payload
      const roomMessages = state.messages[message.room_uuid]

      if (roomMessages) {
        const index = roomMessages.findIndex(m => m.message_id === message.message_id)

        if (index !== -1) {
          roomMessages[index] = message
        }
      }
    },

    removeMessage: (state, action: PayloadAction<{ roomId: string; messageId: string }>) => {
      const { roomId, messageId } = action.payload
      const roomMessages = state.messages[roomId]

      if (roomMessages) {
        const index = roomMessages.findIndex(m => m.message_id === messageId)

        if (index !== -1) {
          roomMessages.splice(index, 1)
        }
      }
    },

    // Optimistic message updates
    addOptimisticMessage: (state, action: PayloadAction<{ roomId: string; message: ChatMessage }>) => {
      const { roomId, message } = action.payload

      if (!state.messages[roomId]) {
        state.messages[roomId] = []
      }

      state.messages[roomId].push({ ...message, isOptimistic: true, isSending: true })
    },

    confirmOptimisticMessage: (state, action: PayloadAction<{ tempId: string; message: ChatMessage }>) => {
      const { tempId, message } = action.payload
      const roomMessages = state.messages[message.room_uuid]

      if (roomMessages) {
        const index = roomMessages.findIndex(m => m.message_id === tempId)

        if (index !== -1) {
          roomMessages[index] = { ...message, isOptimistic: false, isSending: false }
        }
      }
    },

    failOptimisticMessage: (state, action: PayloadAction<{ tempId: string; error: string }>) => {
      const { tempId, error } = action.payload

      Object.values(state.messages).forEach(roomMessages => {
        const index = roomMessages.findIndex(m => m.message_id === tempId)

        if (index !== -1) {
          roomMessages[index] = {
            ...roomMessages[index],
            isOptimistic: false,
            isSending: false,
            sendError: error
          }
        }
      })
    },

    // Typing indicators
    addTypingUser: (state, action: PayloadAction<{ roomId: string; userId: string; userName: string }>) => {
      const { roomId, userId } = action.payload

      if (!state.typingUsers[roomId]) {
        state.typingUsers[roomId] = []
      }

      if (!state.typingUsers[roomId].includes(userId)) {
        state.typingUsers[roomId].push(userId)
      }
    },

    removeTypingUser: (state, action: PayloadAction<{ roomId: string; userId: string }>) => {
      const { roomId, userId } = action.payload

      if (state.typingUsers[roomId]) {
        state.typingUsers[roomId] = state.typingUsers[roomId].filter(id => id !== userId)
      }
    },

    // Presence management
    updateUserPresence: (state, action: PayloadAction<UserPresence>) => {
      const presence = action.payload

      state.userPresence[presence.user_uuid] = presence

      // Update online users list
      if (presence.status === 'online') {
        if (!state.onlineUsers.includes(presence.user_uuid)) {
          state.onlineUsers.push(presence.user_uuid)
        }
      } else {
        state.onlineUsers = state.onlineUsers.filter(id => id !== presence.user_uuid)
      }
    },

    setOnlineUsers: (state, action: PayloadAction<string[]>) => {
      state.onlineUsers = action.payload
    },

    // Unread counts
    updateUnreadCount: (state, action: PayloadAction<{ roomId: string; count: number }>) => {
      const { roomId, count } = action.payload

      state.unreadCounts[roomId] = count
      state.totalUnreadCount = Object.values(state.unreadCounts).reduce((sum, c) => sum + c, 0)
    },

    incrementUnreadCount: (state, action: PayloadAction<{ roomId: string; increment?: number }>) => {
      const { roomId, increment = 1 } = action.payload

      if (!state.unreadCounts[roomId]) {
        state.unreadCounts[roomId] = 0
      }

      state.unreadCounts[roomId] += increment
      state.totalUnreadCount = Object.values(state.unreadCounts).reduce((sum, count) => sum + count, 0)
    },

    // Notifications
    addNotification: (state, action: PayloadAction<ChatNotification>) => {
      state.notifications.unshift(action.payload)

      // Keep only last 100 notifications
      if (state.notifications.length > 100) {
        state.notifications = state.notifications.slice(0, 100)
      }
    },

    markNotificationRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.notification_uuid === action.payload)

      if (notification) {
        notification.is_read = true
      }
    },

    clearNotifications: state => {
      state.notifications = []
    },

    // UI state
    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebarOpen = action.payload
    },

    setSelectedThread: (state, action: PayloadAction<string | null>) => {
      state.selectedThread = action.payload
    },

    // File upload progress
    setUploadProgress: (state, action: PayloadAction<{ fileId: string; progress: number }>) => {
      const { fileId, progress } = action.payload

      state.uploadProgress[fileId] = progress
    },

    removeUploadProgress: (state, action: PayloadAction<string>) => {
      delete state.uploadProgress[action.payload]
    },

    // Error handling
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload
    },

    clearError: state => {
      state.error = null
    },

    // Reset rooms initialization
    resetRoomsInitialization: state => {
      state.roomsInitialized = false
    },

    // Force refresh rooms from API (useful after deletions or when state gets out of sync)
    refreshRooms: state => {
      state.roomsInitialized = false

      // This will trigger a re-fetch in components that depend on roomsInitialized
    },

    // Reset state
    resetChatState: () => initialState
  },

  // Handle async thunk states
  extraReducers: builder => {
    // Fetch rooms
    builder
      .addCase(fetchChatRooms.pending, state => {
        state.roomsLoading = true
        state.error = null
      })
      .addCase(fetchChatRooms.fulfilled, (state, action) => {
        state.roomsLoading = false
        state.roomsInitialized = true

        if (action.payload.success) {
          console.log(`🔄 fetchChatRooms.fulfilled - Processing ${action.payload.data.length} rooms`)
          console.log(`📊 API Response:`, {
            success: action.payload.success,
            dataLength: action.payload.data.length,
            pagination: action.payload.pagination
          })

          // Get the current room IDs from API response
          const apiRoomIds = new Set(action.payload.data.map((room: ChatRoom) => room.room_uuid))

          // Remove rooms that are no longer returned by the API
          const currentRoomIds = Object.keys(state.rooms)
          const roomsToRemove = currentRoomIds.filter(roomId => !apiRoomIds.has(roomId))

          console.log(`🗑️ Rooms to remove: ${roomsToRemove.length}`, roomsToRemove)
          console.log(`📝 Current rooms in state: ${currentRoomIds.length}`, currentRoomIds)
          console.log(`📥 API room IDs: ${apiRoomIds.size}`, Array.from(apiRoomIds))

          roomsToRemove.forEach(roomId => {
            console.log(`❌ Removing room ${roomId} - no longer returned by API`)
            delete state.rooms[roomId]
            delete state.messages[roomId]
            delete state.messagesLoading[roomId]
            delete state.hasMoreMessages[roomId]
            delete state.unreadCounts[roomId]
            delete state.typingUsers[roomId]

            // Clear active room if it was removed
            if (state.activeRoomId === roomId) {
              state.activeRoomId = null
              console.log(`🎯 Cleared active room: ${roomId}`)
            }
          })

          // Add/update rooms from API response
          action.payload.data.forEach((room: ChatRoom) => {
            console.log(`✅ Processing room ${room.room_uuid}:`, {
              roomName: room.room_name,
              hasLastMessage: !!room.last_message,
              lastMessage: room.last_message,
              updateDate: room.update_date
            })

            state.rooms[room.room_uuid] = room

            // Always initialize unread count, even if it's 0
            state.unreadCounts[room.room_uuid] = room.unread_count || 0
          })

          // Recalculate total unread count
          state.totalUnreadCount = Object.values(state.unreadCounts).reduce((sum, count) => sum + count, 0)

          console.log(
            `🎉 Room sync complete - ${action.payload.data.length} rooms from API, ${roomsToRemove.length} rooms removed`
          )
          console.log(`📊 Final state: ${Object.keys(state.rooms).length} rooms in Redux state`)
        }
      })
      .addCase(fetchChatRooms.rejected, (state, action) => {
        state.roomsLoading = false
        state.error = action.error.message || 'Failed to fetch rooms'
      })

    // Fetch individual room
    builder
      .addCase(fetchChatRoom.fulfilled, (state, action) => {
        if (action.payload.success) {
          const room = action.payload.data
          const existingRoom = state.rooms[room.room_uuid]

          // If room already exists, preserve important fields that might not be in the response
          if (existingRoom) {
            const updatedRoom = { ...existingRoom, ...room }

            // Preserve last_message if not provided in the new data
            if (!room.last_message && existingRoom.last_message) {
              updatedRoom.last_message = existingRoom.last_message
            }

            state.rooms[room.room_uuid] = updatedRoom
          } else {
            state.rooms[room.room_uuid] = room
          }
        }
      })
      .addCase(fetchChatRoom.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to fetch room'
      })

    // Create room
    builder
      .addCase(createChatRoom.fulfilled, (state, action) => {
        if (action.payload.success) {
          const room = action.payload.data

          state.rooms[room.room_uuid] = room
          state.activeRoomId = room.room_uuid
        }
      })
      .addCase(createChatRoom.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to create room'
      })

    // Fetch messages
    builder
      .addCase(fetchRoomMessages.pending, (state, action) => {
        const roomId = action.meta.arg.roomId

        state.messagesLoading[roomId] = true
      })
      .addCase(fetchRoomMessages.fulfilled, (state, action) => {
        const { roomId } = action.payload

        state.messagesLoading[roomId] = false

        if (action.payload.success) {
          if (!state.messages[roomId]) {
            state.messages[roomId] = []
          }

          // Add new messages and sort
          const newMessages = action.payload.data
          const existingIds = new Set(state.messages[roomId].map(m => m.message_id))
          const uniqueNewMessages = newMessages.filter((m: ChatMessage) => !existingIds.has(m.message_id))

          state.messages[roomId] = [...state.messages[roomId], ...uniqueNewMessages]
          state.messages[roomId].sort((a, b) => a.created_at - b.created_at)

          state.hasMoreMessages[roomId] = action.payload.pagination.has_more
        }
      })
      .addCase(fetchRoomMessages.rejected, (state, action) => {
        const roomId = action.meta.arg.roomId

        state.messagesLoading[roomId] = false
        state.error = action.error.message || 'Failed to fetch messages'
      })

    // Send message
    builder
      .addCase(sendMessage.fulfilled, (state, action) => {
        if (action.payload.success) {
          const { roomId, originalRoomId } = action.payload
          const message = action.payload.data

          // If this was a virtual room, update the active room ID
          if (originalRoomId && originalRoomId !== roomId && state.activeRoomId === originalRoomId) {
            state.activeRoomId = roomId
          }

          console.log('sendMessage.fulfilled - Processing API response:', {
            messageId: message.message_id,
            content: message.content?.substring(0, 50),
            author: message.author_name,
            roomId
          })

          if (!state.messages[roomId]) {
            state.messages[roomId] = []
          }

          // If this was a virtual room conversion, move any existing messages
          if (originalRoomId && originalRoomId !== roomId && state.messages[originalRoomId]) {
            // Move messages from virtual room to real room
            state.messages[roomId] = [...state.messages[originalRoomId], ...state.messages[roomId]]
            delete state.messages[originalRoomId]
          }

          // Check if message already exists (avoid duplicates from socket broadcast)
          const existingIndex = state.messages[roomId].findIndex(m => m.message_id === message.message_id)

          if (existingIndex === -1) {
            // Check if this message might have already been added via socket broadcast
            const socketDuplicateIndex = state.messages[roomId].findIndex(
              m =>
                m.author_uuid === message.author_uuid &&
                m.content === message.content &&
                Math.abs(m.created_at - message.created_at) < 5 && // Within 5 seconds
                !m.isOptimistic
            )

            if (socketDuplicateIndex !== -1) {
              console.log('Message already exists from socket broadcast, skipping API response duplicate')

              // Just remove optimistic messages and update the existing message ID if needed
              const optimisticMessages = state.messages[roomId].filter(m => m.isOptimistic)

              if (optimisticMessages.length > 0) {
                console.log(`Removing ${optimisticMessages.length} optimistic messages`)
                state.messages[roomId] = state.messages[roomId].filter(m => !m.isOptimistic)
              }
            } else {
              // Remove optimistic messages and add the confirmed message
              const optimisticMessages = state.messages[roomId].filter(m => m.isOptimistic)

              if (optimisticMessages.length > 0) {
                console.log(`Removing ${optimisticMessages.length} optimistic messages`)
                state.messages[roomId] = state.messages[roomId].filter(m => !m.isOptimistic)
              }

              // Add the confirmed message
              state.messages[roomId].push(message)
              state.messages[roomId].sort((a, b) => a.created_at - b.created_at)
              console.log(
                `Added message from API response. Total messages in room ${roomId}: ${state.messages[roomId].length}`
              )
            }
          } else {
            console.log('Message already exists by ID, just removing optimistic messages')

            // Remove optimistic messages but don't add duplicate
            const optimisticMessages = state.messages[roomId].filter(m => m.isOptimistic)

            if (optimisticMessages.length > 0) {
              console.log(`Removing ${optimisticMessages.length} optimistic messages`)
              state.messages[roomId] = state.messages[roomId].filter(m => !m.isOptimistic)
            }
          }

          // Update room's last activity only if this message is newer
          if (state.rooms[roomId]) {
            const currentLastMessage = state.rooms[roomId].last_message

            const shouldUpdateLastMessage =
              !currentLastMessage ||
              message.created_at > currentLastMessage.created_at ||
              (message.created_at === currentLastMessage.created_at &&
                message.message_id > currentLastMessage.message_id)

            if (shouldUpdateLastMessage) {
              state.rooms[roomId] = {
                ...state.rooms[roomId],
                update_date: new Date().toISOString(),
                last_message: {
                  message_id: message.message_id,
                  room_uuid: message.room_uuid,
                  author_uuid: message.author_uuid,
                  content: message.content,
                  message_type: message.message_type,
                  reply_to: message.reply_to,
                  edited_at: message.edited_at,
                  created_at: message.created_at,
                  flags: message.flags,
                  author_name: message.author_name
                }
              }
              console.log('Updated room last_message from API response:', message.message_id)
            } else {
              console.log('Skipped updating last_message from API response - not newer than current')
            }
          }
        }
      })
      .addCase(sendMessage.rejected, (state, action) => {
        // Mark optimistic messages as failed

        const roomId = action.meta.arg.roomId

        if (state.messages[roomId]) {
          state.messages[roomId].forEach(message => {
            if (message.isOptimistic) {
              message.sendError = action.error.message || 'Failed to send message'
              message.isSending = false
            }
          })
        }

        state.error = action.error.message || 'Failed to send message'
      })

    // Mark messages as read
    builder
      .addCase(markMessagesAsRead.fulfilled, (state, action) => {
        if (action.payload.success) {
          const { roomId } = action.payload

          // Clear unread count for this room
          state.unreadCounts[roomId] = 0
          state.totalUnreadCount = Object.values(state.unreadCounts).reduce((sum, count) => sum + count, 0)
        }
      })
      .addCase(markMessagesAsRead.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to mark messages as read'
      })

    // Delete room
    builder
      .addCase(deleteChatRoom.pending, state => {
        state.error = null

        // Optionally add loading state for specific room
      })
      .addCase(deleteChatRoom.fulfilled, (state, action) => {
        const { roomId } = action.payload

        // Remove room from state
        delete state.rooms[roomId]

        // Remove messages for this room
        delete state.messages[roomId]
        delete state.messagesLoading[roomId]
        delete state.hasMoreMessages[roomId]

        // Remove unread count
        delete state.unreadCounts[roomId]
        state.totalUnreadCount = Object.values(state.unreadCounts).reduce((sum, count) => sum + count, 0)

        // Clear active room if it was the deleted one
        if (state.activeRoomId === roomId) {
          state.activeRoomId = null
        }

        // Remove notifications for this room
        state.notifications = state.notifications.filter(notification => notification.room_uuid !== roomId)

        // Remove typing users for this room
        delete state.typingUsers[roomId]

        // Mark rooms as needing refresh to sync with API
        state.roomsInitialized = false
      })
      .addCase(deleteChatRoom.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to delete conversation'
      })
  }
})

// Export actions
export const {
  setConnectionStatus,
  setCurrentUser,
  setActiveRoom,
  addRoom,
  updateRoom,
  removeRoom,
  addMessage,
  updateMessage,
  removeMessage,
  addOptimisticMessage,
  confirmOptimisticMessage,
  failOptimisticMessage,
  addTypingUser,
  removeTypingUser,
  updateUserPresence,
  setOnlineUsers,
  updateUnreadCount,
  incrementUnreadCount,
  addNotification,
  markNotificationRead,
  clearNotifications,
  setSidebarOpen,
  setSelectedThread,
  setUploadProgress,
  removeUploadProgress,
  setError,
  clearError,
  resetRoomsInitialization,
  refreshRooms,
  resetChatState,
  createVirtualRoom,
  removeVirtualRoom
} = internalChatSlice.actions

// Export reducer
export default internalChatSlice.reducer
