// React Imports
import type { SVGAttributes } from 'react'

const Logo = (props: SVGAttributes<SVGElement>) => {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      {...props}
      width='40'
      zoomAndPan='magnify'
      viewBox='0 0 375 374.999991'
      height='40'
      preserveAspectRatio='xMidYMid meet'
      version='1.0'
    >
      <defs>
        <g />
        <clipPath id='id1'>
          <path
            d='M 23.148438 23.148438 L 351.648438 23.148438 L 351.648438 351.648438 L 23.148438 351.648438 Z M 23.148438 23.148438 '
            clipRule='nonzero'
          />
        </clipPath>
      </defs>
      <g clipPath='url(#id1)'>
        <path
          fill='#5271ff'
          d='M 187.398438 23.148438 C 96.6875 23.148438 23.148438 96.6875 23.148438 187.398438 C 23.148438 278.113281 96.6875 351.648438 187.398438 351.648438 C 278.113281 351.648438 351.648438 278.113281 351.648438 187.398438 C 351.648438 96.6875 278.113281 23.148438 187.398438 23.148438 '
          fillOpacity='1'
          fillRule='nonzero'
        />
      </g>
      <g fill='#ffffff' fillOpacity='1'>
        <g transform='translate(47.484072, 252.680323)'>
          <g>
            <path d='M 36.4375 -46.5625 C 36.4375 -41.59375 37.738281 -36.972656 40.34375 -32.703125 C 42.945312 -28.441406 46.5 -25.035156 51 -22.484375 C 55.507812 -19.929688 60.429688 -18.65625 65.765625 -18.65625 C 71.328125 -18.65625 76.390625 -19.898438 80.953125 -22.390625 C 85.515625 -24.878906 89.097656 -28.253906 91.703125 -32.515625 C 94.316406 -36.785156 95.625 -41.46875 95.625 -46.5625 L 95.625 -129.390625 L 116.0625 -129.390625 L 116.0625 -46.03125 C 116.0625 -36.789062 113.835938 -28.585938 109.390625 -21.421875 C 104.953125 -14.253906 98.878906 -8.65625 91.171875 -4.625 C 83.472656 -0.59375 75.003906 1.421875 65.765625 1.421875 C 56.523438 1.421875 48.082031 -0.59375 40.4375 -4.625 C 32.789062 -8.65625 26.773438 -14.253906 22.390625 -21.421875 C 18.003906 -28.585938 15.8125 -36.789062 15.8125 -46.03125 L 15.8125 -129.390625 L 36.4375 -129.390625 Z M 36.4375 -46.5625 ' />
          </g>
        </g>
      </g>
      <g fill='#ffffff' fillOpacity='1'>
        <g transform='translate(179.145912, 252.680323)'>
          <g>
            <path d='M 62.734375 -78.375 C 69.734375 -78.375 75.570312 -76.269531 80.25 -72.0625 C 84.925781 -67.863281 87.328125 -62.445312 87.453125 -55.8125 L 87.453125 0 L 67.546875 0 L 67.546875 -48.703125 C 67.304688 -52.609375 66.148438 -55.6875 64.078125 -57.9375 C 62.003906 -60.1875 58.894531 -61.375 54.75 -61.5 C 48.582031 -61.5 43.515625 -58.859375 39.546875 -53.578125 C 35.578125 -48.304688 33.59375 -41.585938 33.59375 -33.421875 L 33.59375 0 L 13.6875 0 L 13.6875 -75.1875 L 31.640625 -75.1875 L 33.0625 -61.859375 C 35.78125 -67.066406 39.71875 -71.117188 44.875 -74.015625 C 50.03125 -76.921875 55.984375 -78.375 62.734375 -78.375 Z M 62.734375 -78.375 ' />
          </g>
        </g>
      </g>
      <g fill='#ffffff' fillOpacity='1'>
        <g transform='translate(280.246583, 252.680323)'>
          <g>
            <path d='M 33.59375 -75.1875 L 33.59375 0 L 13.6875 0 L 13.6875 -75.1875 Z M 12.53125 -102.328125 C 12.53125 -105.296875 13.710938 -107.816406 16.078125 -109.890625 C 18.453125 -111.960938 21.117188 -113 24.078125 -113 C 26.921875 -113 29.5 -111.960938 31.8125 -109.890625 C 34.125 -107.816406 35.28125 -105.296875 35.28125 -102.328125 C 35.28125 -99.367188 34.125 -96.878906 31.8125 -94.859375 C 29.5 -92.847656 26.921875 -91.84375 24.078125 -91.84375 C 21.117188 -91.84375 18.453125 -92.847656 16.078125 -94.859375 C 13.710938 -96.878906 12.53125 -99.367188 12.53125 -102.328125 Z M 12.53125 -102.328125 ' />
          </g>
        </g>
      </g>
    </svg>
  )
}

export default Logo
