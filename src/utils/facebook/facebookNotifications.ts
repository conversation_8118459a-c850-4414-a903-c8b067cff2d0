// Facebook Notification Utilities
// Handles Facebook-specific notifications and real-time updates

import type { ChatMessage } from '@/types/apps/internal-chat/chatTypes'

export interface FacebookNotification {
  type: 'new_message' | 'agent_assignment' | 'conversation_status' | 'delivery_status'
  room_uuid: string
  facebook_room_uuid?: string
  facebook_user_id?: string
  contact_name?: string
  agent_uuid?: string
  agent_name?: string
  message?: ChatMessage
  status?: string
  timestamp: number
  platform: 'facebook'
}

export interface FacebookMessageBroadcast {
  room_uuid: string
  message: ChatMessage
  contact_info: {
    facebook_user_id: string
    name: string
    profile_pic?: string
  }
  platform: 'facebook'
  timestamp: number
}

/**
 * Create a Facebook notification object
 */
export function createFacebookNotification(
  type: FacebookNotification['type'],
  data: Partial<FacebookNotification>
): FacebookNotification {
  return {
    type,
    room_uuid: data.room_uuid || '',
    facebook_room_uuid: data.facebook_room_uuid,
    facebook_user_id: data.facebook_user_id,
    contact_name: data.contact_name,
    agent_uuid: data.agent_uuid,
    agent_name: data.agent_name,
    message: data.message,
    status: data.status,
    timestamp: Date.now(),
    platform: 'facebook'
  }
}

/**
 * Create a Facebook message broadcast object
 */
export function createFacebookMessageBroadcast(
  room_uuid: string,
  message: ChatMessage,
  contactInfo: {
    facebook_user_id: string
    name: string
    profile_pic?: string
  }
): FacebookMessageBroadcast {
  return {
    room_uuid,
    message,
    contact_info: contactInfo,
    platform: 'facebook',
    timestamp: Date.now()
  }
}

/**
 * Format Facebook notification for display
 */
export function formatFacebookNotification(notification: FacebookNotification): {
  title: string
  body: string
  icon?: string
} {
  switch (notification.type) {
    case 'new_message':
      return {
        title: `New Facebook Message`,
        body: `${notification.contact_name || 'Facebook User'}: ${
          notification.message?.content?.substring(0, 100) || 'Sent a message'
        }`,
        icon: '/icons/facebook-notification.png'
      }

    case 'agent_assignment':
      return {
        title: 'Facebook Conversation Assigned',
        body: `${notification.agent_name || 'Agent'} assigned to ${notification.contact_name || 'Facebook User'}`,
        icon: '/icons/assignment-notification.png'
      }

    case 'conversation_status':
      return {
        title: 'Facebook Conversation Status',
        body: `Conversation with ${notification.contact_name || 'Facebook User'} is now ${notification.status}`,
        icon: '/icons/status-notification.png'
      }

    case 'delivery_status':
      return {
        title: 'Facebook Message Status',
        body: `Message ${notification.status} for ${notification.contact_name || 'Facebook User'}`,
        icon: '/icons/delivery-notification.png'
      }

    default:
      return {
        title: 'Facebook Notification',
        body: 'You have a new Facebook notification',
        icon: '/icons/facebook-notification.png'
      }
  }
}

/**
 * Get Facebook contact display name
 */
export function getFacebookContactDisplayName(contact: {
  first_name?: string | null
  last_name?: string | null
  facebook_user_id: string
}): string {
  const fullName = [contact.first_name, contact.last_name].filter(Boolean).join(' ')

  return fullName || `Facebook User ${contact.facebook_user_id}`
}

/**
 * Determine notification priority based on type and content
 */
export function getFacebookNotificationPriority(notification: FacebookNotification): 'high' | 'normal' | 'low' {
  switch (notification.type) {
    case 'new_message':
      return 'high'
    case 'agent_assignment':
      return 'normal'
    case 'conversation_status':
      return 'normal'
    case 'delivery_status':
      return 'low'
    default:
      return 'normal'
  }
}

/**
 * Check if notification should be shown based on user preferences
 */
export function shouldShowFacebookNotification(
  notification: FacebookNotification,
  userPreferences?: {
    facebook_notifications?: boolean
    new_message_notifications?: boolean
    assignment_notifications?: boolean
    status_notifications?: boolean
  }
): boolean {
  if (!userPreferences?.facebook_notifications) {
    return false
  }

  switch (notification.type) {
    case 'new_message':
      return userPreferences.new_message_notifications !== false
    case 'agent_assignment':
      return userPreferences.assignment_notifications !== false
    case 'conversation_status':
    case 'delivery_status':
      return userPreferences.status_notifications !== false
    default:
      return true
  }
}

/**
 * Create Socket.IO event name for Facebook notifications
 */
export function getFacebookSocketEvent(type: FacebookNotification['type']): string {
  return `facebook:${type}`
}

/**
 * Broadcast Facebook notification via Socket.IO
 */
export async function broadcastFacebookNotification(
  notification: FacebookNotification,
  domainUuid: string,
  socketBroadcast?: any
) {
  if (!socketBroadcast?.broadcastMessage) {
    console.warn('Socket broadcast not available for Facebook notification')

    return
  }

  try {
    const eventName = getFacebookSocketEvent(notification.type)
    
    // Broadcast to room participants
    socketBroadcast.broadcastMessage(notification.room_uuid, {
      event: eventName,
      data: notification
    }, domainUuid)

    // Also broadcast to domain-wide Facebook listeners
    socketBroadcast.broadcastToDomain(`facebook:notification`, notification, domainUuid)
  } catch (error) {
    console.error('Error broadcasting Facebook notification:', error)
  }
}

/**
 * Process inbound Facebook message for notifications
 */
export async function processFacebookInboundMessage(
  message: ChatMessage,
  contactInfo: {
    facebook_user_id: string
    first_name?: string | null
    last_name?: string | null
    profile_pic?: string | null
  },
  domainUuid: string,
  socketBroadcast?: any
) {
  const contactName = getFacebookContactDisplayName(contactInfo)

  // Create notification
  const notification = createFacebookNotification('new_message', {
    room_uuid: message.room_uuid,
    facebook_user_id: contactInfo.facebook_user_id,
    contact_name: contactName,
    message
  })

  // Broadcast notification
  await broadcastFacebookNotification(notification, domainUuid, socketBroadcast)

  // Create message broadcast
  const messageBroadcast = createFacebookMessageBroadcast(
    message.room_uuid,
    message,
    {
      facebook_user_id: contactInfo.facebook_user_id,
      name: contactName,
      profile_pic: contactInfo.profile_pic || undefined
    }
  )

  // Broadcast message
  if (socketBroadcast?.broadcastMessage) {
    socketBroadcast.broadcastMessage(message.room_uuid, {
      event: 'facebook:message',
      data: messageBroadcast
    }, domainUuid)
  }
}

/**
 * Process Facebook agent assignment for notifications
 */
export async function processFacebookAgentAssignment(
  roomUuid: string,
  agentUuid: string,
  agentName: string,
  contactName: string,
  domainUuid: string,
  socketBroadcast?: any
) {
  const notification = createFacebookNotification('agent_assignment', {
    room_uuid: roomUuid,
    agent_uuid: agentUuid,
    agent_name: agentName,
    contact_name: contactName
  })

  await broadcastFacebookNotification(notification, domainUuid, socketBroadcast)
}

/**
 * Process Facebook delivery status update for notifications
 */
export async function processFacebookDeliveryStatus(
  roomUuid: string,
  messageId: string,
  status: string,
  contactName: string,
  domainUuid: string,
  socketBroadcast?: any
) {
  // Only notify for important status changes
  if (!['delivered', 'read', 'failed'].includes(status)) {
    return
  }

  const notification = createFacebookNotification('delivery_status', {
    room_uuid: roomUuid,
    contact_name: contactName,
    status
  })

  await broadcastFacebookNotification(notification, domainUuid, socketBroadcast)
}
