// Centralized Logging Utility
// Provides structured logging with proper levels and sanitization
// Follows clean architecture principles

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3
}

export interface LogContext {
  [key: string]: any
}

export interface LogEntry {
  level: LogLevel
  message: string
  context?: LogContext
  timestamp: string
  component?: string
  userId?: string
  sessionId?: string
}

// =====================================================
// SANITIZATION FUNCTIONS
// =====================================================

/**
 * Sanitize sensitive data from log context
 */
function sanitizeContext(context: LogContext): LogContext {
  const sensitiveFields = [
    'password',
    'token',
    'secret',
    'key',
    'authorization',
    'cookie',
    'session',
    'auth',
    'credential'
  ]

  const sanitized = { ...context }

  function sanitizeObject(obj: any, path = ''): any {
    if (obj === null || obj === undefined) return obj

    if (typeof obj === 'string') {
      // Check if the field name or path contains sensitive keywords
      const fieldName = path.toLowerCase()

      if (sensitiveFields.some(field => fieldName.includes(field))) {
        return '[REDACTED]'
      }

      return obj
    }

    if (typeof obj === 'object') {
      if (Array.isArray(obj)) {
        return obj.map((item, index) => sanitizeObject(item, `${path}[${index}]`))
      }

      const result: any = {}

      for (const [key, value] of Object.entries(obj)) {
        const newPath = path ? `${path}.${key}` : key

        result[key] = sanitizeObject(value, newPath)
      }

      return result
    }

    return obj
  }

  return sanitizeObject(sanitized)
}

/**
 * Convert BigInt values to strings for JSON serialization
 */
function serializeBigInt(obj: any): any {
  if (obj === null || obj === undefined) return obj

  if (typeof obj === 'bigint') {
    return obj.toString()
  }

  if (typeof obj === 'object') {
    if (Array.isArray(obj)) {
      return obj.map(serializeBigInt)
    }

    const result: any = {}

    for (const [key, value] of Object.entries(obj)) {
      result[key] = serializeBigInt(value)
    }

    return result
  }

  return obj
}

// =====================================================
// LOGGER CLASS
// =====================================================

class Logger {
  private currentLevel: LogLevel = LogLevel.INFO
  private component: string = 'APP'

  constructor(component?: string) {
    if (component) {
      this.component = component
    }

    // Set log level from environment
    const envLevel = process.env.LOG_LEVEL?.toUpperCase()

    switch (envLevel) {
      case 'ERROR':
        this.currentLevel = LogLevel.ERROR
        break
      case 'WARN':
        this.currentLevel = LogLevel.WARN
        break
      case 'INFO':
        this.currentLevel = LogLevel.INFO
        break
      case 'DEBUG':
        this.currentLevel = LogLevel.DEBUG
        break
      default:
        this.currentLevel = process.env.NODE_ENV === 'production' ? LogLevel.INFO : LogLevel.DEBUG
    }
  }

  private shouldLog(level: LogLevel): boolean {
    return level <= this.currentLevel
  }

  private formatLogEntry(level: LogLevel, message: string, context?: LogContext): LogEntry {
    return {
      level,
      message,
      context: context ? serializeBigInt(sanitizeContext(context)) : undefined,
      timestamp: new Date().toISOString(),
      component: this.component
    }
  }

  private output(entry: LogEntry): void {
    if (!this.shouldLog(entry.level)) return

    const levelName = LogLevel[entry.level]
    const prefix = `[${entry.timestamp}] [${levelName}] [${entry.component}]`

    if (entry.context && Object.keys(entry.context).length > 0) {
      console.log(`${prefix} ${entry.message}`, entry.context)
    } else {
      console.log(`${prefix} ${entry.message}`)
    }
  }

  error(message: string, context?: LogContext): void {
    this.output(this.formatLogEntry(LogLevel.ERROR, message, context))
  }

  warn(message: string, context?: LogContext): void {
    this.output(this.formatLogEntry(LogLevel.WARN, message, context))
  }

  info(message: string, context?: LogContext): void {
    this.output(this.formatLogEntry(LogLevel.INFO, message, context))
  }

  debug(message: string, context?: LogContext): void {
    this.output(this.formatLogEntry(LogLevel.DEBUG, message, context))
  }

  // Convenience method for API requests
  apiRequest(method: string, path: string, context?: LogContext): void {
    this.info(`${method} ${path}`, context)
  }

  // Convenience method for API responses
  apiResponse(method: string, path: string, status: number, context?: LogContext): void {
    const level = status >= 400 ? LogLevel.ERROR : status >= 300 ? LogLevel.WARN : LogLevel.INFO

    this.output(this.formatLogEntry(level, `${method} ${path} - ${status}`, context))
  }

  // Convenience method for database operations
  dbOperation(operation: string, table: string, context?: LogContext): void {
    this.debug(`DB ${operation} ${table}`, context)
  }

  // Convenience method for socket events
  socketEvent(event: string, context?: LogContext): void {
    this.debug(`Socket ${event}`, context)
  }
}

// =====================================================
// FACTORY FUNCTIONS
// =====================================================

/**
 * Create a logger instance for a specific component
 */
export function createLogger(component: string): Logger {
  return new Logger(component)
}

/**
 * Default logger instance
 */
export const logger = new Logger()

// =====================================================
// COMPONENT-SPECIFIC LOGGERS
// =====================================================

export const chatLogger = createLogger('CHAT')
export const apiLogger = createLogger('API')
export const dbLogger = createLogger('DB')
export const socketLogger = createLogger('SOCKET')
export const authLogger = createLogger('AUTH')

// =====================================================
// LEGACY COMPATIBILITY
// =====================================================

/**
 * Legacy chatLog compatibility
 * @deprecated Use chatLogger instead
 */
export const chatLog = {
  error: (message: string, context?: any) => chatLogger.error(message, context),
  warn: (message: string, context?: any) => chatLogger.warn(message, context),
  info: (message: string, context?: any) => chatLogger.info(message, context),
  debug: (message: string, context?: any) => chatLogger.debug(message, context)
}

// =====================================================
// UTILITY FUNCTIONS
// =====================================================

/**
 * Safe BigInt to string conversion for logging
 */
export function safeBigIntToString(value: any): string {
  if (value === null || value === undefined) return '0'

  return typeof value === 'bigint' ? value.toString() : String(value)
}

/**
 * Create sanitized log context from request/response data
 */
export function createLogContext(data: any): LogContext {
  return sanitizeContext(serializeBigInt(data))
}
