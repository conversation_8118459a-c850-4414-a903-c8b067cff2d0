// Message processing utilities for social platforms
// Following clean code guidelines and DRY principles

import { prisma } from '@/libs/db/prisma'

/**
 * Safely convert BigInt to string for JSON serialization
 * Prevents JSON serialization errors with BigInt message IDs
 */
export function safeBigIntToString(value: bigint | null | undefined): string | null {
  if (value === null || value === undefined) {
    return null
  }

  return value.toString()
}

/**
 * Convert string to BigInt safely
 */
export function safeStringToBigInt(value: string | null | undefined): bigint | null {
  if (!value) {
    return null
  }

  try {
    return BigInt(value)
  } catch (error) {
    console.error('Failed to convert string to BigInt:', value, error)

    return null
  }
}

/**
 * Create internal chat room for external platform conversation
 * Follows the established pattern from Telegram integration
 */
export async function createInternalChatRoom(
  domainUuid: string,
  roomName: string,
  roomDescription: string,
  platform: 'facebook' | 'zalo' | 'telegram',
  externalUserId: string,
  createdByUserUuid?: string
): Promise<{ room_uuid: string }> {
  try {
    const room = await prisma.v_chat_rooms.create({
      data: {
        domain_uuid: domainUuid,
        room_name: roomName,
        room_description: roomDescription,
        room_type: 'direct',
        created_by_user_uuid: createdByUserUuid || null, // Allow null for system-created rooms
        room_settings: {
          platform,
          external_user_id: externalUserId,
          auto_created: true,
          created_from_webhook: true
        },
        is_active: true,
        is_archived: false,
        max_participants: 2, // Direct conversation
        insert_date: new Date(),
        update_date: new Date()
      }
    })

    return { room_uuid: room.room_uuid }
  } catch (error) {
    console.error('Failed to create internal chat room:', error)
    throw new Error('Failed to create chat room')
  }
}

/**
 * Create internal chat message
 * Standardized message creation for all platforms
 */
export async function createInternalMessage(
  roomUuid: string,
  authorUuid: string | null,
  content: string,
  messageType: number = 0,
  replyTo?: bigint,
  flags: number = 0
): Promise<{ message_id: bigint; created_at: number }> {
  try {
    const createdAt = Math.floor(Date.now() / 1000)

    const message = await prisma.v_chat_messages.create({
      data: {
        room_uuid: roomUuid,
        author_uuid: authorUuid,
        content: content.trim(),
        message_type: messageType,
        reply_to: replyTo || null,
        created_at: createdAt,
        flags: flags
      }
    })

    return {
      message_id: message.message_id,
      created_at: createdAt
    }
  } catch (error) {
    console.error('Failed to create internal message:', error)
    throw new Error('Failed to create message')
  }
}

/**
 * Update room last activity
 * Keeps room metadata up to date
 */
export async function updateRoomLastActivity(roomUuid: string, userUuid?: string): Promise<void> {
  try {
    await prisma.v_chat_rooms.update({
      where: { room_uuid: roomUuid },
      data: {
        update_date: new Date(),
        update_user: userUuid || null
      }
    })
  } catch (error) {
    console.error('Failed to update room last activity:', error)

    // Don't throw error as this is not critical
  }
}

/**
 * Get or create room participant
 * Ensures user is added to room for notifications
 */
export async function ensureRoomParticipant(
  roomUuid: string,
  userUuid: string,
  role: 'owner' | 'admin' | 'moderator' | 'member' = 'member'
): Promise<void> {
  try {
    await prisma.v_chat_room_participants.upsert({
      where: {
        room_uuid_user_uuid: {
          room_uuid: roomUuid,
          user_uuid: userUuid
        }
      },
      update: {
        deleted_at: null, // Ensure participant is active
        update_date: new Date()
      },
      create: {
        room_uuid: roomUuid,
        user_uuid: userUuid,
        participant_role: role,
        joined_date: new Date(),
        is_muted: false,
        notification_settings: {
          mentions: true,
          all_messages: true
        },
        insert_date: new Date(),
        update_date: new Date()
      }
    })
  } catch (error) {
    console.error('Failed to ensure room participant:', error)

    // Don't throw error as this is not critical for message processing
  }
}

/**
 * Format message for Socket.IO broadcasting
 * Standardizes message format across all platforms
 */
export function formatMessageForBroadcast(
  message: any,
  platform: 'facebook' | 'zalo' | 'telegram',
  authorName?: string,
  unreadCounts?: Record<string, number>
): any {
  return {
    message_id: safeBigIntToString(message.message_id),
    room_uuid: message.room_uuid,
    author_uuid: message.author_uuid,
    author_name: authorName || `${platform.charAt(0).toUpperCase() + platform.slice(1)} User`,
    content: message.content,
    message_type: message.message_type,
    reply_to: safeBigIntToString(message.reply_to),
    edited_at: safeBigIntToString(message.edited_at),
    created_at: safeBigIntToString(message.created_at),
    flags: message.flags,
    platform,
    unread_counts: unreadCounts || {}
  }
}

/**
 * Calculate unread counts for room participants
 * Used for notification badges
 */
export async function calculateUnreadCounts(roomUuid: string): Promise<Record<string, number>> {
  try {
    const participants = await prisma.v_chat_room_participants.findMany({
      where: {
        room_uuid: roomUuid,
        deleted_at: null
      },
      select: {
        user_uuid: true,
        last_read_message_id: true
      }
    })

    const unreadCounts: Record<string, number> = {}

    for (const participant of participants) {
      const unreadCount = await prisma.v_chat_messages.count({
        where: {
          room_uuid: roomUuid,
          message_id: participant.last_read_message_id ? { gt: participant.last_read_message_id } : undefined,
          author_uuid: { not: participant.user_uuid } // Don't count own messages
        }
      })

      unreadCounts[participant.user_uuid] = unreadCount
    }

    return unreadCounts
  } catch (error) {
    console.error('Failed to calculate unread counts:', error)

    return {}
  }
}

/**
 * Broadcast message via Socket.IO
 * Uses the main chat system's broadcasting mechanism
 */
export async function broadcastMessage(
  roomUuid: string,
  message: any,
  domainUuid: string,
  platform: 'facebook' | 'zalo' | 'telegram'
): Promise<void> {
  try {
    // Check if global socket broadcast is available
    if (!(global as any).socketBroadcast?.broadcastMessage) {
      console.warn('Socket broadcast not available')

      return
    }

    // Calculate unread counts
    const unreadCounts = await calculateUnreadCounts(roomUuid)

    // Format message for broadcast
    const formattedMessage = formatMessageForBroadcast(message, platform, undefined, unreadCounts)

    // Broadcast using main chat system
    ;(global as any).socketBroadcast.broadcastMessage(roomUuid, formattedMessage, domainUuid)
  } catch (error) {
    console.error('Failed to broadcast message:', error)

    // Don't throw error as message processing should continue
  }
}

/**
 * Create notification for new message
 * Integrates with the main notification system
 */
export async function createMessageNotification(roomUuid: string, messageId: bigint): Promise<void> {
  try {
    // Get room participants who should receive notifications
    const participants = await prisma.v_chat_room_participants.findMany({
      where: {
        room_uuid: roomUuid,
        deleted_at: null,
        is_muted: false
      },
      select: {
        user_uuid: true,
        notification_settings: true
      }
    })

    // Create notifications for each participant
    for (const participant of participants) {
      const notificationSettings = participant.notification_settings as any

      if (notificationSettings?.all_messages !== false) {
        await prisma.v_chat_notifications.create({
          data: {
            user_uuid: participant.user_uuid,
            room_uuid: roomUuid,
            message_id: messageId,
            notification_type: 'message',
            is_read: false,
            expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
            insert_date: new Date()
          }
        })
      }
    }
  } catch (error) {
    console.error('Failed to create message notifications:', error)

    // Don't throw error as this is not critical for message processing
  }
}

/**
 * Validate message content
 * Ensures message content meets requirements
 */
export function validateMessageContent(content: string, maxLength: number = 4000): { valid: boolean; error?: string } {
  if (!content || typeof content !== 'string') {
    return { valid: false, error: 'Message content is required' }
  }

  const trimmedContent = content.trim()

  if (trimmedContent.length === 0) {
    return { valid: false, error: 'Message content cannot be empty' }
  }

  if (trimmedContent.length > maxLength) {
    return {
      valid: false,
      error: `Message content exceeds maximum length of ${maxLength} characters`
    }
  }

  return { valid: true }
}

/**
 * Extract mentions from message content
 * Finds @username mentions in message text
 */
export function extractMentions(content: string): string[] {
  const mentionRegex = /@(\w+)/g
  const mentions: string[] = []
  let match

  while ((match = mentionRegex.exec(content)) !== null) {
    mentions.push(match[1])
  }

  return mentions
}

/**
 * Process message attachments
 * Handles file uploads and media processing
 */
export async function processMessageAttachments(
  messageId: bigint,
  attachments: Array<{
    type: string
    url: string
    filename?: string
    size?: number
    mime_type?: string
  }>
): Promise<void> {
  try {
    for (const attachment of attachments) {
      await prisma.v_chat_message_attachments.create({
        data: {
          message_id: messageId,
          filename: attachment.filename || 'attachment',
          file_path: attachment.url,
          file_size: attachment.size || null,
          content_type: attachment.mime_type || null
        }
      })
    }
  } catch (error) {
    console.error('Failed to process message attachments:', error)

    // Don't throw error as message should still be created
  }
}
