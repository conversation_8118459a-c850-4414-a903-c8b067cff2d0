// Shared utilities for social platform integrations
// Following DRY principle and clean code guidelines

import crypto from 'crypto'

/**
 * Verify webhook signature for security
 * Used by both Facebook and ZALO integrations
 */
export function verifyWebhookSignature(
  payload: string,
  signature: string,
  secret: string,
  algorithm: 'sha1' | 'sha256' = 'sha256'
): boolean {
  try {
    const expectedSignature = crypto.createHmac(algorithm, secret).update(payload, 'utf8').digest('hex')

    const providedSignature = signature.replace(`${algorithm}=`, '')

    return crypto.timingSafeEqual(Buffer.from(expectedSignature, 'hex'), Buffer.from(providedSignature, 'hex'))
  } catch (error) {
    console.error('Webhook signature verification failed:', error)

    return false
  }
}

/**
 * Generate secure random token for webhook verification
 */
export function generateVerifyToken(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex')
}

/**
 * Sanitize and validate webhook payload
 */
export function sanitizeWebhookPayload(payload: any): any {
  if (!payload || typeof payload !== 'object') {
    throw new Error('Invalid webhook payload')
  }

  // Remove potentially dangerous fields
  const sanitized = JSON.parse(JSON.stringify(payload))

  // Remove any __proto__ or constructor properties
  delete sanitized.__proto__
  delete sanitized.constructor

  return sanitized
}

/**
 * Format message content for display
 * Handles text truncation and special characters
 */
export function formatMessageContent(
  content: string,
  maxLength: number = 100,
  showAttachmentIndicator: boolean = true
): string {
  if (!content) {
    return showAttachmentIndicator ? '[Attachment]' : ''
  }

  // Remove HTML tags and decode entities
  const cleanContent = content
    .replace(/<[^>]*>/g, '')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .trim()

  if (cleanContent.length <= maxLength) {
    return cleanContent
  }

  return cleanContent.substring(0, maxLength - 3) + '...'
}

/**
 * Convert platform-specific message type to internal message type
 */
export function mapMessageType(platformType: string | number, platform: 'facebook' | 'zalo' | 'telegram'): number {
  const typeMap = {
    facebook: {
      text: 0,
      image: 1,
      file: 2,
      audio: 1,
      video: 1,
      template: 0
    },
    zalo: {
      text: 0,
      image: 1,
      file: 2,
      audio: 2,
      video: 2,
      sticker: 1,
      location: 0
    },
    telegram: {
      text: 0,
      photo: 1,
      document: 2,
      audio: 2,
      video: 2,
      voice: 2,
      sticker: 1,
      location: 0
    }
  }

  const platformMap = typeMap[platform]

  if (!platformMap) {
    return 0 // Default to text
  }

  const mappedType = (platformMap as any)[String(platformType)]

  return mappedType !== undefined ? mappedType : 0
}

/**
 * Generate webhook URL for a platform and domain
 */
export function generateWebhookUrl(
  platform: 'facebook' | 'zalo' | 'telegram',
  domainUuid: string,
  baseUrl?: string
): string {
  const base = baseUrl || process.env.NEXT_PUBLIC_API_URL || 'https://yourdomain.com'

  return `${base}/api/internal-chat/${platform}/webhook?domain=${domainUuid}`
}

/**
 * Validate domain UUID format
 */
export function isValidDomainUuid(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i

  return uuidRegex.test(uuid)
}

/**
 * Rate limiting helper
 */
export class RateLimiter {
  private requests: Map<string, number[]> = new Map()

  constructor(
    private maxRequests: number = 100,
    private windowMs: number = 60000 // 1 minute
  ) {}

  isAllowed(identifier: string): boolean {
    const now = Date.now()
    const windowStart = now - this.windowMs

    // Get existing requests for this identifier
    const requests = this.requests.get(identifier) || []

    // Filter out old requests
    const recentRequests = requests.filter(time => time > windowStart)

    // Check if under limit
    if (recentRequests.length >= this.maxRequests) {
      return false
    }

    // Add current request
    recentRequests.push(now)
    this.requests.set(identifier, recentRequests)

    return true
  }

  getRemainingRequests(identifier: string): number {
    const now = Date.now()
    const windowStart = now - this.windowMs
    const requests = this.requests.get(identifier) || []
    const recentRequests = requests.filter(time => time > windowStart)

    return Math.max(0, this.maxRequests - recentRequests.length)
  }

  reset(identifier: string): void {
    this.requests.delete(identifier)
  }

  cleanup(): void {
    const now = Date.now()
    const windowStart = now - this.windowMs

    for (const [identifier, requests] of this.requests.entries()) {
      const recentRequests = requests.filter(time => time > windowStart)

      if (recentRequests.length === 0) {
        this.requests.delete(identifier)
      } else {
        this.requests.set(identifier, recentRequests)
      }
    }
  }
}

/**
 * Retry mechanism for API calls
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000,
  maxDelay: number = 10000
): Promise<T> {
  let lastError: Error = new Error('Unknown error')

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error

      if (attempt === maxRetries) {
        break
      }

      // Calculate delay with exponential backoff and jitter
      const delay = Math.min(baseDelay * Math.pow(2, attempt) + Math.random() * 1000, maxDelay)

      console.warn(`Attempt ${attempt + 1} failed, retrying in ${delay}ms:`, error)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  throw lastError
}

/**
 * Safe JSON parsing with error handling
 */
export function safeJsonParse<T = any>(json: string, defaultValue: T | null = null): T | null {
  try {
    return JSON.parse(json)
  } catch (error) {
    console.error('JSON parsing failed:', error)

    return defaultValue
  }
}

/**
 * Encrypt sensitive data for database storage
 */
export function encryptSensitiveData(data: string, key?: string): string {
  try {
    const encryptionKey = key || process.env.ENCRYPTION_KEY

    if (!encryptionKey) {
      console.warn('No encryption key provided, storing data in plain text')

      return data
    }

    const cipher = crypto.createCipher('aes-256-cbc', encryptionKey)
    let encrypted = cipher.update(data, 'utf8', 'hex')

    encrypted += cipher.final('hex')

    return encrypted
  } catch (error) {
    console.error('Encryption failed:', error)

    return data // Fallback to plain text
  }
}

/**
 * Decrypt sensitive data from database
 */
export function decryptSensitiveData(encryptedData: string, key?: string): string {
  try {
    const encryptionKey = key || process.env.ENCRYPTION_KEY

    if (!encryptionKey) {
      return encryptedData // Assume plain text
    }

    const decipher = crypto.createDecipher('aes-256-cbc', encryptionKey)
    let decrypted = decipher.update(encryptedData, 'hex', 'utf8')

    decrypted += decipher.final('utf8')

    return decrypted
  } catch (error) {
    console.error('Decryption failed:', error)

    return encryptedData // Fallback to encrypted data
  }
}

/**
 * Format timestamp for display
 */
export function formatTimestamp(
  timestamp: number | Date,
  format: 'relative' | 'absolute' | 'time' = 'relative'
): string {
  const date = typeof timestamp === 'number' ? new Date(timestamp * 1000) : timestamp
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMinutes = Math.floor(diffMs / 60000)
  const diffHours = Math.floor(diffMinutes / 60)
  const diffDays = Math.floor(diffHours / 24)

  switch (format) {
    case 'relative':
      if (diffMinutes < 1) return 'Just now'
      if (diffMinutes < 60) return `${diffMinutes}m ago`
      if (diffHours < 24) return `${diffHours}h ago`
      if (diffDays < 7) return `${diffDays}d ago`

      return date.toLocaleDateString()

    case 'time':
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })

    case 'absolute':
    default:
      return date.toLocaleString()
  }
}

/**
 * Validate URL format
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url)

    return true
  } catch {
    return false
  }
}

/**
 * Extract domain from URL
 */
export function extractDomain(url: string): string | null {
  try {
    const urlObj = new URL(url)

    return urlObj.hostname
  } catch {
    return null
  }
}
