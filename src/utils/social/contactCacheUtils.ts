// Social Platform Contact Cache Utilities
// Common utilities for caching contact information across social platforms
// Supports Issue 3.1 fix: Facebook Chat Missing User Names

/**
 * Generate a display name from contact information
 */
export function generateDisplayName(
  firstName?: string | null,
  lastName?: string | null,
  fallbackName?: string,
  platform?: string
): string {
  // Try to construct full name
  const fullName = [firstName, lastName]
    .filter(name => name && name.trim())
    .join(' ')
    .trim()

  if (fullName) {
    return fullName
  }

  // Use fallback name if provided
  if (fallbackName && fallbackName.trim()) {
    return fallbackName.trim()
  }

  // Use platform-specific default
  switch (platform?.toLowerCase()) {
    case 'facebook':
      return 'Facebook User'
    case 'zalo':
      return 'ZALO User'
    case 'telegram':
      return 'Telegram User'
    default:
      return 'User'
  }
}

/**
 * Check if contact information is considered complete
 */
export function isContactInfoComplete(contact: {
  first_name?: string | null
  last_name?: string | null
  profile_pic?: string | null
}): boolean {
  return !!(
    (contact.first_name && contact.first_name.trim()) ||
    (contact.last_name && contact.last_name.trim()) ||
    (contact.profile_pic && contact.profile_pic.trim())
  )
}

/**
 * Calculate cache expiry time
 */
export function calculateCacheExpiry(
  ttlHours: number = 24,
  baseTime: Date = new Date()
): Date {
  return new Date(baseTime.getTime() + ttlHours * 60 * 60 * 1000)
}

/**
 * Check if cache entry is expired
 */
export function isCacheExpired(expiryDate: Date | null): boolean {
  if (!expiryDate) return true
  
return new Date() > expiryDate
}

/**
 * Generate cache key for contact
 */
export function generateContactCacheKey(
  platform: string,
  userId: string,
  domainUuid?: string
): string {
  const parts = [platform.toLowerCase(), userId]

  if (domainUuid) {
    parts.unshift(domainUuid)
  }

  
return parts.join(':')
}

/**
 * Sanitize contact data for storage
 */
export function sanitizeContactData(data: Record<string, any>): Record<string, any> {
  const sanitized: Record<string, any> = {}

  for (const [key, value] of Object.entries(data)) {
    if (value === null || value === undefined) {
      sanitized[key] = null
    } else if (typeof value === 'string') {
      // Trim strings and convert empty strings to null
      const trimmed = value.trim()

      sanitized[key] = trimmed || null
    } else if (typeof value === 'boolean') {
      sanitized[key] = value
    } else if (typeof value === 'number') {
      sanitized[key] = value
    } else if (value instanceof Date) {
      sanitized[key] = value
    } else {
      // For other types, convert to string or null
      sanitized[key] = value ? String(value) : null
    }
  }

  return sanitized
}

/**
 * Retry function with exponential backoff
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000,
  maxDelay: number = 10000
): Promise<T | null> {
  let lastError: Error | null = null

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error
      
      if (attempt === maxRetries) {
        console.error(`All ${maxRetries} attempts failed:`, lastError)
        break
      }

      // Calculate delay with exponential backoff
      const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay)

      console.warn(`Attempt ${attempt} failed, retrying in ${delay}ms:`, error)
      
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  return null
}

/**
 * Validate API response structure
 */
export function validateApiResponse(
  response: any,
  requiredFields: string[] = []
): boolean {
  if (!response || typeof response !== 'object') {
    return false
  }

  // Check for API error
  if (response.error) {
    console.warn('API response contains error:', response.error)
    
return false
  }

  // Check required fields
  for (const field of requiredFields) {
    if (!(field in response)) {
      console.warn(`API response missing required field: ${field}`)
      
return false
    }
  }

  return true
}

/**
 * Create contact update payload
 */
export function createContactUpdatePayload(
  apiData: any,
  platform: string
): Record<string, any> {
  const now = new Date()

  const payload: Record<string, any> = {
    last_api_fetch: now,
    cache_expires_at: calculateCacheExpiry(24, now),
    last_interaction_date: now,
    is_active: true
  }

  // Platform-specific field mapping
  switch (platform.toLowerCase()) {
    case 'facebook':
      payload.first_name = apiData.first_name || null
      payload.last_name = apiData.last_name || null
      payload.profile_pic = apiData.profile_pic?.data?.url || apiData.profile_pic || null
      payload.locale = apiData.locale || null
      payload.timezone = apiData.timezone || null
      payload.gender = apiData.gender || null
      payload.is_payment_enabled = apiData.is_payment_enabled || false
      break

    case 'telegram':
      payload.first_name = apiData.first_name || null
      payload.last_name = apiData.last_name || null
      payload.username = apiData.username || null
      payload.language_code = apiData.language_code || null
      payload.is_bot = apiData.is_bot || false
      break

    case 'zalo':
      payload.display_name = apiData.display_name || null
      payload.avatar = apiData.avatar || null
      payload.user_id_by_app = apiData.user_id_by_app || null
      break

    default:
      // Generic mapping
      payload.first_name = apiData.first_name || null
      payload.last_name = apiData.last_name || null
      payload.display_name = apiData.display_name || apiData.name || null
      break
  }

  return sanitizeContactData(payload)
}

/**
 * Log cache operation for debugging
 */
export function logCacheOperation(
  operation: 'hit' | 'miss' | 'fetch' | 'error',
  platform: string,
  userId: string,
  details?: any
): void {
  const timestamp = new Date().toISOString()

  const logData = {
    timestamp,
    operation,
    platform,
    userId,
    details
  }

  switch (operation) {
    case 'hit':
      console.log(`[Cache HIT] ${platform} user ${userId}`, logData)
      break
    case 'miss':
      console.log(`[Cache MISS] ${platform} user ${userId}`, logData)
      break
    case 'fetch':
      console.log(`[API FETCH] ${platform} user ${userId}`, logData)
      break
    case 'error':
      console.error(`[Cache ERROR] ${platform} user ${userId}`, logData)
      break
  }
}

/**
 * Get contact display info for UI
 */
export function getContactDisplayInfo(contact: {
  first_name?: string | null
  last_name?: string | null
  display_name?: string | null
  username?: string | null
  profile_pic?: string | null
  avatar?: string | null
}, platform: string): {
  displayName: string
  profilePicture: string | null
  initials: string
} {
  const displayName = generateDisplayName(
    contact.first_name,
    contact.last_name,
    contact.display_name || contact.username || undefined,
    platform
  )

  const profilePicture = contact.profile_pic || contact.avatar || null

  // Generate initials from display name
  const initials = displayName
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('')

  return {
    displayName,
    profilePicture,
    initials
  }
}
