// Chat Message Utility Functions
// Discord-inspired message handling utilities

import type { ChatMessage } from '@/types/apps/internal-chat/chatTypes'
import { MessageType, MESSAGE_FLAGS } from '@/types/apps/internal-chat/chatTypes'

// =====================================================
// MESSAGE FLAG UTILITIES
// =====================================================

export const MessageUtils = {
  // =====================================================
  // FLAG OPERATIONS (Improved with better naming)
  // =====================================================

  // Flag checking functions
  isDeleted: (flags: number): boolean => (flags & MESSAGE_FLAGS.DELETED) !== 0,
  isEdited: (message: ChatMessage): boolean => message.edited_at !== null,
  isPinned: (flags: number): boolean => (flags & MESSAGE_FLAGS.PINNED) !== 0,
  isSystem: (flags: number): boolean => (flags & MESSAGE_FLAGS.SYSTEM) !== 0,
  isUrgent: (flags: number): boolean => (flags & MESSAGE_FLAGS.URGENT) !== 0,
  isSilent: (flags: number): boolean => (flags & MESSAGE_FLAGS.SILENT) !== 0,

  // Flag setting functions
  markDeleted: (flags: number): number => flags | MESSAGE_FLAGS.DELETED,
  markPinned: (flags: number): number => flags | MESSAGE_FLAGS.PINNED,
  markSystem: (flags: number): number => flags | MESSAGE_FLAGS.SYSTEM,
  markUrgent: (flags: number): number => flags | MESSAGE_FLAGS.URGENT,
  markSilent: (flags: number): number => flags | MESSAGE_FLAGS.SILENT,

  // Flag removing functions
  unmarkDeleted: (flags: number): number => flags & ~MESSAGE_FLAGS.DELETED,
  unmarkPinned: (flags: number): number => flags & ~MESSAGE_FLAGS.PINNED,
  unmarkSystem: (flags: number): number => flags & ~MESSAGE_FLAGS.SYSTEM,
  unmarkUrgent: (flags: number): number => flags & ~MESSAGE_FLAGS.URGENT,
  unmarkSilent: (flags: number): number => flags & ~MESSAGE_FLAGS.SILENT,

  // Timestamp utilities
  formatTimestamp: (timestamp: number): Date => new Date(timestamp * 1000),

  getRelativeTime: (timestamp: number): string => {
    const now = Date.now()
    const messageTime = timestamp * 1000
    const diff = now - messageTime

    const seconds = Math.floor(diff / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    const days = Math.floor(hours / 24)

    if (seconds < 60) return 'just now'
    if (minutes < 60) return `${minutes}m ago`
    if (hours < 24) return `${hours}h ago`
    if (days < 7) return `${days}d ago`

    return new Date(messageTime).toLocaleDateString()
  },

  // Message type utilities
  getMessageTypeLabel: (type: MessageType): string => {
    switch (type) {
      case MessageType.TEXT:
        return 'Text'
      case MessageType.IMAGE:
        return 'Image'
      case MessageType.FILE:
        return 'File'
      case MessageType.SYSTEM:
        return 'System'
      case MessageType.CALL:
        return 'Call'
      default:
        return 'Unknown'
    }
  },

  // File type utilities
  getFileIcon: (contentType: string): string => {
    if (!contentType) return 'ri-file-line'

    if (contentType.startsWith('image/')) return 'ri-image-line'
    if (contentType.startsWith('video/')) return 'ri-video-line'
    if (contentType.startsWith('audio/')) return 'ri-music-line'
    if (contentType.includes('pdf')) return 'ri-file-pdf-line'
    if (contentType.includes('word') || contentType.includes('document')) return 'ri-file-word-line'
    if (contentType.includes('excel') || contentType.includes('spreadsheet')) return 'ri-file-excel-line'
    if (contentType.includes('powerpoint') || contentType.includes('presentation')) return 'ri-file-ppt-line'
    if (contentType.includes('zip') || contentType.includes('rar') || contentType.includes('archive'))
      return 'ri-file-zip-line'
    if (contentType.includes('text/')) return 'ri-file-text-line'

    return 'ri-file-line'
  },

  // =====================================================
  // MESSAGE GROUPING (Following DRY principle)
  // =====================================================

  shouldGroupWithPrevious: (current: ChatMessage, previous: ChatMessage | null): boolean => {
    if (!previous) return false
    if (!current.author_uuid || !previous.author_uuid) return false
    if (current.author_uuid !== previous.author_uuid) return false

    // Don't group system messages or deleted messages
    if (MessageUtils.isSystem(current.flags || 0) || MessageUtils.isSystem(previous.flags || 0)) return false
    if (MessageUtils.isDeleted(current.flags || 0) || MessageUtils.isDeleted(previous.flags || 0)) return false

    // Group if messages are within 5 minutes
    const timeDiff = Number(current.created_at) - Number(previous.created_at)

    return timeDiff > 0 && timeDiff < 5 * 60 // 5 minutes in seconds
  },

  // Content utilities
  truncateContent: (content: string, maxLength: number = 100): string => {
    if (!content || content.length <= maxLength) return content

    return content.substring(0, maxLength) + '...'
  },

  // Mention utilities
  extractMentions: (content: string): string[] => {
    const mentionRegex = /@(\w+)/g
    const mentions: string[] = []
    let match

    while ((match = mentionRegex.exec(content)) !== null) {
      mentions.push(match[1])
    }

    return mentions
  },

  // Content formatting
  formatMessageContent: (content: string): string => {
    if (!content) return ''

    // Basic markdown-like formatting
    let formatted = content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Bold
      .replace(/\*(.*?)\*/g, '<em>$1</em>') // Italic
      .replace(/`(.*?)`/g, '<code>$1</code>') // Code
      .replace(/~~(.*?)~~/g, '<del>$1</del>') // Strikethrough

    // Convert URLs to links
    const urlRegex = /(https?:\/\/[^\s]+)/g

    formatted = formatted.replace(urlRegex, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>')

    // Convert mentions to styled spans
    const mentionRegex = /@(\w+)/g

    formatted = formatted.replace(mentionRegex, '<span class="mention">@$1</span>')

    return formatted
  },

  // Message validation
  validateMessageContent: (content: string): { isValid: boolean; error?: string } => {
    if (!content || content.trim().length === 0) {
      return { isValid: false, error: 'Message cannot be empty' }
    }

    if (content.length > 4000) {
      return { isValid: false, error: 'Message too long (max 4000 characters)' }
    }

    return { isValid: true }
  },

  // Search utilities
  searchInMessage: (message: ChatMessage, query: string): boolean => {
    if (!query || !message.content) return false

    const searchTerm = query.toLowerCase()
    const content = message.content.toLowerCase()
    const authorName = message.author_name?.toLowerCase() || ''

    return content.includes(searchTerm) || authorName.includes(searchTerm)
  },

  // Message sorting
  sortMessagesByTime: (messages: ChatMessage[]): ChatMessage[] => {
    return [...messages].sort((a, b) => a.created_at - b.created_at)
  },

  // Generate temporary ID for optimistic updates
  generateTempId: (): string => {
    return `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  },

  // Check if message is from today
  isFromToday: (timestamp: number): boolean => {
    const messageDate = new Date(timestamp * 1000)
    const today = new Date()

    return messageDate.toDateString() === today.toDateString()
  },

  // Get message date separator text
  getDateSeparatorText: (timestamp: number): string => {
    const messageDate = new Date(timestamp * 1000)
    const today = new Date()
    const yesterday = new Date(today)

    yesterday.setDate(yesterday.getDate() - 1)

    if (messageDate.toDateString() === today.toDateString()) {
      return 'Today'
    } else if (messageDate.toDateString() === yesterday.toDateString()) {
      return 'Yesterday'
    } else {
      return messageDate.toLocaleDateString()
    }
  },

  // Message status utilities
  getMessageStatus: (message: ChatMessage): 'sending' | 'sent' | 'failed' | 'delivered' => {
    if (message.isOptimistic || message.isSending) return 'sending'
    if (message.sendError) return 'failed'
    if (message.message_id.startsWith('temp_')) return 'sending'

    return 'sent'
  },

  // Reaction utilities
  getReactionSummary: (reactions: any[]): Record<string, { count: number; users: string[] }> => {
    const summary: Record<string, { count: number; users: string[] }> = {}

    reactions.forEach(reaction => {
      if (!summary[reaction.emoji]) {
        summary[reaction.emoji] = { count: 0, users: [] }
      }

      summary[reaction.emoji].count++
      summary[reaction.emoji].users.push(reaction.user_name || reaction.user_uuid)
    })

    return summary
  },

  // File size formatting
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'

    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  // Content sanitization
  sanitizeContent: (content: string): string => {
    return content
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;')
  }
}

// Utility function to safely convert BigInt to string
export const safeBigIntToString = (value: any): string | null => {
  if (value === null || value === undefined) return null
  if (typeof value === 'bigint') return value.toString()

  return value
}

export default MessageUtils
