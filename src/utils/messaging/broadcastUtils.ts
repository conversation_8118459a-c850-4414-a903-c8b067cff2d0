// Broadcast Utilities
// Helper functions for unified broadcast system
// Supports Issue 1.1 fix: Zalo Chat Message Display Delays

import { unifiedBroadcast, UnifiedBroadcastSystem } from '@/services/messaging/unifiedBroadcastSystem'

/**
 * Convert BigInt values to strings for JSON serialization
 */
export function convertBigIntToString(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj
  }
  
  if (typeof obj === 'bigint') {
    return obj.toString()
  }
  
  if (Array.isArray(obj)) {
    return obj.map(convertBigIntToString)
  }
  
  if (typeof obj === 'object') {
    const converted: any = {}

    for (const [key, value] of Object.entries(obj)) {
      converted[key] = convertBigIntToString(value)
    }

    return converted
  }
  
  return obj
}

/**
 * Broadcast Zalo message using unified system
 * Replaces the dual broadcast pattern in Zalo send-message route
 */
export async function broadcastZaloMessage(
  domainUuid: string,
  roomUuid: string,
  messageData: {
    message_id: string | bigint
    content: string
    author_uuid: string
    author_name?: string
    zalo_message_id?: string
    delivery_status?: 'pending' | 'sent' | 'delivered' | 'failed'
    unread_counts?: Record<string, number>
  }
): Promise<void> {
  try {
    // Create unified message
    const unifiedMessage = UnifiedBroadcastSystem.createUnifiedMessage(
      roomUuid,
      messageData.content,
      messageData.author_uuid,
      'zalo',
      {
        messageId: messageData.message_id,
        authorName: messageData.author_name,
        platformMessageId: messageData.zalo_message_id,
        deliveryStatus: messageData.delivery_status,
        unreadCounts: messageData.unread_counts,
        metadata: {
          zalo_message_id: messageData.zalo_message_id
        }
      }
    )

    // Single unified broadcast instead of multiple broadcasts
    await unifiedBroadcast.broadcastMessage(domainUuid, unifiedMessage)

    console.log('Zalo message broadcasted via unified system:', {
      room_uuid: roomUuid,
      message_id: messageData.message_id,
      zalo_message_id: messageData.zalo_message_id
    })
  } catch (error) {
    console.error('Error broadcasting Zalo message:', error)
    
    // Fallback to legacy broadcast if unified system fails
    await fallbackBroadcast(domainUuid, roomUuid, messageData, 'zalo')
  }
}

/**
 * Broadcast Telegram message using unified system
 */
export async function broadcastTelegramMessage(
  domainUuid: string,
  roomUuid: string,
  messageData: {
    message_id: string | bigint
    content: string
    author_uuid: string
    author_name?: string
    telegram_message_id?: string
    delivery_status?: 'pending' | 'sent' | 'delivered' | 'failed'
    unread_counts?: Record<string, number>
  }
): Promise<void> {
  try {
    const unifiedMessage = UnifiedBroadcastSystem.createUnifiedMessage(
      roomUuid,
      messageData.content,
      messageData.author_uuid,
      'telegram',
      {
        messageId: messageData.message_id,
        authorName: messageData.author_name,
        platformMessageId: messageData.telegram_message_id,
        deliveryStatus: messageData.delivery_status,
        unreadCounts: messageData.unread_counts,
        metadata: {
          telegram_message_id: messageData.telegram_message_id
        }
      }
    )

    await unifiedBroadcast.broadcastMessage(domainUuid, unifiedMessage)

    console.log('Telegram message broadcasted via unified system:', {
      room_uuid: roomUuid,
      message_id: messageData.message_id,
      telegram_message_id: messageData.telegram_message_id
    })
  } catch (error) {
    console.error('Error broadcasting Telegram message:', error)
    await fallbackBroadcast(domainUuid, roomUuid, messageData, 'telegram')
  }
}

/**
 * Broadcast Facebook message using unified system
 */
export async function broadcastFacebookMessage(
  domainUuid: string,
  roomUuid: string,
  messageData: {
    message_id: string | bigint
    content: string
    author_uuid: string
    author_name?: string
    facebook_message_id?: string
    delivery_status?: 'pending' | 'sent' | 'delivered' | 'failed'
    unread_counts?: Record<string, number>
  }
): Promise<void> {
  try {
    const unifiedMessage = UnifiedBroadcastSystem.createUnifiedMessage(
      roomUuid,
      messageData.content,
      messageData.author_uuid,
      'facebook',
      {
        messageId: messageData.message_id,
        authorName: messageData.author_name,
        platformMessageId: messageData.facebook_message_id,
        deliveryStatus: messageData.delivery_status,
        unreadCounts: messageData.unread_counts,
        metadata: {
          facebook_message_id: messageData.facebook_message_id
        }
      }
    )

    await unifiedBroadcast.broadcastMessage(domainUuid, unifiedMessage)

    console.log('Facebook message broadcasted via unified system:', {
      room_uuid: roomUuid,
      message_id: messageData.message_id,
      facebook_message_id: messageData.facebook_message_id
    })
  } catch (error) {
    console.error('Error broadcasting Facebook message:', error)
    await fallbackBroadcast(domainUuid, roomUuid, messageData, 'facebook')
  }
}

/**
 * Broadcast internal chat message using unified system
 */
export async function broadcastInternalMessage(
  domainUuid: string,
  roomUuid: string,
  messageData: {
    message_id: string | bigint
    content: string
    author_uuid: string
    author_name?: string
    unread_counts?: Record<string, number>
  }
): Promise<void> {
  try {
    const unifiedMessage = UnifiedBroadcastSystem.createUnifiedMessage(
      roomUuid,
      messageData.content,
      messageData.author_uuid,
      'internal',
      {
        messageId: messageData.message_id,
        authorName: messageData.author_name,
        deliveryStatus: 'sent',
        unreadCounts: messageData.unread_counts
      }
    )

    await unifiedBroadcast.broadcastMessage(domainUuid, unifiedMessage)

    console.log('Internal message broadcasted via unified system:', {
      room_uuid: roomUuid,
      message_id: messageData.message_id
    })
  } catch (error) {
    console.error('Error broadcasting internal message:', error)
    await fallbackBroadcast(domainUuid, roomUuid, messageData, 'internal')
  }
}

/**
 * Fallback broadcast using legacy system
 */
async function fallbackBroadcast(
  domainUuid: string,
  roomUuid: string,
  messageData: any,
  platform: string
): Promise<void> {
  try {
    const socketBroadcast = (global as any).socketBroadcast
    
    if (!socketBroadcast?.broadcastMessage) {
      console.warn('No broadcast system available')
      
return
    }

    const roomName = `${domainUuid}_room_${roomUuid}`

    const fallbackMessage = {
      type: 'message',
      data: convertBigIntToString({
        ...messageData,
        platform,
        room_uuid: roomUuid,
        created_at: Math.floor(Date.now() / 1000),
        flags: 0
      })
    }

    socketBroadcast.broadcastMessage(roomName, fallbackMessage, domainUuid)
    console.log(`Fallback broadcast completed for ${platform} message`)
  } catch (error) {
    console.error('Error in fallback broadcast:', error)
  }
}

/**
 * Get broadcast system statistics
 */
export function getBroadcastStats(): {
  totalQueued: number
  roomCount: number
  isProcessing: boolean
} {
  return unifiedBroadcast.getQueueStats()
}

/**
 * Clear broadcast queue (for testing/debugging)
 */
export function clearBroadcastQueue(): void {
  unifiedBroadcast.clearQueue()
}
