// Facebook Messenger Integration Types
// Following clean code guidelines with clear, descriptive interfaces

export interface FacebookPageConfig {
  config_uuid: string
  domain_uuid: string
  app_id: string
  app_secret: string
  page_id: string
  page_access_token: string
  verify_token: string
  webhook_url?: string
  is_active: boolean
  allowed_events: string[]
  page_settings: Record<string, any>
  insert_date: Date
  insert_user?: string
  update_date: Date
  update_user?: string
}

export interface FacebookPageConfigRequest {
  app_id: string
  app_secret: string
  page_id: string
  page_access_token: string
  verify_token: string
  webhook_url?: string
  allowed_events?: string[]
  page_settings?: Record<string, any>
}

export interface FacebookPageConfigResponse {
  success: boolean
  config?: Omit<FacebookPageConfig, 'app_secret' | 'page_access_token' | 'verify_token'>
  webhook_setup?: boolean
  webhook_error?: string
  message: string
}

export interface FacebookContact {
  contact_uuid: string
  domain_uuid: string
  facebook_user_id: string
  first_name?: string
  last_name?: string
  profile_pic?: string
  locale?: string
  timezone?: number
  gender?: string
  is_payment_enabled: boolean
  is_active: boolean
  last_interaction_date?: Date
  contact_info: Record<string, any>
  insert_date: Date
  insert_user?: string
  update_date: Date
  update_user?: string
}

export interface FacebookChatRoom {
  facebook_room_uuid: string
  domain_uuid: string
  internal_room_uuid: string
  facebook_contact_uuid: string
  assigned_agent_uuid?: string
  room_status: 'active' | 'closed' | 'transferred' | 'pending'
  last_message_at?: Date
  conversation_metadata: Record<string, any>
  insert_date: Date
  insert_user?: string
  update_date: Date
  update_user?: string
}

export interface FacebookMessageMapping {
  mapping_uuid: string
  domain_uuid: string
  internal_message_id: bigint
  facebook_message_id?: string
  facebook_user_id?: string
  message_direction: 'inbound' | 'outbound'
  facebook_event_type?: string
  delivery_status: 'pending' | 'sent' | 'delivered' | 'read' | 'failed'
  error_message?: string
  message_metadata: Record<string, any>
  retry_count: number
  last_retry_at?: Date
  insert_date: Date
}

export interface FacebookWebhookEvent {
  event_id: string
  domain_uuid?: string
  facebook_user_id?: string
  event_name?: string
  msg: Record<string, any>
  insert_date: Date
  insert_user?: string
  update_date: Date
  update_user?: string
}

// Facebook Graph API Types
export interface FacebookUser {
  id: string
  first_name?: string
  last_name?: string
  profile_pic?: string
  locale?: string
  timezone?: number
  gender?: string
  is_payment_enabled?: boolean
}

export interface FacebookMessage {
  mid: string
  text?: string
  attachments?: FacebookAttachment[]
  quick_reply?: {
    payload: string
  }
  reply_to?: {
    mid: string
  }
}

export interface FacebookAttachment {
  type: 'image' | 'audio' | 'video' | 'file'
  payload: {
    url?: string
    attachment_id?: string
    sticker_id?: string
  }
}

export interface FacebookMessagingEvent {
  sender: {
    id: string
  }
  recipient: {
    id: string
  }
  timestamp: number
  message?: FacebookMessage
  postback?: {
    title: string
    payload: string
    referral?: {
      ref: string
      source: string
      type: string
    }
  }
  delivery?: {
    mids: string[]
    watermark: number
  }
  read?: {
    watermark: number
  }
}

export interface FacebookWebhookPayload {
  object: string
  entry: Array<{
    id: string
    time: number
    messaging: FacebookMessagingEvent[]
  }>
}

// Facebook Graph API Response Types
export interface FacebookApiResponse<T = any> {
  data?: T
  error?: {
    message: string
    type: string
    code: number
    error_subcode?: number
    fbtrace_id: string
  }
}

export interface FacebookPageInfo {
  id: string
  name: string
  category: string
  verification_status: string
  access_token?: string
}

export interface FacebookSendMessageRequest {
  room_uuid: string
  content: string
  message_type?: 'text' | 'image' | 'file'
  quick_replies?: Array<{
    content_type: 'text'
    title: string
    payload: string
  }>
}

export interface FacebookSendMessageResponse {
  success: boolean
  facebook_message_id?: string
  error?: string
  internal_message_id?: string
}

// Message Processing Types
export interface FacebookProcessedMessage {
  internal_message_id: string
  room_uuid: string
  contact_uuid: string
  facebook_message_id: string
  delivery_status: string

  // Contact information for display
  contact_info?: {
    first_name?: string | null
    last_name?: string | null
    display_name: string
    facebook_user_id: string
  }

  // Message content for notifications
  content?: string
  external_user_id?: string
}

export enum FacebookEventType {
  MESSAGE = 'message',
  POSTBACK = 'postback',
  DELIVERY = 'delivery',
  READ = 'read',
  OPTIN = 'optin',
  REFERRAL = 'referral'
}

export enum FacebookMessageType {
  TEXT = 0,
  IMAGE = 1,
  FILE = 2,
  SYSTEM = 3
}

// Configuration validation types
export interface FacebookConfigValidation {
  valid: boolean
  errors: string[]
  warnings: string[]
}

export interface FacebookTokenValidation {
  valid: boolean
  error?: string
  pageInfo?: FacebookPageInfo
}

// Webhook verification types
export interface FacebookWebhookVerification {
  mode: string
  token: string
  challenge: string
}

export interface FacebookWebhookSignature {
  signature: string
  body: string
  timestamp: number
}

// UI Component Props Types
export interface FacebookConfigFormProps {
  initialConfig?: Partial<FacebookPageConfigRequest>
  onSave: (config: FacebookPageConfigRequest) => Promise<void>
  onCancel: () => void
  loading?: boolean
}

export interface FacebookConversationListProps {
  conversations: Array<{
    room_uuid: string
    contact_name: string
    last_message: string
    last_message_time: Date
    unread_count: number
    contact_avatar?: string
  }>
  onSelectConversation: (roomUuid: string) => void
  selectedRoomUuid?: string
}

export interface FacebookMessageItemProps {
  message: {
    message_id: string
    content: string
    author_type: 'agent' | 'customer'
    created_at: Date
    delivery_status?: string
    attachments?: Array<{
      type: string
      url: string
      filename: string
    }>
  }
  showDeliveryStatus?: boolean
}
