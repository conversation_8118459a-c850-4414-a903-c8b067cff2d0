// Internal Domain Chat System Types
// Discord-inspired optimized types for AgentDeskLite

// =====================================================
// ENUMS AND CONSTANTS
// =====================================================

export enum MessageType {
  TEXT = 0,
  IMAGE = 1,
  FILE = 2,
  SYSTEM = 3,
  CALL = 4
}

export enum RoomType {
  DIRECT = 'direct',
  GROUP = 'group',
  DEPARTMENT = 'department',
  BROADCAST = 'broadcast'
}

export enum ParticipantRole {
  OWNER = 'owner',
  ADMIN = 'admin',
  MODERATOR = 'moderator',
  MEMBER = 'member'
}

export enum UserStatus {
  ONLINE = 'online',
  AWAY = 'away',
  BUSY = 'busy',
  OFFLINE = 'offline'
}

export enum NotificationType {
  MESSAGE = 'message',
  MENTION = 'mention',
  REPLY = 'reply',
  ROOM_INVITE = 'room_invite'
}

// Message flags (bitfield)
export const MESSAGE_FLAGS = {
  DELETED: 1 << 0, // 1
  PINNED: 1 << 1, // 2
  SYSTEM: 1 << 2, // 4
  URGENT: 1 << 3, // 8
  SILENT: 1 << 4, // 16
  EDITED: 1 << 5 // 32
} as const

// =====================================================
// CORE INTERFACES
// =====================================================

export interface ChatRoom {
  room_uuid: string
  domain_uuid: string
  room_name: string | null
  room_description: string | null
  room_type: RoomType
  room_avatar: string | null
  created_by_user_uuid: string
  is_active: boolean
  is_archived: boolean
  max_participants: number
  room_settings: Record<string, any>
  insert_date: string
  insert_user: string | null
  update_date: string
  update_user: string | null

  // Platform information
  platform?: 'internal' | 'telegram' | 'zalo' | 'facebook'
  platform_data?: TelegramRoomData | ZaloRoomData | FacebookRoomData | null

  // Computed properties
  participant_count?: number
  unread_count?: number
  last_message?: ChatMessage
  is_muted?: boolean
  user_role?: ParticipantRole
  participants?: ChatRoomParticipant[]
  unread_counts?: Record<string, number> // Map of user_uuid -> unread_count for socket broadcasts
}

export interface TelegramRoomData {
  telegram_room_uuid: string
  telegram_contact_uuid: string
  room_status: string
  assigned_agent_uuid: string | null
  contact: {
    telegram_user_id: string
    telegram_chat_id: string
    username: string | null
    first_name: string | null
    last_name: string | null
    phone: string | null
    language_code: string | null
    last_interaction_date: string | null
  }
}

export interface ZaloRoomData {
  zalo_room_uuid: string
  zalo_contact_uuid: string
  room_status: string
  assigned_agent_uuid: string | null
  contact: {
    zalo_user_id: string
    display_name: string | null
    avatar_url: string | null
    phone: string | null
    last_interaction_date: string | null
  }
}

export interface FacebookRoomData {
  facebook_room_uuid: string
  facebook_contact_uuid: string
  room_status: string
  assigned_agent_uuid: string | null
  contact: {
    facebook_user_id: string
    first_name: string | null
    last_name: string | null
    profile_pic: string | null
    locale: string | null
    timezone: number | null
    is_active: boolean
    last_interaction_date: string | null
  }
}

export interface ChatRoomParticipant {
  participant_uuid: string
  room_uuid: string
  user_uuid: string
  participant_role: ParticipantRole
  joined_date: string
  last_read_message_id: string | null
  is_muted: boolean
  notification_settings: {
    mentions: boolean
    all_messages: boolean
  }
  deleted_at: Date | string | null // Soft deletion timestamp for conversation hiding (Date from Prisma, string from API)
  insert_date: string
  insert_user: string | null
  update_date: string
  update_user: string | null

  // User details (populated from joins)
  user_name?: string
  user_email?: string
  user_avatar?: string
  full_name?: string
  department?: string
}

export interface ChatMessage {
  message_id: string // BigInt as string for JavaScript
  room_uuid: string
  author_uuid: string
  content: string | null
  message_type: MessageType
  reply_to: string | null // BigInt as string
  edited_at: number | null // Unix timestamp
  created_at: number // Unix timestamp
  flags: number

  // Author details (populated from joins)
  author_name?: string
  author_avatar?: string
  author_email?: string

  // Computed properties (can be set by API or computed from flags)
  is_deleted?: boolean // Computed from flags or set by API
  is_edited?: boolean // Computed from edited_at
  is_pinned?: boolean // Computed from flags
  is_system?: boolean // Computed from flags
  is_urgent?: boolean // Computed from flags

  // Relations (populated when needed)
  attachments?: MessageAttachment[]
  reactions?: MessageReaction[]
  reply_parent?: ChatMessage

  // UI state
  isOptimistic?: boolean // For optimistic updates
  isSending?: boolean // For sending state
  sendError?: string // For error state

  // Socket broadcast data
  unread_counts?: Record<string, number> // user_uuid -> unread_count mapping
}

export interface MessageReaction {
  message_id: string
  user_uuid: string
  emoji: string
  created_at: number // Unix timestamp

  // User details (populated from joins)
  user_name?: string
  user_avatar?: string
}

export interface MessageAttachment {
  attachment_id: string
  message_id: string
  filename: string
  file_path: string
  file_size: number | null
  content_type: string | null
  width: number | null
  height: number | null

  // Computed properties
  is_image?: boolean
  is_video?: boolean
  is_audio?: boolean
  is_document?: boolean
  thumbnail_url?: string
  download_url?: string
}

export interface UserPresence {
  presence_uuid: string
  user_uuid: string
  domain_uuid: string
  status: UserStatus
  last_seen: string
  current_activity: string | null
  socket_id: string | null
  update_date: string

  // User details (populated from joins)
  user_name?: string
  user_avatar?: string
  user_email?: string
}

export interface DomainUser {
  user_uuid: string
  username: string
  email: string
  display_name: string
  status: string
  enabled: boolean
}

export interface ChatNotification {
  notification_uuid: string
  user_uuid: string
  room_uuid: string
  message_id: string
  notification_type: NotificationType
  is_read: boolean
  is_sent: boolean
  insert_date: string
  read_date: string | null
  expires_at: string

  // Related data (populated from joins)
  room_name?: string
  sender_name?: string
  message_content?: string
  message_preview?: string // Truncated content
}

export interface UnreadCount {
  user_uuid: string
  room_uuid: string
  unread_count: number
  last_read_message_id: string | null
  last_updated: string
}

// =====================================================
// API REQUEST/RESPONSE TYPES
// =====================================================

export interface CreateRoomRequest {
  room_name: string
  room_description?: string
  room_type: RoomType
  participant_uuids?: string[]
  room_settings?: Record<string, any>
}

export interface SendMessageRequest {
  content: string
  message_type?: MessageType
  reply_to?: string
  attachments?: File[]
}

export interface EditMessageRequest {
  content: string
}

export interface UpdatePresenceRequest {
  status: UserStatus
  current_activity?: string
}

export interface SearchMessagesRequest {
  query: string
  room_uuid?: string
  message_type?: MessageType
  date_from?: string
  date_to?: string
  page?: number
  limit?: number
}

export interface PaginationParams {
  page: number
  limit: number
  before?: string // Message ID for pagination
  after?: string // Message ID for pagination
}

export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
  pagination?: {
    page: number
    limit: number
    total: number
    has_more: boolean
  }
}

// =====================================================
// UTILITY TYPES
// =====================================================

export interface MessageUtils {
  isDeleted: (flags: number) => boolean
  isEdited: (message: ChatMessage) => boolean
  isPinned: (flags: number) => boolean
  isSystem: (flags: number) => boolean
  isUrgent: (flags: number) => boolean

  markDeleted: (flags: number) => number
  markPinned: (flags: number) => number
  markSystem: (flags: number) => number
  markUrgent: (flags: number) => number

  unmarkDeleted: (flags: number) => number
  unmarkPinned: (flags: number) => number

  formatTimestamp: (timestamp: number) => Date
  getRelativeTime: (timestamp: number) => string
  getMessageTypeLabel: (type: MessageType) => string
  getFileIcon: (contentType: string) => string
}

export interface ChatState {

  // Connection state
  isConnected: boolean
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error'

  // Current user
  currentUser: any | null

  // Rooms
  rooms: Record<string, ChatRoom>
  activeRoomId: string | null
  roomsLoading: boolean
  roomsInitialized: boolean

  // Messages
  messages: Record<string, ChatMessage[]> // roomId -> messages
  messagesLoading: Record<string, boolean>
  hasMoreMessages: Record<string, boolean>

  // Users & Presence
  domainUsers: Record<string, any>
  userPresence: Record<string, UserPresence>
  onlineUsers: string[]

  // UI State
  sidebarOpen: boolean
  selectedThread: string | null
  typingUsers: Record<string, string[]> // roomId -> userIds

  // Notifications
  unreadCounts: Record<string, number> // roomId -> count
  notifications: ChatNotification[]
  totalUnreadCount: number

  // File uploads
  uploadProgress: Record<string, number>

  // Error state
  error: string | null

  // Virtual rooms for new conversations (before first message)
  virtualRooms: Record<string, { user: any; created_at: string }>
}

// =====================================================
// WEBSOCKET EVENT TYPES
// =====================================================

export interface SocketEvents {

  // Client to Server
  chat_connect: { domain_uuid: string; user_uuid: string }
  join_room: { room_uuid: string }
  leave_room: { room_uuid: string }
  send_message: SendMessageRequest & { room_uuid: string }
  typing_start: { room_uuid: string }
  typing_stop: { room_uuid: string }
  update_presence: UpdatePresenceRequest

  // Server to Client
  chat_connected: { user_uuid: string; domain_uuid: string; online_users: string[] }
  room_joined: { room_uuid: string; room_name: string; participant_count: number }
  new_message: ChatMessage
  message_updated: ChatMessage
  message_deleted: { message_uuid: string; room_uuid: string }
  user_typing: { room_uuid: string; user_uuid: string; user_name: string }
  user_stopped_typing: { room_uuid: string; user_uuid: string }
  user_presence_changed: UserPresence
  room_created: ChatRoom
  room_updated: ChatRoom
  participant_added: { room_uuid: string; participant: ChatRoomParticipant }
  participant_removed: { room_uuid: string; user_uuid: string }
  notification: ChatNotification
  error: { error_code: string; error_message: string; context?: any }
}
