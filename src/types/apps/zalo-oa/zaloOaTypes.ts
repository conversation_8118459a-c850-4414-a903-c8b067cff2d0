// ZALO OA Integration Types
// Types for ZALO Official Account integration with internal chat system

import type { JsonValue } from '@prisma/client/runtime/library'

// =====================================================
// ZALO OA CONTACT TYPES
// =====================================================

export interface ZaloOaContact {
  contact_uuid: string
  domain_uuid: string
  zalo_user_id: string
  display_name?: string
  avatar_url?: string
  phone?: string
  is_follower?: boolean
  last_interaction_date?: Date
  contact_info?: JsonValue
  insert_date?: Date
  insert_user?: string
  update_date?: Date
  update_user?: string
}

export interface CreateZaloOaContactRequest {
  zalo_user_id: string
  display_name?: string
  avatar_url?: string
  phone?: string
  is_follower?: boolean
  contact_info?: JsonValue
}

export interface UpdateZaloOaContactRequest {
  display_name?: string
  avatar_url?: string
  phone?: string
  is_follower?: boolean
  contact_info?: JsonValue
}

// =====================================================
// ZALO OA CHAT ROOM TYPES
// =====================================================

export type ZaloOaChatRoomStatus = 'active' | 'closed' | 'transferred' | 'pending'

export interface ZaloOaChatRoom {
  zalo_room_uuid: string
  domain_uuid: string
  internal_room_uuid: string
  zalo_contact_uuid: string
  assigned_agent_uuid?: string
  room_status: ZaloOaChatRoomStatus
  last_message_at?: Date
  conversation_metadata?: JsonValue
  insert_date?: Date
  insert_user?: string
  update_date?: Date
  update_user?: string
}

export interface CreateZaloOaChatRoomRequest {
  internal_room_uuid: string
  zalo_contact_uuid: string
  assigned_agent_uuid?: string
  room_status?: ZaloOaChatRoomStatus
  conversation_metadata?: JsonValue
}

export interface UpdateZaloOaChatRoomRequest {
  assigned_agent_uuid?: string
  room_status?: ZaloOaChatRoomStatus
  conversation_metadata?: JsonValue
}

export interface AssignZaloOaChatRoomRequest {
  zalo_room_uuid: string
  assigned_agent_uuid: string
}

// =====================================================
// ZALO MESSAGE MAPPING TYPES
// =====================================================

export type ZaloMessageDirection = 'inbound' | 'outbound'
export type ZaloMessageDeliveryStatus = 'pending' | 'sent' | 'delivered' | 'failed'

export interface ZaloMessageMapping {
  mapping_uuid: string
  domain_uuid: string
  internal_message_id: bigint
  zalo_message_id?: string
  zalo_user_id?: string
  message_direction: ZaloMessageDirection
  zalo_event_name?: string
  delivery_status: ZaloMessageDeliveryStatus
  error_message?: string
  insert_date?: Date
}

export interface CreateZaloMessageMappingRequest {
  internal_message_id: bigint
  zalo_message_id?: string
  zalo_user_id?: string
  message_direction: ZaloMessageDirection
  zalo_event_name?: string
  delivery_status?: ZaloMessageDeliveryStatus
  error_message?: string
}

export interface UpdateZaloMessageMappingRequest {
  delivery_status?: ZaloMessageDeliveryStatus
  error_message?: string
}

// =====================================================
// ZALO OA API REQUEST/RESPONSE TYPES
// =====================================================

export interface ZaloOaContactListRequest {
  domain_uuid: string
  page?: number
  limit?: number
  search?: string
  is_follower?: boolean
}

export interface ZaloOaContactListResponse {
  contacts: ZaloOaContact[]
  total: number
  page: number
  limit: number
}

export interface ZaloOaChatRoomListRequest {
  domain_uuid: string
  page?: number
  limit?: number
  status?: ZaloOaChatRoomStatus
  assigned_agent_uuid?: string
}

export interface ZaloOaChatRoomListResponse {
  chat_rooms: ZaloOaChatRoom[]
  total: number
  page: number
  limit: number
}

// =====================================================
// ZALO OA WEBHOOK TYPES
// =====================================================

export interface ZaloOaWebhookEvent {
  app_id: string
  sender: {
    id: string
  }
  user_id_by_app: string
  recipient: {
    id: string
  }
  event_name: string
  message?: {
    text?: string
    msg_id: string
    attachments?: any[]
  }
  timestamp: number
}

export interface ZaloOaWebhookProcessingResult {
  success: boolean
  contact_uuid?: string
  room_uuid?: string
  message_id?: bigint
  error?: string
}

// =====================================================
// ZALO OA SERVICE TYPES
// =====================================================

export interface ZaloOaMessageSendRequest {
  zalo_user_id: string
  message_content: string
  message_type?: 'text' | 'image' | 'file'
  internal_message_id?: bigint
}

export interface ZaloOaMessageSendResponse {
  success: boolean
  zalo_message_id?: string
  error?: string
  quota_info?: {
    quota_type: string
    remain?: string
    total?: string
  }
}

// =====================================================
// ZALO OA SOCKET EVENTS
// =====================================================

export interface ZaloOaSocketEvents {
  zalo_message_received: (data: { room_uuid: string; contact: ZaloOaContact; message: any }) => void

  zalo_contact_status_changed: (data: { contact_uuid: string; is_follower: boolean }) => void

  zalo_room_assigned: (data: { room_uuid: string; agent_uuid: string; agent_name: string }) => void

  zalo_message_delivered: (data: {
    internal_message_id: bigint
    zalo_message_id: string
    delivery_status: ZaloMessageDeliveryStatus
  }) => void
}

// =====================================================
// ZALO OA INTEGRATION CONTEXT
// =====================================================

export interface ZaloOaIntegrationContext {
  domain_uuid: string
  user_uuid: string
  contacts: ZaloOaContact[]
  active_rooms: ZaloOaChatRoom[]
  is_connected: boolean
  last_sync: Date
}
