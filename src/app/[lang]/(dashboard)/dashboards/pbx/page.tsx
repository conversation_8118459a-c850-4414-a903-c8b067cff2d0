import DashboardFusionPBX from '@/views/dashboards/pbx'

import Cook<PERSON><PERSON><PERSON>Auth from '@/libs/sessionTokenAuthJS'
import { getUserDomain } from '@/libs/dashboards/pbx/server/common'

const Dashboard = async () => {
  const sessionTokenAuthJS = await CookieTokenAuth()
  const domain_obj = await getUserDomain(sessionTokenAuthJS)

  return (
    <div>
      <DashboardFusionPBX sessionTokenAuthJS={sessionTokenAuthJS} domainName={domain_obj.domain_name || ''} />
    </div>
  )
}

export default Dashboard
