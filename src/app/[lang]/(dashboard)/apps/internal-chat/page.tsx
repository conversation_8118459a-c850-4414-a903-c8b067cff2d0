// Internal Chat Full Page
// Complete responsive chat interface for expanded chat experience

'use client'

import React, { useEffect } from 'react'

import { useAppDispatch, useAppSelector } from '@/redux-store/hooks'
import {
  setActiveRoom,
  setSidebarOpen,
  fetchRoomMessages,
  markMessagesAsRead
} from '@/redux-store/slices/internal-chat/internalChatSlice'

// Hooks
import { useChatInitialization } from '@/hooks/chat/useChatInitialization'
import { useChatSocket } from '@/hooks/chat/useChatSocket'
import useMediaQuery from '@/@menu/hooks/useMediaQuery'

// Components
import ChatRoomList from '@/components/chat/ChatRoomList'
import ChatHeader from '@/components/chat/ChatHeader'
import VirtualChatHeader from '@/components/chat/VirtualChatHeader'
import MessageList from '@/components/chat/MessageList'
import SmartMessageInput from '@/components/chat/SmartMessageInput'

// Responsive breakpoints following the application's design system
const BREAKPOINTS = {
  sm: '600px',
  md: '900px',
  lg: '1200px',
  xl: '1536px'
} as const

const InternalChatPage = () => {
  const dispatch = useAppDispatch()

  const { rooms, activeRoomId, sidebarOpen, isConnected, connectionStatus, unreadCounts, virtualRooms } =
    useAppSelector(state => state.internalChatReducer)

  // Media queries for responsive behavior
  const isMobile = useMediaQuery(BREAKPOINTS.md) // Mobile below 900px
  const isDesktop = !useMediaQuery(BREAKPOINTS.lg) // Desktop above 1200px

  // Initialize chat rooms using centralized hook
  useChatInitialization({
    autoInitialize: true // Auto-initialize when page loads
  })

  // Socket functionality for room management - reuse existing connection
  const { joinRoom, leaveRoom, isSocketConnected } = useChatSocket()

  // Load messages when room is selected (skip for virtual rooms)
  useEffect(() => {
    if (activeRoomId && !virtualRooms[activeRoomId]) {
      dispatch(fetchRoomMessages({ roomId: activeRoomId, limit: 50 }))
    }
  }, [activeRoomId, dispatch, virtualRooms])

  // Join/leave rooms via socket when active room changes (skip for virtual rooms)
  useEffect(() => {
    if (activeRoomId && (isConnected || isSocketConnected) && !virtualRooms[activeRoomId]) {
      console.log('Internal Chat Page - Joining room:', activeRoomId)
      joinRoom(activeRoomId)

      // Mark messages as read when entering a room
      if (unreadCounts[activeRoomId] > 0) {
        dispatch(markMessagesAsRead({ roomId: activeRoomId }))
      }

      // Leave previous room if there was one
      return () => {
        console.log('Internal Chat Page - Leaving room:', activeRoomId)
        leaveRoom(activeRoomId)
      }
    }
  }, [activeRoomId, isConnected, isSocketConnected, joinRoom, leaveRoom, unreadCounts, dispatch, virtualRooms])

  // Handle responsive sidebar behavior
  useEffect(() => {
    const handleResize = () => {
      // On mobile, close sidebar when room is selected
      if (isMobile && activeRoomId) {
        dispatch(setSidebarOpen(false))
      }

      // On desktop, keep sidebar open
      else if (isDesktop) {
        dispatch(setSidebarOpen(true))
      }
    }

    handleResize() // Initial check
    window.addEventListener('resize', handleResize)

    return () => window.removeEventListener('resize', handleResize)
  }, [activeRoomId, dispatch, isMobile, isDesktop])

  const handleRoomSelect = (roomId: string) => {
    dispatch(setActiveRoom(roomId))

    // On mobile, close sidebar after selecting room
    if (isMobile) {
      dispatch(setSidebarOpen(false))
    }
  }

  const handleBackToRoomList = () => {
    dispatch(setActiveRoom(null))
    dispatch(setSidebarOpen(true))
  }

  // Get active room (real or virtual)
  const activeRoom = activeRoomId ? rooms[activeRoomId] : null
  const activeVirtualRoom = activeRoomId ? virtualRooms[activeRoomId] : null
  const isVirtualRoom = !!activeVirtualRoom

  // Responsive container classes
  const getContainerClasses = () => {
    if (isMobile) {
      return 'flex flex-col h-[calc(100vh-120px)] overflow-hidden'
    }

    return 'flex h-[calc(100vh-160px)] overflow-hidden'
  }

  // Responsive sidebar classes
  const getSidebarClasses = () => {
    if (isMobile) {
      return `${
        sidebarOpen ? 'w-full' : 'w-0'
      } transition-all duration-300 bg-white border-r overflow-hidden absolute inset-0 z-10`
    }

    return `${sidebarOpen ? 'w-80' : 'w-0'} transition-all duration-300 border-r bg-gray-50 overflow-hidden lg:w-80`
  }

  return (
    <div className={getContainerClasses()}>
      {/* Mobile overlay for sidebar */}
      {isMobile && sidebarOpen && (
        <div className='fixed inset-0 bg-black bg-opacity-50 z-5' onClick={() => dispatch(setSidebarOpen(false))} />
      )}

      {/* Chat Content */}
      <div className='flex flex-1 overflow-hidden relative'>
        {/* Sidebar */}
        <div className={getSidebarClasses()}>
          <ChatRoomList onRoomSelect={handleRoomSelect} compact={isMobile} className='h-full' />
        </div>

        {/* Main Chat Area */}
        <div className='flex-1 flex flex-col min-w-0'>
          {activeRoom || isVirtualRoom ? (
            <>
              {/* Chat Header */}
              {isVirtualRoom ? (
                <VirtualChatHeader
                  user={activeVirtualRoom.user}
                  onBack={!sidebarOpen || isMobile ? handleBackToRoomList : undefined}
                  compact={isMobile}
                />
              ) : (
                <ChatHeader
                  room={activeRoom!}
                  onBack={!sidebarOpen || isMobile ? handleBackToRoomList : undefined}
                  compact={isMobile}
                />
              )}

              {/* Connection Status */}
              {!isConnected && (
                <div className={`${isMobile ? 'px-4 py-3' : 'px-4 py-2'} bg-yellow-50 border-b border-yellow-200`}>
                  <div className='flex items-center space-x-2 text-yellow-700'>
                    <div
                      className={`animate-spin rounded-full ${
                        isMobile ? 'h-4 w-4 border-b-2' : 'h-3 w-3 border-b-2'
                      } border-yellow-600`}
                    />
                    <span className={`${isMobile ? 'text-sm' : 'text-xs'}`}>
                      {connectionStatus === 'connecting' ? 'Connecting...' : 'Disconnected'}
                    </span>
                  </div>
                </div>
              )}

              {/* Messages */}
              <MessageList roomId={activeRoomId!} compact={isMobile} className='flex-1 min-h-0' />

              {/* Message Input */}
              <div className='flex-shrink-0'>
                <SmartMessageInput roomId={activeRoomId!} compact={isMobile} />
              </div>
            </>
          ) : (

            // Welcome screen
            <div className='flex-1 flex items-center justify-center bg-gray-50'>
              <div className={`text-center ${isMobile ? 'max-w-sm px-6' : 'max-w-md'}`}>
                <div
                  className={`${
                    isMobile ? 'w-20 h-20' : 'w-24 h-24'
                  } bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6`}
                >
                  <i className={`ri-chat-3-line ${isMobile ? 'text-3xl' : 'text-4xl'} text-blue-600`} />
                </div>
                <h2 className={`${isMobile ? 'text-xl' : 'text-2xl'} font-semibold text-gray-800 mb-3`}>
                  Welcome to Internal Chat
                </h2>
                <p className={`text-gray-600 mb-6 leading-relaxed ${isMobile ? 'text-sm' : ''}`}>
                  Connect and collaborate with your team members in real-time. Select a conversation from the sidebar to
                  start chatting.
                </p>

                {/* Quick actions */}
                <div className='space-y-3'>
                  <button
                    onClick={() => dispatch(setSidebarOpen(true))}
                    className={`w-full bg-blue-600 text-white ${
                      isMobile ? 'px-4 py-3 text-sm' : 'px-6 py-3'
                    } rounded-lg hover:bg-blue-700 transition-colors font-medium`}
                  >
                    <i className='ri-chat-new-line mr-2' />
                    Browse Conversations
                  </button>

                  <button
                    className={`w-full bg-gray-100 text-gray-700 ${
                      isMobile ? 'px-4 py-3 text-sm' : 'px-6 py-3'
                    } rounded-lg hover:bg-gray-200 transition-colors font-medium`}
                  >
                    <i className='ri-group-line mr-2' />
                    Create New Group
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default InternalChatPage
