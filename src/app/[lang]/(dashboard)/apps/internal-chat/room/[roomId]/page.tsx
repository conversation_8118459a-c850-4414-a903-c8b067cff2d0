// Internal Chat Room Page
// Direct link to specific chat room with responsive design

'use client'

import React, { useEffect } from 'react'

import { usePara<PERSON>, useRouter } from 'next/navigation'

import { useAppDispatch, useAppSelector } from '@/redux-store/hooks'
import {
  setActiveRoom,
  setSidebarOpen,
  fetchRoomMessages,
  markMessagesAsRead
} from '@/redux-store/slices/internal-chat/internalChatSlice'

// Hooks
import { useChatInitialization } from '@/hooks/chat/useChatInitialization'
import { useChatSocket } from '@/hooks/chat/useChatSocket'
import useMediaQuery from '@/@menu/hooks/useMediaQuery'

// Components
import ChatRoomList from '@/components/chat/ChatRoomList'
import ChatHeader from '@/components/chat/ChatHeader'
import MessageList from '@/components/chat/MessageList'
import SmartMessageInput from '@/components/chat/SmartMessageInput'

// Responsive breakpoints following the application's design system
const BREAKPOINTS = {
  sm: '600px',
  md: '900px',
  lg: '1200px',
  xl: '1536px'
} as const

const ChatRoomPage = () => {
  const params = useParams()
  const router = useRouter()
  const dispatch = useAppDispatch()

  const roomId = params.roomId as string

  const { rooms, activeRoomId, sidebarOpen, connectionStatus, isConnected, unreadCounts } = useAppSelector(
    state => state.internalChatReducer
  )

  // Media queries for responsive behavior
  const isMobile = useMediaQuery(BREAKPOINTS.md) // Mobile below 900px

  // Initialize chat rooms using centralized hook
  const { isLoading: roomsLoading } = useChatInitialization({
    autoInitialize: true // Auto-initialize when page loads
  })

  // Socket functionality for room management - reuse existing connection
  const { joinRoom, leaveRoom, isSocketConnected } = useChatSocket()

  // Set active room when roomId changes
  useEffect(() => {
    if (roomId && roomId !== activeRoomId) {
      dispatch(setActiveRoom(roomId))
    }
  }, [roomId, activeRoomId, dispatch])

  // Load messages when room is selected
  useEffect(() => {
    if (roomId) {
      dispatch(fetchRoomMessages({ roomId, limit: 50 }))
    }
  }, [roomId, dispatch])

  // Join/leave rooms via socket when room changes
  useEffect(() => {
    if (roomId && (isConnected || isSocketConnected)) {
      joinRoom(roomId)

      // Mark messages as read when entering a room
      if (unreadCounts[roomId] > 0) {
        dispatch(markMessagesAsRead({ roomId }))
      }

      // Leave previous room if there was one
      return () => {
        leaveRoom(roomId)
      }
    }
  }, [roomId, isConnected, isSocketConnected, joinRoom, leaveRoom, unreadCounts, dispatch])

  // Check if room exists
  const activeRoom = roomId ? rooms[roomId] : null
  const roomExists = activeRoom !== undefined

  const handleRoomSelect = (newRoomId: string) => {
    router.push(`/apps/internal-chat/room/${newRoomId}`)
  }

  const handleBackToRoomList = () => {
    router.push('/apps/internal-chat')
  }

  const handleToggleSidebar = () => {
    dispatch(setSidebarOpen(!sidebarOpen))
  }

  // Show loading while fetching rooms
  if (roomsLoading) {
    return (
      <div className='h-[calc(100vh-120px)] bg-white rounded-lg shadow-sm border border-gray-200 flex items-center justify-center'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4' />
          <p className='text-gray-600'>Loading chat rooms...</p>
        </div>
      </div>
    )
  }

  // Show error if room doesn't exist
  if (!roomsLoading && !roomExists && Object.keys(rooms).length > 0) {
    return (
      <div className='h-[calc(100vh-120px)] bg-white rounded-lg shadow-sm border border-gray-200 flex items-center justify-center'>
        <div className='text-center max-w-md'>
          <div className='w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4'>
            <i className='ri-error-warning-line text-2xl text-red-600' />
          </div>
          <h2 className='text-xl font-semibold text-gray-800 mb-2'>Room Not Found</h2>
          <p className='text-gray-600 mb-6'>
            The chat room you&apos;re looking for doesn&apos;t exist or you don&apos;t have access to it.
          </p>
          <button
            onClick={handleBackToRoomList}
            className='bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors'
          >
            <i className='ri-arrow-left-line mr-2' />
            Back to Chat
          </button>
        </div>
      </div>
    )
  }

  // Responsive container classes
  const getContainerClasses = () => {
    if (isMobile) {
      return 'bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden h-[calc(100vh-120px)]'
    }

    return 'bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden h-[calc(100vh-160px)]'
  }

  // Responsive sidebar classes
  const getSidebarClasses = () => {
    if (isMobile) {
      return `${
        sidebarOpen ? 'w-full' : 'w-0'
      } transition-all duration-300 bg-white border-r overflow-hidden absolute inset-0 z-10`
    }

    return `${sidebarOpen ? 'w-80' : 'w-0'} transition-all duration-300 border-r bg-gray-50 overflow-hidden lg:w-80`
  }

  return (
    <div className={getContainerClasses()}>
      {/* Mobile overlay for sidebar */}
      {isMobile && sidebarOpen && (
        <div className='fixed inset-0 bg-black bg-opacity-50 z-5' onClick={() => dispatch(setSidebarOpen(false))} />
      )}

      {/* Header */}
      <div className={`flex items-center justify-between ${isMobile ? 'p-3' : 'p-4'} border-b bg-gray-50`}>
        <div className='flex items-center space-x-3 flex-1 min-w-0'>
          <button
            onClick={handleToggleSidebar}
            className={`${isMobile ? 'p-2' : 'p-2'} hover:bg-gray-200 rounded transition-colors ${
              isMobile ? 'block' : 'lg:hidden'
            }`}
          >
            <i className='ri-menu-line text-gray-600' />
          </button>

          <div className='flex items-center space-x-2 flex-1 min-w-0'>
            <div
              className={`${isMobile ? 'w-3 h-3' : 'w-3 h-3'} rounded-full flex-shrink-0 ${
                connectionStatus === 'connected'
                  ? 'bg-green-500'
                  : connectionStatus === 'connecting'
                    ? 'bg-yellow-500'
                    : 'bg-red-500'
              }`}
            />
            <h1 className={`${isMobile ? 'text-lg' : 'text-xl'} font-semibold text-gray-800 truncate`}>
              Internal Chat
            </h1>
            {activeRoom && !isMobile && (
              <span className='text-sm text-gray-500 truncate'>/ {activeRoom.room_name || 'Unnamed Room'}</span>
            )}
          </div>
        </div>

        <div className='flex items-center space-x-2 flex-shrink-0'>
          <button
            onClick={() => dispatch(setSidebarOpen(!sidebarOpen))}
            className={`p-2 hover:bg-gray-200 rounded transition-colors ${isMobile ? 'hidden' : 'hidden lg:block'}`}
            title={sidebarOpen ? 'Hide sidebar' : 'Show sidebar'}
          >
            <i className={`ri-sidebar-${sidebarOpen ? 'unfold' : 'fold'}-line text-gray-600`} />
          </button>
        </div>
      </div>

      {/* Chat Content */}
      <div className='flex h-full relative'>
        {/* Sidebar */}
        <div className={getSidebarClasses()}>
          <ChatRoomList onRoomSelect={handleRoomSelect} compact={isMobile} className='h-full' />
        </div>

        {/* Main Chat Area */}
        <div className='flex-1 flex flex-col min-w-0'>
          {activeRoom ? (
            <>
              {/* Chat Header */}
              <ChatHeader
                room={activeRoom}
                onBack={!sidebarOpen || isMobile ? handleBackToRoomList : undefined}
                compact={isMobile}
              />

              {/* Messages */}
              <MessageList roomId={roomId} compact={isMobile} className='flex-1 min-h-0' />

              {/* Message Input */}
              <div className='flex-shrink-0'>
                <SmartMessageInput roomId={roomId} compact={isMobile} />
              </div>
            </>
          ) : (

            // Loading state for specific room
            <div className='flex-1 flex items-center justify-center bg-gray-50'>
              <div className='text-center'>
                <div
                  className={`animate-spin rounded-full ${
                    isMobile ? 'h-6 w-6' : 'h-8 w-8'
                  } border-b-2 border-blue-600 mx-auto mb-4`}
                />
                <p className={`text-gray-600 ${isMobile ? 'text-sm' : ''}`}>Loading room...</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ChatRoomPage
