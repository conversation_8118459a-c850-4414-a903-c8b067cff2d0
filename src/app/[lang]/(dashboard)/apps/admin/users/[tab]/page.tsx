// React Imports
import type { ReactElement } from 'react'

// Next Imports
import dynamic from 'next/dynamic'

// MUI Imports

// Type Imports
import type { v_user_settings, v_users } from '@prisma/client'

import UserView from '@views/apps/admin/users/view/index'

import type { UserProfilesType } from '@/types/apps/admin/userTypes'

// Component Imports
import UserList from '@views/apps/admin/users/list'

// Data Imports

import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

import { getUserData } from '@/libs/admin/Users'
import { getListUserSetting } from '@/libs/admin/UserSetting'
import { getListContactName } from '@/libs/admin/Contacts'

import { getListDomainName } from '@/libs/pbx/Domains'

import { getListGroupName } from '@/libs/admin/Groups'

import type { GroupNameType } from '@/types/apps/admin/groupTypes'
import type { DomainNameType } from '@/types/apps/admin/domainTypes'

import type { ContactNameType } from '@/types/apps/admin/contactTypes'

const OverViewTab = dynamic(() => import('@views/apps/admin/users/view/overview'))
const SecurityTab = dynamic(() => import('@views/apps/admin/users/view/security'))
const ContactTab = dynamic(() => import('@views/apps/admin/users/view/contact'))
const LogTab = dynamic(() => import('@views/apps/admin/users/view/logs'))

// Vars
const tabContentList = (data: UserProfilesType): { [key: string]: ReactElement } => ({
  overview: <OverViewTab data={data} />,
  security: <SecurityTab data={data} />,
  contact: <ContactTab uuid={data.users.contact_uuid} token={data.token} />,
  log: <LogTab uuid={data.users.user_uuid} token={data.token} />
})

/**
 * ! If you need data using an API call, uncomment the below API code, update the `process.env.API_URL` variable in the
 * ! `.env` file found at root of your project and also update the API endpoints like `/pages/pricing` in below example.
 * ! Also, remove the above server action import and the action itself from the `src/app/server/actions.ts` file to clean up unused code
 * ! because we've used the server action for getting our static data.
 */

const UserViewTab = async ({
  params,
  searchParams
}: {
  params: { tab: string }
  searchParams: { [key: string]: string | undefined }
}) => {
  // Vars
  const sessionTokenAuthJS = await CookieTokenAuth()
  const tab = params.tab

  switch (tab) {
    case 'edit':
      break
    case 'view':
      const uuid = searchParams['id'] ?? '' // default value is "1"

      try {
        // Lấy các dữ liệu  song song
        const [user_data, usersetting_data, contact_data, domain_data, group_data]: [
          v_users,
          v_user_settings[],
          ContactNameType[],
          DomainNameType[],
          GroupNameType[]
        ] = await Promise.all([
          getUserData(uuid),
          getListUserSetting(),
          getListContactName(),
          getListDomainName(),
          getListGroupName()
        ])

        const user_uuid = user_data.user_uuid
        const contact_uuid = user_data.contact_uuid

        console.log('user_data = ', user_data)
        console.log('user_uuid = ', user_uuid)
        console.log('contact_uuid = ', contact_uuid)

        // Tạo đối tượng usergroup
        const usergroup: UserProfilesType = {
          token: sessionTokenAuthJS,
          users: user_data,
          userSettings: usersetting_data,
          contacts: contact_data,
          domains: domain_data,
          groups: group_data
        }

        // Xử lý dữ liệu usergroup ở đây
        return <UserView data={user_data} tabContentList={tabContentList(usergroup)} />
      } catch (error) {
        console.error('Lỗi khi tải dữ liệu:', error)

        return <>Loading database ... error</>
      }

      break
    case 'list':
    default:
      return <UserList token={sessionTokenAuthJS} />
  }
}

export default UserViewTab
