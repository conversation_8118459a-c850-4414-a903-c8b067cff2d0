// Component Imports
import User<PERSON>ist from '@views/apps/admin/users/list'

import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

/**
 * ! If you need data using an API call, uncomment the below API code, update the `process.env.API_URL` variable in the
 * ! `.env` file found at root of your project and also update the API endpoints like `/apps/user-list` in below example.
 * ! Also, remove the above server action import and the action itself from the `src/app/server/actions.ts` file to clean up unused code
 * ! because we've used the server action for getting our static data.
 */

const UserListApp = async () => {
  // Vars

  const sessionTokenAuthJS = await CookieTokenAuth()

  return <UserList token={sessionTokenAuthJS} />
}

export default UserListApp
