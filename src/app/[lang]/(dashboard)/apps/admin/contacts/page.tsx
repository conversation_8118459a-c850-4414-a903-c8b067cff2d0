import type { v_contacts } from '@prisma/client'

// Component Imports
import { getListContact } from '@/libs/admin/Contacts'
import ContactList from '@views/apps/admin/contacts/list'
import <PERSON>ieTokenAuth from '@/libs/sessionTokenAuthJS'

/**
 * ! If you need data using an API call, uncomment the below API code, update the `process.env.API_URL` variable in the
 * ! `.env` file found at root of your project and also update the API endpoints like `/apps/user-list` in below example.
 * ! Also, remove the above server action import and the action itself from the `src/app/server/actions.ts` file to clean up unused code
 * ! because we've used the server action for getting our static data.
 */

const ContactListApp = async () => {
  // Vars

  const data = await getListContact()

  // console.log('DATA HERE ...')
  // console.log(data)
  const sessionTokenAuthJS = await CookieTokenAuth()
  const contact_data: v_contacts[] = data

  return <ContactList token={sessionTokenAuthJS} contactData={contact_data} />
}

export default ContactListApp
