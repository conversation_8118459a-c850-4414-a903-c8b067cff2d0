// React Imports
import type { ReactElement } from 'react'

// Next Imports
import dynamic from 'next/dynamic'

// MUI Imports

// Type Imports

import type { v_contacts } from '@prisma/client'

import CookieTokenAuth from '@/libs/sessionTokenAuthJS'
import type { ContactProfilesType } from '@/types/apps/admin/contactTypes'

// Component Imports
import ContactView from '@views/apps/admin/contacts/view'
import ContactList from '@views/apps/admin/contacts/list'

// Data Imports

import { getListContact, getContactData } from '@/libs/admin/Contacts'

const OverViewTab = dynamic(() => import('@views/apps/admin/contacts/view/overview'))
const AddressTab = dynamic(() => import('@views/apps/admin/contacts/view/address'))
const EmailTab = dynamic(() => import('@views/apps/admin/contacts/view/email'))
const NumberTab = dynamic(() => import('@views/apps/admin/contacts/view/number'))
const UrlTab = dynamic(() => import('@views/apps/admin/contacts/view/url'))
const RelationTab = dynamic(() => import('@views/apps/admin/contacts/view/relation'))
const SettingTab = dynamic(() => import('@views/apps/admin/contacts/view/setting'))

// Vars
const tabContentList = (data: ContactProfilesType): { [key: string]: ReactElement } => ({
  overview: <OverViewTab data={data.contact} />,
  number: <NumberTab />,
  address: <AddressTab />,
  email: <EmailTab />,
  url: <UrlTab />,
  relation: <RelationTab />,
  setting: <SettingTab />
})

/**
 * ! If you need data using an API call, uncomment the below API code, update the `process.env.API_URL` variable in the
 * ! `.env` file found at root of your project and also update the API endpoints like `/pages/pricing` in below example.
 * ! Also, remove the above server action import and the action itself from the `src/app/server/actions.ts` file to clean up unused code
 * ! because we've used the server action for getting our static data.
 */

const UserViewTab = async ({
  params,
  searchParams
}: {
  params: { tab: string }
  searchParams: { [key: string]: string | undefined }
}) => {
  // Vars
  const tab = params.tab
  const sessionTokenAuthJS = await CookieTokenAuth()

  switch (tab) {
    case 'edit':
      break
    case 'view':
      const uuid = searchParams['id'] ?? '' // default value is "1"

      console.log(uuid)

      // const data = await getCallBlockData(uuid, sessionTokenAuthJS)

      // console.log('DATA HERE ...')
      // console.log(data)
      const contact_data: v_contacts = await getContactData(uuid)

      const contactgroup: ContactProfilesType = {
        contact: contact_data
      }

      return <ContactView data={contactgroup} tabContentList={tabContentList(contactgroup)} />
      break
    case 'list':
    default:
      const data = await getListContact()

      // console.log('DATA HERE ...')
      // console.log(data)

      const contact: v_contacts[] = data

      return <ContactList token={sessionTokenAuthJS} contactData={contact} />
  }
}

export default UserViewTab
