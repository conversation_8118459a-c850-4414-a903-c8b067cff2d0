import type { v_user_logs } from '@prisma/client'

// Component Imports
import { getListUserLog } from '@/libs/admin/UserLogs'
import UserLogsList from '@views/apps/admin/logs/list'

// Data Imports
// import { getUserData } from '@/app/server/actions'

/**
 * ! If you need data using an API call, uncomment the below API code, update the `process.env.API_URL` variable in the
 * ! `.env` file found at root of your project and also update the API endpoints like `/apps/user-list` in below example.
 * ! Also, remove the above server action import and the action itself from the `src/app/server/actions.ts` file to clean up unused code
 * ! because we've used the server action for getting our static data.
 */

const UserListApp = async () => {
  // Vars

  const data = await getListUserLog()

  // console.log('DATA HERE ...')
  // console.log(data)

  const userLogs_data: v_user_logs[] = data

  return <UserLogsList userLogsData={userLogs_data} />
}

export default UserListApp
