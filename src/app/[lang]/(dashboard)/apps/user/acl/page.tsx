'use client'

// ** React Imports
import { useContext } from 'react'

// ** Context Imports

// ** MUI Imports
import Grid from '@mui/material/Grid'
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import Typography from '@mui/material/Typography'
import Card<PERSON>ontent from '@mui/material/CardContent'

import { AbilityContext } from '@/libs/acl/abilities'

const ACLPage = () => {
  // ** Hooks
  const ability = useContext(AbilityContext)

  console.log('Current ability:', ability?.rules) // Debug: In ra các quy tắc hiện tại

  return (
    <Grid container spacing={6}>
      <Grid item md={6} xs={12}>
        <Card>
          <CardHeader title='Common' />
          <CardContent>
            <Typography sx={{ mb: 4 }}>No ability is required to view this card</Typography>
            <Typography sx={{ color: 'primary.main' }}>
              This card is visible to &lsquo;user&lsquo; and &lsquo;admin&lsquo; both
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      {ability && ability.can('read', 'user') ? (
        <Grid item md={6} xs={12}>
          <Card>
            <CardHeader title='Analytics' />
            <CardContent>
              <Typography sx={{ mb: 4 }}>
                User with &lsquo;Analytics&lsquo; subject&lsquo;s &lsquo;Read&lsquo; ability can view this card
              </Typography>
              <Typography sx={{ color: 'error.main' }}>This card is visible to &lsquo;admin&lsquo; only</Typography>
            </CardContent>
          </Card>
        </Grid>
      ) : null}
    </Grid>
  )
}

export default ACLPage
