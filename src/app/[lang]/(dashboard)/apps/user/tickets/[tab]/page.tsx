import TicketList from '@views/apps/user/tickets/list'

import CookieTokenAuth from '@/libs/sessionTokenAuthJS'
import TicketView from '@/views/apps/user/tickets/view'

const TicketListApp = async ({
  params,
  searchParams
}: {
  params: { tab: string }
  searchParams: { [key: string]: string | undefined }
}) => {
  // Vars
  const sessionTokenAuthJS = await CookieTokenAuth()

  // console.log('session-token : %s', sessionTokenAuthJS)

  const tab = params.tab

  switch (tab) {
    case 'edit':
      break
    case 'view':
      const uuid = searchParams['id'] ?? '' // default value is "1"

      return <TicketView token={sessionTokenAuthJS} ticket_uuid={uuid} />
      break
    case 'list':
      return <TicketList token={sessionTokenAuthJS} />
    default:
  }
}

export default TicketListApp
