// MUI Imports
import Grid from '@mui/material/Grid'

// Components Imports
import CongratulationsJohn from '@views/apps/user/dashboard/CongratulationsJohn'
import CardStatVertical from '@components/card-statistics/Vertical'
import LineChart from '@views/apps/user/dashboard/LineChart'
import TotalTransactions from '@views/apps/user/dashboard/TotalTransactions'
import Performance from '@views/apps/user/dashboard/Performance'
import ProjectStatistics from '@views/apps/user/dashboard/ProjectStatistics'
import Bar<PERSON>hart from '@views/apps/user/dashboard/BarChart'
import RadialBar<PERSON>hart from '@views/apps/user/dashboard/RadialBarChart'
import SalesCountry from '@views/apps/user/dashboard/SalesCountry'
import TopReferralSources from '@views/apps/user/dashboard/TopReferralSources'
import WeeklySales from '@views/apps/user/dashboard/WeeklySales'
import VisitsByDay from '@views/apps/user/dashboard/VisitsByDay'
import ActivityTimeline from '@views/apps/user/dashboard/ActivityTimeline'

// Server Action Imports
import { getServerMode } from '@core/utils/serverHelpers'

const DashboardAnalytics = () => {
  // Vars
  const serverMode = getServerMode()

  return (
    <Grid container spacing={6}>
      <Grid item xs={12} md={8}>
        <CongratulationsJohn serverMode={serverMode} />
      </Grid>
      <Grid item xs={12} sm={6} md={2}>
        <CardStatVertical
          stats='155k'
          avatarColor='primary'
          title='Total Orders'
          chipText='Last 4 Month'
          avatarIcon='ri-shopping-cart-line'
          avatarSkin='light'
          chipColor='secondary'
        />
      </Grid>
      <Grid item xs={12} sm={6} md={2}>
        <LineChart serverMode={serverMode} />
      </Grid>
      <Grid item xs={12} md={8}>
        <TotalTransactions serverMode={serverMode} />
      </Grid>
      <Grid item xs={12} sm={6} md={4}>
        <Performance serverMode={serverMode} />
      </Grid>
      <Grid item xs={12} sm={6} md={4}>
        <ProjectStatistics serverMode={serverMode} />
      </Grid>
      <Grid item xs={12} sm={6} md={4}>
        <Grid container spacing={6}>
          <Grid item xs={6}>
            <BarChart />
          </Grid>
          <Grid item xs={6}>
            <CardStatVertical
              stats='$13.4k'
              avatarColor='success'
              title='Total Sales'
              chipText='Last Six Month'
              avatarIcon='ri-handbag-line'
              avatarSkin='light'
              chipColor='secondary'
            />
          </Grid>
          <Grid item xs={6}>
            <CardStatVertical
              stats='142.8k'
              avatarColor='info'
              title='Total Impression'
              chipText='Last One Month'
              avatarIcon='ri-link'
              avatarSkin='light'
              chipColor='secondary'
            />
          </Grid>
          <Grid item xs={6}>
            <RadialBarChart serverMode={serverMode} />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} sm={6} md={4}>
        <SalesCountry serverMode={serverMode} />
      </Grid>
      <Grid item xs={12} md={8}>
        <TopReferralSources />
      </Grid>
      <Grid item xs={12} sm={6} md={4}>
        <WeeklySales serverMode={serverMode} />
      </Grid>
      <Grid item xs={12} sm={6} md={4}>
        <VisitsByDay serverMode={serverMode} />
      </Grid>
      <Grid item xs={12} md={8}>
        <ActivityTimeline />
      </Grid>
    </Grid>
  )
}

export default DashboardAnalytics
