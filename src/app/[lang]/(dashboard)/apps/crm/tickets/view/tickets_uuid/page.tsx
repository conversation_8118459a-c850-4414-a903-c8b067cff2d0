import TicketList from '@views/apps/crm/tickets/preview'
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

const TicketListApp = async ({ params }: { params: { ticket_uuid: string } }) => {
  const { ticket_uuid } = params
  const sessionTokenAuthJS = await CookieTokenAuth()

  return <TicketList sessionTokenAuthJS={sessionTokenAuthJS} ticket_uuid={ticket_uuid} />
}

export default TicketListApp
