import Grid from '@mui/material/Grid'

import EditTickets from '@/views/apps/crm/tickets/edit/EditTickets'

// Data Imports
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

const EditPage = async ({ searchParams }: { searchParams: { id: string } }) => {
  const { id } = searchParams

  const sessionTokenAuthJS = await CookieTokenAuth()

  return (
    <Grid container spacing={6}>
      <Grid item xs={12}>
        <EditTickets ticket_uuid={id} sessionTokenAuthJS={sessionTokenAuthJS} />
      </Grid>
    </Grid>
  )
}

export default EditPage
