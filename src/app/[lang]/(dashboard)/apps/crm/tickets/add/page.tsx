import Grid from '@mui/material/Grid'

import AddActions from '@views/apps/crm/tickets/add/AddActions'
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

const TicketAdd = async () => {
  const sessionTokenAuthJS = await CookieTokenAuth()

  return (
    <Grid container spacing={6}>
      <Grid item xs={12}>
        <AddActions sessionTokenAuthJS={sessionTokenAuthJS} />
      </Grid>
    </Grid>
  )
}

export default TicketAdd
