// Third-party Imports

import classnames from 'classnames'

// Component Imports
import KanbanBoard from '@/views/apps/crm/tasks/KanbanBoard'

// Util Imports
import { commonLayoutClasses } from '@layouts/utils/layoutClasses'

// Styles Imports
import styles from '@/views/apps/crm/tasks/styles.module.css'
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

const KanbanPage = async () => {
  const sessionTokenAuthJS = await CookieTokenAuth()

  return (
    <div
      className={classnames(
        commonLayoutClasses.contentHeightFixed,
        styles.scroll,
        'is-full overflow-auto pis-2 -mis-2'
      )}
    >
      <KanbanBoard sessionTokenAuthJS={sessionTokenAuthJS} />
    </div>
  )
}

export default KanbanPage
