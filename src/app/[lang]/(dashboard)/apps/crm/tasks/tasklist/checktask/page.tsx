import StartTask from '@/views/apps/crm/tasks/tasklist/starttask/StartTask'

import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

const CheckTaskApp = async ({ searchParams }: { searchParams: { id: string } }) => {
  const sessionTokenAuthJS = await CookieTokenAuth()
  const id = searchParams.id

  return <StartTask sessionTokenAuthJS={sessionTokenAuthJS} id={id} />
}

export default CheckTaskApp
