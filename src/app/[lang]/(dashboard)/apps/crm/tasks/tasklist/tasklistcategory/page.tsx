import CookieTokenAuth from '@/libs/sessionTokenAuthJS'
import ListTaskView from '@/views/apps/crm/tasks/tasklist/tasklistcategory/TaskList'

const TaskListCategoryApp = async ({ searchParams }: { searchParams: { id: string } }) => {
  const sessionTokenAuthJS = await CookieTokenAuth()
  const id = searchParams.id

  return <ListTaskView sessionTokenAuthJS={sessionTokenAuthJS} id={id} />
}

export default TaskListCategoryApp
