import UserView from '@/views/apps/crm/accounts/view'
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'
import type { sessionToken } from '@/types/otherTypes'

const UserViewTab = async ({ searchParams }: { params: { contact_uuid: string }; searchParams: { id: string } }) => {
  const sessionTokenAuthJS: sessionToken = await CookieTokenAuth()

  const { id } = searchParams

  return <UserView id={id} sessionTokenAuthJS={sessionTokenAuthJS} />
}

export default UserViewTab
