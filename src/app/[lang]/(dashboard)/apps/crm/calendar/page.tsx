import Card from '@mui/material/Card'

import CalendarWrapper from '@views/apps/crm/calendar/CalendarWrapper'

import AppFullCalendar from '@/libs/styles/AppFullCalendar'
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

const CalendarApp = async () => {
  const sessionTokenAuthJS = await CookieTokenAuth()

  return (
    <Card className='overflow-visible'>
      <AppFullCalendar className='app-calendar'>
        <CalendarWrapper sessionTokenAuthJS={sessionTokenAuthJS} />
      </AppFullCalendar>
    </Card>
  )
}

export default CalendarApp
