import QueueManagement from '@/views/apps/supervisor/queue-management'

import type { AgentRealtimeType } from '@/types/apps/pbx/agentTypes'
import { getAgentRealtimes } from '@/libs/pbx/Agents'
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

const QueueManagementPage = async () => {
  // Fetch agent data for the page
  const agentData: AgentRealtimeType[] = await getAgentRealtimes()

  const sessionTokenAuthJS = await CookieTokenAuth()

  return <QueueManagement agentData={agentData} token={sessionTokenAuthJS} />
}

export default QueueManagementPage
