// Supervisor - Social Platforms Configuration
// Page for configuring and managing social platform integrations (Telegram, ZALO, etc.)

import type { Metadata } from 'next'

import SocialPlatformsDashboard from '@/components/admin/social/SocialPlatformsDashboard'

export const metadata: Metadata = {
  title: 'Social Platforms - Supervisor',
  description: 'Configure and manage social platform integrations for internal chat system'
}

const SocialPlatformsPage = () => {
  return (
    <div className='p-6'>
      <div className='mb-6'>
        <h1 className='text-2xl font-bold text-gray-900'>Social Platforms</h1>
        <p className='text-gray-600 mt-2'>
          Configure and manage social platform integrations (Telegram, ZALO, etc.) for the internal chat system.
        </p>
      </div>

      <SocialPlatformsDashboard />
    </div>
  )
}

export default SocialPlatformsPage
