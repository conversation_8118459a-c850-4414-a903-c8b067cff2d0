import SupervisorQueue from '@/views/apps/supervisor/index'

import type { AgentRealtimeType } from '@/types/apps/pbx/agentTypes'
import { getAgentRealtimes } from '@/libs/pbx/Agents'
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

const SupervisorPage = async () => {
  // Vars

  const listData: AgentRealtimeType[] = await getAgentRealtimes()

  // console.log('DATA HERE ...')

  const sessionTokenAuthJS = await CookieTokenAuth()

  const list_data: AgentRealtimeType[] = listData

  return <SupervisorQueue agentData={list_data} token={sessionTokenAuthJS} />
}

export default SupervisorPage
