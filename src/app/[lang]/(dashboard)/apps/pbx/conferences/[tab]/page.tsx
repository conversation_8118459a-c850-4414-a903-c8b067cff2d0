// Component Imports

import Cook<PERSON><PERSON>okenAuth from '@/libs/sessionTokenAuthJS'
import CfrList from '@views/apps/pbx/conferences/list'
import CFRView from '@views/apps/pbx/conferences/view'
import { getListCFR, getCFRData } from '@/libs/pbx/CFR'

// Data Imports

const CfrPage = async ({
  params,
  searchParams
}: {
  params: { tab: string }
  searchParams: { [key: string]: string | undefined }
}) => {
  // Vars

  const tab = params.tab
  const uuid = searchParams['id'] ?? ''
  const token = await CookieTokenAuth()

  switch (tab) {
    case 'edit':
    case 'add':
      return <CFRView token={token} />
    case 'view':
      const viewData = await getCFRData(uuid)

      return <CFRView data={viewData} token={token} uuid={uuid} />
    case 'list':
      break
    default:
      const data = await getListCFR()
      const sessionTokenAuthJS = await <PERSON>ieTokenAuth()

      return <CfrList token={sessionTokenAuthJS} data={data} />
  }
}

export default CfrPage
