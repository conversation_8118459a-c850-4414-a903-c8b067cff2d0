// Component Imports
import type { v_xml_cdr } from '@prisma/client'

import { getCDRByID } from '@/libs/pbx/Cdr'
import CDRsList from '@/views/apps/pbx/cdr/list'
import CDRsView from '@/views/apps/pbx/cdr/view'
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

// Vars

const CDRsPage = async ({
  params,
  searchParams
}: {
  params: { tab: string }
  searchParams: { [key: string]: string | undefined }
}) => {
  // Vars

  const tab = params.tab
  const sessionTokenAuthJS = await CookieTokenAuth()

  switch (tab) {
    case 'edit':
      break
    case 'view':
      const uuid = searchParams['id'] ?? '' // default value is "1"

      console.log(uuid)

      const data = await getCDRByID(uuid)

      const ext_data: v_xml_cdr = data

      return <CDRsView data={ext_data} />
      break
    case 'list':
    default:
      return <CDRsList sessionTokenAuthJS={sessionTokenAuthJS} />
  }
}

export default CDRsPage
