import type { v_devices } from '@prisma/client'

import { getListDevice } from '@/libs/pbx/Devices'

// Component Imports

import DevicesList from '@views/apps/pbx/devices/list'

// Data Imports
// import { getUserData } from '@/app/server/actions'

/**
 * ! If you need data using an API call, uncomment the below API code, update the `process.env.API_URL` variable in the
 * ! `.env` file found at root of your project and also update the API endpoints like `/apps/user-list` in below example.
 * ! Also, remove the above server action import and the action itself from the `src/app/server/actions.ts` file to clean up unused code
 * ! because we've used the server action for getting our static data.
 */

const DevicesListApp = async () => {
  // Vars
  const data = await getListDevice()

  // console.log('DATA HERE ...')
  // console.log(data)

  const device_data: v_devices[] = data

  return <DevicesList data={device_data} />
}

export default DevicesListApp
