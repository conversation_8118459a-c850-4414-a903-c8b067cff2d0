import type { ReactElement } from 'react'

import dynamic from 'next/dynamic'

import type { v_devices } from '@prisma/client'

import DeviceView from '@views/apps/pbx/devices/view/index'

import DevicesList from '@/views/apps/pbx/devices/list'
import { getListDevice, getDevice } from '@/libs/pbx/Devices'

// Component Imports

// Data Imports
// import { getUserData } from '@/app/server/actions'
const DeviceTab = dynamic(() => import('@views/apps/pbx/devices/view/device/index'))

// Vars
const tabContentList = (data: v_devices): { [key: string]: ReactElement } => ({
  endpoint: <DeviceTab data={data} />

  // profile: <InfoTab data={data?.users.profile} />,
  // teams: <BranchesTab data={data?.users.teams} />,
  // projects: <ProjectsTab data={data?.users.projects} />,
  // connections: <ConnectionsTab data={data?.users.connections} />
})

/**
 * ! If you need data using an API call, uncomment the below API code, update the `process.env.API_URL` variable in the
 * ! `.env` file found at root of your project and also update the API endpoints like `/apps/user-list` in below example.
 * ! Also, remove the above server action import and the action itself from the `src/app/server/actions.ts` file to clean up unused code
 * ! because we've used the server action for getting our static data.
 */

const DevicePage = async ({
  params,
  searchParams
}: {
  params: { tab: string }
  searchParams: { [key: string]: string | undefined }
}) => {
  // Vars
  const tab = params.tab

  switch (tab) {
    case 'edit':
      break
    case 'view':
      const uuid = searchParams['id'] ?? '' // default value is "1"

      console.log(uuid)

      const data = await getDevice(uuid)

      // console.log('DATA HERE ...')
      // console.log(data)

      const device_data: v_devices = data

      return <DeviceView data={device_data} tabContentList={tabContentList(device_data)} />
      break
    case 'list':
    default:
      const listData = await getListDevice()

      // console.log('DATA HERE ...')
      // console.log(data)

      const list_device_data: v_devices[] = listData

      return <DevicesList data={list_device_data} />
  }
}

export default DevicePage
