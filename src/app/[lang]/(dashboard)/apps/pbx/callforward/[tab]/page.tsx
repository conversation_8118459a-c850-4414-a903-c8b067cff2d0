// Component Imports
import type { v_extensions } from '@prisma/client'

import { getExtData, getListExtData } from '@/libs/pbx/Extensions'
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'
import CallfwList from '@/views/apps/pbx/callforward/list'
import CallfwView from '@/views/apps/pbx/callforward/view'

// Vars

const CallfwPage = async ({
  params,
  searchParams
}: {
  params: { tab: string }
  searchParams: { [key: string]: string | undefined }
}) => {
  // Vars

  const tab = params.tab
  const uuid = searchParams['id'] ?? ''

  switch (tab) {
    case 'edit':
      break
    case 'view':
      const viewData = await getExtData(uuid)
      const token = await CookieTokenAuth()

      return <CallfwView data={viewData} token={token} />
      break
    case 'list':
      break
    default:
      const data = await getListExtData()
      const sessionTokenAuthJS = await CookieTokenAuth()
      const ext_data: v_extensions[] = data

      return <CallfwList token={sessionTokenAuthJS} data={ext_data} />
  }
}

export default CallfwPage
