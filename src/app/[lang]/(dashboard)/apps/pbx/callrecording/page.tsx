import type { view_call_recordings } from '@prisma/client'

import { getListCallRecordingData } from '@/libs/pbx/CallRecording'

import CallRecordList from '@views/apps/pbx/callrecording/list'

const CallRecordListApp = async () => {
  // Vars

  const data = await getListCallRecordingData()

  // console.log('DATA HERE ...')
  // console.log(data)

  const gw_data: view_call_recordings[] = data

  return <CallRecordList data={gw_data} />
}

export default CallRecordListApp
