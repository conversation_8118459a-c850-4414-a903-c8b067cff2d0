import type { ReactElement } from 'react'

import dynamic from 'next/dynamic'

import type { view_call_recordings } from '@prisma/client'

import CallRecordingView from '@views/apps/pbx/callrecording/view/index'

import CallRecordingList from '@/views/apps/pbx/callrecording/list'
import { getListCallRecordingData, getCallRecordingData } from '@/libs/pbx/CallRecording'

import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

const RecordingTab = dynamic(() => import('@views/apps/pbx/callrecording/view/recording'))

// Vars
const tabContentList = (data: view_call_recordings): { [key: string]: ReactElement } => ({
  endpoint: <RecordingTab data={data} />
})

const CallRecordingPage = async ({
  params,
  searchParams
}: {
  params: { tab: string }
  searchParams: { [key: string]: string | undefined }
}) => {
  // Vars
  const tab = params.tab
  const sessionTokenAuthJS = await CookieTokenAuth()

  switch (tab) {
    case 'edit':
      break
    case 'view':
      const uuid = searchParams['id'] ?? '' // default value is "1"

      console.log(uuid)

      const data = await getCallRecordingData(uuid)

      // console.log('DATA HERE ...')
      // console.log(data)

      const callrecording_data: view_call_recordings = data

      return <CallRecordingView data={callrecording_data} tabContentList={tabContentList(callrecording_data)} />
      break
    case 'list':
    default:
      const listData = await getListCallRecordingData()

      // console.log('DATA HERE ...')
      // console.log(data)

      const list_callrecording_data: view_call_recordings[] = listData

      return <CallRecordingList data={list_callrecording_data} sessionTokenAuthJS={sessionTokenAuthJS} />
  }
}

export default CallRecordingPage
