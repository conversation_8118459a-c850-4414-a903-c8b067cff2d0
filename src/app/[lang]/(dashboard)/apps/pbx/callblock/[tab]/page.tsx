import type { ReactElement } from 'react'

import dynamic from 'next/dynamic'

import Call<PERSON>lockView from '@views/apps/pbx/callblock/view/index'
import { getListExtName } from '@/libs/pbx/Extensions'
import type { CallBlockEditType, CallBlockViewType, CallBlockGroupType } from '@/types/apps/pbx/callblockTypes'
import CallBlockList from '@/views/apps/pbx/callblock/list'

// Component Imports

// Data Imports

import type { ListExt } from '@/types/apps/pbx/extensionTypes'
import type { DomainNameType } from '@/types/apps/admin/domainTypes'
import { getListDomainName } from '@/libs/pbx/Domains'
import type { ListVoicemail } from '@/types/apps/pbx/voicemailTypes'
import { getListVoiceMailName } from '@/libs/pbx/Voicemail'
import { getCallBlockData, getListCallBlockData } from '@/libs/pbx/CallBlock'

// import { getUserData } from '@/app/server/actions'
const CallBlockTab = dynamic(() => import('@views/apps/pbx/callblock/view/callblock'))
const ListBlockTab = dynamic(() => import('@views/apps/pbx/callblock/view/listblock'))

// Vars
const tabContentList = (data: CallBlockGroupType): { [key: string]: ReactElement } => ({
  callblock: <CallBlockTab data={data} />,
  listblock: <ListBlockTab data={data} />

  // profile: <InfoTab data={data?.users.profile} />,
  // teams: <BranchesTab data={data?.users.teams} />,
  // projects: <ProjectsTab data={data?.users.projects} />,
  // connections: <ConnectionsTab data={data?.users.connections} />
})

/**
 * ! If you need data using an API call, uncomment the below API code, update the `process.env.API_URL` variable in the
 * ! `.env` file found at root of your project and also update the API endpoints like `/apps/user-list` in below example.
 * ! Also, remove the above server action import and the action itself from the `src/app/server/actions.ts` file to clean up unused code
 * ! because we've used the server action for getting our static data.
 */

const CallBlockPage = async ({
  params,
  searchParams
}: {
  params: { tab: string }
  searchParams: { [key: string]: string | undefined }
}) => {
  // Vars
  const tab = params.tab

  switch (tab) {
    case 'edit':
      break
    case 'view':
      const uuid = searchParams['id'] ?? '' // default value is "1"

      console.log(uuid)

      // const data = await getCallBlockData(uuid, sessionTokenAuthJS)

      // console.log('DATA HERE ...')
      // console.log(data)

      const cb_data: CallBlockEditType = await getCallBlockData(uuid)
      const ext_data: ListExt[] = await getListExtName()
      const domain_data: DomainNameType[] = await getListDomainName()
      const vm_data: ListVoicemail[] = await getListVoiceMailName()

      const callblockgroup: CallBlockGroupType = {
        callblocks: cb_data,
        domains: domain_data,
        extensions: ext_data,
        voicemails: vm_data
      }

      return <CallBlockView data={callblockgroup} tabContentList={tabContentList(callblockgroup)} />
      break
    case 'list':
    default:
      const listData = await getListCallBlockData()

      // console.log('DATA HERE ...')
      // console.log(data)

      const list_cb_data: CallBlockViewType[] = listData

      return <CallBlockList data={list_cb_data} />
  }
}

export default CallBlockPage
