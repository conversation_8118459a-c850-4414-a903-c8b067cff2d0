// Component Imports

import Call<PERSON><PERSON><PERSON>ist from '@views/apps/pbx/callblock/list'

// Data Imports

import type { CallBlockViewType } from '@/types/apps/pbx/callblockTypes'
import { getListCallBlockData } from '@/libs/pbx/CallBlock'

// import { getUserData } from '@/app/server/actions'

/**
 * ! If you need data using an API call, uncomment the below API code, update the `process.env.API_URL` variable in the
 * ! `.env` file found at root of your project and also update the API endpoints like `/apps/user-list` in below example.
 * ! Also, remove the above server action import and the action itself from the `src/app/server/actions.ts` file to clean up unused code
 * ! because we've used the server action for getting our static data.
 */

const CallBlockListApp = async () => {
  // Vars

  const data = await getListCallBlockData()

  // console.log('DATA HERE ...')
  // console.log(data)

  const cb_data: CallBlockViewType[] = data

  return <CallBlockList data={cb_data} />
}

export default CallBlockListApp
