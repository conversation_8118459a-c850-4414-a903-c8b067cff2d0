// Component Imports

import CookieTokenAuth from '@/libs/sessionTokenAuthJS'
import CfrList from '@views/apps/pbx/conferences/list'
import CFRView from '@views/apps/pbx/conferences/view'
import { getListCFR, getCFRData } from '@/libs/pbx/CFR'
import ConferencesInteractive from '@/views/apps/pbx/conferences_active/view'

// Data Imports

const CfrPage = async ({
  params,
  searchParams
}: {
  params: { tab: string }
  searchParams: { [key: string]: string | undefined }
}) => {
  // Vars

  const tab = params.tab
  const uuid = searchParams['id'] ?? ''
  const c = searchParams['c'] ?? ''
  const token = await CookieTokenAuth()

  switch (tab) {
    case 'edit':
    case 'add':
      return <CFRView token={token} />
    case 'view':
      const viewData = await getCFRData(uuid)

      return <CFRView data={viewData} token={token} />
    case 'conference_interactive':
      return <ConferencesInteractive c={c} token={token} />
    case 'list':
      break
    default:
      const data = await getListCFR()
      const sessionTokenAuthJS = await CookieTokenAuth()

      return <CfrList token={sessionTokenAuthJS} data={data} />
  }
}

export default CfrPage
