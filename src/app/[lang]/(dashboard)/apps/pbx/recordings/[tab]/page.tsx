// Component Imports
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'
import RecordingEdit from '@/views/apps/pbx/recordings/view/RecordingDetails'
import RecordingList from '@/views/apps/pbx/recordings/list'
import { getRecordingData } from '@/libs/pbx/Recordings'
import { getDictionary } from '@/utils/getDictionary'
import type { Locale } from '@/configs/i18n'

// Vars

const RecordingPage = async ({
  params,
  searchParams
}: {
  params: { tab: string; lang: Locale }
  searchParams: { [key: string]: string | undefined }
}) => {
  // Vars

  const tab = params.tab

  const sessionTokenAuthJS = await CookieTokenAuth()
  const lang = params.lang
  const dictionary = await getDictionary(lang)

  switch (tab) {
    case 'edit':
      break
    case 'view':
      const uuid = searchParams['id'] ?? '' // default value is "1"

      console.log(uuid)

      const data = await getRecordingData(uuid)

      return <RecordingEdit dictionary={dictionary} data={data} token={sessionTokenAuthJS} />
      break
    case 'list':
    default:
      return <RecordingList sessionTokenAuthJS={sessionTokenAuthJS} />
  }
}

export default RecordingPage
