import type { sessionToken } from '@/types/otherTypes'

import ManagerList from '@views/apps/pbx/dialplans/managers/list'

import type { DialplansType } from '@/types/apps/pbx/dialplanTypes'

// import getSession from '@/actions/getSession'

// Component Imports

// Data Imports
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

// import { getUserData } from '@/app/server/actions'

/**
 * ! If you need data using an API call, uncomment the below API code, update the `process.env.API_URL` variable in the
 * ! `.env` file found at root of your project and also update the API endpoints like `/apps/user-list` in below example.
 * ! Also, remove the above server action import and the action itself from the `src/app/server/actions.ts` file to clean up unused code
 * ! because we've used the server action for getting our static data.
 */

const getListDialplanData = async (cId: string, sessionTokenAuthJS: sessionToken) => {
  // Vars

  const res = await fetch(`${process.env.API_URL}/pbx/dialplans/${cId}/list`, {
    method: 'GET',
    headers: {
      Cookie: `${sessionTokenAuthJS.name}=${sessionTokenAuthJS.value}`
    }
  })

  if (!res.ok) {
    throw new Error('Failed to fetch userData')
  }

  return await res.json()
}

const ManagerListApp = async ({ params }: { params: { categoryId: string } }) => {
  // Vars

  const cId = params.categoryId

  const sessionTokenAuthJS = await CookieTokenAuth()

  const data = await getListDialplanData(cId, sessionTokenAuthJS)

  // console.log('DATA HERE ...')
  // console.log(data)

  const mgt_data: DialplansType[] = data

  return <ManagerList data={mgt_data} />
}

export default ManagerListApp
