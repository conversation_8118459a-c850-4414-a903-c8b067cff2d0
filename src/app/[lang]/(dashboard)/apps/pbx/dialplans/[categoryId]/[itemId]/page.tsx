import type { ReactElement } from 'react'

import dynamic from 'next/dynamic'

import type { sessionToken } from '@/types/otherTypes'

import DialplanList from '@views/apps/pbx/dialplans/managers/list'
import DialplanView from '@views/apps/pbx/dialplans/managers/view'

import type { DialplansType } from '@/types/apps/pbx/dialplanTypes'

// Component Imports
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

// Data Imports
// import { getUserData } from '@/app/server/actions'

const DialplanTab = dynamic(() => import('@views/apps/pbx/dialplans/managers/view/manager'))

// Vars
const tabContentList = (data: DialplansType): { [key: string]: ReactElement } => ({
  dialplan: <DialplanTab data={data} />

  // profile: <InfoTab data={data?.users.profile} />,
  // teams: <BranchesTab data={data?.users.teams} />,
  // projects: <ProjectsTab data={data?.users.projects} />,
  // connections: <ConnectionsTab data={data?.users.connections} />
})

/**
 * ! If you need data using an API call, uncomment the below API code, update the `process.env.API_URL` variable in the
 * ! `.env` file found at root of your project and also update the API endpoints like `/apps/user-list` in below example.
 * ! Also, remove the above server action import and the action itself from the `src/app/server/actions.ts` file to clean up unused code
 * ! because we've used the server action for getting our static data.
 */

const getListDialplanData = async (cId: string, sessionTokenAuthJS: sessionToken) => {
  // Vars

  // console.log('session-token : %s', sessionTokenAuthJS)

  const res = await fetch(`${process.env.API_URL}/pbx/dialplans/${cId}/list`, {
    method: 'GET',
    headers: {
      Cookie: `${sessionTokenAuthJS.name}=${sessionTokenAuthJS.value}`
    }
  })

  if (!res.ok) {
    throw new Error('Failed to fetch userData')
  }

  return await res.json()
}

const getDialplanData = async (cId: string, uuid: string, sessionTokenAuthJS: sessionToken) => {
  // Vars

  if (uuid == '') return JSON.stringify('') // trả về giá trị '' khi không có uuid

  // console.log('session-token : %s', sessionTokenAuthJS)

  const res = await fetch(`${process.env.API_URL}/pbx/dialplans/${cId}/view?id=` + uuid, {
    method: 'GET',
    headers: {
      Cookie: `${sessionTokenAuthJS.name}=${sessionTokenAuthJS.value}`
    }
  })

  if (!res.ok) {
    throw new Error('Failed to fetch userData')
  }

  return await res.json()
}

const DialplanPage = async ({
  params,
  searchParams
}: {
  params: { categoryId: string; itemId: string }
  searchParams: { [key: string]: string | undefined }
}) => {
  // Vars
  const cId = params.categoryId
  const tab = params.itemId
  const sessionTokenAuthJS = await CookieTokenAuth()

  switch (tab) {
    case 'edit':
      break
    case 'view':
      const uuid = searchParams['id'] ?? '' // default value is "1"

      console.log(uuid)

      const data = await getDialplanData(cId, uuid, sessionTokenAuthJS)

      // console.log('DATA HERE ...')
      // console.log(data)

      const dst_data: DialplansType = data

      return <DialplanView data={dst_data} tabContentList={tabContentList(dst_data)} />
      break
    default:
      const listData = await getListDialplanData(cId, sessionTokenAuthJS)

      // console.log('DATA HERE ...')
      // console.log(data)

      const list_dest_data: DialplansType[] = listData

      return <DialplanList data={list_dest_data} />
  }
}

export default DialplanPage
