import type { ReactElement } from 'react'

import dynamic from 'next/dynamic'

import DestinationList from '@views/apps/pbx/dialplans/destinations/list'
import DestView from '@views/apps/pbx/dialplans/destinations/view'
import type { DestinationsType } from '@/types/apps/pbx/destinationTypes'

// Component Imports
import type { sessionToken } from '@/types/otherTypes'

// Data Imports
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

// import { getUserData } from '@/app/server/actions'
const DestTab = dynamic(() => import('@views/apps/pbx/dialplans/destinations/view/destination'))

// Vars
const tabContentList = (data: DestinationsType): { [key: string]: ReactElement } => ({
  destination: <DestTab data={data} />

  // profile: <InfoTab data={data?.users.profile} />,
  // teams: <BranchesTab data={data?.users.teams} />,
  // projects: <ProjectsTab data={data?.users.projects} />,
  // connections: <ConnectionsTab data={data?.users.connections} />
})

/**
 * ! If you need data using an API call, uncomment the below API code, update the `process.env.API_URL` variable in the
 * ! `.env` file found at root of your project and also update the API endpoints like `/apps/user-list` in below example.
 * ! Also, remove the above server action import and the action itself from the `src/app/server/actions.ts` file to clean up unused code
 * ! because we've used the server action for getting our static data.
 */

const getListDestData = async (sessionTokenAuthJS: sessionToken) => {
  // Vars

  // console.log('session-token : %s', sessionTokenAuthJS)

  const res = await fetch(`${process.env.API_URL}/pbx/dialplans/destinations/list`, {
    method: 'GET',
    headers: {
      Cookie: `${sessionTokenAuthJS.name}=${sessionTokenAuthJS.value}`
    }
  })

  if (!res.ok) {
    throw new Error('Failed to fetch userData')
  }

  return await res.json()
}

const getDestData = async (uuid: string, sessionTokenAuthJS: sessionToken) => {
  // Vars

  if (uuid == '') return JSON.stringify('') // trả về giá trị '' khi không có uuid

  // console.log('session-token : %s', sessionTokenAuthJS)

  const res = await fetch(`${process.env.API_URL}/pbx/dialplans/destinations/view?id=` + uuid, {
    method: 'GET',
    headers: {
      Cookie: `${sessionTokenAuthJS.name}=${sessionTokenAuthJS.value}`
    }
  })

  if (!res.ok) {
    throw new Error('Failed to fetch userData')
  }

  return await res.json()
}

const DestinationPage = async ({
  params,
  searchParams
}: {
  params: { tab: string }
  searchParams: { [key: string]: string | undefined }
}) => {
  // Vars
  const sessionTokenAuthJS = await CookieTokenAuth()
  const tab = params.tab

  switch (tab) {
    case 'edit':
      break
    case 'view':
      const uuid = searchParams['id'] ?? '' // default value is "1"

      console.log(uuid)

      const data = await getDestData(uuid, sessionTokenAuthJS)

      // console.log('DATA HERE ...')
      // console.log(data)

      const dst_data: DestinationsType = data

      return <DestView data={dst_data} tabContentList={tabContentList(dst_data)} />
      break
    case 'list':
    default:
      const listData = await getListDestData(sessionTokenAuthJS)

      // console.log('DATA HERE ...')
      // console.log(data)

      const list_dest_data: DestinationsType[] = listData

      return <DestinationList data={list_dest_data} />
  }
}

export default DestinationPage
