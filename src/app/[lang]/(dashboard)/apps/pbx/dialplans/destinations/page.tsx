import type { sessionToken } from '@/types/otherTypes'

import DestinationList from '@views/apps/pbx/dialplans/destinations/list'

import type { DestinationsType } from '@/types/apps/pbx/destinationTypes'

// Component Imports

// Data Imports
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

// import { getUserData } from '@/app/server/actions'

/**
 * ! If you need data using an API call, uncomment the below API code, update the `process.env.API_URL` variable in the
 * ! `.env` file found at root of your project and also update the API endpoints like `/apps/user-list` in below example.
 * ! Also, remove the above server action import and the action itself from the `src/app/server/actions.ts` file to clean up unused code
 * ! because we've used the server action for getting our static data.
 */

const getDestinationData = async (sessionTokenAuthJS: sessionToken) => {
  // Vars

  // console.log('session-token : %s', sessionTokenAuthJS)

  const res = await fetch(`${process.env.API_URL}/pbx/dialplans/destinations/list`, {
    method: 'GET',
    headers: {
      Cookie: `${sessionTokenAuthJS.name}=${sessionTokenAuthJS.value}`
    }
  })

  if (!res.ok) {
    throw new Error('Failed to fetch userData')
  }

  return await res.json()
}

const DialplanListApp = async () => {
  // Vars
  const sessionTokenAuthJS = await CookieTokenAuth()

  const data = await getDestinationData(sessionTokenAuthJS)

  // console.log('DATA HERE ...')
  // console.log(data)

  const dest_data: DestinationsType[] = data

  return <DestinationList data={dest_data} />
}

export default DialplanListApp
