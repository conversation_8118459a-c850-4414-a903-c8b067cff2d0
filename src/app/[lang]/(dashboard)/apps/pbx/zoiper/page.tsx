import type { v_extensions } from '@prisma/client'

// Component Imports
import Cookie<PERSON><PERSON>Auth from '@/libs/sessionTokenAuthJS'

// Data Imports

import { getListExtData } from '@/libs/pbx/Extensions'
import ZoiperList from '@/views/apps/pbx/zoiper/list'

const ZoiperPage = async () => {
  // Vars

  // console.log('session-token : %s', sessionTokenAuthJS)
  try {
    const data = await getListExtData()
    const sessionTokenAuthJS = await CookieTokenAuth()
    const ext_data: v_extensions[] = data

    return <ZoiperList token={sessionTokenAuthJS} data={ext_data} />
  } catch (error) {
    return <>Read database is error</>
  }
}

export default ZoiperPage
