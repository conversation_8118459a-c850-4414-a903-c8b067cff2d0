import type { ReactElement } from 'react'

import dynamic from 'next/dynamic'

import type { v_domains, v_extension_users, v_extensions, v_voicemails } from '@prisma/client'

import type { Locale } from '@configs/i18n'
import ExtView from '@views/apps/pbx/extensions/view/index'

// Type Imports
import type { ExtensionEditType } from '@/types/apps/pbx/extensionTypes'
import type { UsernamesType } from '@/types/apps/admin/userTypes'
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

// Component Imports
import ExtensionsList from '@views/apps/pbx/extensions/list'

// Data Imports
import { getListExtData, getExtData, getExtOptions } from '@/libs/pbx/Extensions'
import { getExtUserData } from '@/libs/pbx/ExtensionUsers'
import { getListDomainName } from '@/libs/pbx/Domains'
import { getListUserName } from '@/libs/admin/Users'
import { getDictionary } from '@/utils/getDictionary'

// import { getUserData } from '@/app/server/actions'
const BasicTab = dynamic<{
  data: ExtensionEditType
}>(() => import('@views/apps/pbx/extensions/view/basic'))

const AdvanceTab = dynamic(() => import('@views/apps/pbx/extensions/view/advance'))
const VoiceMailTab = dynamic(() => import('@views/apps/pbx/extensions/view/voicemail'))
const DeviceTab = dynamic(() => import('@views/apps/pbx/extensions/view/device'))
const UserTab = dynamic(() => import('@views/apps/pbx/extensions/view/user'))
const CDRTab = dynamic(() => import('@views/apps/pbx/extensions/view/cdr'))

// Vars
const tabContentList = (data: ExtensionEditType): { [key: string]: ReactElement } => ({
  basic: <BasicTab data={data} />,
  advance: <AdvanceTab data={data} />,
  voicemail: <VoiceMailTab data={data} />,
  device: <DeviceTab data={data} />,
  user: <UserTab data={data} />,
  cdr: <CDRTab data={data} />
})

const ExtentionsPage = async ({
  params,
  searchParams
}: {
  params: { tab: string; lang: Locale }
  searchParams: { [key: string]: string | undefined }
}) => {
  // Vars
  const sessionTokenAuthJS = await CookieTokenAuth()
  const tab = params.tab
  const dictionary = await getDictionary(params.lang)

  switch (tab) {
    case 'edit':
      break
    case 'view':
      const uuid = searchParams['id'] ?? '' // default value is "1"

      const ext_data: v_extensions = await getExtData(uuid)
      const extUser_data: v_extension_users = await getExtUserData(uuid)
      const user_data: UsernamesType[] = await getListUserName()
      const domain_data: v_domains[] = await getListDomainName()
      const extOptions: { transcribeEnabled: boolean; voicemail: v_voicemails } = await getExtOptions(uuid)

      const data: ExtensionEditType = {
        token: sessionTokenAuthJS,
        extension: ext_data,
        extUser: extUser_data,
        domains: domain_data,
        users: user_data,
        dictionary: dictionary,
        voicemail: extOptions.voicemail,
        transcribeEnabled: extOptions.transcribeEnabled
      }

      return <ExtView data={data} tabContentList={tabContentList(data)} />
    case 'list':

    default:
      try {
        const listData = await getListExtData()

        const list_ext_data: v_extensions[] = listData

        return <ExtensionsList token={sessionTokenAuthJS} data={list_ext_data} />
      } catch (error) {
        return <>Read database is error</>
      }
  }
}

export default ExtentionsPage
