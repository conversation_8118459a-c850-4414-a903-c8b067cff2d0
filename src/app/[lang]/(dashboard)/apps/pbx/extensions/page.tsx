import type { v_extensions, v_users } from '@prisma/client'

// Component Imports
import Cookie<PERSON>okenAuth from '@/libs/sessionTokenAuthJS'
import ExtensionsList from '@views/apps/pbx/extensions/list'

// Data Imports

import { getListExtData, getListUsers } from '@/libs/pbx/Extensions'
import { getRegistrationsAsJson } from '@/libs/pbx/ActiveCall'
import type { RegistrationType } from '@/types/apps/pbx/registrationTypes'

const ExtentionsPage = async () => {
  // Vars

  // console.log('session-token : %s', sessionTokenAuthJS)
  try {
    const data = await getListExtData()
    const registrations = await getRegistrationsAsJson()

    const registeredUsers = new Set(registrations.map((reg: RegistrationType) => reg.user.split('@')[0]))

    const updatedData = data.map((item: v_extensions) => ({
      ...item,
      registration_active: registeredUsers.has(item.extension)
    }))

    const sessionTokenAuthJS = await CookieTokenAuth()
    const users: v_users[] = await getListUsers()

    return <ExtensionsList token={sessionTokenAuthJS} data={updatedData} users={users} />
  } catch (error) {
    return <>Read database is error</>
  }
}

export default ExtentionsPage
