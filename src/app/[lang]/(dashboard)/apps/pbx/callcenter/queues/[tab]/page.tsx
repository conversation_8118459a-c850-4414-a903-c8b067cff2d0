import type { QueuesType } from '@/types/apps/pbx/queueTypes'
import type { sessionToken } from '@/types/otherTypes'

// Component Imports

import CookieTokenAuth from '@/libs/sessionTokenAuthJS'
import QueuesList from '@/views/apps/pbx/callcenter/queues/list'
import QueuesView from '@/views/apps/pbx/callcenter/queues/view'

// Vars

/**
 * ! If you need data using an API call, uncomment the below API code, update the `process.env.API_URL` variable in the
 * ! `.env` file found at root of your project and also update the API endpoints like `/apps/user-list` in below example.
 * ! Also, remove the above server action import and the action itself from the `src/app/server/actions.ts` file to clean up unused code
 * ! because we've used the server action for getting our static data.
 */

const getListQueuesData = async (sessionTokenAuthJS: sessionToken) => {
  // Try to get queues with categories first
  try {
    const res = await fetch(`${process.env.API_URL}/pbx/callcenter/queues/with-categories`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Cookie: `${sessionTokenAuthJS.name}=${sessionTokenAuthJS.value}`
      },
      body: JSON.stringify({
        page: 0,
        limit: 1000, // Get all queues for the list page
        action: 'list'
      })
    })

    if (res.ok) {
      const result = await res.json()

      return result.queues || []
    }
  } catch (error) {
    console.warn('Failed to fetch queues with categories, falling back to basic list:', error)
  }

  // Fallback to basic queue list
  const res = await fetch(`${process.env.API_URL}/pbx/callcenter/queues/list`, {
    method: 'GET',
    headers: {
      Cookie: `${sessionTokenAuthJS.name}=${sessionTokenAuthJS.value}`
    }
  })

  if (!res.ok) {
    throw new Error('Failed to fetch userData')
  }

  return await res.json()
}

const getQueueData = async (uuid: string, sessionTokenAuthJS: sessionToken) => {
  // Vars

  if (uuid == '') return JSON.stringify('') // trả về giá trị '' khi không có uuid

  const res = await fetch(`${process.env.API_URL}/pbx/callcenter/queues/view?id=` + uuid, {
    method: 'GET',
    headers: {
      Cookie: `${sessionTokenAuthJS.name}=${sessionTokenAuthJS.value}`
    }
  })

  if (!res.ok) {
    throw new Error('Failed to fetch userData')
  }

  return await res.json()
}

const QueuesPage = async ({
  params,
  searchParams
}: {
  params: { tab: string }
  searchParams: { [key: string]: string | undefined }
}) => {
  // Vars
  const sessionTokenAuthJS = await CookieTokenAuth()

  // console.log('session-token : %s', sessionTokenAuthJS)

  const tab = params.tab

  switch (tab) {
    case 'add':
      return <QueuesView data={{} as QueuesType} sessionToken={sessionTokenAuthJS} />
    case 'edit':
    case 'view':
      const uuid = searchParams['id'] ?? '' // default value is empty string

      console.log(uuid)

      const data = await getQueueData(uuid, sessionTokenAuthJS)

      const ext_data: QueuesType = data

      return <QueuesView data={ext_data} sessionToken={sessionTokenAuthJS} />
      break
    case 'list':
    default:
      const listData = await getListQueuesData(sessionTokenAuthJS)

      const list_ext_data: QueuesType[] = listData

      return <QueuesList data={list_ext_data} />
  }
}

export default QueuesPage
