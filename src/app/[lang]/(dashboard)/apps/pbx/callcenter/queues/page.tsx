import QueuesList from '@views/apps/pbx/callcenter/queues/list'

import type { QueuesType } from '@/types/apps/pbx/queueTypes'
import type { sessionToken } from '@/types/otherTypes'

// Component Imports

// Data Imports
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

/**
 * ! If you need data using an API call, uncomment the below API code, update the `process.env.API_URL` variable in the
 * ! `.env` file found at root of your project and also update the API endpoints like `/apps/user-list` in below example.
 * ! Also, remove the above server action import and the action itself from the `src/app/server/actions.ts` file to clean up unused code
 * ! because we've used the server action for getting our static data.
 */

const getQueuesData = async (sessionTokenAuthJS: sessionToken) => {
  // Try to get queues with categories first
  try {
    const res = await fetch(`${process.env.API_URL}/pbx/callcenter/queues/with-categories`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Cookie: `${sessionTokenAuthJS.name}=${sessionTokenAuthJS.value}`
      },
      body: JSON.stringify({
        page: 0,
        limit: 1000, // Get all queues for the list page
        action: 'list'
      })
    })

    if (res.ok) {
      const result = await res.json()

      return result.queues || []
    }
  } catch (error) {
    console.warn('Failed to fetch queues with categories, falling back to basic list:', error)
  }

  // Fallback to basic queue list
  const res = await fetch(`${process.env.API_URL}/pbx/callcenter/queues/list`, {
    method: 'GET',
    headers: {
      Cookie: `${sessionTokenAuthJS.name}=${sessionTokenAuthJS.value}`
    }
  })

  if (!res.ok) {
    throw new Error('Failed to fetch userData')
  }

  return await res.json()
}

const QueuesPage = async () => {
  // Vars

  const sessionTokenAuthJS = await CookieTokenAuth()

  // console.log('session-token : %s', sessionTokenAuthJS)

  const data = await getQueuesData(sessionTokenAuthJS)

  // console.log('DATA HERE ...')
  // console.log(data)

  const ext_data: QueuesType[] = data

  return <QueuesList data={ext_data} />
}

export default QueuesPage
