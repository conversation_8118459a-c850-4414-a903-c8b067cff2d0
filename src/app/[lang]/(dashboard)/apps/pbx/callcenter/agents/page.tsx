import AgentsList from '@views/apps/pbx/callcenter/agents/list'

import type { AgentVoicesType } from '@/types/apps/pbx/agentTypes'
import { getListAgentsData } from '@/libs/pbx/Agents'

// Component Imports

// Data Imports

/**
 * ! If you need data using an API call, uncomment the below API code, update the `process.env.API_URL` variable in the
 * ! `.env` file found at root of your project and also update the API endpoints like `/apps/user-list` in below example.
 * ! Also, remove the above server action import and the action itself from the `src/app/server/actions.ts` file to clean up unused code
 * ! because we've used the server action for getting our static data.
 */

const AgentsPage = async () => {
  // Vars

  const data = await getListAgentsData()

  const agent_data: AgentVoicesType[] = data

  return <AgentsList data={agent_data} />
}

export default AgentsPage
