import type { AgentV<PERSON>cesType, AgentVoiceEditType } from '@/types/apps/pbx/agentTypes'
import type { sessionToken } from '@/types/otherTypes'
import { getListAgentsData, getAgentData, getUIConfig } from '@/libs/pbx/Agents'
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

// Component Imports

import AgentsList from '@/views/apps/pbx/callcenter/agents/list'
import AgentsView from '@/views/apps/pbx/callcenter/agents/view'

// Vars

/**
 * ! If you need data using an API call, uncomment the below API code, update the `process.env.API_URL` variable in the
 * ! `.env` file found at root of your project and also update the API endpoints like `/apps/user-list` in below example.
 * ! Also, remove the above server action import and the action itself from the `src/app/server/actions.ts` file to clean up unused code
 * ! because we've used the server action for getting our static data.
 */

const AgentsPage = async ({
  params,
  searchParams
}: {
  params: { tab: string }
  searchParams: { [key: string]: string | undefined }
}) => {
  const tab = params.tab

  // Get session token for authentication
  const sessionToken: sessionToken = await CookieTokenAuth()

  switch (tab) {
    case 'add':
      // For add case, we need user data for the username dropdown
      const uiConfig = await getUIConfig()

      const addData: AgentVoiceEditType = {
        agent: {} as AgentVoicesType,
        username: uiConfig.userOptions,
        contacts: uiConfig.contactOptions
      }

      return <AgentsView data={addData} sessionToken={sessionToken} />
    case 'edit':
    case 'view':
      const uuid = searchParams['id'] ?? '' // default value is "1"

      console.log(uuid)

      const data = await getAgentData(uuid)
      const { userOptions, contactOptions } = await getUIConfig()

      // console.log('DATA HERE ...')
      // console.log(data)
      const agentData: AgentVoiceEditType = {
        agent: data,
        username: userOptions,
        contacts: contactOptions
      }

      return <AgentsView data={agentData} sessionToken={sessionToken} />
    case 'list':
    default:
      const listData = await getListAgentsData()

      // console.log('DATA HERE ...')
      // console.log(data)

      const list_data: AgentVoicesType[] = listData

      return <AgentsList data={list_data} />
  }
}

export default AgentsPage
