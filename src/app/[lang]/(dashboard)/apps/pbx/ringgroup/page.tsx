// Component Imports
import { getListRingGroups } from '@/libs/pbx/RingGroups'
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

// Data Imports

import RinggroupList from '@/views/apps/pbx/ringgroup/list'

const RinggroupPage = async () => {
  try {
    const sessionTokenAuthJS = await CookieTokenAuth()
    const res = await getListRingGroups()

    return <RinggroupList token={sessionTokenAuthJS} data={res} />
  } catch (error) {
    return <>Read database is error {error}</>
  }
}

export default RinggroupPage
