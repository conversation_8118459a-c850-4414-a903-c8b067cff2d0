// Component Imports

import Cookie<PERSON><PERSON>Auth from '@/libs/sessionTokenAuthJS'
import RingGroupView from '@/views/apps/pbx/ringgroup/view'
import RinggroupList from '@/views/apps/pbx/ringgroup/list'
import { getListRingGroups, getRingGroupData } from '@/libs/pbx/RingGroups'

// Vars

const RingGroupPage = async ({
  params,
  searchParams
}: {
  params: { tab: string }
  searchParams: { [key: string]: string | undefined }
}) => {
  // Vars

  const tab = params.tab
  const uuid = searchParams['id'] ?? ''
  const token = await CookieTokenAuth()

  switch (tab) {
    case 'edit':
    case 'add':
      return <RingGroupView token={token} />
    case 'view':
      const viewData = await getRingGroupData(uuid)

      return <RingGroupView data={viewData} token={token} />
    case 'list':
      break
    default:
      const data = await getListRingGroups()
      const sessionTokenAuthJS = await CookieTokenAuth()

      return <RinggroupList token={sessionTokenAuthJS} data={data} />
  }
}

export default RingGroupPage
