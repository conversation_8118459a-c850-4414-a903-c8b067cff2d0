// Component Imports

import Cookie<PERSON><PERSON>Auth from '@/libs/sessionTokenAuthJS'
import IVRList from '@views/apps/pbx/ivrmenu/list'
import IVRView from '@views/apps/pbx/ivrmenu/view'
import { getListIVR, getIVRData } from '@/libs/pbx/IVR'

// Data Imports

const IVRPage = async ({
  params,
  searchParams
}: {
  params: { tab: string }
  searchParams: { [key: string]: string | undefined }
}) => {
  // Vars

  const tab = params.tab
  const uuid = searchParams['id'] ?? ''
  const token = await CookieTokenAuth()

  switch (tab) {
    case 'edit':
    case 'add':
      return <IVRView token={token} />
    case 'view':
      const viewData = await getIVRData(uuid)

      return <IVRView data={viewData} token={token} />
    case 'list':
      break
    default:
      const data = await getListIVR()
      const sessionTokenAuthJS = await <PERSON><PERSON><PERSON><PERSON>Auth()

      return <IVRList token={sessionTokenAuthJS} data={data} />
  }
}

export default IVRPage
