import { getListIVR } from '@/libs/pbx/IVR'
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

// Data Imports

import IVRList from '@/views/apps/pbx/ivrmenu/list'

const IVRsListApp = async () => {
  try {
    const sessionTokenAuthJS = await CookieTokenAuth()
    const res = await getListIVR()

    return <IVRList token={sessionTokenAuthJS} data={res} />
  } catch (error) {
    return <>Read database is error {error}</>
  }
}

export default IVRsListApp
