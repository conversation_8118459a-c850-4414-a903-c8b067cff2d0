// Component Imports

import Cookie<PERSON><PERSON>Auth from '@/libs/sessionTokenAuthJS'
import CFRCenterView from '@views/apps/pbx/conference_centers/view'
import Conference<PERSON>enter from '@/views/apps/pbx/conference_centers/list'
import ConferenceRoom from '@/views/apps/pbx/conference_rooms/list'
import CFRRoomView from '@/views/apps/pbx/conference_rooms/view'

const CfrCentersPage = async ({
  params,
  searchParams
}: {
  params: { tab: string }
  searchParams: { [key: string]: string | undefined }
}) => {
  const tab = params.tab
  const uuid = searchParams['id'] ?? ''
  const token = await CookieTokenAuth()

  switch (tab) {
    case 'edit':
    case 'add':
      return <CFRCenterView token={token} />
    case 'view':
      return <CFRCenterView token={token} uuid={uuid} />
    case 'list':
      break
    case 'conference_rooms':
      return <ConferenceRoom token={token} />

    case 'conference_room_edit':
      return <CFRRoomView token={token} uuid={uuid} />
    default:
      const sessionTokenAuthJS = await <PERSON>ieTokenAuth()

      return <ConferenceCenter token={sessionTokenAuthJS} />
  }
}

export default CfrCentersPage
