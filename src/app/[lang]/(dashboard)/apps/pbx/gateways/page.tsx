import type { GatewaysType } from '@/types/apps/pbx/gatewayTypes'
import { getListGwData } from '@/libs/pbx/Gateways'

// Component Imports

import GatewaysList from '@views/apps/pbx/gateways/list'

// Data Imports

// import { getUserData } from '@/app/server/actions'

/**
 * ! If you need data using an API call, uncomment the below API code, update the `process.env.API_URL` variable in the
 * ! `.env` file found at root of your project and also update the API endpoints like `/apps/user-list` in below example.
 * ! Also, remove the above server action import and the action itself from the `src/app/server/actions.ts` file to clean up unused code
 * ! because we've used the server action for getting our static data.
 */

const GatewayListApp = async () => {
  // Vars

  const data = await getListGwData()

  // console.log('DATA HERE ...')
  // console.log(data)

  const gw_data: GatewaysType[] = data

  return <GatewaysList data={gw_data} />
}

export default GatewayListApp
