import type { ReactElement } from 'react'

import dynamic from 'next/dynamic'

import GatewayView from '@views/apps/pbx/gateways/view/index'

import type { GatewaysType } from '@/types/apps/pbx/gatewayTypes'
import GatewaysList from '@/views/apps/pbx/gateways/list'

// Component Imports

// Data Imports

import { getGatewayData, getListGwData } from '@/libs/pbx/Gateways'

// import { getUserData } from '@/app/server/actions'
const BasicTab = dynamic(() => import('@views/apps/pbx/gateways/view/basic'))
const AdvanceTab = dynamic(() => import('@views/apps/pbx/gateways/view/advance'))

// Vars
const tabContentList = (data: GatewaysType): { [key: string]: ReactElement } => ({
  basic: <BasicTab data={data} />,
  advance: <AdvanceTab data={data} />

  // profile: <InfoTab data={data?.users.profile} />,
  // teams: <BranchesTab data={data?.users.teams} />,
  // projects: <ProjectsTab data={data?.users.projects} />,
  // connections: <ConnectionsTab data={data?.users.connections} />
})

/**
 * ! If you need data using an API call, uncomment the below API code, update the `process.env.API_URL` variable in the
 * ! `.env` file found at root of your project and also update the API endpoints like `/apps/user-list` in below example.
 * ! Also, remove the above server action import and the action itself from the `src/app/server/actions.ts` file to clean up unused code
 * ! because we've used the server action for getting our static data.
 */

const GatewayPage = async ({
  params,
  searchParams
}: {
  params: { tab: string }
  searchParams: { [key: string]: string | undefined }
}) => {
  // Vars
  const tab = params.tab

  switch (tab) {
    case 'edit':
      break
    case 'view':
      const uuid = searchParams['id'] ?? '' // default value is "1"

      console.log(uuid)

      const data = await getGatewayData(uuid)

      // console.log('DATA HERE ...')
      // console.log(data)

      const gw_data: GatewaysType = data

      return <GatewayView data={gw_data} tabContentList={tabContentList(gw_data)} />
      break
    case 'list':
    default:
      const listData = await getListGwData()

      // console.log('DATA HERE ...')
      // console.log(data)

      const list_gw_data: GatewaysType[] = listData

      return <GatewaysList data={list_gw_data} />
  }
}

export default GatewayPage
