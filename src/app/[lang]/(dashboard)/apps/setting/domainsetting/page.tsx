// Next Imports
import { getListDomainSettings } from '@/libs/setting/DomainSettings'
import { transformDomainSettings } from '@/libs/setting/DomainSettingUtil'
import type { DomainSetting } from '@/types/apps/setting/settingTypes'

// Component Imports
import DomainSettingView from '@/views/apps/setting/domainsetting'

const DomainSettingPage = async () => {
  const listdata = await getListDomainSettings()

  const data: DomainSetting[] = transformDomainSettings(listdata)

  return <DomainSettingView data={data} />
}

export default DomainSettingPage
