import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

import { getDomainById } from '@/libs/setting/Domain'
import DomainView from '@/views/apps/setting/domains/view'

// Next Imports
import { getDomainSettingById, getListDomainSettings } from '@/libs/setting/DomainSettings'
import { transformDomainSettings } from '@/libs/setting/DomainSettingUtil'
import type { DomainSetting } from '@/types/apps/setting/settingTypes'

// Component Imports
import DomainSettingView from '@/views/apps/setting/domainsetting'

const DomainSettingPage = async ({
  params,
  searchParams
}: {
  params: { tab: string }
  searchParams: { [key: string]: string | undefined }
}) => {
  // Vars
  const sessionTokenAuthJS = await CookieTokenAuth()

  // console.log('session-token : %s', sessionTokenAuthJS)

  const tab = params.tab
  const domain_uuid = searchParams['domain'] ?? '' // default value is "1"
  const domain_setting_uuid = searchParams['id'] ?? '' // default value is "1"

  switch (tab) {
    case 'edit':
      break
    case 'viewsetting':
      const _detailData = await getDomainById(domain_setting_uuid)

      return <DomainView token={sessionTokenAuthJS} data={_detailData} />
      break
    case 'view':
      const _listdata = await getDomainSettingById(domain_uuid)

      const _data: DomainSetting[] = transformDomainSettings(_listdata)

      return <DomainSettingView data={_data} />
      break
    case 'all':
      const listdata = await getListDomainSettings()

      const data: DomainSetting[] = transformDomainSettings(listdata)

      return <DomainSettingView data={data} />
    default:
  }
}

export default DomainSettingPage
