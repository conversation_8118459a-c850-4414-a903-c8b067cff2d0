import type { v_domains } from '@prisma/client'

import DomainList from '@views/apps/setting/domains/list'
import Cook<PERSON><PERSON>okenAuth from '@/libs/sessionTokenAuthJS'

import { getDomainById, getListDomains } from '@/libs/setting/Domain'
import DomainView from '@/views/apps/setting/domains/view'

const DomainPage = async ({
  params,
  searchParams
}: {
  params: { tab: string }
  searchParams: { [key: string]: string | undefined }
}) => {
  // Vars
  const sessionTokenAuthJS = await CookieTokenAuth()

  // console.log('session-token : %s', sessionTokenAuthJS)

  const tab = params.tab

  switch (tab) {
    case 'edit':
      break
    case 'view':
      const uuid = searchParams['id'] ?? '' // default value is "1"
      const _detailData = await getDomainById(uuid)

      return <DomainView token={sessionTokenAuthJS} data={_detailData} />
      break
    case 'list':
      const data = await getListDomains()
      const domain_data: v_domains[] = data

      return <DomainList token={sessionTokenAuthJS} domainData={domain_data} />
    default:
  }
}

export default DomainPage
