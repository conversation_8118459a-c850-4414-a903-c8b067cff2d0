import type { v_domains } from '@prisma/client'

// Component Imports
import DomainList from '@views/apps/setting/domains/list'

import { getListDomains } from '@/libs/setting/Domain'
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

const DomainPage = async () => {
  // Vars

  const data = await getListDomains()

  // console.log('DATA HERE ...')
  // console.log(data)
  const sessionTokenAuthJS = await CookieTokenAuth()
  const domain_data: v_domains[] = data

  return <DomainList token={sessionTokenAuthJS} domainData={domain_data} />
}

export default DomainPage
