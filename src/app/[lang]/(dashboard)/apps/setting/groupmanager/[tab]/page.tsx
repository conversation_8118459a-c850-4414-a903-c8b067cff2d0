import type { v_groups } from '@prisma/client'

import GroupList from '@views/apps/setting/groupmanager/list'
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'
import GroupView from '@views/apps/setting/groupmanager/view'

// import PermissionView from '@views/apps/setting/groupmanager/permissions'
// import MemberView from '@views/apps/setting/groupmanager/members'
import { getGroupById, getListGroupPermissions } from '@/libs/setting/GroupManager'

const GroupPage = async ({
  params,
  searchParams
}: {
  params: { tab: string }
  searchParams: { [key: string]: string | undefined }
}) => {
  // Vars
  const sessionTokenAuthJS = await CookieTokenAuth()

  // console.log('session-token : %s', sessionTokenAuthJS)

  const tab = params.tab
  const uuid = searchParams['id'] ?? '' // default value is "1"

  switch (tab) {
    case 'permissions':
      // return <PermissionView token={sessionTokenAuthJS} data={_detailData} />
      break
    case 'members':
      // return <MemberView token={sessionTokenAuthJS} data={_detailData} />
      break
    case 'view':
      const _detailData = await getGroupById(uuid)

      return <GroupView token={sessionTokenAuthJS} data={_detailData} />
      break
    case 'list':
      const data = await getListGroupPermissions()
      const group_data: v_groups[] = data

      return <GroupList token={sessionTokenAuthJS} groupData={group_data} />
    default:
  }
}

export default GroupPage
