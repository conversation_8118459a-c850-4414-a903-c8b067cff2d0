import type { v_groups } from '@prisma/client'

// Component Imports
import GroupList from '@views/apps/setting/groupmanager/list'

import { getListGroupPermissions } from '@/libs/setting/GroupManager'
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

const GroupPage = async () => {
  // Vars

  const data = await getListGroupPermissions()

  // console.log('DATA HERE ...')
  // console.log(data)
  const sessionTokenAuthJS = await CookieTokenAuth()
  const group_data: v_groups[] = data

  return <GroupList token={sessionTokenAuthJS} groupData={group_data} />
}

export default GroupPage
