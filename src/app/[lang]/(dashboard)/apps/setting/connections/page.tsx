// React Imports
import type { ReactElement } from 'react'

// import { cookies } from 'next/headers'
import dynamic from 'next/dynamic'

// Next Imports
import CookieTokenAuth from '@/libs/sessionTokenAuthJS'

// Component Imports
import ConnectionView from '@/views/apps/setting/connections'
import type { sessionToken } from '@/types/otherTypes'

const ZaloTab = dynamic(() => import('@views/apps/setting/connections/zalo'))
const FacebookTab = dynamic(() => import('@views/apps/setting/connections/facebook'))

// Import Types

// Vars
const tabContentList = (token: sessionToken): { [key: string]: ReactElement } => ({
  zalo: <ZaloTab token={token} />,
  facebook: <FacebookTab />
})

const ConnectionPage = async () => {
  const sessionTokenAuthJS = await CookieTokenAuth()

  return <ConnectionView tabContentList={tabContentList(sessionTokenAuthJS)} />
}

export default ConnectionPage
