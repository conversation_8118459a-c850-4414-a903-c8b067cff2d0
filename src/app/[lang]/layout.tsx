// Third-party Imports
import 'react-perfect-scrollbar/dist/css/styles.css'

// Type Imports
import type { Metadata } from 'next'

import type { ChildrenType } from '@core/types'
import type { Locale } from '@configs/i18n'
import { LanguageProvider } from '@/contexts/LanguageProvider'
import { getDictionary } from '@/utils/getDictionary'

// Component Imports
// Config Imports
import { i18n } from '@configs/i18n'

// Style Imports
import '@/app/globals.css'

// Generated Icon CSS Imports
import '@assets/iconify-icons/generated-icons.css'

export const metadata: Metadata = {
  title: 'UniAgent - Omni Channel Customer Care Platform',
  description: 'UniAgent - Nền tảng chăm sóc khách hàng đa kênh',
  keywords: 'Agentdesk, UniAgent',
  openGraph: {
    title: 'UniAgent - Omni Channel Customer Care Platform',
    url: 'https://agentdesk.univoice.com',
    description: 'UniAgent - Nền tảng chăm sóc khách hàng đa kênh',
    siteName: 'AgentDesk',
    images: [
      {
        url: 'https://univoice.vn/wp-content/uploads/2022/08/logo-univoice.png'
      }
    ]
  }

  // other: { ['zalo-platform-site-verification']: process.env.ZALO_PLATFORM_SITE_VERIFICATION ?? '' }
}

if (process.env?.ZALO_PLATFORM_SITE_VERIFICATION)
  metadata.other = { ['zalo-platform-site-verification']: process.env.ZALO_PLATFORM_SITE_VERIFICATION }

const RootLayout = async ({ children, params }: ChildrenType & { params: { lang: Locale } }) => {
  // Vars
  const direction = i18n.langDirection[params.lang]
  const dictionary = await getDictionary(params.lang)

  return (
    <html id='__next' lang={params.lang} dir={direction}>
      <LanguageProvider dictionary={dictionary} lang={params.lang}>
        <body className='flex is-full min-bs-full flex-auto flex-col'>{children}</body>
      </LanguageProvider>
    </html>
  )
}

export default RootLayout
