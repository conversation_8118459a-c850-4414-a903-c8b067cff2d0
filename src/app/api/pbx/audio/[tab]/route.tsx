// Next Imports
import fs from 'fs'
import path from 'path'

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'

export async function GET(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  const session = await getSession()

  if (!session) {
    return NextResponse.json({ warning: 'Please authentication !!!' })
  }

  switch (tab) {
    case 'play':
      const url = request.nextUrl.searchParams.get('url') ?? '' // lấy parameter khi gửi qua url

      if (!url || typeof url !== 'string') {
        return NextResponse.json({ error: 'Invalid URL' }, { status: 500 })
      }

      const filePath = path.resolve(url)

      try {
        const data = fs.readFileSync(filePath)

        return new NextResponse(data, {
          headers: {
            'Content-Type': 'audio/wav'
          }
        })
      } catch (err) {
        return NextResponse.json({ error: 'Failed to read file' }, { status: 500 })
      }

      break

    default:
      return NextResponse.json({ error: 'Not Support' }, { status: 500 })
  }
}
