// Next Imports
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { prisma } from '@/libs/db/prisma'
import getSession from '@/actions/getSession'

export async function GET(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  const session = await getSession()

  if (!session) {
    return NextResponse.json({ warning: 'Please authentication !!!' })
  }

  const uuid = session?.user?.domain ?? ''

  switch (tab) {
    case 'list':
      try {
        const list_viewcallblock = await prisma.view_call_block.findMany({
          where: {
            domain_uuid: uuid // lấy extensions trong domain đăng nhập
          }
        })

        if (!list_viewcallblock) {
          return NextResponse.json({ error: 'Lỗi truy vấn danh sách call block' }, { status: 404 })
        }

        return NextResponse.json(list_viewcallblock)
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'view':
      const callblock_uuid = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của extensions cần truy vấn

      try {
        const callblock = await prisma.v_call_block.findUnique({
          where: {
            domain_uuid: uuid, // lấy extensions trong domain đăng nhập
            call_block_uuid: callblock_uuid
          }
        })

        return NextResponse.json(callblock, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break

    default:
      return NextResponse.json({ error: 'Not Support' }, { status: 500 })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  switch (tab) {
    default:
      return NextResponse.json({ error: 'Not Support' }, { status: 500 })
  }
}
