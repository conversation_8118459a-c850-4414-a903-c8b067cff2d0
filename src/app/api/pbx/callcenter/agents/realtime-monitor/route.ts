// API endpoint for realtime agent monitor data from FreeSWITCH
import { NextResponse } from 'next/server'

import type { NextRequest } from 'next/server'

import { prisma } from '@/libs/db/prisma'
import getSession from '@/actions/getSession'
import FreeSwitchSocket from '@/libs/pbx/FreeSwitchSocket'

export async function POST(request: NextRequest) {
  const session = await getSession()

  try {
    if (!session?.user?.domain) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()

    const {
      category_filter,
      action = 'list' // 'list' or 'count'
    } = body

    console.log('Realtime Agent Monitor POST request body:', body)
    console.log('Parsed parameters:', { category_filter, action })

    // Handle count action
    if (action === 'count') {
      try {
        let count = 0

        // Get agents from database first
        const dbAgents = await prisma.v_call_center_agents.findMany({
          where: {
            domain_uuid: session.user.domain
          },
          select: {
            call_center_agent_uuid: true
          }
        })

        if (category_filter && category_filter !== 'all') {
          // Get agent UUIDs that belong to queues with the specified category
          const agentUuids = dbAgents.map(agent => agent.call_center_agent_uuid)

          if (agentUuids.length > 0) {
            // Get tiers for these agents
            const agentTiers = await prisma.v_call_center_tiers.findMany({
              where: {
                call_center_agent_uuid: {
                  in: agentUuids
                }
              },
              select: {
                call_center_agent_uuid: true,
                call_center_queue_uuid: true
              }
            })

            // Get queue UUIDs
            const queueUuids = agentTiers.map(tier => tier.call_center_queue_uuid).filter(Boolean) as string[]

            if (queueUuids.length > 0) {
              // Get category assignments for these queues
              const categoryAssignments = await prisma.v_queue_category_assignments.findMany({
                where: {
                  call_center_queue_uuid: {
                    in: queueUuids
                  }
                },
                select: {
                  call_center_queue_uuid: true,
                  queue_category_uuid: true
                }
              })

              // Get category details
              const categoryUuids = categoryAssignments.map(ca => ca.queue_category_uuid)

              const categories = await prisma.v_queue_categories.findMany({
                where: {
                  queue_category_uuid: {
                    in: categoryUuids
                  },
                  category_name: category_filter
                },
                select: {
                  queue_category_uuid: true
                }
              })

              if (categories.length > 0) {
                // Get queue UUIDs that have the specified category
                const filteredQueueUuids = categoryAssignments
                  .filter(ca => categories.some(cat => cat.queue_category_uuid === ca.queue_category_uuid))
                  .map(ca => ca.call_center_queue_uuid)

                // Get unique agent UUIDs for these queues
                const uniqueAgentUuids = new Set(
                  agentTiers
                    .filter(
                      tier => tier.call_center_queue_uuid && filteredQueueUuids.includes(tier.call_center_queue_uuid)
                    )
                    .map(tier => tier.call_center_agent_uuid)
                    .filter(Boolean)
                )

                count = uniqueAgentUuids.size
              } else {
                count = 0
              }
            } else {
              count = 0
            }
          } else {
            count = 0
          }
        } else {
          count = dbAgents.length
        }

        return NextResponse.json({ count }, { status: 200 })
      } catch (countError) {
        console.error('Count query error:', countError)
        throw countError
      }
    }

    // Handle list action - Get realtime agents from FreeSWITCH
    try {
      // Get all agents from database first with user information for status fallback
      const dbAgents = await prisma.v_call_center_agents.findMany({
        where: {
          domain_uuid: session.user.domain
        },
        select: {
          call_center_agent_uuid: true,
          agent_name: true,
          agent_id: true,
          agent_contact: true,
          agent_status: true,
          user_uuid: true,
          domain_uuid: true,
          agent_type: true,
          agent_call_timeout: true
        }
      })

      if (!dbAgents || dbAgents.length === 0) {
        return NextResponse.json(
          {
            agents: [],
            total_count: 0,
            online_count: 0,
            available_count: 0,
            on_call_count: 0,
            timestamp: new Date().toISOString()
          },
          { status: 200 }
        )
      }

      // Get real-time agent data from FreeSWITCH
      let realtimeAgents: any[] = []

      try {
        const freeswitch = await FreeSwitchSocket.getInstance()
        const agentListCmd = 'callcenter_config agent list'
        const agentListResult = await freeswitch.command(agentListCmd)

        // Parse FreeSWitch response - CSV format with '|' delimiter
        const parseAgentList = (data: string) => {
          const lines = data.trim().split('\n')
          const agents = []

          for (let i = 1; i < lines.length; i++) {
            // Skip header
            const fields = lines[i].split('|')

            if (fields.length >= 15) {
              agents.push({
                agent_name: fields[0]?.trim(),
                agent_system: fields[1]?.trim(),
                agent_uuid: fields[2]?.trim(),
                agent_type: fields[3]?.trim(),
                agent_contact: fields[4]?.trim(),
                agent_status: fields[5]?.trim(),
                agent_state: fields[6]?.trim(),
                max_no_answer: parseInt(fields[7]?.trim() || '0'),
                wrap_up_time: parseInt(fields[8]?.trim() || '0'),
                reject_delay_time: parseInt(fields[9]?.trim() || '0'),
                busy_delay_time: parseInt(fields[10]?.trim() || '0'),
                no_answer_delay_time: parseInt(fields[11]?.trim() || '0'),
                no_answer_count: parseInt(fields[12]?.trim() || '0'),
                calls_answered: parseInt(fields[13]?.trim() || '0'),
                talk_time: parseInt(fields[14]?.trim() || '0'),
                ready_time: fields[15]?.trim() || '0',
                last_bridge_start: fields[16]?.trim() || null,
                last_bridge_end: fields[17]?.trim() || null,
                last_offered_call: fields[18]?.trim() || null,
                last_status_change: fields[19]?.trim() || null
              })
            }
          }

          return agents
        }

        realtimeAgents = parseAgentList(agentListResult)
        console.log('Retrieved real-time agent data from FreeSWITCH:', realtimeAgents.length, 'agents')
      } catch (freeswitchError) {
        console.warn('Failed to get real-time agent data from FreeSWITCH:', freeswitchError)

        // Continue with database data only
      }

      // Get agent queue assignments with categories using Prisma ORM
      const agentUuids = dbAgents.map(agent => agent.call_center_agent_uuid)

      // Get tiers for these agents
      const agentTiers =
        agentUuids.length > 0
          ? await prisma.v_call_center_tiers.findMany({
              where: {
                call_center_agent_uuid: {
                  in: agentUuids
                }
              },
              select: {
                call_center_agent_uuid: true,
                call_center_queue_uuid: true,
                tier_level: true,
                tier_position: true
              }
            })
          : []

      // Get queue information with categories
      const queueUuids = agentTiers.map(tier => tier.call_center_queue_uuid).filter(Boolean) as string[]

      const queuesWithCategories =
        queueUuids.length > 0
          ? await prisma.v_call_center_queues.findMany({
              where: {
                call_center_queue_uuid: {
                  in: queueUuids
                }
              },
              select: {
                call_center_queue_uuid: true,
                queue_name: true,
                queue_extension: true
              }
            })
          : []

      // Get category assignments for these queues
      const categoryAssignments =
        queueUuids.length > 0
          ? await prisma.v_queue_category_assignments.findMany({
              where: {
                call_center_queue_uuid: {
                  in: queueUuids
                }
              },
              select: {
                call_center_queue_uuid: true,
                queue_category_uuid: true
              }
            })
          : []

      // Get category details
      const categoryUuids = categoryAssignments.map(ca => ca.queue_category_uuid)

      const categories =
        categoryUuids.length > 0
          ? await prisma.v_queue_categories.findMany({
              where: {
                queue_category_uuid: {
                  in: categoryUuids
                }
              },
              select: {
                queue_category_uuid: true,
                category_name: true,
                category_label: true
              }
            })
          : []

      // Create category map
      const categoryMap = new Map()

      categories.forEach(cat => {
        categoryMap.set(cat.queue_category_uuid, cat)
      })

      // Create a map for quick queue lookup
      const queueMap = new Map()

      queuesWithCategories.forEach(queue => {
        const queueCategories = categoryAssignments
          .filter(ca => ca.call_center_queue_uuid === queue.call_center_queue_uuid)
          .map(ca => categoryMap.get(ca.queue_category_uuid))
          .filter(Boolean)

        queueMap.set(queue.call_center_queue_uuid, {
          queue_name: queue.queue_name,
          queue_extension: queue.queue_extension,
          categories: queueCategories
        })
      })

      // Process agents and combine database + real-time data
      const processedAgents = dbAgents.map(agent => {
        // Find corresponding real-time agent data
        const realtimeAgent = realtimeAgents.find(
          rt => rt.agent_name === agent.agent_id || rt.agent_contact === agent.agent_contact
        )

        // Get agent's queue assignments
        const agentTierData = agentTiers.filter(tier => tier.call_center_agent_uuid === agent.call_center_agent_uuid)

        // Build queue data with categories
        const queueData = {
          queues: agentTierData.map(tier => {
            const queueInfo = queueMap.get(tier.call_center_queue_uuid)

            return {
              call_center_queue_uuid: tier.call_center_queue_uuid,
              queue_name: queueInfo?.queue_name || 'Unknown',
              queue_extension: queueInfo?.queue_extension || '',
              tier_level: tier.tier_level,
              tier_position: tier.tier_position,
              tier_state: 'Ready' // Default tier state, real-time state comes from FreeSWITCH
            }
          }),
          categories: agentTierData.flatMap(tier => {
            const queueInfo = queueMap.get(tier.call_center_queue_uuid)

            return queueInfo?.categories || []
          })
        }

        // Determine current status and state
        const currentStatus = realtimeAgent?.agent_status || agent.agent_status || 'Logged Out'
        const currentState = realtimeAgent?.agent_state || 'Idle'

        // Status indicators
        const is_available = ['Available', 'Available (On Demand)'].includes(currentStatus)
        const is_logged_in = currentStatus !== 'Logged Out'
        const is_on_call = currentState === 'In a queue call'

        return {
          ...agent,
          queues: queueData.queues,
          categories: queueData.categories,

          // Use real-time status from FreeSWITCH (preferred over database)
          agent_status: currentStatus,
          agent_state: currentState,

          // Real-time metrics from FreeSWITCH
          calls_answered: parseInt(realtimeAgent?.calls_answered || '0'),
          talk_time: parseInt(realtimeAgent?.talk_time || '0'),
          ready_time: parseInt(realtimeAgent?.ready_time || '0'),
          no_answer_count: parseInt(realtimeAgent?.no_answer_count || '0'),
          last_status_change: realtimeAgent?.last_status_change || null,
          last_offered_call: realtimeAgent?.last_offered_call || null,
          last_bridge_start: realtimeAgent?.last_bridge_start || null,
          last_bridge_end: realtimeAgent?.last_bridge_end || null,

          // Configuration from FreeSWITCH
          max_no_answer: parseInt(realtimeAgent?.max_no_answer || '3'),
          wrap_up_time: parseInt(realtimeAgent?.wrap_up_time || '10'),
          reject_delay_time: parseInt(realtimeAgent?.reject_delay_time || '10'),
          busy_delay_time: parseInt(realtimeAgent?.busy_delay_time || '60'),
          no_answer_delay_time: parseInt(realtimeAgent?.no_answer_delay_time || '60'),

          // Status indicators
          is_available,
          is_logged_in,
          is_on_call,

          // Timestamp
          updated_at: new Date().toISOString()
        }
      })

      // Apply category filtering if specified
      let filteredAgents = processedAgents

      if (category_filter && category_filter !== 'all') {
        filteredAgents = processedAgents.filter(agent => {
          // Check if agent has queues with the specified category
          return agent.categories.some((category: any) => category.category_name === category_filter)
        })
      }

      // Calculate statistics
      const total_count = filteredAgents.length
      const online_count = filteredAgents.filter(agent => agent.is_logged_in).length
      const available_count = filteredAgents.filter(agent => agent.is_available).length
      const on_call_count = filteredAgents.filter(agent => agent.is_on_call).length

      console.log(
        `Realtime Agent Monitor: Returning ${total_count} agents (${online_count} online, ${available_count} available, ${on_call_count} on call)`
      )

      return NextResponse.json(
        {
          agents: filteredAgents,
          total_count,
          online_count,
          available_count,
          on_call_count,
          timestamp: new Date().toISOString()
        },
        { status: 200 }
      )
    } catch (error) {
      console.error('Error in realtime agent monitor API:', error)

      return NextResponse.json(
        {
          error: 'Failed to fetch realtime agent data',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Error in realtime agent monitor API:', error)

    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
