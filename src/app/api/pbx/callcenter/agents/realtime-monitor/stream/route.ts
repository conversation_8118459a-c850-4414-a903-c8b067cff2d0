// SSE streaming endpoint for realtime agent monitor data
import { NextResponse } from 'next/server'

import type { NextRequest } from 'next/server'

import { PrismaClient } from '@prisma/client'

import getSession from '@/actions/getSession'
import FreeSwitchSocket from '@/libs/pbx/FreeSwitchSocket'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  const session = await getSession()

  try {
    if (!session?.user?.domain) {
      console.log('SSE Stream - Unauthorized: No session or domain')

      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const categoryFilter = searchParams.get('category') || 'voice' // Default to voice category

    const CONTEXT_PREFIX = 'realtime_agent_monitor_'
    const contextId = `${session.user.domain}_${categoryFilter}`
    const CONTEXT = `${CONTEXT_PREFIX}${contextId}`

    console.log(`Starting SSE stream for realtime agent monitor: ${CONTEXT}`)
    console.log('SSE Stream - Category filter:', categoryFilter)
    console.log('SSE Stream - Domain:', session.user.domain)

    console.log('SSE Stream - Getting FreeSWITCH socket instance...')
    const fsSocket = await FreeSwitchSocket.getInstance()

    console.log('SSE Stream - FreeSWITCH socket instance obtained')

    // Function to fetch and process realtime agent data
    const fetchRealtimeAgentData = async () => {
      console.log('SSE Stream - fetchRealtimeAgentData called')

      try {
        // Get domain UUID

        if (!session.user?.domain) {
          throw new Error('Domain not found')
        }

        const uuid = session.user?.domain

        console.log('SSE Stream - Using domain UUID:', uuid)

        // Get all agents from database first
        const dbAgents = await prisma.v_call_center_agents.findMany({
          where: { domain_uuid: uuid },
          select: {
            call_center_agent_uuid: true,
            agent_name: true,
            agent_id: true,
            agent_contact: true,
            agent_status: true,
            user_uuid: true,
            domain_uuid: true,
            agent_type: true,
            agent_call_timeout: true
          }
        })

        if (!dbAgents || dbAgents.length === 0) {
          return {
            agents: [],
            total_count: 0,
            online_count: 0,
            available_count: 0,
            on_call_count: 0,
            timestamp: new Date().toISOString()
          }
        }

        // Get real-time agent status from FreeSWITCH
        let realtimeAgents: any[] = []

        try {
          const freeswitch = await FreeSwitchSocket.getInstance()
          const agentListCmd = 'callcenter_config agent list'
          const agentListResult = await freeswitch.command(agentListCmd)

          // Parse FreeSWITCH response - CSV format with '|' delimiter
          const parseAgentList = (data: string) => {
            const lines = data.trim().split('\n')
            const agents = []

            for (let i = 1; i < lines.length; i++) {
              // Skip header
              const fields = lines[i].split('|')

              if (fields.length >= 15) {
                agents.push({
                  agent_name: fields[0]?.trim(),
                  agent_system: fields[1]?.trim(),
                  agent_uuid: fields[2]?.trim(),
                  agent_type: fields[3]?.trim(),
                  agent_contact: fields[4]?.trim(),
                  agent_status: fields[5]?.trim(),
                  agent_state: fields[6]?.trim(),
                  max_no_answer: parseInt(fields[7]?.trim() || '0'),
                  wrap_up_time: parseInt(fields[8]?.trim() || '0'),
                  reject_delay_time: parseInt(fields[9]?.trim() || '0'),
                  busy_delay_time: parseInt(fields[10]?.trim() || '0'),
                  no_answer_delay_time: parseInt(fields[11]?.trim() || '0'),
                  no_answer_count: parseInt(fields[12]?.trim() || '0'),
                  calls_answered: parseInt(fields[13]?.trim() || '0'),
                  talk_time: parseInt(fields[14]?.trim() || '0'),
                  ready_time: fields[15]?.trim() || '0',
                  last_bridge_start: fields[16]?.trim() || null,
                  last_bridge_end: fields[17]?.trim() || null,
                  last_offered_call: fields[18]?.trim() || null,
                  last_status_change: fields[19]?.trim() || null
                })
              }
            }

            return agents
          }

          realtimeAgents = parseAgentList(agentListResult)
          console.log('Retrieved real-time agent data from FreeSWITCH:', realtimeAgents.length, 'agents')
        } catch (freeswitchError) {
          console.warn('Failed to get real-time agent data from FreeSWITCH:', freeswitchError)

          // Continue with database data only
        }

        // Get agent tier assignments
        const agentTiers = await prisma.v_call_center_tiers.findMany({
          where: { domain_uuid: uuid },
          select: {
            call_center_agent_uuid: true,
            call_center_queue_uuid: true,
            tier_level: true,
            tier_position: true
          }
        })

        // Get queue information with categories
        const queues = await prisma.v_call_center_queues.findMany({
          where: { domain_uuid: uuid },
          select: {
            call_center_queue_uuid: true,
            queue_name: true,
            queue_extension: true
          }
        })

        // Get queue category assignments
        const queueUuids = queues.map(q => q.call_center_queue_uuid)

        const categoryAssignments =
          queueUuids.length > 0
            ? await prisma.v_queue_category_assignments.findMany({
                where: {
                  call_center_queue_uuid: {
                    in: queueUuids
                  }
                },
                select: {
                  call_center_queue_uuid: true,
                  queue_category_uuid: true
                }
              })
            : []

        // Get category details
        const categoryUuids = categoryAssignments.map(ca => ca.queue_category_uuid)
        const uniqueCategoryUuids = [...new Set(categoryUuids)]

        const categories =
          uniqueCategoryUuids.length > 0
            ? await prisma.v_queue_categories.findMany({
                where: {
                  queue_category_uuid: {
                    in: uniqueCategoryUuids
                  },
                  category_enabled: true
                },
                select: {
                  queue_category_uuid: true,
                  category_name: true,
                  category_label: true
                }
              })
            : []

        // Create category map
        const categoryMap = new Map()

        categories.forEach(cat => {
          categoryMap.set(cat.queue_category_uuid, cat)
        })

        // Create queue map with categories
        const queueMap = new Map()

        queues.forEach(queue => {
          const queueCategories = categoryAssignments
            .filter(ca => ca.call_center_queue_uuid === queue.call_center_queue_uuid)
            .map(ca => categoryMap.get(ca.queue_category_uuid))
            .filter(Boolean)

          queueMap.set(queue.call_center_queue_uuid, {
            ...queue,
            categories: queueCategories
          })
        })

        // Process agents and combine database + real-time data
        const processedAgents = dbAgents
          .map(agent => {
            // Find matching real-time agent data
            const realtimeAgent = realtimeAgents.find(
              ra => ra.agent_name === agent.agent_name || ra.agent_contact === agent.agent_contact
            )

            // Determine current status and state
            const currentStatus = realtimeAgent?.agent_status || agent.agent_status || 'Logged Out'
            const currentState = realtimeAgent?.agent_state || 'Idle'

            // Status indicators
            const is_available = ['Available', 'Available (On Demand)'].includes(currentStatus)
            const is_logged_in = currentStatus !== 'Logged Out'
            const is_on_call = currentState === 'In a queue call'

            // Get agent's queue assignments
            const agentTierData = agentTiers.filter(
              tier => tier.call_center_agent_uuid === agent.call_center_agent_uuid
            )

            // Build queue data with categories
            const queueData = {
              queues: agentTierData.map(tier => {
                const queueInfo = queueMap.get(tier.call_center_queue_uuid)

                return {
                  call_center_queue_uuid: tier.call_center_queue_uuid,
                  queue_name: queueInfo?.queue_name || 'Unknown',
                  queue_extension: queueInfo?.queue_extension || '',
                  tier_level: tier.tier_level,
                  tier_position: tier.tier_position,
                  tier_state: 'Ready' // Default tier state, real-time state comes from FreeSWITCH
                }
              }),
              categories: agentTierData.flatMap(tier => {
                const queueInfo = queueMap.get(tier.call_center_queue_uuid)

                return queueInfo?.categories || []
              })
            }

            // Apply category filter if specified
            if (categoryFilter && categoryFilter !== 'all') {
              console.log(`Filtering agent ${agent.agent_name} for category ${categoryFilter}`)
              console.log(
                'Agent categories:',
                queueData.categories.map(c => c.category_name)
              )
              console.log('Agent queues count:', queueData.queues.length)

              if (categoryFilter === 'voice') {
                // For voice category: include agents with voice queues OR agents not assigned to any queue
                const hasVoiceCategory = queueData.categories.some(cat => cat.category_name === 'voice')

                const hasNoQueues = queueData.queues.length === 0

                console.log(
                  `Agent ${agent.agent_name}: hasVoiceCategory=${hasVoiceCategory}, hasNoQueues=${hasNoQueues}`
                )

                if (!hasVoiceCategory && !hasNoQueues) {
                  console.log(`Filtering out agent ${agent.agent_name} - no voice category and has queues`)

                  return null // Filter out this agent
                }
              } else {
                // For other categories: only include agents with matching category
                const hasMatchingCategory = queueData.categories.some(cat => cat.category_name === categoryFilter)

                console.log(`Agent ${agent.agent_name}: hasMatchingCategory=${hasMatchingCategory}`)

                if (!hasMatchingCategory) {
                  console.log(`Filtering out agent ${agent.agent_name} - no matching category`)

                  return null // Filter out this agent
                }
              }
            }

            return {
              ...agent,
              queues: queueData.queues,
              categories: queueData.categories,

              // Use real-time status from FreeSWITCH (preferred over database)
              agent_status: currentStatus,
              agent_state: currentState,

              // Real-time metrics from FreeSWITCH
              calls_answered: parseInt(realtimeAgent?.calls_answered || '0'),
              talk_time: parseInt(realtimeAgent?.talk_time || '0'),
              ready_time: parseInt(realtimeAgent?.ready_time || '0'),
              no_answer_count: parseInt(realtimeAgent?.no_answer_count || '0'),
              last_status_change: realtimeAgent?.last_status_change || null,
              last_offered_call: realtimeAgent?.last_offered_call || null,
              last_bridge_start: realtimeAgent?.last_bridge_start || null,
              last_bridge_end: realtimeAgent?.last_bridge_end || null,

              // Configuration from FreeSWITCH
              max_no_answer: parseInt(realtimeAgent?.max_no_answer || '3'),
              wrap_up_time: parseInt(realtimeAgent?.wrap_up_time || '10'),
              reject_delay_time: parseInt(realtimeAgent?.reject_delay_time || '10'),
              busy_delay_time: parseInt(realtimeAgent?.busy_delay_time || '60'),
              no_answer_delay_time: parseInt(realtimeAgent?.no_answer_delay_time || '60'),

              // Status indicators
              is_available,
              is_logged_in,
              is_on_call,

              // Timestamp
              updated_at: new Date().toISOString()
            }
          })
          .filter((agent): agent is NonNullable<typeof agent> => agent !== null) // Remove null entries from category filtering

        // Calculate summary statistics
        const total_count = processedAgents.length
        const online_count = processedAgents.filter(agent => agent.is_logged_in).length
        const available_count = processedAgents.filter(agent => agent.is_available).length
        const on_call_count = processedAgents.filter(agent => agent.is_on_call).length

        const result = {
          agents: processedAgents,
          total_count,
          online_count,
          available_count,
          on_call_count,
          timestamp: new Date().toISOString()
        }

        console.log('SSE Stream - Returning data:', {
          agentCount: result.agents.length,
          total_count: result.total_count,
          online_count: result.online_count,
          available_count: result.available_count,
          on_call_count: result.on_call_count
        })

        return result
      } catch (error) {
        console.error('Error fetching realtime agent data:', error)
        throw error
      }
    }

    // Start polling if not already active with increased interval to reduce load
    if (!fsSocket.isPolling(CONTEXT)) {
      console.log(`Starting FreeSWITCH polling for context: ${CONTEXT}`)

      fsSocket.startPolling(CONTEXT, 'callcenter_config agent list', 10000, async freeswitchResult => {
        try {
          // Only process data if FreeSWITCH command succeeded
          if (freeswitchResult && freeswitchResult !== 'Error' && !freeswitchResult.includes('-ERR')) {
            console.log('Processing FreeSWITCH agent list data for SSE broadcast')

            // Fetch and process complete agent data
            const data = await fetchRealtimeAgentData()

            fsSocket.broadcast(CONTEXT, JSON.stringify(data))
          } else {
            console.warn('FreeSWITCH command failed, sending error state to SSE clients')

            // Send error state to clients
            const errorData = {
              agents: [],
              total_count: 0,
              online_count: 0,
              available_count: 0,
              on_call_count: 0,
              timestamp: new Date().toISOString(),
              error: 'FreeSWITCH connection error'
            }

            fsSocket.broadcast(CONTEXT, JSON.stringify(errorData))
          }
        } catch (error) {
          console.error('Error in SSE polling callback:', error)

          // Send error state to clients
          const errorData = {
            agents: [],
            total_count: 0,
            online_count: 0,
            available_count: 0,
            on_call_count: 0,
            timestamp: new Date().toISOString(),
            error: error instanceof Error ? error.message : 'Unknown error'
          }

          fsSocket.broadcast(CONTEXT, JSON.stringify(errorData))
        }
      })
    }

    // Create SSE stream
    const stream = new ReadableStream({
      async start(controller) {
        const encoder = new TextEncoder()
        let closed = false

        const send = (data: string) => {
          if (!closed && controller.desiredSize !== null) {
            try {
              controller.enqueue(encoder.encode(`data: ${data}\n\n`))
            } catch (error) {
              console.error('Error sending SSE data:', error)
              closed = true
            }
          }
        }

        // Register client for updates
        fsSocket.registerClient(CONTEXT, send)

        // Send initial data with a small delay to ensure connection is established
        console.log('SSE Stream - Sending initial data to client')

        // Use setTimeout to ensure the connection is fully established
        setTimeout(async () => {
          if (!closed && controller.desiredSize !== null) {
            try {
              const initialData = await fetchRealtimeAgentData()

              console.log('SSE Stream - Initial data fetched successfully, sending to client')
              send(JSON.stringify(initialData))
            } catch (error) {
              console.error('SSE Stream - Error fetching initial data:', error)
              send(
                JSON.stringify({
                  agents: [],
                  total_count: 0,
                  online_count: 0,
                  available_count: 0,
                  on_call_count: 0,
                  timestamp: new Date().toISOString(),
                  error: 'Failed to fetch initial data'
                })
              )
            }
          }
        }, 100) // 100ms delay

        // Handle stream close
        const cleanup = () => {
          if (!closed) {
            closed = true
            fsSocket.unregisterClient(CONTEXT, send)
            console.log(`SSE client disconnected from ${CONTEXT}`)
          }
        }

        // Return cleanup function for cancel
        return cleanup
      },
      cancel() {
        console.log('SSE Stream cancelled by client')
      }
    })

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      }
    })
  } catch (error) {
    console.error('Error in realtime agent monitor SSE:', error)

    return NextResponse.json(
      { error: 'Failed to start SSE stream', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
