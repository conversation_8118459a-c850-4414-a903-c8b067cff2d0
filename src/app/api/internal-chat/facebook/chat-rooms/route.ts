// Internal Chat - Facebook Chat Rooms API
// GET /api/internal-chat/facebook/chat-rooms - Get Facebook chat rooms for domain
// POST /api/internal-chat/facebook/chat-rooms - Create Facebook chat room bridge

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'

import { RoomType, ParticipantRole } from '@/types/apps/internal-chat/chatTypes'

// GET /api/internal-chat/facebook/chat-rooms
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user!.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    const searchParams = request.nextUrl.searchParams
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')
    const status = searchParams.get('status') || 'active'

    // Get Facebook chat rooms with related data
    const facebookRooms = await prisma.v_facebook_chat_rooms.findMany({
      where: {
        domain_uuid: user.domain_uuid,
        room_status: status
      },
      include: {
        v_chat_rooms: {
          select: {
            room_uuid: true,
            room_name: true,
            room_description: true,
            room_type: true,
            room_avatar: true,
            is_active: true,
            is_archived: true,
            room_settings: true,
            insert_date: true,
            update_date: true
          }
        },
        v_facebook_contacts: {
          select: {
            contact_uuid: true,
            facebook_user_id: true,
            first_name: true,
            last_name: true,
            profile_pic: true,
            locale: true,
            timezone: true,
            is_active: true,
            contact_info: true,
            last_interaction_date: true
          }
        }
      },
      orderBy: { last_message_at: 'desc' },
      take: limit,
      skip: offset
    })

    // Get agent information for assigned rooms
    const assignedAgentUuids = facebookRooms
      .map(room => room.assigned_agent_uuid)
      .filter(Boolean) as string[]

    const agents = assignedAgentUuids.length > 0 ? await prisma.v_users.findMany({
      where: {
        user_uuid: { in: assignedAgentUuids }
      },
      select: {
        user_uuid: true,
        username: true
      }
    }) : []

    // Create agent lookup map
    const agentMap = new Map(
      agents.map(agent => [
        agent.user_uuid,
        {
          user_uuid: agent.user_uuid,
          username: agent.username || agent.user_uuid,
          display_name: agent.username || agent.user_uuid,
          first_name: null,
          last_name: null
        }
      ])
    )

    // Transform to expected format
    const chatRooms = facebookRooms.map(room => ({
      facebook_room_uuid: room.facebook_room_uuid,
      internal_room_uuid: room.internal_room_uuid,
      facebook_contact_uuid: room.facebook_contact_uuid,
      assigned_agent_uuid: room.assigned_agent_uuid,
      assigned_agent: room.assigned_agent_uuid ? agentMap.get(room.assigned_agent_uuid) || null : null,
      room_status: room.room_status,
      last_message_at: room.last_message_at,
      conversation_metadata: room.conversation_metadata,
      contact: {
        contact_uuid: room.v_facebook_contacts.contact_uuid,
        facebook_user_id: room.v_facebook_contacts.facebook_user_id,
        first_name: room.v_facebook_contacts.first_name,
        last_name: room.v_facebook_contacts.last_name,
        profile_pic: room.v_facebook_contacts.profile_pic,
        locale: room.v_facebook_contacts.locale,
        timezone: room.v_facebook_contacts.timezone,
        is_active: room.v_facebook_contacts.is_active,
        contact_info: room.v_facebook_contacts.contact_info,
        last_interaction_date: room.v_facebook_contacts.last_interaction_date
      },
      chat_room: {
        room_uuid: room.v_chat_rooms.room_uuid,
        room_name: room.v_chat_rooms.room_name,
        room_description: room.v_chat_rooms.room_description,
        room_type: room.v_chat_rooms.room_type,
        room_avatar: room.v_chat_rooms.room_avatar,
        is_active: room.v_chat_rooms.is_active,
        is_archived: room.v_chat_rooms.is_archived,
        room_settings: room.v_chat_rooms.room_settings,
        insert_date: room.v_chat_rooms.insert_date,
        update_date: room.v_chat_rooms.update_date
      },
      is_assigned: !!room.assigned_agent_uuid,
      display_name: [room.v_facebook_contacts.first_name, room.v_facebook_contacts.last_name]
        .filter(Boolean)
        .join(' ') || `Facebook User ${room.v_facebook_contacts.facebook_user_id}`
    }))

    return NextResponse.json({
      chat_rooms: chatRooms,
      total: chatRooms.length,
      limit,
      offset
    })
  } catch (error) {
    console.error('Error fetching Facebook chat rooms:', error)

    return NextResponse.json({ error: 'Failed to fetch chat rooms' }, { status: 500 })
  }
}

// POST /api/internal-chat/facebook/chat-rooms
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user!.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    const body = await request.json()

    const { facebook_contact_uuid, assigned_agent_uuid, room_name, room_description, conversation_metadata = {} } = body

    // Validate required fields
    if (!facebook_contact_uuid) {
      return NextResponse.json(
        {
          error: 'facebook_contact_uuid is required'
        },
        { status: 400 }
      )
    }

    // Verify contact exists and belongs to domain
    const contact = await prisma.v_facebook_contacts.findFirst({
      where: {
        contact_uuid: facebook_contact_uuid,
        domain_uuid: user.domain_uuid
      }
    })

    if (!contact) {
      return NextResponse.json(
        {
          error: 'Facebook contact not found or access denied'
        },
        { status: 404 }
      )
    }

    // Check if room already exists for this contact
    const existingRoom = await prisma.v_facebook_chat_rooms.findFirst({
      where: {
        domain_uuid: user.domain_uuid,
        facebook_contact_uuid
      }
    })

    if (existingRoom) {
      return NextResponse.json(
        {
          error: 'Room already exists for this Facebook contact',
          internal_room_uuid: existingRoom.internal_room_uuid
        },
        { status: 409 }
      )
    }

    // Generate display name
    const displayName = [contact.first_name, contact.last_name].filter(Boolean).join(' ') || 
                       `Facebook User ${contact.facebook_user_id}`

    const result = await prisma.$transaction(async tx => {
      // Create the internal room
      const internalRoom = await tx.v_chat_rooms.create({
        data: {
          domain_uuid: user.domain_uuid!,
          room_name: room_name || `Facebook: ${displayName}`,
          room_description: room_description || `Facebook conversation with ${displayName}`,
          room_type: RoomType.DIRECT,
          created_by_user_uuid: session.user!.id,
          room_settings: {
            platform: 'facebook',
            facebook_user_id: contact.facebook_user_id,
            auto_created: false
          },
          insert_date: new Date(),
          insert_user: session.user!.id,
          update_date: new Date(),
          update_user: session.user!.id
        }
      })

      // Add creator as owner
      await tx.v_chat_room_participants.create({
        data: {
          room_uuid: internalRoom.room_uuid,
          user_uuid: session.user!.id,
          participant_role: ParticipantRole.OWNER,
          insert_user: session.user!.id,
          update_user: session.user!.id
        }
      })

      // Create Facebook chat room bridge
      const facebookRoom = await tx.v_facebook_chat_rooms.create({
        data: {
          domain_uuid: user.domain_uuid!,
          internal_room_uuid: internalRoom.room_uuid,
          facebook_contact_uuid,
          assigned_agent_uuid,
          room_status: 'active',
          last_message_at: new Date(),
          conversation_metadata: {
            ...conversation_metadata,
            facebook_user_id: contact.facebook_user_id,
            created_manually: true
          }
        }
      })

      return {
        internal_room_uuid: internalRoom.room_uuid,
        facebook_room_uuid: facebookRoom.facebook_room_uuid,
        room_name: internalRoom.room_name,
        contact_name: displayName
      }
    })

    return NextResponse.json({
      success: true,
      ...result,
      message: 'Facebook chat room created successfully'
    })
  } catch (error) {
    console.error('Error creating Facebook chat room:', error)

    return NextResponse.json({ error: 'Failed to create chat room' }, { status: 500 })
  }
}
