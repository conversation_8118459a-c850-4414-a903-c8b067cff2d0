// Facebook Page Configuration API
// Manages Facebook app and page settings per domain

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'

interface FacebookPageConfigRequest {
  app_id: string
  app_secret: string
  page_id: string
  page_access_token: string
  verify_token: string
  webhook_url?: string
  allowed_events?: string[]
  page_settings?: Record<string, any>
}

/**
 * Validate Facebook Page Access Token
 */
async function validatePageAccessToken(
  pageAccessToken: string,
  pageId: string
): Promise<{ valid: boolean; error?: string; pageInfo?: any }> {
  try {
    const response = await fetch(
      `https://graph.facebook.com/v18.0/${pageId}?access_token=${pageAccessToken}&fields=id,name,category,verification_status`
    )

    if (!response.ok) {
      const error = await response.json()

      return {
        valid: false,
        error: error.error?.message || 'Invalid page access token'
      }
    }

    const pageInfo = await response.json()

    return {
      valid: true,
      pageInfo
    }
  } catch (error) {
    return {
      valid: false,
      error: 'Failed to validate page access token'
    }
  }
}

/**
 * Set Facebook webhook using Graph API
 */
async function setFacebookWebhook(
  pageAccessToken: string,
  webhookUrl: string,
  verifyToken: string,
  allowedEvents: string[] = ['messages', 'messaging_postbacks']
): Promise<{ success: boolean; error?: string }> {
  try {
    const response = await fetch(
      `https://graph.facebook.com/v18.0/me/subscribed_apps?access_token=${pageAccessToken}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          subscribed_fields: allowedEvents.join(',')
        })
      }
    )

    if (!response.ok) {
      const error = await response.json()

      return {
        success: false,
        error: error.error?.message || 'Failed to set webhook'
      }
    }

    return { success: true }
  } catch (error) {
    return {
      success: false,
      error: 'Failed to configure Facebook webhook'
    }
  }
}

// GET /api/internal-chat/facebook/page-config
export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user!.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    // Get Facebook configuration for this domain
    const config = await prisma.v_facebook_page_config.findFirst({
      where: {
        domain_uuid: user.domain_uuid
      },
      select: {
        config_uuid: true,
        domain_uuid: true,
        app_id: true,
        page_id: true,
        webhook_url: true,
        is_active: true,
        allowed_events: true,
        page_settings: true,
        insert_date: true,
        update_date: true

        // Exclude sensitive fields like app_secret, page_access_token, verify_token
      }
    })

    if (!config) {
      return NextResponse.json({
        configured: false,
        message: 'Facebook configuration not found for this domain'
      })
    }

    return NextResponse.json({
      configured: true,
      config: {
        ...config,
        webhook_url:
          config.webhook_url ||
          `${process.env.NEXT_PUBLIC_API_URL}/api/internal-chat/facebook/webhook?domain=${user.domain_uuid}`
      }
    })
  } catch (error) {
    console.error('Error retrieving Facebook configuration:', error)

    return NextResponse.json({ error: 'Failed to retrieve configuration' }, { status: 500 })
  }
}

// POST /api/internal-chat/facebook/page-config
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user!.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    const body: FacebookPageConfigRequest = await request.json()

    const {
      app_id,
      app_secret,
      page_id,
      page_access_token,
      verify_token,
      webhook_url,
      allowed_events = ['messages', 'messaging_postbacks', 'messaging_optins'],
      page_settings = {}
    } = body

    // Validate required fields
    if (!app_id || !app_secret || !page_id || !page_access_token || !verify_token) {
      return NextResponse.json(
        { error: 'Missing required fields: app_id, app_secret, page_id, page_access_token, verify_token' },
        { status: 400 }
      )
    }

    // Validate page access token
    console.log('Validating Facebook page access token...')
    const tokenValidation = await validatePageAccessToken(page_access_token.trim(), page_id.trim())

    if (!tokenValidation.valid) {
      return NextResponse.json({ error: `Invalid page access token: ${tokenValidation.error}` }, { status: 400 })
    }

    // Generate webhook URL if not provided
    const finalWebhookUrl =
      webhook_url?.trim() ||
      `${process.env.NEXT_PUBLIC_API_URL}/api/internal-chat/facebook/webhook?domain=${user.domain_uuid}`

    const configData = {
      domain_uuid: user.domain_uuid,
      app_id: app_id.trim(),
      app_secret: app_secret.trim(),
      page_id: page_id.trim(),
      page_access_token: page_access_token.trim(),
      verify_token: verify_token.trim(),
      webhook_url: finalWebhookUrl,
      is_active: true,
      allowed_events,
      page_settings: {
        ...page_settings,
        page_name: tokenValidation.pageInfo?.name,
        page_category: tokenValidation.pageInfo?.category,
        verification_status: tokenValidation.pageInfo?.verification_status
      },
      update_date: new Date(),
      update_user: session.user!.id
    }

    // Check if configuration already exists
    const existingConfig = await prisma.v_facebook_page_config.findFirst({
      where: { domain_uuid: user.domain_uuid }
    })

    let config

    if (existingConfig) {
      // Update existing configuration
      config = await prisma.v_facebook_page_config.update({
        where: { config_uuid: existingConfig.config_uuid },
        data: configData
      })
    } else {
      // Create new configuration
      config = await prisma.v_facebook_page_config.create({
        data: {
          ...configData,
          insert_date: new Date(),
          insert_user: session.user!.id
        }
      })
    }

    // Set up Facebook webhook subscription
    console.log('Setting up Facebook webhook subscription...')

    const webhookResult = await setFacebookWebhook(
      page_access_token.trim(),
      finalWebhookUrl,
      verify_token.trim(),
      allowed_events
    )

    if (!webhookResult.success) {
      console.warn('Failed to set Facebook webhook:', webhookResult.error)

      // Don't fail the configuration save, just warn
    }

    // Return configuration without sensitive data
    const responseConfig = {
      config_uuid: config.config_uuid,
      domain_uuid: config.domain_uuid,
      app_id: config.app_id,
      page_id: config.page_id,
      webhook_url: config.webhook_url,
      is_active: config.is_active,
      allowed_events: config.allowed_events,
      page_settings: config.page_settings,
      insert_date: config.insert_date,
      update_date: config.update_date
    }

    return NextResponse.json({
      success: true,
      config: responseConfig,
      webhook_setup: webhookResult.success,
      webhook_error: webhookResult.error || null,
      message: existingConfig ? 'Configuration updated successfully' : 'Configuration created successfully'
    })
  } catch (error) {
    console.error('Error saving Facebook configuration:', error)

    return NextResponse.json({ error: 'Failed to save configuration' }, { status: 500 })
  }
}

// PUT /api/internal-chat/facebook/page-config
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user!.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    const body = await request.json()
    const { is_active, allowed_events, page_settings } = body

    // Find existing configuration
    const existingConfig = await prisma.v_facebook_page_config.findFirst({
      where: {
        domain_uuid: user.domain_uuid
      }
    })

    if (!existingConfig) {
      return NextResponse.json({ error: 'Facebook configuration not found' }, { status: 404 })
    }

    // Update configuration
    const updatedConfig = await prisma.v_facebook_page_config.update({
      where: { config_uuid: existingConfig.config_uuid },
      data: {
        is_active: is_active !== undefined ? is_active : existingConfig.is_active,
        allowed_events: allowed_events || existingConfig.allowed_events,
        page_settings: page_settings || existingConfig.page_settings,
        update_date: new Date(),
        update_user: session.user!.id
      }
    })

    return NextResponse.json({
      success: true,
      config: {
        config_uuid: updatedConfig.config_uuid,
        domain_uuid: updatedConfig.domain_uuid,
        app_id: updatedConfig.app_id,
        page_id: updatedConfig.page_id,
        webhook_url: updatedConfig.webhook_url,
        is_active: updatedConfig.is_active,
        allowed_events: updatedConfig.allowed_events,
        page_settings: updatedConfig.page_settings,
        update_date: updatedConfig.update_date
      },
      message: 'Configuration updated successfully'
    })
  } catch (error) {
    console.error('Error updating Facebook configuration:', error)

    return NextResponse.json({ error: 'Failed to update configuration' }, { status: 500 })
  }
}

// DELETE /api/internal-chat/facebook/page-config
export async function DELETE() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user!.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    // Find and delete configuration
    const deletedConfig = await prisma.v_facebook_page_config.deleteMany({
      where: {
        domain_uuid: user.domain_uuid
      }
    })

    if (deletedConfig.count === 0) {
      return NextResponse.json({ error: 'Facebook configuration not found' }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      message: 'Facebook configuration deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting Facebook configuration:', error)

    return NextResponse.json({ error: 'Failed to delete configuration' }, { status: 500 })
  }
}
