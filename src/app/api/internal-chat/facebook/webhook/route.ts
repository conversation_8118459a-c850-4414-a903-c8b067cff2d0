// Facebook Messenger Webhook Integration
// Handles webhook verification and message processing

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { prisma } from '@/libs/db/prisma'
import { FacebookMessageService } from '@/services/facebook/facebookMessageService'
import type { FacebookWebhookPayload } from '@/types/social/facebookTypes'
import { sanitizeWebhookPayload, isValidDomainUuid } from '@/utils/social/platformUtils'
import { calculateUnreadCounts } from '@/utils/social/messageUtils'

/**
 * GET - Facebook webhook verification
 * Required by Facebook to verify webhook endpoint
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const mode = searchParams.get('hub.mode')
    const token = searchParams.get('hub.verify_token')
    const challenge = searchParams.get('hub.challenge')
    const domainUuid = searchParams.get('domain')

    console.log('=== FACEBOOK WEBHOOK VERIFICATION ===')
    console.log('Full URL:', request.url)
    console.log('Mode:', mode)
    console.log('Domain UUID:', domainUuid)
    console.log('Token provided:', !!token)
    console.log('Challenge provided:', !!challenge)
    console.log('Challenge value:', challenge)

    // Facebook requires mode to be 'subscribe'
    if (mode !== 'subscribe') {
      console.log('❌ Invalid mode:', mode)

      return new Response('Invalid mode', { status: 403 })
    }

    // Facebook requires challenge parameter
    if (!challenge) {
      console.log('❌ Missing challenge parameter')

      return new Response('Missing challenge', { status: 400 })
    }

    // Facebook requires verify_token parameter
    if (!token) {
      console.log('❌ Missing verify_token parameter')

      return new Response('Missing verify_token', { status: 400 })
    }

    // Domain UUID is required for our multi-tenant setup
    if (!domainUuid) {
      console.log('❌ Missing domain parameter')

      return new Response('Missing domain parameter', { status: 400 })
    }

    console.log('✅ All required parameters present')

    // Verify token against stored configuration
    let config

    try {
      config = await prisma.v_facebook_page_config.findFirst({
        where: {
          domain_uuid: domainUuid,
          is_active: true
        },
        select: { verify_token: true, page_id: true, app_id: true }
      })
    } catch (dbError) {
      console.error('❌ Database error:', dbError)

      return new Response('Database error', { status: 500 })
    }

    if (!config) {
      console.log('❌ Facebook configuration not found for domain:', domainUuid)

      return new Response('Configuration not found', { status: 404 })
    }

    console.log('✅ Configuration found for page:', config.page_id)

    if (config.verify_token !== token) {
      console.log('❌ Token verification failed')
      console.log('Expected token:', config.verify_token)
      console.log('Received token:', token)

      return new Response('Token verification failed', { status: 403 })
    }

    console.log('✅ Token verification successful')
    console.log('✅ Returning challenge:', challenge)

    // Return challenge exactly as Facebook sent it
    return new Response(challenge, {
      status: 200,
      headers: {
        'Content-Type': 'text/plain',
        'Cache-Control': 'no-cache'
      }
    })
  } catch (error) {
    console.error('=== FACEBOOK WEBHOOK VERIFICATION ERROR ===')
    console.error('Error during webhook verification:', error)
    console.error('Request URL:', request.url)
    console.error('Error stack:', (error as Error)?.stack)
    console.error('=========================================')

    return new Response('Verification failed', { status: 500 })
  }
}

/**
 * POST - Process Facebook webhook events
 * Handles incoming messages and other events
 */
export async function POST(request: NextRequest) {
  try {
    const domainUuid = request.nextUrl.searchParams.get('domain')

    if (!domainUuid) {
      return NextResponse.json({ error: 'Domain UUID is required' }, { status: 400 })
    }

    // Validate domain UUID format
    if (!isValidDomainUuid(domainUuid)) {
      return NextResponse.json({ error: 'Invalid domain UUID format' }, { status: 400 })
    }

    // Get request body and signature
    const body = await request.text()
    const signature = request.headers.get('x-hub-signature-256')

    console.log('=== FACEBOOK WEBHOOK RECEIVED ===')
    console.log('Domain UUID:', domainUuid)
    console.log('Signature provided:', !!signature)
    console.log('Body length:', body.length)
    console.log('Request body:', body) // Log first 100 chars for brevity

    // Verify webhook signature
    const signatureValid = await FacebookMessageService.verifyWebhookSignature(domainUuid, body, signature)

    if (!signatureValid) {
      console.log('Webhook signature verification failed')

      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 })
    }

    // Parse and sanitize webhook payload
    let webhookEvent: FacebookWebhookPayload

    try {
      const rawPayload = JSON.parse(body)

      webhookEvent = sanitizeWebhookPayload(rawPayload)
    } catch (error) {
      console.error('Failed to parse webhook payload:', error)

      return NextResponse.json({ error: 'Invalid payload format' }, { status: 400 })
    }

    console.log('Webhook Event Object:', webhookEvent.object)
    console.log('Number of entries:', webhookEvent.entry?.length || 0)

    // Log webhook event for audit
    await prisma.v_facebook_webhook_event.create({
      data: {
        domain_uuid: domainUuid,
        facebook_user_id: webhookEvent.entry?.[0]?.messaging?.[0]?.sender?.id,
        event_name: webhookEvent.object,
        msg: webhookEvent as any,
        insert_date: new Date(),
        update_date: new Date()
      }
    })

    // Process each entry in the webhook
    const processedMessages: any[] = []

    for (const entry of webhookEvent.entry || []) {
      console.log('Processing entry ID:', entry.id)
      console.log('Entry time:', entry.time)
      console.log('Number of messaging events:', entry.messaging?.length || 0)

      // Process each messaging event
      for (const messagingEvent of entry.messaging || []) {
        try {
          console.log('Processing messaging event:', {
            sender: messagingEvent.sender.id,
            recipient: messagingEvent.recipient.id,
            timestamp: messagingEvent.timestamp,
            hasMessage: !!messagingEvent.message,
            hasPostback: !!messagingEvent.postback,
            hasDelivery: !!messagingEvent.delivery,
            hasRead: !!messagingEvent.read
          })

          // Handle different event types
          if (messagingEvent.message) {
            // Process inbound message
            const result = await FacebookMessageService.processInboundMessage(domainUuid, messagingEvent)

            processedMessages.push(result)

            // Broadcast message using main chat system
            await broadcastFacebookMessage(domainUuid, result)
          } else if (messagingEvent.postback) {
            // Handle postback events (button clicks, etc.)
            console.log('Postback event received:', messagingEvent.postback.title)

            // TODO: Implement postback handling if needed
          } else if (messagingEvent.delivery) {
            // Handle delivery confirmations
            console.log('Delivery confirmation received for messages:', messagingEvent.delivery.mids)
            await updateMessageDeliveryStatus(domainUuid, messagingEvent.delivery.mids, 'delivered')
          } else if (messagingEvent.read) {
            // Handle read receipts
            console.log('Read receipt received, watermark:', messagingEvent.read.watermark)
            await updateMessageReadStatus(domainUuid, messagingEvent.read.watermark)
          } else {
            console.log('Unknown messaging event type, skipping')
          }
        } catch (eventError) {
          console.error('Error processing messaging event:', eventError)

          // Continue processing other events even if one fails
        }
      }
    }

    console.log('=== FACEBOOK WEBHOOK SUCCESS ===')
    console.log('Processed messages:', processedMessages.length)
    console.log('Processing completed successfully')
    console.log('===============================')

    return NextResponse.json(
      {
        status: 'processed',
        processed_messages: processedMessages.length,
        message_ids: processedMessages.map(m => m.internal_message_id)
      },
      { status: 200 }
    )
  } catch (error) {
    console.error('=== FACEBOOK WEBHOOK ERROR ===')
    console.error('Error processing Facebook webhook:', error)
    console.error('Domain UUID:', request.nextUrl.searchParams.get('domain'))
    console.error('Error details:', {
      name: (error as Error)?.name,
      message: (error as Error)?.message,
      stack: (error as Error)?.stack
    })
    console.error('=============================')

    return NextResponse.json({ error: 'Webhook processing failed' }, { status: 500 })
  }

  /**
   * Broadcast Facebook message via Socket.IO
   */
  async function broadcastFacebookMessage(domainUuid: string, result: any): Promise<void> {
    try {
      if (!(global as any).socketBroadcast?.broadcastMessage) {
        console.warn('Socket broadcast not available')

        return
      }

      // Get the full message data for socket emission
      const fullMessage = await prisma.v_chat_messages.findUnique({
        where: { message_id: BigInt(result.internal_message_id) }
      })

      if (!fullMessage) {
        console.warn('Message not found for broadcasting:', result.internal_message_id)

        return
      }

      // Get room data for domain validation
      const roomData = await prisma.v_chat_rooms.findUnique({
        where: { room_uuid: result.room_uuid },
        select: { domain_uuid: true }
      })

      if (!roomData || roomData.domain_uuid !== domainUuid) {
        console.warn('Room domain mismatch or room not found')

        return
      }

      // Calculate unread counts
      const unreadCounts = await calculateUnreadCounts(result.room_uuid)

      // Use unified broadcast system for consistent message delivery
      const { broadcastFacebookMessage } = await import('@/utils/messaging/broadcastUtils')

      await broadcastFacebookMessage(
        domainUuid,
        result.room_uuid,
        {
          message_id: result.internal_message_id,
          content: result.content || '[Message]',
          author_uuid: result.external_user_id || 'facebook_user',
          author_name: result.contact_info?.display_name || 'Facebook User',
          facebook_message_id: result.facebook_message_id,
          delivery_status: 'delivered',
          unread_counts: unreadCounts
        }
      )

      // Check if conversation is unassigned and create notifications
      const { unifiedNotificationService } = await import('@/services/notifications/unifiedNotificationService')

      // Check if room has assigned agents
      const assignedAgents = await prisma.v_chat_room_participants.findMany({
        where: {
          room_uuid: result.room_uuid,
          deleted_at: null,
          participant_role: { in: ['admin', 'moderator'] } // Agents typically have these roles
        }
      })

      if (assignedAgents.length === 0) {
        // No agents assigned, create unassigned conversation notification
        await unifiedNotificationService.createUnassignedConversationNotification(
          domainUuid,
          result.room_uuid,
          {
            message_id: result.internal_message_id.toString(),
            content: result.content || '[Message]',
            author_name: result.contact_info?.display_name || 'Facebook User',
            platform: 'facebook'
          }
        )
      }

      console.log('Facebook webhook message broadcasted successfully via unified system')
    } catch (socketError) {
      console.error('Failed to broadcast Facebook message:', socketError)

      // Don't fail the webhook if socket emission fails
    }
  }

  /**
   * Update message delivery status
   */
  async function updateMessageDeliveryStatus(domainUuid: string, messageIds: string[], status: string): Promise<void> {
    try {
      await prisma.v_facebook_message_mapping.updateMany({
        where: {
          domain_uuid: domainUuid,
          facebook_message_id: { in: messageIds }
        },
        data: {
          delivery_status: status
        }
      })

      console.log(`Updated delivery status to ${status} for ${messageIds.length} messages`)
    } catch (error) {
      console.error('Failed to update message delivery status:', error)
    }
  }

  /**
   * Update message read status
   */
  async function updateMessageReadStatus(domainUuid: string, watermark: number): Promise<void> {
    try {
      // Update all messages sent before the watermark timestamp
      await prisma.v_facebook_message_mapping.updateMany({
        where: {
          domain_uuid: domainUuid,
          message_direction: 'outbound',
          delivery_status: { in: ['sent', 'delivered'] }
        },
        data: {
          delivery_status: 'read'
        }
      })

      console.log(`Updated read status for messages before watermark: ${watermark}`)
    } catch (error) {
      console.error('Failed to update message read status:', error)
    }
  }
}
