// Internal Chat - Facebook Contacts API
// GET /api/internal-chat/facebook/contacts - Get Facebook contacts for domain
// POST /api/internal-chat/facebook/contacts - Create room for Facebook contact

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'
import { FacebookIntegrationService } from '@/services/facebook/facebookIntegrationService'

// GET /api/internal-chat/facebook/contacts
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user!.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const search = searchParams.get('search') || undefined
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')
    const is_active = searchParams.get('is_active')

    const activeFilter = is_active === 'true' ? true : is_active === 'false' ? false : undefined

    // Get contacts using the integration service
    const contacts = await FacebookIntegrationService.getFacebookContactsForDomain(user.domain_uuid, {
      search,
      limit: Math.min(limit, 100), // Cap at 100
      offset: Math.max(offset, 0), // Ensure non-negative
      is_active: activeFilter
    })

    return NextResponse.json({
      contacts,
      total: contacts.length,
      limit,
      offset
    })
  } catch (error) {
    console.error('Error fetching Facebook contacts:', error)

    return NextResponse.json({ error: 'Failed to fetch contacts' }, { status: 500 })
  }
}

// POST /api/internal-chat/facebook/contacts
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user!.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    const body = await request.json()
    const { contact_uuid } = body

    if (!contact_uuid) {
      return NextResponse.json({ error: 'contact_uuid is required' }, { status: 400 })
    }

    // Verify contact belongs to this domain
    const contact = await prisma.v_facebook_contacts.findFirst({
      where: {
        contact_uuid,
        domain_uuid: user.domain_uuid
      }
    })

    if (!contact) {
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 })
    }

    // Create room for contact
    const roomUuid = await FacebookIntegrationService.createRoomForContact(user.domain_uuid, contact_uuid)

    if (!roomUuid) {
      return NextResponse.json({ error: 'Failed to create room' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      room_uuid: roomUuid,
      message: 'Room created successfully'
    })
  } catch (error) {
    console.error('Error creating Facebook room:', error)

    return NextResponse.json({ error: 'Failed to create room' }, { status: 500 })
  }
}
