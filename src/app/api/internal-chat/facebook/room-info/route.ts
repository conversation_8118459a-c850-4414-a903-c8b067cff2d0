// Facebook Room Info API
// Provides information about Facebook chat rooms for integration detection

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { prisma } from '@/libs/db/prisma'
import getSession from '@/actions/getSession'

/**
 * GET /api/internal-chat/facebook/room-info
 * Get Facebook room information for a given room UUID
 */
export async function GET(request: NextRequest) {

  const session = await getSession()
      
  try {
    // Authenticate user

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true, username: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    // Get room UUID from query parameters
    const roomUuid = request.nextUrl.searchParams.get('room_uuid')

    if (!roomUuid) {
      return NextResponse.json({ error: 'Room UUID is required' }, { status: 400 })
    }

    console.log('=== FACEBOOK ROOM INFO REQUEST ===')
    console.log('User:', user.username)
    console.log('Domain:', user.domain_uuid)
    console.log('Room UUID:', roomUuid)

    // Check if this is a Facebook room
    const facebookRoom = await prisma.v_facebook_chat_rooms.findFirst({
      where: {
        domain_uuid: user.domain_uuid,
        internal_room_uuid: roomUuid
      },
      include: {
        v_facebook_contacts: {
          select: {
            contact_uuid: true,
            facebook_user_id: true,
            first_name: true,
            last_name: true,
            profile_pic: true,
            is_active: true
          }
        }
      }
    })

    if (!facebookRoom) {
      console.log('Room is not a Facebook room')

      return NextResponse.json({ error: 'Not a Facebook room' }, { status: 404 })
    }

    // Prepare room info response
    const roomInfo = {
      internal_room_uuid: facebookRoom.internal_room_uuid,
      facebook_contact_uuid: facebookRoom.facebook_contact_uuid,
      room_status: facebookRoom.room_status,
      contact_info: {
        facebook_user_id: facebookRoom.v_facebook_contacts.facebook_user_id,
        first_name: facebookRoom.v_facebook_contacts.first_name,
        last_name: facebookRoom.v_facebook_contacts.last_name,
        profile_pic: facebookRoom.v_facebook_contacts.profile_pic,
        is_active: facebookRoom.v_facebook_contacts.is_active
      }
    }

    console.log('=== FACEBOOK ROOM INFO SUCCESS ===')
    console.log('Room Info:', {
      internal_room_uuid: roomInfo.internal_room_uuid,
      facebook_user_id: roomInfo.contact_info.facebook_user_id,
      room_status: roomInfo.room_status,
      contact_active: roomInfo.contact_info.is_active
    })
    console.log('====================================')

    return NextResponse.json({
      room_info: roomInfo
    })
  } catch (error) {
    console.error('=== FACEBOOK ROOM INFO ERROR ===')
    console.error('Error getting Facebook room info:', error)
    console.error('Error details:', {
      name: (error as Error)?.name,
      message: (error as Error)?.message,
      stack: (error as Error)?.stack
    })
    console.error('=================================')

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Failed to get room info'
      },
      { status: 500 }
    )
  }
}
