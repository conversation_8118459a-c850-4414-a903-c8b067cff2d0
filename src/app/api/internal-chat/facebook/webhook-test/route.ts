// Facebook Webhook Test Endpoint
// Simple endpoint to test webhook URL accessibility and parameters

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

/**
 * GET - Test webhook accessibility
 * This endpoint helps debug webhook URL issues
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const allParams = Object.fromEntries(searchParams.entries())

    console.log('=== FACEBOOK WEBHOOK TEST ===')
    console.log('Full URL:', request.url)
    console.log('All parameters:', allParams)
    console.log('Headers:', Object.fromEntries(request.headers.entries()))
    console.log('============================')

    return NextResponse.json({
      status: 'success',
      message: 'Facebook webhook test endpoint is accessible',
      url: request.url,
      parameters: allParams,
      timestamp: new Date().toISOString(),
      instructions: {
        webhook_verification: 'Use /api/internal-chat/facebook/webhook for actual webhook verification',
        required_params: ['hub.mode', 'hub.verify_token', 'hub.challenge', 'domain'],
        example_url: `${request.nextUrl.origin}/api/internal-chat/facebook/webhook?domain=YOUR_DOMAIN_UUID`
      }
    })
  } catch (error) {
    console.error('Webhook test error:', error)

    return NextResponse.json({
      status: 'error',
      message: 'Webhook test failed',
      error: (error as Error)?.message
    }, { status: 500 })
  }
}

/**
 * POST - Test webhook POST requests
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const searchParams = request.nextUrl.searchParams
    const allParams = Object.fromEntries(searchParams.entries())

    console.log('=== FACEBOOK WEBHOOK POST TEST ===')
    console.log('Full URL:', request.url)
    console.log('Parameters:', allParams)
    console.log('Body length:', body.length)
    console.log('Headers:', Object.fromEntries(request.headers.entries()))
    console.log('=================================')

    return NextResponse.json({
      status: 'success',
      message: 'Facebook webhook POST test endpoint received data',
      url: request.url,
      parameters: allParams,
      body_length: body.length,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Webhook POST test error:', error)

    return NextResponse.json({
      status: 'error',
      message: 'Webhook POST test failed',
      error: (error as Error)?.message
    }, { status: 500 })
  }
}
