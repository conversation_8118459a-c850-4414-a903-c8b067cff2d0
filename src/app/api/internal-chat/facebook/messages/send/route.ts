// Facebook Messenger Send Message API
// Handles outbound message sending from agents to Facebook users

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'
import { FacebookMessageService } from '@/services/facebook/facebookMessageService'
import type { FacebookSendMessageRequest, FacebookSendMessageResponse } from '@/types/social/facebookTypes'
import { validateMessageContent, broadcastMessage } from '@/utils/social/messageUtils'

/**
 * POST /api/internal-chat/facebook/messages/send
 * Send message from agent to Facebook user
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true, username: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    // Parse request body
    const body: FacebookSendMessageRequest = await request.json()
    const { room_uuid, content, message_type = 'text', quick_replies } = body

    console.log('=== FACEBOOK SEND MESSAGE REQUEST ===')
    console.log('User:', user.username)
    console.log('Domain:', user.domain_uuid)
    console.log('Room UUID:', room_uuid)
    console.log('Message Type:', message_type)
    console.log('Content Length:', content?.length || 0)
    console.log('Quick Replies:', quick_replies?.length || 0)

    // Validate required fields
    if (!room_uuid || !content) {
      return NextResponse.json({ error: 'Room UUID and content are required' }, { status: 400 })
    }

    // Validate message content
    const contentValidation = validateMessageContent(content)

    if (!contentValidation.valid) {
      return NextResponse.json({ error: contentValidation.error }, { status: 400 })
    }

    // Verify Facebook configuration exists and is active
    const facebookConfig = await prisma.v_facebook_page_config.findFirst({
      where: {
        domain_uuid: user.domain_uuid,
        is_active: true
      },
      select: {
        config_uuid: true,
        page_id: true,
        is_active: true
      }
    })

    if (!facebookConfig) {
      return NextResponse.json({ error: 'Facebook integration not configured or inactive' }, { status: 400 })
    }

    // Verify room exists and is a Facebook room
    const facebookRoom = await prisma.v_facebook_chat_rooms.findFirst({
      where: {
        domain_uuid: user.domain_uuid,
        internal_room_uuid: room_uuid
      },
      include: {
        v_facebook_contacts: {
          select: {
            contact_uuid: true,
            facebook_user_id: true,
            first_name: true,
            last_name: true,
            is_active: true
          }
        }
      }
    })

    if (!facebookRoom) {
      return NextResponse.json({ error: 'Facebook conversation not found' }, { status: 404 })
    }

    if (!facebookRoom.v_facebook_contacts.is_active) {
      return NextResponse.json({ error: 'Facebook contact is inactive' }, { status: 400 })
    }

    // Check if room is in active status
    if (facebookRoom.room_status !== 'active') {
      console.warn('Sending message to non-active room:', facebookRoom.room_status)
    }

    console.log('=== PROCESSING OUTBOUND MESSAGE ===')
    console.log('Facebook User ID:', facebookRoom.v_facebook_contacts.facebook_user_id)
    console.log(
      'Contact Name:',
      `${facebookRoom.v_facebook_contacts.first_name || ''} ${facebookRoom.v_facebook_contacts.last_name || ''}`.trim()
    )
    console.log('Room Status:', facebookRoom.room_status)

    // Process outbound message
    const result = await FacebookMessageService.processOutboundMessage(
      user.domain_uuid,
      room_uuid,
      session.user.id,
      content,
      message_type
    )

    console.log('=== MESSAGE PROCESSING RESULT ===')
    console.log('Internal Message ID:', result.internal_message_id)
    console.log('Facebook Message ID:', result.facebook_message_id)
    console.log('Delivery Status:', result.delivery_status)

    // Update room last message time
    await prisma.v_facebook_chat_rooms.update({
      where: { facebook_room_uuid: facebookRoom.facebook_room_uuid },
      data: {
        last_message_at: new Date(),
        update_date: new Date(),
        update_user: session.user.id
      }
    })

    // Broadcast message to other agents via Socket.IO
    try {
      if ((global as any).socketBroadcast?.broadcastMessage) {
        // Get the full message data for broadcasting
        const fullMessage = await prisma.v_chat_messages.findUnique({
          where: { message_id: BigInt(result.internal_message_id) }
        })

        // Get user info separately since there's no direct relation
        const messageAuthor = fullMessage?.author_uuid
          ? await prisma.v_users.findUnique({
              where: { user_uuid: fullMessage.author_uuid },
              select: {
                username: true
              }
            })
          : null

        if (fullMessage) {
          const authorName = messageAuthor?.username || user.username

          const formattedMessage = {
            message_id: result.internal_message_id,
            room_uuid: room_uuid,
            author_uuid: session.user.id,
            author_name: authorName,
            content: content,
            message_type: fullMessage.message_type,
            reply_to: fullMessage.reply_to?.toString() || null,
            created_at: fullMessage.created_at,
            flags: fullMessage.flags,
            platform: 'facebook',
            delivery_status: result.delivery_status,
            facebook_message_id: result.facebook_message_id
          }

          // Use the imported broadcastMessage utility
          await broadcastMessage(room_uuid, formattedMessage, user.domain_uuid, 'facebook')

          console.log('Message broadcasted to other agents successfully')
        }
      }
    } catch (broadcastError) {
      console.error('Failed to broadcast message to agents:', broadcastError)

      // Don't fail the API if broadcast fails
    }

    // Prepare response
    const response: FacebookSendMessageResponse = {
      success: result.delivery_status === 'sent',
      facebook_message_id: result.facebook_message_id,
      internal_message_id: result.internal_message_id,
      error: result.delivery_status === 'failed' ? 'Message delivery failed' : undefined
    }

    console.log('=== FACEBOOK SEND MESSAGE SUCCESS ===')
    console.log('Response:', response)
    console.log('====================================')

    return NextResponse.json(response, {
      status: response.success ? 200 : 500
    })
  } catch (error) {
    console.error('=== FACEBOOK SEND MESSAGE ERROR ===')
    console.error('Error sending Facebook message:', error)
    console.error('User ID:', (await getServerSession(authOptions))?.user?.id)
    console.error('Error details:', {
      name: (error as Error)?.name,
      message: (error as Error)?.message,
      stack: (error as Error)?.stack
    })
    console.error('=================================')

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to send message'
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/internal-chat/facebook/messages/send
 * Get Facebook conversation status and capabilities
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    // Get room UUID from query parameters
    const roomUuid = request.nextUrl.searchParams.get('room_uuid')

    if (!roomUuid) {
      return NextResponse.json({ error: 'Room UUID is required' }, { status: 400 })
    }

    // Get Facebook room information
    const facebookRoom = await prisma.v_facebook_chat_rooms.findFirst({
      where: {
        domain_uuid: user.domain_uuid,
        internal_room_uuid: roomUuid
      },
      include: {
        v_facebook_contacts: {
          select: {
            facebook_user_id: true,
            first_name: true,
            last_name: true,
            is_active: true,
            last_interaction_date: true
          }
        }
      }
    })

    if (!facebookRoom) {
      return NextResponse.json({ error: 'Facebook conversation not found' }, { status: 404 })
    }

    // Get Facebook configuration status
    const facebookConfig = await prisma.v_facebook_page_config.findFirst({
      where: {
        domain_uuid: user.domain_uuid,
        is_active: true
      },
      select: {
        is_active: true,
        page_id: true
      }
    })

    const conversationInfo = {
      room_uuid: roomUuid,
      facebook_room_uuid: facebookRoom.facebook_room_uuid,
      room_status: facebookRoom.room_status,
      last_message_at: facebookRoom.last_message_at,
      contact: {
        facebook_user_id: facebookRoom.v_facebook_contacts.facebook_user_id,
        name: `${facebookRoom.v_facebook_contacts.first_name || ''} ${facebookRoom.v_facebook_contacts.last_name || ''}`.trim(),
        is_active: facebookRoom.v_facebook_contacts.is_active,
        last_interaction_date: facebookRoom.v_facebook_contacts.last_interaction_date
      },
      capabilities: {
        can_send_messages: !!(facebookConfig?.is_active && facebookRoom.v_facebook_contacts.is_active),
        can_send_attachments: true,
        can_send_quick_replies: true,
        supports_read_receipts: true,
        supports_delivery_confirmations: true
      },
      configuration: {
        facebook_configured: !!facebookConfig?.is_active,
        page_id: facebookConfig?.page_id
      }
    }

    return NextResponse.json(conversationInfo)
  } catch (error) {
    console.error('Error getting Facebook conversation info:', error)

    return NextResponse.json({ error: 'Failed to get conversation information' }, { status: 500 })
  }
}
