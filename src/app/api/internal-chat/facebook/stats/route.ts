// Internal Chat - Facebook Integration Statistics API
// GET /api/internal-chat/facebook/stats - Get Facebook integration statistics

import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'

interface FacebookStats {
  total_contacts: number
  active_rooms: number
  unassigned_rooms: number
  total_messages_sent: number
  total_messages_received: number
  failed_messages: number
  last_activity: Date | null
}

// GET /api/internal-chat/facebook/stats
export async function GET() {
  const session = await getServerSession(authOptions)

  try {
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user!.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    // Check if Facebook configuration exists and is properly configured
    const pageConfig = await prisma.v_facebook_page_config.findFirst({
      where: {
        domain_uuid: user.domain_uuid
      }
    })

    if (!pageConfig) {
      return NextResponse.json(
        {
          error: 'Facebook integration not configured',
          configured: false,
          message: 'Please configure your Facebook Messenger integration first'
        },
        { status: 404 }
      )
    }

    if (!pageConfig.page_access_token || pageConfig.page_access_token.trim().length === 0) {
      return NextResponse.json(
        {
          error: 'Page access token is required',
          configured: false,
          message: 'Please provide a valid page access token in the configuration'
        },
        { status: 400 }
      )
    }

    if (!pageConfig.is_active) {
      return NextResponse.json(
        {
          error: 'Facebook integration is not active',
          configured: true,
          message: 'Please activate your Facebook integration configuration'
        },
        { status: 400 }
      )
    }

    // Get Facebook integration statistics
    const stats = await getFacebookStats(user.domain_uuid)

    return NextResponse.json({
      stats,
      configured: true,
      integration_active: true
    })
  } catch (error) {
    console.error('Error fetching Facebook statistics:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * Get Facebook integration statistics for domain
 */
async function getFacebookStats(domain_uuid: string): Promise<FacebookStats> {
  const [totalContacts, activeRooms, messageStats, lastActivity] = await Promise.all([
    // Total contacts
    prisma.v_facebook_contacts.count({
      where: { domain_uuid }
    }),

    // Active rooms
    prisma.v_facebook_chat_rooms.count({
      where: {
        domain_uuid,
        room_status: 'active'
      }
    }),

    // Message statistics
    prisma.v_facebook_message_mapping.groupBy({
      by: ['message_direction', 'delivery_status'],
      where: { domain_uuid },
      _count: true
    }),

    // Last activity
    prisma.v_facebook_chat_rooms.findFirst({
      where: { domain_uuid },
      orderBy: { last_message_at: 'desc' },
      select: { last_message_at: true }
    })
  ])

  // Count unassigned rooms (rooms without any active participants)
  const facebookRooms = await prisma.v_facebook_chat_rooms.findMany({
    where: {
      domain_uuid,
      room_status: 'active'
    },
    include: {
      v_chat_rooms: {
        include: {
          v_chat_room_participants: {
            where: {
              deleted_at: null
            }
          }
        }
      }
    }
  })

  const unassignedRooms = facebookRooms.filter(
    room => !room.v_chat_rooms?.v_chat_room_participants || room.v_chat_rooms.v_chat_room_participants.length === 0
  ).length

  // Process message statistics
  let totalMessagesSent = 0
  let totalMessagesReceived = 0
  let failedMessages = 0

  messageStats.forEach(stat => {
    if (stat.message_direction === 'outbound') {
      if (stat.delivery_status === 'failed') {
        failedMessages += stat._count
      } else {
        totalMessagesSent += stat._count
      }
    } else if (stat.message_direction === 'inbound') {
      totalMessagesReceived += stat._count
    }
  })

  return {
    total_contacts: totalContacts,
    active_rooms: activeRooms,
    unassigned_rooms: unassignedRooms,
    total_messages_sent: totalMessagesSent,
    total_messages_received: totalMessagesReceived,
    failed_messages: failedMessages,
    last_activity: lastActivity?.last_message_at || null
  }
}
