// Internal Chat - File Download API
// GET /api/internal-chat/files/[attachmentId] - Download file attachment

import { readFile } from 'fs/promises'

import { existsSync } from 'fs'

import path from 'path'

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'

const UPLOAD_DIR = process.env.CHAT_UPLOAD_PATH || './uploads/chat'

// GET /api/internal-chat/files/[attachmentId]
export async function GET(request: NextRequest, { params }: { params: { attachmentId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { attachmentId } = params
    const { searchParams } = new URL(request.url)
    const download = searchParams.get('download') === 'true'

    // Get attachment info with message and room details
    const attachment = await prisma.v_chat_message_attachments.findUnique({
      where: { attachment_id: BigInt(attachmentId) },
      include: {
        v_chat_messages: {
          include: {
            v_chat_rooms: {
              include: {
                v_chat_room_participants: {
                  where: { user_uuid: session.user.id }
                }
              }
            }
          }
        }
      }
    })

    if (!attachment) {
      return NextResponse.json({ error: 'File not found' }, { status: 404 })
    }

    // Check if user has access to the room
    if (attachment.v_chat_messages.v_chat_rooms.v_chat_room_participants.length === 0) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // Construct file path
    const filePath = path.join(UPLOAD_DIR, '..', attachment.file_path)

    // Check if file exists on disk
    if (!existsSync(filePath)) {
      return NextResponse.json({ error: 'File not found on disk' }, { status: 404 })
    }

    try {
      // Read file from disk
      const fileBuffer = await readFile(filePath)

      // Set appropriate headers
      const headers = new Headers()

      headers.set('Content-Type', attachment.content_type || 'application/octet-stream')
      headers.set('Content-Length', fileBuffer.length.toString())

      if (download) {
        headers.set('Content-Disposition', `attachment; filename="${attachment.filename}"`)
      } else {
        headers.set('Content-Disposition', `inline; filename="${attachment.filename}"`)
      }

      // Set cache headers for images and videos
      if (attachment.content_type?.startsWith('image/') || attachment.content_type?.startsWith('video/')) {
        headers.set('Cache-Control', 'public, max-age=31536000') // 1 year
        headers.set('ETag', `"${attachmentId}-${attachment.file_size}"`)
      }

      // Check if client has cached version
      const ifNoneMatch = request.headers.get('if-none-match')

      if (ifNoneMatch === `"${attachmentId}-${attachment.file_size}"`) {
        return new NextResponse(null, { status: 304, headers })
      }

      return new NextResponse(fileBuffer, { headers })
    } catch (fileError) {
      console.error('Error reading file:', fileError)

      return NextResponse.json({ error: 'Failed to read file' }, { status: 500 })
    }
  } catch (error) {
    console.error('Error downloading file:', error)

    return NextResponse.json({ error: 'Failed to download file' }, { status: 500 })
  }
}

// DELETE /api/internal-chat/files/[attachmentId]
export async function DELETE(_request: NextRequest, { params }: { params: { attachmentId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { attachmentId } = params

    // Get attachment with message and room info
    const attachment = await prisma.v_chat_message_attachments.findUnique({
      where: { attachment_id: BigInt(attachmentId) },
      include: {
        v_chat_messages: {
          include: {
            v_chat_rooms: {
              include: {
                v_chat_room_participants: {
                  where: {
                    user_uuid: session.user.id,
                    participant_role: { in: ['owner', 'admin', 'moderator'] }
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!attachment) {
      return NextResponse.json({ error: 'Attachment not found' }, { status: 404 })
    }

    // Check permissions - user must be message author or room moderator
    const canDelete =
      attachment.v_chat_messages.author_uuid === session.user.id ||
      attachment.v_chat_messages.v_chat_rooms.v_chat_room_participants.length > 0

    if (!canDelete) {
      return NextResponse.json(
        { error: 'Permission denied. You can only delete your own attachments or you must be a room moderator.' },
        { status: 403 }
      )
    }

    // Delete attachment record from database
    await prisma.v_chat_message_attachments.delete({
      where: { attachment_id: BigInt(attachmentId) }
    })

    // Optionally delete file from disk (be careful with this)
    // const filePath = path.join(UPLOAD_DIR, '..', attachment.file_path);
    // if (existsSync(filePath)) {
    //   await unlink(filePath);
    // }

    return NextResponse.json({
      success: true,
      message: 'Attachment deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting attachment:', error)

    return NextResponse.json({ error: 'Failed to delete attachment' }, { status: 500 })
  }
}
