// Internal Chat - Available Agents API
// GET /api/internal-chat/agents - Get available agents for assignment (generic endpoint for all platforms)

import { NextResponse } from 'next/server'

import { prisma } from '@/libs/db/prisma'
import getSession from '@/actions/getSession'

// GET /api/internal-chat/agents
export async function GET() {
  const session = await getSession()

  try {
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    // Get available agents for assignment
    const agents = await prisma.v_users.findMany({
      where: {
        domain_uuid: user.domain_uuid,
        user_enabled: 'true'

        // Add any additional filters for agents (e.g., specific roles)
      },
      select: {
        user_uuid: true,
        username: true,
        user_email: true,
        contact_uuid: true
      },
      orderBy: [
        { username: 'asc' }
      ]
    })

    // Transform to consistent format matching Facebook's pattern
    const transformedAgents = agents.map(agent => ({
      user_uuid: agent.user_uuid,
      username: agent.username || agent.user_uuid,
      display_name: agent.username || agent.user_uuid,
      first_name: null,
      last_name: null
    }))

    return NextResponse.json(
      {
        agents: transformedAgents
      },
      { status: 200 }
    )
  } catch (error) {
    console.error('Error fetching available agents:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
