// Internal Chat - Telegram Webhook Integration
// POST /api/internal-chat/telegram/webhook - Process Telegram webhook events

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { prisma } from '@/libs/db/prisma'
import type { TelegramWebhookEvent } from '@/types/apps/telegramTypes'
import { TelegramMessageService } from '@/services/telegram/telegramMessageService'

import { safeBigIntToString } from '@/utils/chat/messageUtils'

// Telegram webhook processing for internal chat integration
export async function POST(request: NextRequest) {
  try {
    const domain_uuid = request.nextUrl.searchParams.get('domain')

    if (!domain_uuid) {
      return NextResponse.json({ error: 'Domain UUID is required' }, { status: 400 })
    }

    const webhookEvent: TelegramWebhookEvent = await request.json()

    // Log received Telegram payload
    console.log('=== TELEGRAM WEBHOOK RECEIVED ===')
    console.log('Domain UUID:', domain_uuid)
    console.log('Webhook Event:', JSON.stringify(webhookEvent, null, 2))
    console.log('Event Type:', webhookEvent.message?.chat?.type || 'unknown')
    console.log('From User:', webhookEvent.message?.from?.id, webhookEvent.message?.from?.first_name)
    console.log('Message Text:', webhookEvent.message?.text || '[no text]')
    console.log('================================')

    // Skip if no message or if it's a group member addition event
    if (!webhookEvent.message || (webhookEvent as any).new_chat_participant) {
      console.log('Telegram webhook ignored: no message or group member addition event')

      return NextResponse.json({ status: 'ignored' }, { status: 200 })
    }

    const message = webhookEvent.message

    // Log webhook event for audit
    await prisma.v_telegram_webhook_event.create({
      data: {
        domain_uuid,
        telegram_userid: message.from.id.toString(),
        event_name: message.chat.type,
        msg: webhookEvent as any,
        insert_date: new Date(),
        update_date: new Date()
      }
    })

    // Process the inbound message using service
    const result = await TelegramMessageService.processInboundMessage(domain_uuid, message)

    // Note: Agent assignment is now handled manually through the admin interface
    // using the participant-based approach

    // Broadcast message using the main chat system's broadcasting mechanism
    try {
      if ((global as any).socketBroadcast && (global as any).socketBroadcast.broadcastMessage) {
        // Get the full message data for socket emission
        const fullMessage = await prisma.v_chat_messages.findUnique({
          where: { message_id: BigInt(result.internal_message_id) }

          // Note: v_users relationship removed because external platform messages don't have internal users
        })

        if (fullMessage) {
          // Get room data including domain
          const roomData = await prisma.v_chat_rooms.findUnique({
            where: { room_uuid: result.room_uuid },
            select: {
              domain_uuid: true,
              room_type: true,
              room_name: true
            }
          })

          if (roomData?.domain_uuid) {
            // Format message for broadcasting (same format as internal chat)
            const formattedMessage = {
              message_id: safeBigIntToString(fullMessage.message_id),
              room_uuid: result.room_uuid,
              author_uuid: fullMessage.author_uuid,
              content: fullMessage.content,
              message_type: fullMessage.message_type,
              created_at:
                typeof fullMessage.created_at === 'bigint' ? Number(fullMessage.created_at) : fullMessage.created_at,
              flags: fullMessage.flags,
              author_name:
                [message.from.first_name, message.from.last_name].filter(Boolean).join(' ') ||
                message.from.username ||
                `Telegram User ${message.from.id}`,
              platform: 'telegram'
            }

            // Get updated unread counts for all participants
            const unreadCounts = await prisma.v_chat_unread_counts.findMany({
              where: {
                room_uuid: result.room_uuid
              },
              select: {
                user_uuid: true,
                unread_count: true
              }
            })

            // Create a map of user_uuid -> unread_count
            const unreadCountMap = unreadCounts.reduce(
              (acc: Record<string, number>, count: any) => {
                acc[count.user_uuid] = count.unread_count || 0

                return acc
              },
              {} as Record<string, number>
            )

            // Add unread count to the message
            const messageWithUnreadCount = {
              ...formattedMessage,
              unread_counts: unreadCountMap
            }

            // Use the main chat system's broadcasting mechanism
            ;(global as any).socketBroadcast.broadcastMessage(
              result.room_uuid,
              messageWithUnreadCount,
              roomData.domain_uuid
            )

            // Check if conversation is unassigned and create notifications
            const { unifiedNotificationService } = await import('@/services/notifications/unifiedNotificationService')

            // Check if room has assigned agents
            const assignedAgents = await prisma.v_chat_room_participants.findMany({
              where: {
                room_uuid: result.room_uuid,
                deleted_at: null,
                participant_role: { in: ['admin', 'moderator'] } // Agents typically have these roles
              }
            })

            if (assignedAgents.length === 0) {
              // No agents assigned, create unassigned conversation notification
              await unifiedNotificationService.createUnassignedConversationNotification(
                roomData.domain_uuid,
                result.room_uuid,
                {
                  message_id: result.internal_message_id.toString(),
                  content: fullMessage.content || 'Telegram message',
                  author_name: 'Telegram User',
                  platform: 'telegram'
                }
              )
            }
          }
        }
      }
    } catch (socketError) {
      console.error('Failed to emit socket event:', socketError)

      // Don't fail the webhook if socket emission fails
    }

    console.log('=== TELEGRAM WEBHOOK SUCCESS ===')
    console.log('Processing completed successfully')
    console.log('Internal Message ID:', result.internal_message_id)
    console.log('Room UUID:', result.room_uuid)
    console.log('===============================')

    return NextResponse.json(
      {
        status: 'processed',
        room_uuid: result.room_uuid,
        message_id: result.internal_message_id
      },
      { status: 200 }
    )
  } catch (error) {
    console.error('=== TELEGRAM WEBHOOK ERROR ===')
    console.error('Error processing Telegram webhook:', error)
    console.error('Domain UUID:', request.nextUrl.searchParams.get('domain'))
    console.error('Error details:', {
      name: (error as Error)?.name,
      message: (error as Error)?.message,
      stack: (error as Error)?.stack
    })
    console.error('=============================')

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
