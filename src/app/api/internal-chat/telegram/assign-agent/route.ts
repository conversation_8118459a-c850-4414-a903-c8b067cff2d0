// Telegram Agent Assignment API
// Handles agent assignment and unassignment for Telegram conversations

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'
import { TelegramIntegrationService } from '@/services/telegram/telegramIntegrationService'

// POST /api/internal-chat/telegram/assign-agent
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true, username: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    // Parse request body
    const body = await request.json()
    const { telegram_room_uuid, agent_uuid } = body

    // Validate required fields
    if (!telegram_room_uuid || !agent_uuid) {
      return NextResponse.json(
        {
          error: 'telegram_room_uuid and agent_uuid are required'
        },
        { status: 400 }
      )
    }

    // Verify the agent exists and belongs to the same domain
    const agent = await prisma.v_users.findFirst({
      where: {
        user_uuid: agent_uuid,
        domain_uuid: user.domain_uuid,
        user_enabled: 'true'
      },
      select: {
        user_uuid: true,
        username: true,
        user_email: true,
        contact_uuid: true
      }
    })

    if (!agent) {
      return NextResponse.json({ error: 'Agent not found or access denied' }, { status: 404 })
    }

    // Verify Telegram room exists and belongs to domain
    const telegramRoom = await prisma.v_telegram_chat_rooms.findFirst({
      where: {
        telegram_room_uuid,
        domain_uuid: user.domain_uuid
      },
      include: {
        v_telegram_contacts: true,
        v_chat_rooms: true
      }
    })

    if (!telegramRoom) {
      return NextResponse.json({ error: 'Telegram room not found or access denied' }, { status: 404 })
    }

    // Use the enhanced assignment service
    const result = await TelegramIntegrationService.assignAgent(
      telegram_room_uuid,
      agent_uuid,
      session.user!.id
    )

    if (!result.success) {
      return NextResponse.json(
        {
          error: result.message
        },
        { status: 400 }
      )
    }

    // Create unified assignment notification
    try {
      const { unifiedNotificationService } = await import('@/services/notifications/unifiedNotificationService')

      await unifiedNotificationService.createAgentAssignmentNotification(
        user.domain_uuid,
        telegramRoom.internal_room_uuid,
        agent.user_uuid,
        {
          assigner_name: session.user.name || 'System',
          contact_name: result.contact_name || 'Telegram User',
          platform: 'telegram'
        }
      )

      console.log(`Created unified assignment notification for agent ${agent.user_uuid}`)
    } catch (notificationError) {
      console.warn('Failed to create assignment notification:', notificationError)

      // Don't fail the request if notification fails
    }

    return NextResponse.json({
      success: true,
      assignment: result,
      message: result.message
    })
  } catch (error) {
    console.error('Error assigning agent to Telegram room:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// DELETE /api/internal-chat/telegram/assign-agent
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    const { searchParams } = request.nextUrl
    const room_uuid = searchParams.get('room_uuid')

    if (!room_uuid) {
      return NextResponse.json({ error: 'room_uuid is required' }, { status: 400 })
    }

    // Verify this is a Telegram room
    const telegramRoom = await prisma.v_telegram_chat_rooms.findFirst({
      where: {
        domain_uuid: user.domain_uuid,
        internal_room_uuid: room_uuid
      }
    })

    if (!telegramRoom) {
      return NextResponse.json({ error: 'Telegram room not found' }, { status: 404 })
    }

    // Remove assignment
    await prisma.v_telegram_chat_rooms.update({
      where: { telegram_room_uuid: telegramRoom.telegram_room_uuid },
      data: {
        assigned_agent_uuid: null,
        update_date: new Date(),
        update_user: session.user.id
      }
    })

    // Create unassignment notification message
    const unassignmentMessage = 'Agent has been unassigned from this Telegram conversation.'

    await prisma.v_chat_messages.create({
      data: {
        room_uuid,
        author_uuid: null, // System message
        content: unassignmentMessage,
        message_type: 3, // System message type
        created_at: Math.floor(Date.now() / 1000),
        flags: 1 // System flag
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Agent unassigned from Telegram conversation'
    })
  } catch (error) {
    console.error('Error unassigning agent from Telegram room:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
