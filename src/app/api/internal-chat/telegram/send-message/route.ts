// Internal Chat - Telegram Send Message API
// POST /api/internal-chat/telegram/send-message - Send message to Telegram user

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'
import { MessageType } from '@/types/apps/internal-chat/chatTypes'
import { TelegramMessageService } from '@/services/telegram/telegramMessageService'

interface SendTelegramMessageRequest {
  room_uuid: string
  content: string
  message_type?: MessageType
  reply_to?: string
}

// In-memory cache to prevent duplicate requests (simple deduplication)
const requestCache = new Map<string, Promise<any>>()

// Clean up old cache entries every 5 minutes
setInterval(() => {
  requestCache.clear()
}, 5 * 60 * 1000)

// POST /api/internal-chat/telegram/send-message
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user!.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    const body: SendTelegramMessageRequest = await request.json()
    const { room_uuid, content, message_type = MessageType.TEXT } = body

    // Validate required fields
    if (!room_uuid || !content?.trim()) {
      return NextResponse.json(
        {
          error: 'room_uuid and content are required'
        },
        { status: 400 }
      )
    }

    if (content.length > 4000) {
      return NextResponse.json(
        {
          error: 'Message too long (max 4000 characters)'
        },
        { status: 400 }
      )
    }

    // Create a unique request key to prevent duplicate processing
    const requestKey = `${session.user.id}-${room_uuid}-${content.trim()}-${Date.now()}`
    const shortKey = `${session.user.id}-${room_uuid}-${content.substring(0, 50)}`

    // Check if this exact request is already being processed
    if (requestCache.has(shortKey)) {
      console.log('Duplicate request detected, returning cached result')
      const cachedResult = await requestCache.get(shortKey)

      return NextResponse.json(cachedResult, { status: 201 })
    }

    // Cache the processing promise to prevent duplicate requests
    const processingPromise = (async () => {
      console.log('Processing Telegram message send request:', requestKey)

      // Use service to process outbound message
      const result = await TelegramMessageService.processOutboundMessage(
        user.domain_uuid!,
        room_uuid,
        session.user!.id,
        content.trim(),
        message_type
      )

      // Prepare response
      const response = {
        message_id: result.internal_message_id,
        room_uuid,
        content: content.trim(),
        message_type,
        author_uuid: session.user!.id,
        delivery_status: result.delivery_status,
        telegram_message_id: result.telegram_message_id,
        created_at: Math.floor(Date.now() / 1000)
      }

      console.log('Telegram message processing completed:', {
        message_id: result.internal_message_id,
        delivery_status: result.delivery_status
      })

      return response
    })()

    // Cache the promise
    requestCache.set(shortKey, processingPromise)

    // Clean up cache after processing
    processingPromise.finally(() => {
      setTimeout(() => {
        requestCache.delete(shortKey)
      }, 1000) // Keep in cache for 1 second to handle immediate duplicates
    })

    const response = await processingPromise

    // Return appropriate status based on delivery
    if (response.delivery_status === 'failed') {
      return NextResponse.json(
        {
          ...response,
          warning: 'Message saved to internal chat but failed to send to Telegram'
        },
        { status: 207 }
      ) // 207 Multi-Status
    }

    return NextResponse.json(response, { status: 201 })
  } catch (error) {
    console.error('Error sending Telegram message:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
