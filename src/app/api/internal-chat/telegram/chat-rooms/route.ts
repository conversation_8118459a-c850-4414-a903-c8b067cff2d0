// Internal Chat - Telegram Chat Rooms API
// GET /api/internal-chat/telegram/chat-rooms - Get Telegram chat rooms for domain
// POST /api/internal-chat/telegram/chat-rooms - Create Telegram chat room bridge

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'
import { RoomType, ParticipantRole } from '@/types/apps/internal-chat/chatTypes'

// GET /api/internal-chat/telegram/chat-rooms
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user!.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    const searchParams = request.nextUrl.searchParams
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')
    const status = searchParams.get('status') || 'active'

    // Get Telegram chat rooms with related data
    const telegramRooms = await prisma.v_telegram_chat_rooms.findMany({
      where: {
        domain_uuid: user.domain_uuid,
        room_status: status
      },
      include: {
        v_chat_rooms: {
          select: {
            room_uuid: true,
            room_name: true,
            room_description: true,
            room_type: true,
            room_avatar: true,
            is_active: true,
            is_archived: true,
            room_settings: true,
            insert_date: true,
            update_date: true
          }
        },
        v_telegram_contacts: {
          select: {
            contact_uuid: true,
            telegram_user_id: true,
            telegram_chat_id: true,
            username: true,
            first_name: true,
            last_name: true,
            phone: true,
            language_code: true,
            contact_info: true,
            last_interaction_date: true
          }
        }
      },
      orderBy: { last_message_at: 'desc' },
      take: limit,
      skip: offset
    })

    // Get agent information for assigned rooms
    const assignedAgentUuids = telegramRooms
      .map(room => room.assigned_agent_uuid)
      .filter(Boolean) as string[]

    const agents = assignedAgentUuids.length > 0 ? await prisma.v_users.findMany({
      where: {
        user_uuid: { in: assignedAgentUuids }
      },
      select: {
        user_uuid: true,
        username: true,
        user_email: true,
        contact_uuid: true
      }
    }) : []

    // Create agent map for quick lookup
    const agentMap = new Map(agents.map(agent => [
      agent.user_uuid,
      {
        user_uuid: agent.user_uuid,
        username: agent.username,
        display_name: agent.username || agent.user_uuid,
        first_name: null,
        last_name: null
      }
    ]))

    // Transform rooms for frontend
    const transformedRooms = telegramRooms.map(telegramRoom => {
      const contact = telegramRoom.v_telegram_contacts
      const internalRoom = telegramRoom.v_chat_rooms

      return {
        telegram_room_uuid: telegramRoom.telegram_room_uuid,
        internal_room_uuid: telegramRoom.internal_room_uuid,
        assigned_agent_uuid: telegramRoom.assigned_agent_uuid,
        assigned_agent: telegramRoom.assigned_agent_uuid ? agentMap.get(telegramRoom.assigned_agent_uuid) || null : null,
        room_status: telegramRoom.room_status,
        last_message_at: telegramRoom.last_message_at,
        conversation_metadata: telegramRoom.conversation_metadata,

        // Internal room data
        room: internalRoom
          ? {
              room_uuid: internalRoom.room_uuid,
              room_name: internalRoom.room_name,
              room_description: internalRoom.room_description,
              room_type: internalRoom.room_type,
              room_avatar: internalRoom.room_avatar,
              is_active: internalRoom.is_active,
              is_archived: internalRoom.is_archived,
              room_settings: internalRoom.room_settings,
              insert_date: internalRoom.insert_date,
              update_date: internalRoom.update_date
            }
          : null,

        // Contact data
        contact: contact
          ? {
              contact_uuid: contact.contact_uuid,
              telegram_user_id: contact.telegram_user_id.toString(),
              telegram_chat_id: contact.telegram_chat_id.toString(),
              username: contact.username,
              first_name: contact.first_name,
              last_name: contact.last_name,
              display_name:
                [contact.first_name, contact.last_name].filter(Boolean).join(' ') ||
                contact.username ||
                `User ${contact.telegram_user_id}`,
              phone: contact.phone,
              language_code: contact.language_code,
              contact_info: contact.contact_info,
              last_interaction_date: contact.last_interaction_date
            }
          : null
      }
    })

    // Get total count for pagination
    const totalCount = await prisma.v_telegram_chat_rooms.count({
      where: {
        domain_uuid: user.domain_uuid,
        room_status: status
      }
    })

    return NextResponse.json({
      chat_rooms: transformedRooms,
      pagination: {
        total: totalCount,
        limit,
        offset,
        has_more: offset + limit < totalCount
      }
    })
  } catch (error) {
    console.error('Error fetching Telegram chat rooms:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// POST /api/internal-chat/telegram/chat-rooms
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user!.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    const body = await request.json()

    const { telegram_contact_uuid, assigned_agent_uuid, room_name, room_description, conversation_metadata = {} } = body

    // Validate required fields
    if (!telegram_contact_uuid) {
      return NextResponse.json(
        {
          error: 'telegram_contact_uuid is required'
        },
        { status: 400 }
      )
    }

    // Verify contact exists and belongs to domain
    const contact = await prisma.v_telegram_contacts.findFirst({
      where: {
        contact_uuid: telegram_contact_uuid,
        domain_uuid: user.domain_uuid
      }
    })

    if (!contact) {
      return NextResponse.json(
        {
          error: 'Telegram contact not found or access denied'
        },
        { status: 404 }
      )
    }

    // Check if chat room already exists for this contact
    const existingRoom = await prisma.v_telegram_chat_rooms.findFirst({
      where: {
        domain_uuid: user.domain_uuid,
        telegram_contact_uuid
      }
    })

    if (existingRoom) {
      return NextResponse.json(
        {
          error: 'Chat room already exists for this contact',
          existing_room_uuid: existingRoom.telegram_room_uuid
        },
        { status: 409 }
      )
    }

    // Create internal chat room with participants in transaction
    const displayName =
      [contact.first_name, contact.last_name].filter(Boolean).join(' ') ||
      contact.username ||
      `Telegram User ${contact.telegram_user_id}`

    const result = await prisma.$transaction(async tx => {
      // Create the internal room
      const internalRoom = await tx.v_chat_rooms.create({
        data: {
          domain_uuid: user.domain_uuid!,
          room_name: room_name || `Telegram: ${displayName}`,
          room_description: room_description || `Telegram conversation with ${displayName}`,
          room_type: RoomType.DIRECT,
          created_by_user_uuid: session.user!.id,
          room_settings: {
            platform: 'telegram',
            telegram_user_id: contact.telegram_user_id.toString(),
            telegram_chat_id: contact.telegram_chat_id.toString(),
            auto_created: false
          },
          insert_date: new Date(),
          insert_user: session.user!.id,
          update_date: new Date(),
          update_user: session.user!.id
        }
      })

      // Create Telegram room bridge
      const telegramRoom = await tx.v_telegram_chat_rooms.create({
        data: {
          domain_uuid: user.domain_uuid!,
          internal_room_uuid: internalRoom.room_uuid,
          telegram_contact_uuid,
          assigned_agent_uuid,
          room_status: 'active',
          conversation_metadata: {
            ...conversation_metadata,
            telegram_chat_type: (contact.contact_info as any)?.chat_type || 'private',
            contact_display_name: displayName,
            created_by: session.user!.id
          },
          insert_date: new Date(),
          insert_user: session.user!.id,
          update_date: new Date(),
          update_user: session.user!.id
        }
      })

      // Create participant for room creator (always)
      await tx.v_chat_room_participants.create({
        data: {
          room_uuid: internalRoom.room_uuid,
          user_uuid: session.user!.id,
          participant_role: ParticipantRole.OWNER,
          insert_user: session.user!.id,
          update_user: session.user!.id,
          notification_settings: {
            mentions: true,
            all_messages: true,
            member_additions: true,
            member_removals: true,
            role_changes: true,
            group_info_changes: true
          }
        }
      })

      // Create participant for assigned agent if specified and different from creator
      if (assigned_agent_uuid && assigned_agent_uuid !== session.user!.id) {
        await tx.v_chat_room_participants.create({
          data: {
            room_uuid: internalRoom.room_uuid,
            user_uuid: assigned_agent_uuid,
            participant_role: ParticipantRole.MEMBER,
            insert_user: session.user!.id,
            update_user: session.user!.id,
            notification_settings: {
              mentions: true,
              all_messages: true,
              member_additions: true,
              member_removals: true,
              role_changes: true,
              group_info_changes: true
            }
          }
        })
      }

      return { internalRoom, telegramRoom }
    })

    return NextResponse.json(
      {
        telegram_room_uuid: result.telegramRoom.telegram_room_uuid,
        internal_room_uuid: result.internalRoom.room_uuid,
        room_status: result.telegramRoom.room_status,
        contact_display_name: displayName
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('Error creating Telegram chat room:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
