// Internal Chat - Telegram Integration Statistics API
// GET /api/internal-chat/telegram/stats - Get Telegram integration statistics

import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { TelegramIntegrationService } from '@/services/telegram/telegramIntegrationService'
import { prisma } from '@/libs/db/prisma'

// GET /api/internal-chat/telegram/stats
export async function GET() {
  const session = await getServerSession(authOptions)

  try {
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user!.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    // Check if bot configuration exists and is properly configured
    const botConfig = await prisma.v_telegram_bot_config.findFirst({
      where: {
        domain_uuid: user.domain_uuid
      }
    })

    if (!botConfig) {
      return NextResponse.json(
        {
          error: 'Telegram bot not configured',
          configured: false,
          message: 'Please configure your Telegram bot first'
        },
        { status: 404 }
      )
    }

    if (!botConfig.bot_token || botConfig.bot_token.trim().length === 0) {
      return NextResponse.json(
        {
          error: 'Bot token is required',
          configured: false,
          message: 'Please provide a valid bot token in the configuration'
        },
        { status: 400 }
      )
    }

    if (!botConfig.is_active) {
      return NextResponse.json(
        {
          error: 'Telegram bot is not active',
          configured: true,
          message: 'Please activate your Telegram bot configuration'
        },
        { status: 400 }
      )
    }

    // Get Telegram integration statistics
    const stats = await TelegramIntegrationService.getTelegramStats(user.domain_uuid)

    return NextResponse.json({
      stats,
      configured: true,
      bot_active: true
    })
  } catch (error) {
    console.error('Error fetching Telegram statistics:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
