// Internal Chat - Telegram Bot Configuration API
// GET /api/internal-chat/telegram/bot-config - Get bot configuration for domain
// POST /api/internal-chat/telegram/bot-config - Create or update bot configuration
// PUT /api/internal-chat/telegram/bot-config - Update bot configuration
// DELETE /api/internal-chat/telegram/bot-config - Deactivate bot configuration

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'

interface BotConfigRequest {
  bot_token: string
  bot_username?: string
  webhook_url?: string
  webhook_secret?: string
  allowed_updates?: string[]
  bot_settings?: Record<string, any>
}

/**
 * Set Telegram webhook using bot token
 */
async function setTelegramWebhook(
  botToken: string,
  webhookUrl: string,
  allowedUpdates: string[] = ['message', 'edited_message', 'callback_query']
): Promise<{ success: boolean; error?: string }> {
  try {
    const telegramApiUrl = `https://api.telegram.org/bot${botToken}/setWebhook`

    const response = await fetch(telegramApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        url: webhookUrl,
        allowed_updates: allowedUpdates,
        drop_pending_updates: true // Clear any pending updates
      })
    })

    const result = await response.json()

    if (!response.ok || !result.ok) {
      console.error('Telegram setWebhook failed:', result)

      return {
        success: false,
        error: result.description || 'Failed to set webhook with Telegram'
      }
    }

    console.log('Telegram webhook set successfully:', result.description)

    return { success: true }
  } catch (error) {
    console.error('Error setting Telegram webhook:', error)

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error setting webhook'
    }
  }
}

/**
 * Remove Telegram webhook
 */
async function removeTelegramWebhook(botToken: string): Promise<{ success: boolean; error?: string }> {
  try {
    const telegramApiUrl = `https://api.telegram.org/bot${botToken}/deleteWebhook`

    const response = await fetch(telegramApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        drop_pending_updates: true
      })
    })

    const result = await response.json()

    if (!response.ok || !result.ok) {
      console.error('Telegram deleteWebhook failed:', result)

      return {
        success: false,
        error: result.description || 'Failed to remove webhook from Telegram'
      }
    }

    console.log('Telegram webhook removed successfully:', result.description)

    return { success: true }
  } catch (error) {
    console.error('Error removing Telegram webhook:', error)

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error removing webhook'
    }
  }
}

// GET /api/internal-chat/telegram/bot-config
export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user!.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    // Get bot configuration for domain
    const botConfig = await prisma.v_telegram_bot_config.findFirst({
      where: {
        domain_uuid: user.domain_uuid
      }
    })

    if (!botConfig) {
      return NextResponse.json(
        {
          error: 'Bot configuration not found',
          configured: false
        },
        { status: 404 }
      )
    }

    // Return configuration (excluding sensitive data)
    const safeConfig = {
      config_uuid: botConfig.config_uuid,
      domain_uuid: botConfig.domain_uuid,
      bot_username: botConfig.bot_username,
      webhook_url: botConfig.webhook_url,
      is_active: botConfig.is_active,
      allowed_updates: botConfig.allowed_updates,
      bot_settings: botConfig.bot_settings,
      insert_date: botConfig.insert_date,
      update_date: botConfig.update_date,
      configured: true,

      // Mask bot token for security
      bot_token_masked: botConfig.bot_token
        ? `${botConfig.bot_token.substring(0, 10)}...${botConfig.bot_token.slice(-4)}`
        : null
    }

    return NextResponse.json({ bot_config: safeConfig })
  } catch (error) {
    console.error('Error fetching bot configuration:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// POST /api/internal-chat/telegram/bot-config
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user!.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    const body: BotConfigRequest = await request.json()

    const {
      bot_token,
      bot_username,
      webhook_url,
      webhook_secret,
      allowed_updates = ['message', 'edited_message', 'callback_query'],
      bot_settings = {}
    } = body

    // Check if configuration already exists first
    const existingConfig = await prisma.v_telegram_bot_config.findFirst({
      where: {
        domain_uuid: user.domain_uuid
      }
    })

    // Validate bot token - only required for new configurations
    if (!existingConfig && (!bot_token || bot_token.trim().length === 0)) {
      return NextResponse.json(
        {
          error: 'bot_token is required for new configuration'
        },
        { status: 400 }
      )
    }

    // Validate bot token format if provided
    if (bot_token && bot_token.trim() && !bot_token.match(/^\d+:[A-Za-z0-9_-]+$/)) {
      return NextResponse.json(
        {
          error: 'Invalid bot token format'
        },
        { status: 400 }
      )
    }

    let botConfig
    let webhookResult: { success: boolean; error?: string } | null = null

    // Prepare update data - only include bot_token if provided
    const updateData: any = {
      bot_username: bot_username?.trim() || null,
      webhook_url: webhook_url?.trim() || null,
      webhook_secret: webhook_secret?.trim() || null,
      is_active: true,
      allowed_updates,
      bot_settings,
      update_date: new Date(),
      update_user: session.user!.id
    }

    // Only update token if provided (allows updating other fields without re-entering token)
    const tokenToUse = bot_token?.trim() || existingConfig?.bot_token

    if (bot_token?.trim()) {
      updateData.bot_token = bot_token.trim()
    }

    if (existingConfig) {
      // Update existing configuration
      botConfig = await prisma.v_telegram_bot_config.update({
        where: { config_uuid: existingConfig.config_uuid },
        data: updateData
      })
    } else {
      // Create new configuration
      botConfig = await prisma.v_telegram_bot_config.create({
        data: {
          domain_uuid: user.domain_uuid,
          bot_token: bot_token.trim(),
          bot_username: bot_username?.trim() || null,
          webhook_url: webhook_url?.trim() || null,
          webhook_secret: webhook_secret?.trim() || null,
          is_active: true,
          allowed_updates,
          bot_settings,
          insert_date: new Date(),
          insert_user: session.user!.id,
          update_date: new Date(),
          update_user: session.user!.id
        }
      })
    }

    // Automatically set webhook with Telegram if webhook URL is provided
    if (botConfig.webhook_url && tokenToUse) {
      webhookResult = await setTelegramWebhook(tokenToUse, botConfig.webhook_url, botConfig.allowed_updates as string[])

      // Log webhook setup result but don't fail the configuration save
      if (!webhookResult.success) {
        console.warn('Failed to set Telegram webhook:', webhookResult.error)
      }
    } else if (!botConfig.webhook_url && tokenToUse) {
      // Remove webhook if no URL is provided
      webhookResult = await removeTelegramWebhook(tokenToUse)

      if (!webhookResult.success) {
        console.warn('Failed to remove Telegram webhook:', webhookResult.error)
      }
    }

    // Return safe configuration
    const safeConfig = {
      config_uuid: botConfig.config_uuid,
      domain_uuid: botConfig.domain_uuid,
      bot_username: botConfig.bot_username,
      webhook_url: botConfig.webhook_url,
      is_active: botConfig.is_active,
      allowed_updates: botConfig.allowed_updates,
      bot_settings: botConfig.bot_settings,
      bot_token_masked: `${botConfig.bot_token.substring(0, 10)}...${botConfig.bot_token.slice(-4)}`,
      created: !existingConfig
    }

    // Prepare response message
    let message = existingConfig ? 'Bot configuration updated' : 'Bot configuration created'
    const warnings: string[] = []

    // Add webhook status to response
    if (webhookResult) {
      if (webhookResult.success) {
        if (botConfig.webhook_url) {
          message += ' and webhook set with Telegram'
        } else {
          message += ' and webhook removed from Telegram'
        }
      } else {
        warnings.push(`Webhook setup failed: ${webhookResult.error}`)
      }
    }

    const response: any = {
      bot_config: safeConfig,
      message
    }

    if (warnings.length > 0) {
      response.warnings = warnings
    }

    return NextResponse.json(response, { status: existingConfig ? 200 : 201 })
  } catch (error) {
    console.error('Error creating/updating bot configuration:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// PUT /api/internal-chat/telegram/bot-config
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user!.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    const body = await request.json()
    const { is_active, bot_settings, allowed_updates } = body

    // Find existing configuration
    const existingConfig = await prisma.v_telegram_bot_config.findFirst({
      where: {
        domain_uuid: user.domain_uuid
      }
    })

    if (!existingConfig) {
      return NextResponse.json(
        {
          error: 'Bot configuration not found'
        },
        { status: 404 }
      )
    }

    // Update configuration
    const updatedConfig = await prisma.v_telegram_bot_config.update({
      where: { config_uuid: existingConfig.config_uuid },
      data: {
        is_active: is_active !== undefined ? is_active : existingConfig.is_active,
        bot_settings: bot_settings || existingConfig.bot_settings,
        allowed_updates: allowed_updates || existingConfig.allowed_updates,
        update_date: new Date(),
        update_user: session.user!.id
      }
    })

    return NextResponse.json({
      message: 'Bot configuration updated',
      is_active: updatedConfig.is_active
    })
  } catch (error) {
    console.error('Error updating bot configuration:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// DELETE /api/internal-chat/telegram/bot-config
export async function DELETE() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user!.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    // Find and deactivate configuration
    const existingConfig = await prisma.v_telegram_bot_config.findFirst({
      where: {
        domain_uuid: user.domain_uuid
      }
    })

    if (!existingConfig) {
      return NextResponse.json(
        {
          error: 'Bot configuration not found'
        },
        { status: 404 }
      )
    }

    // Deactivate instead of deleting
    await prisma.v_telegram_bot_config.update({
      where: { config_uuid: existingConfig.config_uuid },
      data: {
        is_active: false,
        update_date: new Date(),
        update_user: session.user!.id
      }
    })

    return NextResponse.json({
      message: 'Bot configuration deactivated'
    })
  } catch (error) {
    console.error('Error deactivating bot configuration:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
