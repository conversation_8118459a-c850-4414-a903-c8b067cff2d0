// Internal Chat - Telegram Available Agents API
// GET /api/internal-chat/telegram/available-agents - Get available agents for assignment

import { NextResponse } from 'next/server'

import { TelegramAssignmentService } from '@/services/telegram/telegramAssignmentService'
import getSession from '@/actions/getSession'

// GET /api/internal-chat/telegram/available-agents
export async function GET() {
  const session = await getSession()

  try {
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const domain_uuid = session?.user?.domain ?? ''

    if (!domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    // Get available agents for assignment
    const agents = await TelegramAssignmentService.getAvailableAgents(domain_uuid)

    return NextResponse.json(
      {
        agents
      },
      { status: 200 }
    )
  } catch (error) {
    console.error('Error fetching available agents:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
