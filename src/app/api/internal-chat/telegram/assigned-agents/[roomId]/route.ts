// Internal Chat - Telegram Assigned Agents API
// GET /api/internal-chat/telegram/assigned-agents/[roomId] - Get assigned agents for a Telegram room

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { TelegramAssignmentService } from '@/services/telegram/telegramAssignmentService'

// GET /api/internal-chat/telegram/assigned-agents/[roomId]
export async function GET(request: NextRequest, { params }: { params: { roomId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { roomId } = params

    // Get assigned agents for the room
    const agents = await TelegramAssignmentService.getAssignedAgents(roomId)

    return NextResponse.json(
      {
        agents
      },
      { status: 200 }
    )
  } catch (error) {
    console.error('Error fetching assigned agents:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
