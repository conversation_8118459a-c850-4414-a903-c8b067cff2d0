// Internal Chat - Telegram Contacts API
// GET /api/internal-chat/telegram/contacts - Get Telegram contacts for domain
// POST /api/internal-chat/telegram/contacts - Create or update Telegram contact

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'

// GET /api/internal-chat/telegram/contacts
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user!.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    const searchParams = request.nextUrl.searchParams
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')
    const search = searchParams.get('search') || ''

    // Build where clause for search
    const whereClause: any = {
      domain_uuid: user.domain_uuid
    }

    if (search) {
      whereClause.OR = [
        { username: { contains: search, mode: 'insensitive' } },
        { first_name: { contains: search, mode: 'insensitive' } },
        { last_name: { contains: search, mode: 'insensitive' } }
      ]
    }

    // Get contacts with their associated chat rooms
    const contacts = await prisma.v_telegram_contacts.findMany({
      where: whereClause,
      include: {
        v_telegram_chat_rooms: {
          include: {
            v_chat_rooms: {
              select: {
                room_uuid: true,
                room_name: true,
                room_type: true,
                is_active: true
              }
            }
          }
        }
      },
      orderBy: { last_interaction_date: 'desc' },
      take: limit,
      skip: offset
    })

    // Transform contacts for frontend
    const transformedContacts = contacts.map(contact => ({
      contact_uuid: contact.contact_uuid,
      telegram_user_id: contact.telegram_user_id.toString(),
      telegram_chat_id: contact.telegram_chat_id.toString(),
      username: contact.username,
      first_name: contact.first_name,
      last_name: contact.last_name,
      display_name:
        [contact.first_name, contact.last_name].filter(Boolean).join(' ') ||
        contact.username ||
        `User ${contact.telegram_user_id}`,
      phone: contact.phone,
      is_bot: contact.is_bot,
      language_code: contact.language_code,
      contact_info: contact.contact_info,
      last_interaction_date: contact.last_interaction_date,
      chat_room: contact.v_telegram_chat_rooms?.[0]?.v_chat_rooms || null,
      room_status: contact.v_telegram_chat_rooms?.[0]?.room_status || null
    }))

    // Get total count for pagination
    const totalCount = await prisma.v_telegram_contacts.count({
      where: whereClause
    })

    return NextResponse.json({
      contacts: transformedContacts,
      pagination: {
        total: totalCount,
        limit,
        offset,
        has_more: offset + limit < totalCount
      }
    })
  } catch (error) {
    console.error('Error fetching Telegram contacts:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// POST /api/internal-chat/telegram/contacts
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user!.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    const body = await request.json()

    const {
      telegram_user_id,
      telegram_chat_id,
      username,
      first_name,
      last_name,
      phone,
      language_code,
      contact_info = {}
    } = body

    // Validate required fields
    if (!telegram_user_id || !telegram_chat_id) {
      return NextResponse.json(
        {
          error: 'telegram_user_id and telegram_chat_id are required'
        },
        { status: 400 }
      )
    }

    // Check if contact already exists
    const existingContact = await prisma.v_telegram_contacts.findFirst({
      where: {
        domain_uuid: user.domain_uuid,
        telegram_user_id: BigInt(telegram_user_id),
        telegram_chat_id: BigInt(telegram_chat_id)
      }
    })

    let contact

    if (existingContact) {
      // Update existing contact
      contact = await prisma.v_telegram_contacts.update({
        where: { contact_uuid: existingContact.contact_uuid },
        data: {
          username: username || existingContact.username,
          first_name: first_name || existingContact.first_name,
          last_name: last_name || existingContact.last_name,
          phone: phone || existingContact.phone,
          language_code: language_code || existingContact.language_code,
          contact_info: { ...(existingContact.contact_info as any), ...contact_info },
          last_interaction_date: new Date(),
          update_date: new Date(),
          update_user: session.user!.id
        }
      })
    } else {
      // Create new contact
      contact = await prisma.v_telegram_contacts.create({
        data: {
          domain_uuid: user.domain_uuid,
          telegram_user_id: BigInt(telegram_user_id),
          telegram_chat_id: BigInt(telegram_chat_id),
          username,
          first_name,
          last_name,
          phone,
          language_code,
          contact_info,
          last_interaction_date: new Date(),
          insert_date: new Date(),
          insert_user: session.user!.id,
          update_date: new Date(),
          update_user: session.user!.id
        }
      })
    }

    // Transform contact for response
    const transformedContact = {
      contact_uuid: contact.contact_uuid,
      telegram_user_id: contact.telegram_user_id.toString(),
      telegram_chat_id: contact.telegram_chat_id.toString(),
      username: contact.username,
      first_name: contact.first_name,
      last_name: contact.last_name,
      display_name:
        [contact.first_name, contact.last_name].filter(Boolean).join(' ') ||
        contact.username ||
        `User ${contact.telegram_user_id}`,
      phone: contact.phone,
      is_bot: contact.is_bot,
      language_code: contact.language_code,
      contact_info: contact.contact_info,
      last_interaction_date: contact.last_interaction_date
    }

    return NextResponse.json(
      {
        contact: transformedContact,
        created: !existingContact
      },
      { status: existingContact ? 200 : 201 }
    )
  } catch (error) {
    console.error('Error creating/updating Telegram contact:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
