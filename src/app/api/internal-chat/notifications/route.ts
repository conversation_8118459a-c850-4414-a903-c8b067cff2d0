// Internal Chat - Notifications API
// GET /api/internal-chat/notifications - Get user's notifications
// PUT /api/internal-chat/notifications - Mark notifications as read

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'

// GET /api/internal-chat/notifications
export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions)

  try {
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '50')
    const unreadOnly = searchParams.get('unread_only') === 'true'

    // Build where clause
    const whereClause: any = {
      user_uuid: session.user.id,
      expires_at: {
        gt: new Date() // Only get non-expired notifications
      }
    }

    if (unreadOnly) {
      whereClause.is_read = false
    }

    // Fetch notifications with related data
    const notifications = await prisma.v_chat_notifications.findMany({
      where: whereClause,
      include: {
        v_chat_rooms: {
          select: {
            room_name: true,
            room_type: true
          }
        },
        v_chat_messages: {
          select: {
            content: true,
            message_type: true
          }
        }
      },
      orderBy: {
        insert_date: 'desc'
      },
      take: limit
    })

    // Format notifications
    const formattedNotifications = notifications.map(notification => ({
      notification_uuid: notification.notification_uuid,
      user_uuid: notification.user_uuid,
      room_uuid: notification.room_uuid,
      message_id: notification.message_id.toString(),
      notification_type: notification.notification_type,
      is_read: notification.is_read,
      is_sent: notification.is_sent,
      insert_date: notification.insert_date,
      read_date: notification.read_date,
      expires_at: notification.expires_at,

      // Related data
      room_name: notification.v_chat_rooms?.room_name,
      room_type: notification.v_chat_rooms?.room_type,
      message_content: notification.v_chat_messages?.content,
      message_type: notification.v_chat_messages?.message_type
    }))

    // Get unread count from SINGLE SOURCE OF TRUTH
    const unreadCountResult = await prisma.v_chat_unread_counts.aggregate({
      where: {
        user_uuid: session.user.id
      },
      _sum: {
        unread_count: true
      }
    })

    const unreadCount = unreadCountResult._sum.unread_count || 0

    return NextResponse.json({
      success: true,
      data: {
        notifications: formattedNotifications,
        unread_count: unreadCount,
        total_count: notifications.length
      }
    })
  } catch (error) {
    console.error('Error fetching notifications:', error)

    return NextResponse.json({ error: 'Failed to fetch notifications' }, { status: 500 })
  }
}

// PUT /api/internal-chat/notifications - Mark notifications as read
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { notification_uuids, mark_all = false } = body

    if (mark_all) {
      // Mark all unread notifications as read (notifications table only)
      // NOTE: This only affects push notifications, not unread counts
      // Unread counts are managed through /rooms/[roomId]/read endpoint
      await prisma.v_chat_notifications.updateMany({
        where: {
          user_uuid: session.user.id,
          is_read: false
        },
        data: {
          is_read: true,
          read_date: new Date()
        }
      })
    } else if (notification_uuids && Array.isArray(notification_uuids)) {
      // Mark specific notifications as read (notifications table only)
      // NOTE: This only affects push notifications, not unread counts
      // Unread counts are managed through /rooms/[roomId]/read endpoint
      await prisma.v_chat_notifications.updateMany({
        where: {
          notification_uuid: { in: notification_uuids },
          user_uuid: session.user.id
        },
        data: {
          is_read: true,
          read_date: new Date()
        }
      })
    } else {
      return NextResponse.json({ error: 'Invalid request body' }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      message: 'Notifications marked as read'
    })
  } catch (error) {
    console.error('Error updating notifications:', error)

    return NextResponse.json({ error: 'Failed to update notifications' }, { status: 500 })
  }
}
