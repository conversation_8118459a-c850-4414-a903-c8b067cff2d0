// Internal Chat - File Upload API
// POST /api/internal-chat/upload - Upload files for chat messages

import { writeFile, mkdir } from 'fs/promises'

import { existsSync } from 'fs'

import path from 'path'

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { v4 as uuidv4 } from 'uuid'

import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'

// File upload configuration
const MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB

const ALLOWED_TYPES = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  'application/pdf',
  'text/plain',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'video/mp4',
  'video/webm',
  'video/ogg',
  'audio/mp3',
  'audio/wav',
  'audio/ogg',
  'audio/mpeg'
]

const UPLOAD_DIR = process.env.CHAT_UPLOAD_PATH || './uploads/chat'

// POST /api/internal-chat/upload
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const roomId = formData.get('roomId') as string
    const messageId = formData.get('messageId') as string

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    if (!roomId) {
      return NextResponse.json({ error: 'Room ID is required' }, { status: 400 })
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: `File too large. Maximum size is ${MAX_FILE_SIZE / 1024 / 1024}MB` },
        { status: 400 }
      )
    }

    // Validate file type
    if (!ALLOWED_TYPES.includes(file.type)) {
      return NextResponse.json({ error: 'File type not allowed' }, { status: 400 })
    }

    // Validate filename
    if (!/^[a-zA-Z0-9._-]+$/.test(file.name.replace(/\s+/g, '_'))) {
      return NextResponse.json(
        { error: 'Invalid filename. Only letters, numbers, dots, hyphens, and underscores are allowed' },
        { status: 400 }
      )
    }

    // Check if user has access to the room
    const participant = await prisma.v_chat_room_participants.findFirst({
      where: {
        room_uuid: roomId,
        user_uuid: session.user.id
      }
    })

    if (!participant) {
      return NextResponse.json({ error: 'Room not found or access denied' }, { status: 404 })
    }

    // If messageId is provided, verify the message exists and user owns it
    if (messageId) {
      const message = await prisma.v_chat_messages.findFirst({
        where: {
          message_id: BigInt(messageId),
          room_uuid: roomId,
          author_uuid: session.user.id
        }
      })

      if (!message) {
        return NextResponse.json(
          { error: 'Message not found or you do not have permission to add attachments to it' },
          { status: 404 }
        )
      }
    }

    // Create upload directory structure
    const roomDir = path.join(UPLOAD_DIR, roomId)

    if (!existsSync(roomDir)) {
      await mkdir(roomDir, { recursive: true })
    }

    // Generate unique filename
    const fileExtension = path.extname(file.name)
    const baseName = path.basename(file.name, fileExtension)
    const uniqueFilename = `${uuidv4()}_${baseName.replace(/\s+/g, '_')}${fileExtension}`
    const filePath = path.join(roomDir, uniqueFilename)
    const relativePath = path.join('chat', roomId, uniqueFilename)

    // Save file to disk
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    await writeFile(filePath, buffer)

    // Get image dimensions if it's an image
    const width: number | null = null
    const height: number | null = null

    if (file.type.startsWith('image/')) {
      try {
        // You can use a library like 'sharp' or 'image-size' here
        // For now, we'll leave dimensions as null
        // const dimensions = await getImageDimensions(filePath);
        // width = dimensions.width;
        // height = dimensions.height;
      } catch (error) {
        console.warn('Failed to get image dimensions:', error)
      }
    }

    // Create attachment record if messageId is provided
    let attachment = null

    if (messageId) {
      attachment = await prisma.v_chat_message_attachments.create({
        data: {
          message_id: BigInt(messageId),
          filename: file.name,
          file_path: relativePath,
          file_size: file.size,
          content_type: file.type,
          width,
          height
        }
      })
    }

    // Generate download URL
    const downloadUrl = `/api/internal-chat/files/${attachment?.attachment_id || 'temp'}`

    const response = {
      success: true,
      data: {
        attachment_id: attachment?.attachment_id.toString(),
        filename: file.name,
        file_path: relativePath,
        file_size: file.size,
        content_type: file.type,
        width,
        height,
        download_url: downloadUrl,

        // Computed properties
        is_image: file.type.startsWith('image/'),
        is_video: file.type.startsWith('video/'),
        is_audio: file.type.startsWith('audio/'),
        is_document:
          !file.type.startsWith('image/') && !file.type.startsWith('video/') && !file.type.startsWith('audio/')
      },
      message: 'File uploaded successfully'
    }

    return NextResponse.json(response, { status: 201 })
  } catch (error) {
    console.error('Error uploading file:', error)

    return NextResponse.json({ error: 'Failed to upload file' }, { status: 500 })
  }
}

// Helper function to validate file content (basic security check)
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function validateFileContent(buffer: Buffer, mimeType: string): boolean {
  // Basic file signature validation
  const signatures: Record<string, number[][]> = {
    'image/jpeg': [[0xff, 0xd8, 0xff]],
    'image/png': [[0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a]],
    'image/gif': [
      [0x47, 0x49, 0x46, 0x38, 0x37, 0x61],
      [0x47, 0x49, 0x46, 0x38, 0x39, 0x61]
    ],
    'application/pdf': [[0x25, 0x50, 0x44, 0x46]],
    'video/mp4': [
      [0x00, 0x00, 0x00, 0x18, 0x66, 0x74, 0x79, 0x70],
      [0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70]
    ]
  }

  const fileSignatures = signatures[mimeType]

  if (!fileSignatures) {
    return true // Allow unknown types for now
  }

  return fileSignatures.some(signature => {
    if (buffer.length < signature.length) return false

    return signature.every((byte, index) => buffer[index] === byte)
  })
}

// Rate limiting helper (you can implement this with Redis or in-memory cache)
// eslint-disable-next-line @typescript-eslint/no-unused-vars
async function checkUploadRateLimit(_userId: string): Promise<{ allowed: boolean; remaining: number }> {
  // Implement rate limiting logic here
  // For now, return allowed
  return { allowed: true, remaining: 10 }
}

// File size formatting helper
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
