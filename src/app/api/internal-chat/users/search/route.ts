// API endpoint for searching domain users for internal chat
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { prisma } from '@/libs/db/prisma'
import getSession from '@/actions/getSession'

export async function GET(request: NextRequest) {
  const session = await getSession()

  try {
    if (!session?.user?.domain) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q') || ''
    const role = searchParams.get('role') || ''
    const limit = parseInt(searchParams.get('limit') || '20')

    // If role=agent is specified, return all enabled users (agents)
    if (role === 'agent') {
      const users = await prisma.v_users.findMany({
        where: {
          domain_uuid: session.user.domain,
          user_enabled: 'true',
          user_uuid: {
            not: session.user.id // Exclude current user
          }
        },
        select: {
          user_uuid: true,
          username: true,
          user_email: true,
          user_status: true,
          user_enabled: true,
          contact_uuid: true
        },
        take: limit,
        orderBy: [{ username: 'asc' }]
      })

      // Get contact information for users who have contact_uuid
      const contacts = await prisma.v_contacts.findMany({
        where: {
          contact_uuid: {
            in: users.map(u => u.contact_uuid).filter(Boolean) as string[]
          }
        },
        select: {
          contact_uuid: true,
          contact_name_given: true,
          contact_name_family: true
        }
      })

      // Create a map of contact information
      const contactMap = new Map(
        contacts.map(contact => [
          contact.contact_uuid,
          {
            given_name: contact.contact_name_given,
            family_name: contact.contact_name_family
          }
        ])
      )

      // Format users for agent assignment
      const formattedUsers = users.map(user => {
        const contact = user.contact_uuid ? contactMap.get(user.contact_uuid) : null

        const displayName = contact
          ? [contact.given_name, contact.family_name].filter(Boolean).join(' ') || user.username
          : user.username

        return {
          user_uuid: user.user_uuid,
          username: user.username,
          email: user.user_email,
          display_name: displayName,
          status: user.user_status,
          enabled: user.user_enabled === 'true'
        }
      })

      return NextResponse.json({
        success: true,
        users: formattedUsers,
        total: formattedUsers.length
      })
    }

    if (!query || query.length < 2) {
      return NextResponse.json({
        success: true,
        data: [],
        message: 'Query too short'
      })
    }

    // Search users in the same domain with contact information
    const users = await prisma.v_users.findMany({
      where: {
        domain_uuid: session.user.domain,
        user_enabled: 'true',
        user_uuid: {
          not: session.user.id // Exclude current user
        },
        OR: [
          {
            username: {
              contains: query,
              mode: 'insensitive'
            }
          },
          {
            user_email: {
              contains: query,
              mode: 'insensitive'
            }
          }
        ]
      },
      select: {
        user_uuid: true,
        username: true,
        user_email: true,
        user_status: true,
        user_enabled: true,
        contact_uuid: true
      },
      take: limit,
      orderBy: [{ username: 'asc' }]
    })

    // Get contact information for users who have contact_uuid
    const contacts = await prisma.v_contacts.findMany({
      where: {
        contact_uuid: {
          in: users.map(u => u.contact_uuid).filter(Boolean) as string[]
        }
      },
      select: {
        contact_uuid: true,
        contact_name_given: true,
        contact_name_family: true
      }
    })

    // Create a map of contact information
    const contactMap = new Map(
      contacts.map(contact => [
        contact.contact_uuid,
        {
          given_name: contact.contact_name_given,
          family_name: contact.contact_name_family
        }
      ])
    )

    // Search Telegram contacts
    const telegramContacts = await prisma.v_telegram_contacts.findMany({
      where: {
        domain_uuid: session.user.domain,
        OR: [
          {
            username: {
              contains: query,
              mode: 'insensitive'
            }
          },
          {
            first_name: {
              contains: query,
              mode: 'insensitive'
            }
          },
          {
            last_name: {
              contains: query,
              mode: 'insensitive'
            }
          }
        ]
      },
      select: {
        contact_uuid: true,
        telegram_user_id: true,
        telegram_chat_id: true,
        username: true,
        first_name: true,
        last_name: true,
        phone: true,
        language_code: true
      },
      take: Math.floor(limit / 2), // Reserve half the results for social contacts
      orderBy: { last_interaction_date: 'desc' }
    })

    // TODO: Search ZALO contacts when implemented
    // const zaloContacts = await prisma.v_zalo_contacts.findMany({...})

    // Format internal users
    const formattedUsers = users.map(user => {
      const contact = user.contact_uuid ? contactMap.get(user.contact_uuid) : null

      const displayName = contact
        ? [contact.given_name, contact.family_name].filter(Boolean).join(' ') || user.username
        : user.username

      return {
        type: 'internal_user',
        user_uuid: user.user_uuid,
        username: user.username,
        email: user.user_email,
        display_name: displayName,
        status: user.user_status,
        enabled: user.user_enabled === 'true',
        platform: 'internal'
      }
    })

    // Format Telegram contacts
    const formattedTelegramContacts = telegramContacts.map(contact => {
      const displayName =
        [contact.first_name, contact.last_name].filter(Boolean).join(' ') ||
        contact.username ||
        `Telegram User ${contact.telegram_user_id}`

      return {
        type: 'telegram_contact',
        contact_uuid: contact.contact_uuid,
        telegram_user_id: contact.telegram_user_id.toString(),
        telegram_chat_id: contact.telegram_chat_id.toString(),
        username: contact.username,
        display_name: displayName,
        phone: contact.phone,
        platform: 'telegram'
      }
    })

    // Combine all results
    const allResults = [
      ...formattedUsers,
      ...formattedTelegramContacts

      // TODO: Add ZALO contacts here
    ]

    return NextResponse.json({
      success: true,
      data: allResults,
      total: allResults.length,
      breakdown: {
        internal_users: formattedUsers.length,
        telegram_contacts: formattedTelegramContacts.length,
        zalo_contacts: 0 // TODO: Add ZALO count
      }
    })
  } catch (error) {
    console.error('Error searching users:', error)

    return NextResponse.json({ error: 'Failed to search users' }, { status: 500 })
  }
}
