// Internal Chat - Room Management API
// GET /api/internal-chat/rooms - List user's rooms
// POST /api/internal-chat/rooms - Create new room

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'
import type { CreateRoomRequest } from '@/types/apps/internal-chat/chatTypes'
import { RoomType, ParticipantRole, MESSAGE_FLAGS } from '@/types/apps/internal-chat/chatTypes'

/**
 * Get the author name for a message, handling both internal users and external platform contacts
 */
async function getMessageAuthorName(message: any, room: any): Promise<string> {
  // For internal messages with author_uuid, get the actual user info
  if (message.author_uuid) {
    try {
      const user = await prisma.v_users.findUnique({
        where: { user_uuid: message.author_uuid },
        select: { username: true }
      })

      return user?.username || 'Internal User'
    } catch (error) {
      console.error('Error fetching user info:', error)

      return 'Internal User'
    }
  }

  // For external platform messages, check message mapping to identify platform
  try {
    // Check Facebook mapping first
    const facebookMapping = await prisma.v_facebook_message_mapping.findUnique({
      where: { internal_message_id: message.message_id }
    })

    if (facebookMapping) {
      // This is a Facebook message - get contact info from room data
      if (room.v_facebook_chat_rooms) {
        const contact = room.v_facebook_chat_rooms.v_facebook_contacts

        return contact.first_name && contact.last_name
          ? `${contact.first_name} ${contact.last_name}`.trim()
          : contact.first_name || `Facebook User ${contact.facebook_user_id}`
      }
    }

    const telegramMapping = await prisma.v_telegram_message_mapping.findUnique({
      where: { internal_message_id: message.message_id }
    })

    if (telegramMapping) {
      // This is a Telegram message - get contact info from room data
      if (room.v_telegram_chat_rooms) {
        const contact = room.v_telegram_chat_rooms.v_telegram_contacts

        return contact.first_name && contact.last_name
          ? `${contact.first_name} ${contact.last_name}`.trim()
          : contact.first_name || contact.username || `Telegram User ${contact.telegram_user_id}`
      }
    }

    const zaloMapping = await prisma.v_zalo_message_mapping.findUnique({
      where: { internal_message_id: message.message_id }
    })

    if (zaloMapping) {
      // This is a ZALO message - get contact info from room data
      if (room.v_zalo_oa_chat_rooms) {
        const contact = room.v_zalo_oa_chat_rooms.v_zalo_oa_contacts

        return contact.display_name || `ZALO User ${contact.zalo_user_id}`
      }
    }
  } catch (error) {
    console.error('Error checking message mapping:', error)
  }

  return 'Unknown User'
}

// GET /api/internal-chat/rooms
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const roomType = searchParams.get('type') as RoomType | null
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const skip = (page - 1) * limit

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user!.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 400 })
    }

    // Build where clause - show conversations where user is participant (including deleted ones)
    // We need to include deleted participants to check if new messages arrived after deletion
    const whereClause: any = {
      domain_uuid: user.domain_uuid!,
      is_active: true,
      v_chat_room_participants: {
        some: {
          user_uuid: session.user!.id

          // Don't filter by deleted_at here - we need to check message timestamps
        }
      }
    }

    if (roomType) {
      whereClause.room_type = roomType
    }

    // Get all rooms where user is participant with messages - we'll filter properly in application logic
    const [rooms] = await Promise.all([
      prisma.v_chat_rooms.findMany({
        where: {
          ...whereClause,
          v_chat_messages: {
            some: {} // Only rooms with at least one message
          }
        },
        select: {
          room_uuid: true,
          domain_uuid: true,
          room_name: true,
          room_description: true,
          room_type: true,
          room_avatar: true,
          created_by_user_uuid: true,
          is_active: true,
          is_archived: true,
          max_participants: true,
          room_settings: true,
          insert_date: true,
          update_date: true,

          // Participants with minimal user data
          v_chat_room_participants: {
            select: {
              user_uuid: true,
              participant_role: true,
              joined_date: true,
              is_muted: true,
              notification_settings: true,
              deleted_at: true,
              v_users: {
                select: {
                  user_uuid: true,
                  username: true,
                  user_email: true
                }
              }
            }
          },

          // Latest message only with minimal data
          v_chat_messages: {
            select: {
              message_id: true,
              author_uuid: true,
              content: true,
              message_type: true,
              created_at: true,
              flags: true
            },
            orderBy: { created_at: 'desc' },
            take: 1
          },

          // Integration data with minimal includes
          v_telegram_chat_rooms: {
            select: {
              telegram_room_uuid: true,
              telegram_contact_uuid: true,
              assigned_agent_uuid: true,
              room_status: true,
              v_telegram_contacts: {
                select: {
                  contact_uuid: true,
                  telegram_user_id: true,
                  telegram_chat_id: true,
                  username: true,
                  first_name: true,
                  last_name: true,
                  phone: true,
                  language_code: true,
                  last_interaction_date: true
                }
              }
            }
          },

          v_facebook_chat_rooms: {
            select: {
              facebook_room_uuid: true,
              facebook_contact_uuid: true,
              assigned_agent_uuid: true,
              room_status: true,
              v_facebook_contacts: {
                select: {
                  contact_uuid: true,
                  facebook_user_id: true,
                  first_name: true,
                  last_name: true,
                  profile_pic: true,
                  is_active: true,
                  last_interaction_date: true
                }
              }
            }
          },

          v_zalo_oa_chat_rooms: {
            select: {
              zalo_room_uuid: true,
              zalo_contact_uuid: true,
              assigned_agent_uuid: true,
              room_status: true,
              v_zalo_oa_contacts: {
                select: {
                  contact_uuid: true,
                  zalo_user_id: true,
                  display_name: true,
                  avatar_url: true,
                  phone: true,
                  last_interaction_date: true
                }
              }
            }
          },

          _count: {
            select: {
              v_chat_room_participants: true,
              v_chat_messages: true
            }
          }
        },
        orderBy: [{ update_date: 'desc' }, { insert_date: 'desc' }],
        skip,
        take: limit
      })
    ])

    // Get unread counts for these rooms
    const roomUuids = rooms.map(room => room.room_uuid)

    const unreadCounts = await prisma.v_chat_unread_counts.findMany({
      where: {
        user_uuid: session.user!.id,
        room_uuid: { in: roomUuids }
      }
    })

    const unreadMap = unreadCounts.reduce(
      (acc, count) => {
        acc[count.room_uuid] = count.unread_count || 0

        return acc
      },
      {} as Record<string, number>
    )

    // Filter rooms: exclude rooms where user has deleted_at set AND no new messages after deletion
    const formattedRooms = await Promise.all(
      rooms.map(async room => {
        const userParticipant = room.v_chat_room_participants.find(p => p.user_uuid === session.user!.id)

        // If user participant not found, don't show room
        if (!userParticipant) {
          return null
        }

        // If user hasn't deleted the conversation, always show
        if (!userParticipant.deleted_at) {
          return room
        }

        // User has deleted the conversation - check if there are new messages after deletion
        const deletionTimestamp = Math.floor(userParticipant.deleted_at.getTime() / 1000)

        const messagesAfterDeletion = await prisma.v_chat_messages.count({
          where: {
            room_uuid: room.room_uuid,
            created_at: {
              gt: deletionTimestamp
            }
          }
        })

        // Only show if there are messages after deletion
        return messagesAfterDeletion > 0 ? room : null
      })
    ).then(results => results.filter(room => room !== null))

    // Now format the filtered rooms - make this async to handle author name lookup
    const finalFormattedRooms = await Promise.all(
      formattedRooms.map(async room => {
        if (!room) return null
        const userParticipant = room.v_chat_room_participants.find((p: any) => p.user_uuid === session.user!.id)
        const lastMessage = room.v_chat_messages[0] || null

        // Check if message is deleted (but still show the room)
        const isMessageDeleted = lastMessage?.flags && (lastMessage.flags & MESSAGE_FLAGS.DELETED) !== 0

        // Get proper author name for last message
        let lastMessageAuthorName = null

        if (lastMessage) {
          lastMessageAuthorName = await getMessageAuthorName(lastMessage, room)
        }

        // Determine platform and platform-specific data
        let platform = 'internal'
        let platformData = null
        let displayName = room.room_name

        if (room.v_telegram_chat_rooms) {
          platform = 'telegram'
          const telegramRoom = room.v_telegram_chat_rooms
          const contact = telegramRoom.v_telegram_contacts

          platformData = {
            telegram_room_uuid: telegramRoom.telegram_room_uuid,
            telegram_contact_uuid: telegramRoom.telegram_contact_uuid,
            room_status: telegramRoom.room_status,
            assigned_agent_uuid: telegramRoom.assigned_agent_uuid,
            contact: {
              telegram_user_id: contact.telegram_user_id.toString(),
              telegram_chat_id: contact.telegram_chat_id.toString(),
              username: contact.username,
              first_name: contact.first_name,
              last_name: contact.last_name,
              phone: contact.phone,
              language_code: contact.language_code,
              last_interaction_date: contact.last_interaction_date?.toISOString() || null
            }
          }

          // Use Telegram contact name as display name
          displayName =
            contact.first_name && contact.last_name
              ? `${contact.first_name} ${contact.last_name}`.trim()
              : contact.first_name || contact.username || `Telegram User ${contact.telegram_user_id}`
        } else if (room.v_zalo_oa_chat_rooms) {
          platform = 'zalo'
          const zaloRoom = room.v_zalo_oa_chat_rooms
          const contact = zaloRoom.v_zalo_oa_contacts

          platformData = {
            zalo_room_uuid: zaloRoom.zalo_room_uuid,
            zalo_contact_uuid: zaloRoom.zalo_contact_uuid,
            room_status: zaloRoom.room_status,
            assigned_agent_uuid: zaloRoom.assigned_agent_uuid,
            contact: {
              zalo_user_id: contact.zalo_user_id,
              display_name: contact.display_name,
              avatar_url: contact.avatar_url,
              phone: contact.phone,
              last_interaction_date: contact.last_interaction_date?.toISOString() || null
            }
          }

          // Use ZALO contact name as display name
          displayName = contact.display_name || `ZALO User ${contact.zalo_user_id}`
        }

        return {
          room_uuid: room.room_uuid,
          domain_uuid: room.domain_uuid,
          room_name: displayName, // Use platform-specific display name
          room_description: room.room_description,
          room_type: room.room_type,
          room_avatar: room.room_avatar,
          created_by_user_uuid: room.created_by_user_uuid,
          is_active: room.is_active,
          is_archived: room.is_archived,
          max_participants: room.max_participants,
          room_settings: room.room_settings,
          insert_date: room.insert_date?.toISOString() || null,
          update_date: room.update_date?.toISOString() || null,

          // Platform information
          platform,
          platform_data: platformData,

          // Computed properties
          participant_count: Number(room._count.v_chat_room_participants),
          unread_count: Number(unreadMap[room.room_uuid] || 0),
          is_muted: userParticipant?.is_muted || false,
          user_role: userParticipant?.participant_role as ParticipantRole,

          // Last message - include deleted status for frontend handling
          last_message: lastMessage
            ? {
                message_id: lastMessage.message_id.toString(),
                content: isMessageDeleted ? null : lastMessage.content, // Hide content if deleted
                message_type: lastMessage.message_type,
                created_at: lastMessage.created_at.toString(),
                author_name: lastMessageAuthorName || 'Unknown User',
                flags: lastMessage.flags,
                is_deleted: isMessageDeleted
              }
            : null,

          // Participants
          participants: room.v_chat_room_participants.map((p: any) => ({
            participant_uuid: p.participant_uuid,
            user_uuid: p.user_uuid,
            participant_role: p.participant_role,
            user_name: p.v_users.username,
            user_email: p.v_users.user_email,
            is_muted: p.is_muted,
            joined_date: p.joined_date?.toISOString() || null,
            last_read_message_id: p.last_read_message_id?.toString() || null
          }))
        }
      })
    )

    console.log(`Returning ${finalFormattedRooms.length} rooms to client`)
    finalFormattedRooms.forEach(room => {
      if (room) {
        console.log(
          `Room ${room.room_uuid}: hasLastMessage=${!!room.last_message}, lastMessageId=${room.last_message?.message_id}`
        )
      }
    })

    return NextResponse.json({
      success: true,
      data: finalFormattedRooms,
      pagination: {
        page,
        limit,
        total: finalFormattedRooms.length, // Use actual filtered count
        has_more: skip + limit < finalFormattedRooms.length
      }
    })
  } catch (error) {
    console.error('Error fetching chat rooms:', error)

    return NextResponse.json({ error: 'Failed to fetch chat rooms' }, { status: 500 })
  }
}

// POST /api/internal-chat/rooms
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body: CreateRoomRequest = await request.json()
    const { room_name, room_description, room_type, participant_uuids = [], room_settings = {} } = body

    // Validate required fields
    if (!room_name || !room_type) {
      return NextResponse.json({ error: 'Room name and type are required' }, { status: 400 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user!.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 400 })
    }

    // Validate room type
    if (!Object.values(RoomType).includes(room_type)) {
      return NextResponse.json({ error: 'Invalid room type' }, { status: 400 })
    }

    // For direct messages, ensure only 2 participants
    if (room_type === RoomType.DIRECT && participant_uuids.length !== 1) {
      return NextResponse.json({ error: 'Direct messages must have exactly 2 participants' }, { status: 400 })
    }

    // Check if direct message room already exists
    if (room_type === RoomType.DIRECT) {
      const participantIds = [session.user!.id, participant_uuids[0]].sort()

      const existingRoom = await prisma.v_chat_rooms.findFirst({
        where: {
          domain_uuid: user.domain_uuid!,
          room_type: 'direct',
          is_active: true
        },
        include: {
          v_chat_room_participants: {
            select: {
              user_uuid: true
            }
          }
        }
      })

      // Check if the existing room has exactly the same participants
      if (existingRoom) {
        const existingParticipantIds = existingRoom.v_chat_room_participants.map(p => p.user_uuid).sort()

        if (
          existingParticipantIds.length === 2 &&
          existingParticipantIds[0] === participantIds[0] &&
          existingParticipantIds[1] === participantIds[1]
        ) {
          // Return the existing room instead of creating a new one
          const completeExistingRoom = await prisma.v_chat_rooms.findUnique({
            where: { room_uuid: existingRoom.room_uuid },
            include: {
              v_chat_room_participants: {
                include: {
                  v_users: {
                    select: {
                      user_uuid: true,
                      username: true,
                      user_email: true
                    }
                  }
                }
              }
            }
          })

          return NextResponse.json(
            {
              success: true,
              data: {
                room_uuid: completeExistingRoom!.room_uuid,
                domain_uuid: completeExistingRoom!.domain_uuid,
                room_name: completeExistingRoom!.room_name,
                room_description: completeExistingRoom!.room_description,
                room_type: completeExistingRoom!.room_type,
                room_avatar: completeExistingRoom!.room_avatar,
                created_by_user_uuid: completeExistingRoom!.created_by_user_uuid,
                is_active: completeExistingRoom!.is_active,
                is_archived: completeExistingRoom!.is_archived,
                max_participants: completeExistingRoom!.max_participants,
                room_settings: completeExistingRoom!.room_settings,
                insert_date: completeExistingRoom!.insert_date?.toISOString() || null,
                update_date: completeExistingRoom!.update_date?.toISOString() || null,
                participant_count: completeExistingRoom!.v_chat_room_participants.length,
                unread_count: 0,
                participants: completeExistingRoom!.v_chat_room_participants.map(p => ({
                  participant_uuid: p.participant_uuid,
                  user_uuid: p.user_uuid,
                  participant_role: p.participant_role,
                  user_name: p.v_users.username,
                  user_email: p.v_users.user_email,
                  is_muted: p.is_muted,
                  joined_date: p.joined_date?.toISOString() || null,
                  last_read_message_id: p.last_read_message_id?.toString() || null
                }))
              },
              message: 'Existing room found'
            },
            { status: 200 }
          )
        }
      }
    }

    // Create room and participants in transaction
    const result = await prisma.$transaction(async tx => {
      // Create the room
      const room = await tx.v_chat_rooms.create({
        data: {
          domain_uuid: user.domain_uuid!,
          room_name,
          room_description,
          room_type,
          room_settings,
          created_by_user_uuid: session.user!.id,
          insert_user: session.user!.id,
          update_user: session.user!.id
        }
      })

      // Add creator as owner
      await tx.v_chat_room_participants.create({
        data: {
          room_uuid: room.room_uuid,
          user_uuid: session.user!.id,
          participant_role: ParticipantRole.OWNER,
          insert_user: session.user!.id,
          update_user: session.user!.id
        }
      })

      // Add other participants as members
      if (participant_uuids.length > 0) {
        console.log(`Adding ${participant_uuids.length} participants to room ${room.room_uuid}:`, participant_uuids)

        const participantData = participant_uuids.map(uuid => ({
          room_uuid: room.room_uuid,
          user_uuid: uuid,
          participant_role: ParticipantRole.MEMBER,
          insert_user: session.user!.id,
          update_user: session.user!.id
        }))

        await tx.v_chat_room_participants.createMany({
          data: participantData
        })

        console.log(`Successfully added participants to room ${room.room_uuid}`)
      } else {
        console.log(`No additional participants to add to room ${room.room_uuid}`)
      }

      // Initialize unread counts for all participants
      const allParticipants = [session.user!.id, ...participant_uuids]

      const unreadCountData = allParticipants.map(uuid => ({
        user_uuid: uuid,
        room_uuid: room.room_uuid,
        unread_count: 0
      }))

      await tx.v_chat_unread_counts.createMany({
        data: unreadCountData
      })

      // Don't create system message or notifications immediately
      // Only send notifications when the first actual message is sent
      // This prevents accidental room creation notifications

      return room
    })

    console.log(`Room ${result.room_uuid} created successfully with participants`)

    // Small delay to ensure database transaction is fully committed
    await new Promise(resolve => setTimeout(resolve, 100))

    // Fetch the complete room data with participants
    const completeRoom = await prisma.v_chat_rooms.findUnique({
      where: { room_uuid: result.room_uuid },
      include: {
        v_chat_room_participants: {
          include: {
            v_users: {
              select: {
                user_uuid: true,
                username: true,
                user_email: true
              }
            }
          }
        }
      }
    })

    // Don't broadcast room creation immediately
    // Room will be broadcasted when the first message is sent

    return NextResponse.json(
      {
        success: true,
        data: {
          room_uuid: completeRoom!.room_uuid,
          domain_uuid: completeRoom!.domain_uuid,
          room_name: completeRoom!.room_name,
          room_description: completeRoom!.room_description,
          room_type: completeRoom!.room_type,
          room_avatar: completeRoom!.room_avatar,
          created_by_user_uuid: completeRoom!.created_by_user_uuid,
          is_active: completeRoom!.is_active,
          is_archived: completeRoom!.is_archived,
          max_participants: completeRoom!.max_participants,
          room_settings: completeRoom!.room_settings,
          insert_date: completeRoom!.insert_date?.toISOString() || null,
          update_date: completeRoom!.update_date?.toISOString() || null,
          participant_count: completeRoom!.v_chat_room_participants.length,
          unread_count: 0,
          participants: completeRoom!.v_chat_room_participants.map(p => ({
            participant_uuid: p.participant_uuid,
            user_uuid: p.user_uuid,
            participant_role: p.participant_role,
            user_name: p.v_users.username,
            user_email: p.v_users.user_email,
            is_muted: p.is_muted,
            joined_date: p.joined_date?.toISOString() || null,
            last_read_message_id: p.last_read_message_id?.toString() || null
          }))
        },
        message: 'Room created successfully'
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('Error creating chat room:', error)

    return NextResponse.json({ error: 'Failed to create chat room' }, { status: 500 })
  }
}
