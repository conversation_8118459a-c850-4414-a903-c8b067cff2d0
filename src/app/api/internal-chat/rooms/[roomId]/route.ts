// Internal Chat - Individual Room Management API
// GET /api/internal-chat/rooms/[roomId] - Get room details
// PUT /api/internal-chat/rooms/[roomId] - Update room
// DELETE /api/internal-chat/rooms/[roomId] - Archive room (soft delete conversation)

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'
import { MESSAGE_FLAGS, ParticipantRole } from '@/types/apps/internal-chat/chatTypes'
import { safeBigIntToString } from '@/utils/chat/messageUtils'

/**
 * Get author information for a message, handling both internal users and external platform contacts
 */
async function getMessageAuthorInfo(
  message: any,
  roomId: string
): Promise<{ author_name: string; author_email: string }> {
  // For internal messages with author_uuid, get user info
  if (message.author_uuid) {
    try {
      const user = await prisma.v_users.findUnique({
        where: { user_uuid: message.author_uuid },
        select: {
          username: true,
          user_email: true
        }
      })

      return {
        author_name: user?.username || 'Internal User',
        author_email: user?.user_email || ''
      }
    } catch (error) {
      console.error('Error fetching user info:', error)

      return {
        author_name: 'Internal User',
        author_email: ''
      }
    }
  }

  // For external platform messages, check message mapping to identify platform
  try {
    const telegramMapping = await prisma.v_telegram_message_mapping.findUnique({
      where: { internal_message_id: message.message_id }
    })

    if (telegramMapping) {
      // This is a Telegram message - get contact info through room bridge
      const telegramRoom = await prisma.v_telegram_chat_rooms.findFirst({
        where: { internal_room_uuid: roomId },
        include: {
          v_telegram_contacts: {
            select: {
              first_name: true,
              last_name: true,
              username: true,
              telegram_user_id: true
            }
          }
        }
      })

      if (telegramRoom?.v_telegram_contacts) {
        const contact = telegramRoom.v_telegram_contacts

        const displayName =
          contact.first_name && contact.last_name
            ? `${contact.first_name} ${contact.last_name}`.trim()
            : contact.first_name || contact.username || `Telegram User ${contact.telegram_user_id}`

        return {
          author_name: displayName,
          author_email: ''
        }
      }
    }

    // Check for ZALO mapping
    const zaloMapping = await prisma.v_zalo_message_mapping.findUnique({
      where: { internal_message_id: message.message_id }
    })

    if (zaloMapping) {
      // This is a ZALO message - get contact info through room bridge
      const zaloRoom = await prisma.v_zalo_oa_chat_rooms.findFirst({
        where: { internal_room_uuid: roomId },
        include: {
          v_zalo_oa_contacts: {
            select: {
              display_name: true,
              zalo_user_id: true
            }
          }
        }
      })

      if (zaloRoom?.v_zalo_oa_contacts) {
        const contact = zaloRoom.v_zalo_oa_contacts

        return {
          author_name: contact.display_name || `ZALO User ${contact.zalo_user_id}`,
          author_email: ''
        }
      }
    }
  } catch (error) {
    console.error('Error checking message mapping:', error)
  }

  // Fallback for unknown external messages
  return {
    author_name: 'External User',
    author_email: ''
  }
}

// Recursively convert all BigInt values in an object to strings
const convertBigIntToString = (obj: any): any => {
  if (obj === null || obj === undefined) return obj
  if (typeof obj === 'bigint') return obj.toString()
  if (Array.isArray(obj)) return obj.map(convertBigIntToString)

  if (typeof obj === 'object') {
    const converted: any = {}

    for (const [key, value] of Object.entries(obj)) {
      converted[key] = convertBigIntToString(value)
    }

    return converted
  }

  return obj
}

// GET /api/internal-chat/rooms/[roomId]
export async function GET(_request: NextRequest, { params }: { params: { roomId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { roomId } = params

    // Check if user is participant in the room (including hidden ones)
    const participant = await prisma.v_chat_room_participants.findFirst({
      where: {
        room_uuid: roomId,
        user_uuid: session.user.id
      }
    })

    if (!participant) {
      return NextResponse.json({ error: 'Room not found or access denied' }, { status: 404 })
    }

    // TODO: If room was hidden, unhide it when user accesses it
    // Temporarily disabled until Prisma client recognizes is_hidden field
    // if (participant.is_hidden) {
    //   await prisma.v_chat_room_participants.update({
    //     where: { participant_uuid: participant.participant_uuid },
    //     data: {
    //       is_hidden: false,
    //       update_date: new Date()
    //     }
    //   })
    // }

    // Get room details with participants and recent messages
    const room = await prisma.v_chat_rooms.findUnique({
      where: { room_uuid: roomId },
      include: {
        v_chat_room_participants: {
          include: {
            v_users: {
              select: {
                user_uuid: true,
                username: true,
                user_email: true
              }
            }
          },
          orderBy: { joined_date: 'asc' }
        },
        v_chat_messages: {
          orderBy: { created_at: 'desc' },
          take: 5 // Get more messages to filter out deleted ones
          // Note: v_users relationship removed because external platform messages don't have internal users
        },
        _count: {
          select: {
            v_chat_room_participants: true,
            v_chat_messages: true
          }
        }
      }
    })

    if (!room) {
      return NextResponse.json({ error: 'Room not found' }, { status: 404 })
    }

    if (!room) {
      return NextResponse.json({ error: 'Room not found' }, { status: 404 })
    }

    // Get unread count for current user from SINGLE SOURCE OF TRUTH
    const unreadCount = await prisma.v_chat_unread_counts.findFirst({
      where: {
        user_uuid: session.user.id,
        room_uuid: roomId
      }
    })

    // Find the first non-deleted message
    const lastMessageData = room.v_chat_messages.find(msg => {
      if (!msg.flags) return true // No flags means not deleted

      return (msg.flags & MESSAGE_FLAGS.DELETED) === 0 // Check if DELETED bit is not set
    })

    const userParticipant = room.v_chat_room_participants.find(p => p.user_uuid === session.user!.id)

    // Get proper author name for last message if it exists
    let lastMessageAuthorName = null

    if (lastMessageData) {
      const authorInfo = await getMessageAuthorInfo(lastMessageData, roomId)

      lastMessageAuthorName = authorInfo.author_name
    }

    const response = {
      room_uuid: room.room_uuid,
      domain_uuid: room.domain_uuid,
      room_name: room.room_name,
      room_description: room.room_description,
      room_type: room.room_type,
      room_avatar: room.room_avatar,
      created_by_user_uuid: room.created_by_user_uuid,
      is_active: room.is_active,
      is_archived: room.is_archived,
      max_participants: room.max_participants,
      room_settings: room.room_settings,
      insert_date: room.insert_date?.toISOString() || null,
      update_date: room.update_date?.toISOString() || null,

      // Computed properties
      participant_count: room._count.v_chat_room_participants,
      message_count: room._count.v_chat_messages,
      unread_count: safeBigIntToString(unreadCount?.unread_count) || 0,
      is_muted: userParticipant?.is_muted || false,
      user_role: userParticipant?.participant_role as ParticipantRole,

      // Last message - now with proper author name
      last_message: lastMessageData
        ? {
            message_id: safeBigIntToString(lastMessageData.message_id),
            content: lastMessageData.content,
            message_type: lastMessageData.message_type,
            created_at: lastMessageData.created_at,
            author_name: lastMessageAuthorName || 'Unknown User',
            flags: lastMessageData.flags
          }
        : null,

      // Participants
      participants: room.v_chat_room_participants.map(p => ({
        participant_uuid: p.participant_uuid,
        user_uuid: p.user_uuid,
        participant_role: p.participant_role,
        user_name: p.v_users.username,
        user_email: p.v_users.user_email,
        is_muted: p.is_muted,
        joined_date: p.joined_date?.toISOString() || null,
        last_read_message_id: safeBigIntToString(p.last_read_message_id)
      }))
    }

    return NextResponse.json(
      convertBigIntToString({
        success: true,
        data: response
      })
    )
  } catch (error) {
    console.error('Error fetching room details:', error)

    return NextResponse.json({ error: 'Failed to fetch room details' }, { status: 500 })
  }
}

// PUT /api/internal-chat/rooms/[roomId]
export async function PUT(request: NextRequest, { params }: { params: { roomId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { roomId } = params
    const body = await request.json()
    const { room_name, room_description, room_settings, max_participants } = body

    // Check if user has permission to update room (owner or admin)
    const participant = await prisma.v_chat_room_participants.findFirst({
      where: {
        room_uuid: roomId,
        user_uuid: session.user.id,
        participant_role: { in: [ParticipantRole.OWNER, ParticipantRole.ADMIN] }
      }
    })

    if (!participant) {
      return NextResponse.json(
        { error: 'Permission denied. Only room owners and admins can update room settings.' },
        { status: 403 }
      )
    }

    // Update room
    const updatedRoom = await prisma.v_chat_rooms.update({
      where: { room_uuid: roomId },
      data: {
        ...(room_name && { room_name }),
        ...(room_description !== undefined && { room_description }),
        ...(room_settings && { room_settings }),
        ...(max_participants && { max_participants }),
        update_date: new Date(),
        update_user: session.user!.id
      },
      include: {
        v_chat_room_participants: {
          include: {
            v_users: {
              select: {
                user_uuid: true,
                username: true,
                user_email: true
              }
            }
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        room_uuid: updatedRoom.room_uuid,
        domain_uuid: updatedRoom.domain_uuid,
        room_name: updatedRoom.room_name,
        room_description: updatedRoom.room_description,
        room_type: updatedRoom.room_type,
        room_avatar: updatedRoom.room_avatar,
        created_by_user_uuid: updatedRoom.created_by_user_uuid,
        is_active: updatedRoom.is_active,
        is_archived: updatedRoom.is_archived,
        max_participants: updatedRoom.max_participants,
        room_settings: updatedRoom.room_settings,
        insert_date: updatedRoom.insert_date?.toISOString() || null,
        update_date: updatedRoom.update_date?.toISOString() || null,
        participants: updatedRoom.v_chat_room_participants.map(p => ({
          participant_uuid: p.participant_uuid,
          user_uuid: p.user_uuid,
          participant_role: p.participant_role,
          user_name: p.v_users.username,
          user_email: p.v_users.user_email,
          is_muted: p.is_muted,
          joined_date: p.joined_date?.toISOString() || null
        }))
      },
      message: 'Room updated successfully'
    })
  } catch (error) {
    console.error('Error updating room:', error)

    return NextResponse.json({ error: 'Failed to update room' }, { status: 500 })
  }
}

// DELETE /api/internal-chat/rooms/[roomId]
export async function DELETE(_request: NextRequest, { params }: { params: { roomId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { roomId } = params

    // Check if user is participant in the room (including hidden ones for deletion)
    const participant = await prisma.v_chat_room_participants.findFirst({
      where: {
        room_uuid: roomId,
        user_uuid: session.user.id
      }
    })

    if (!participant) {
      return NextResponse.json({ error: 'You are not a participant in this conversation.' }, { status: 403 })
    }

    // Get user's domain for security
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 400 })
    }

    // Verify room belongs to user's domain
    const room = await prisma.v_chat_rooms.findFirst({
      where: {
        room_uuid: roomId,
        domain_uuid: user.domain_uuid
      }
    })

    if (!room) {
      return NextResponse.json({ error: 'Room not found or access denied' }, { status: 404 })
    }

    // Check if this is the last active participant (not hidden)
    // TODO: Count only non-hidden participants
    // Temporarily disabled until Prisma client recognizes is_hidden field
    const activeParticipantCount = await prisma.v_chat_room_participants.count({
      where: {
        room_uuid: roomId

        // is_hidden: { not: true }
      }
    })

    const isLastActiveParticipant = activeParticipantCount === 1

    // Hide conversation for user (soft delete) in a transaction
    await prisma.$transaction(async tx => {
      // 1. Mark user's notifications as read (don't delete them)
      await tx.v_chat_notifications.updateMany({
        where: {
          room_uuid: roomId,
          user_uuid: session.user!.id,
          is_read: false
        },
        data: {
          is_read: true,
          read_date: new Date()
        }
      })

      // 2. Reset user's unread counts for this room using SINGLE SOURCE OF TRUTH
      await tx.v_chat_unread_counts.updateMany({
        where: {
          room_uuid: roomId,
          user_uuid: session.user!.id
        },
        data: {
          unread_count: 0,
          last_updated: new Date()
        }
      })

      // 3. Mark conversation as deleted for the user with timestamp
      await tx.v_chat_room_participants.update({
        where: { participant_uuid: participant.participant_uuid },
        data: {
          deleted_at: new Date(),
          update_date: new Date(),
          update_user: session.user!.id
        }
      })

      // 4. If this was the last active participant, clean up the entire room
      if (isLastActiveParticipant) {
        // Delete all message attachments
        const messages = await tx.v_chat_messages.findMany({
          where: { room_uuid: roomId },
          select: { message_id: true }
        })

        const messageIds = messages.map(m => m.message_id)

        if (messageIds.length > 0) {
          await tx.v_chat_message_attachments.deleteMany({
            where: { message_id: { in: messageIds } }
          })

          await tx.v_chat_message_reactions.deleteMany({
            where: { message_id: { in: messageIds } }
          })
        }

        // Delete all remaining notifications and unread counts
        await tx.v_chat_notifications.deleteMany({
          where: { room_uuid: roomId }
        })

        await tx.v_chat_unread_counts.deleteMany({
          where: { room_uuid: roomId }
        })

        // Delete all messages
        await tx.v_chat_messages.deleteMany({
          where: { room_uuid: roomId }
        })

        // Delete all participants (including hidden ones)
        await tx.v_chat_room_participants.deleteMany({
          where: { room_uuid: roomId }
        })

        // Delete ZALO OA mappings if they exist
        await tx.v_zalo_oa_chat_rooms.deleteMany({
          where: { internal_room_uuid: roomId }
        })

        // Finally, delete the room itself
        await tx.v_chat_rooms.delete({
          where: { room_uuid: roomId }
        })
      }
    })

    return NextResponse.json({
      success: true,
      message: isLastActiveParticipant ? 'Conversation deleted successfully' : 'Conversation hidden from your list'
    })
  } catch (error) {
    console.error('Error deleting room:', error)

    return NextResponse.json({ error: 'Failed to delete conversation' }, { status: 500 })
  }
}
