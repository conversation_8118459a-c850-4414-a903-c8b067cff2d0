// Internal Chat - Messages API
// GET /api/internal-chat/rooms/[roomId]/messages - Get messages with pagination
// POST /api/internal-chat/rooms/[roomId]/messages - Send new message

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import type { SendMessageRequest } from '@/types/apps/internal-chat/chatTypes'
import { MessageType, MESSAGE_FLAGS } from '@/types/apps/internal-chat/chatTypes'
import { prisma } from '@/libs/db/prisma'

/**
 * Get user information for internal messages
 */
async function getUserInfo(author_uuid: string | null): Promise<{ username: string; user_email: string } | null> {
  if (!author_uuid) return null

  const user = await prisma.v_users.findUnique({
    where: { user_uuid: author_uuid },
    select: {
      username: true,
      user_email: true
    }
  })

  if (!user) return null

  return {
    username: user.username || 'Unknown User',
    user_email: user.user_email || ''
  }
}

/**
 * Get author information for a message, handling both internal users and external platform contacts
 */
async function getMessageAuthorInfo(
  message: any,
  roomId: string
): Promise<{ author_name: string; author_email: string }> {
  // For internal messages with author_uuid, get user info
  if (message.author_uuid) {
    const userInfo = await getUserInfo(message.author_uuid)

    return {
      author_name: userInfo?.username || 'Internal User',
      author_email: userInfo?.user_email || ''
    }
  }

  // For external platform messages, check message mapping to identify platform
  const telegramMapping = await prisma.v_telegram_message_mapping.findUnique({
    where: { internal_message_id: message.message_id }
  })

  if (telegramMapping) {
    // This is a Telegram message - get contact info through room bridge
    const telegramRoom = await prisma.v_telegram_chat_rooms.findFirst({
      where: { internal_room_uuid: roomId },
      include: {
        v_telegram_contacts: {
          select: {
            first_name: true,
            last_name: true,
            username: true,
            telegram_user_id: true
          }
        }
      }
    })

    if (telegramRoom?.v_telegram_contacts) {
      const contact = telegramRoom.v_telegram_contacts

      const displayName =
        contact.first_name && contact.last_name
          ? `${contact.first_name} ${contact.last_name}`.trim()
          : contact.first_name || contact.username || `Telegram User ${contact.telegram_user_id}`

      return {
        author_name: displayName,
        author_email: ''
      }
    }
  }

  // Check for ZALO mapping
  const zaloMapping = await prisma.v_zalo_message_mapping.findUnique({
    where: { internal_message_id: message.message_id }
  })

  if (zaloMapping) {
    // This is a ZALO message - get contact info through room bridge
    const zaloRoom = await prisma.v_zalo_oa_chat_rooms.findFirst({
      where: { internal_room_uuid: roomId },
      include: {
        v_zalo_oa_contacts: {
          select: {
            display_name: true,
            zalo_user_id: true
          }
        }
      }
    })

    if (zaloRoom?.v_zalo_oa_contacts) {
      const contact = zaloRoom.v_zalo_oa_contacts

      return {
        author_name: contact.display_name || `ZALO User ${contact.zalo_user_id}`,
        author_email: ''
      }
    }
  }

  // Fallback for unknown external messages
  return {
    author_name: 'External User',
    author_email: ''
  }
}

// Utility function to safely convert BigInt to string
const safeBigIntToString = (value: any): string | null => {
  if (value === null || value === undefined) return null
  if (typeof value === 'bigint') return value.toString()

  return value
}

// Recursively convert all BigInt values in an object to strings
const convertBigIntToString = (obj: any): any => {
  if (obj === null || obj === undefined) return obj
  if (typeof obj === 'bigint') return obj.toString()
  if (Array.isArray(obj)) return obj.map(convertBigIntToString)

  if (typeof obj === 'object') {
    const converted: any = {}

    for (const [key, value] of Object.entries(obj)) {
      converted[key] = convertBigIntToString(value)
    }

    return converted
  }

  return obj
}

// GET /api/internal-chat/rooms/[roomId]/messages
export async function GET(request: NextRequest, { params }: { params: { roomId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { roomId } = params
    const { searchParams } = new URL(request.url)

    // Validate roomId is a valid UUID
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i

    if (!uuidRegex.test(roomId)) {
      console.error('Invalid roomId format:', roomId)

      return NextResponse.json({ error: 'Invalid room ID format' }, { status: 400 })
    }

    const limit = parseInt(searchParams.get('limit') || '50')
    const before = searchParams.get('before') // Message ID for pagination
    const after = searchParams.get('after') // Message ID for pagination

    // Check if user is participant in the room (including deleted ones for message access)
    const participant = await prisma.v_chat_room_participants.findFirst({
      where: {
        room_uuid: roomId,
        user_uuid: session.user!.id
      }
    })

    if (!participant) {
      return NextResponse.json({ error: 'Room not found or access denied' }, { status: 404 })
    }

    // If room was soft deleted, check if we should restore it
    // Only restore if there are new messages after deletion (indicating new conversation activity)
    if (participant.deleted_at) {
      const messagesAfterDeletion = await prisma.v_chat_messages.count({
        where: {
          room_uuid: roomId,
          created_at: {
            gt: Math.floor(participant.deleted_at.getTime() / 1000)
          }
        }
      })

      // If there are messages after deletion, restore the conversation
      if (messagesAfterDeletion > 0) {
        await prisma.v_chat_room_participants.update({
          where: { participant_uuid: participant.participant_uuid },
          data: {
            deleted_at: null,
            update_date: new Date()
          }
        })
      }
    }

    // Build where clause for pagination
    const whereClause: any = {
      room_uuid: roomId
    }

    if (before) {
      whereClause.message_id = { lt: BigInt(before) }
    } else if (after) {
      whereClause.message_id = { gt: BigInt(after) }
    }

    // Optimized query: Get messages with minimal includes to reduce N+1 problems
    const allMessages = await prisma.v_chat_messages.findMany({
      where: whereClause,
      select: {
        message_id: true,
        room_uuid: true,
        author_uuid: true,
        content: true,
        message_type: true,
        reply_to: true,
        edited_at: true,
        created_at: true,
        flags: true,

        // Only include attachments if needed
        v_chat_message_attachments: {
          select: {
            attachment_id: true,
            filename: true,
            file_size: true,
            content_type: true,
            file_path: true
          }
        },

        // Simplified reactions without nested user data (fetch separately if needed)
        v_chat_message_reactions: {
          select: {
            user_uuid: true,
            emoji: true,
            created_at: true
          }
        },

        // Reply parent with minimal data
        v_chat_messages: {
          select: {
            message_id: true,
            content: true,
            author_uuid: true,
            message_type: true,
            created_at: true
          }
        }
      },
      orderBy: { message_id: before ? 'desc' : 'asc' },
      take: limit // Use exact limit instead of limit * 2
    })

    // Filter messages based on participant deletion timestamp and message deletion flags
    const messagesWithData = allMessages
      .filter(message => {
        // First check if message is deleted using bitwise operation
        if (message.flags && (message.flags & MESSAGE_FLAGS.DELETED) !== 0) {
          return false // Message is deleted, don't show
        }

        // If participant has deleted the conversation, only show messages after deletion timestamp
        if (participant.deleted_at) {
          const messageTimestamp = new Date(Number(message.created_at) * 1000) // Convert BigInt Unix timestamp to Date

          return messageTimestamp > participant.deleted_at
        }

        return true // Show message if not deleted and participant hasn't deleted conversation
      })
      .slice(0, limit) // Take only the requested number of messages

    // If we're paginating backwards (before), reverse the results
    if (before) {
      messagesWithData.reverse()
    }

    // Format messages for response - get author info for each message
    const formattedMessages = await Promise.all(
      messagesWithData.map(async message => {
        const authorInfo = await getMessageAuthorInfo(message, roomId)

        // Get reply parent author info if exists
        let replyParentAuthorName = null

        if (message.v_chat_messages) {
          const replyAuthorInfo = await getMessageAuthorInfo(message.v_chat_messages, roomId)

          replyParentAuthorName = replyAuthorInfo.author_name
        }

        return {
          message_id: safeBigIntToString(message.message_id),
          room_uuid: message.room_uuid,
          author_uuid: message.author_uuid,
          content: message.content,
          message_type: message.message_type,
          reply_to: safeBigIntToString(message.reply_to),
          edited_at: message.edited_at,
          created_at: message.created_at,
          flags: message.flags,

          // Author details - now properly handles external platform messages
          author_name: authorInfo.author_name,
          author_email: authorInfo.author_email,

          // Computed properties
          is_deleted: message.flags ? (message.flags & MESSAGE_FLAGS.DELETED) !== 0 : false,
          is_edited: message.edited_at !== null,
          is_pinned: message.flags ? (message.flags & MESSAGE_FLAGS.PINNED) !== 0 : false,
          is_system: message.flags ? (message.flags & MESSAGE_FLAGS.SYSTEM) !== 0 : false,
          is_urgent: message.flags ? (message.flags & MESSAGE_FLAGS.URGENT) !== 0 : false,

          // Attachments
          attachments: message.v_chat_message_attachments.map(att => ({
            attachment_id: safeBigIntToString(att.attachment_id),
            filename: att.filename,
            file_size: safeBigIntToString(att.file_size),
            content_type: att.content_type,
            file_path: att.file_path
          })),

          // Reactions (simplified without user names to avoid N+1)
          reactions: message.v_chat_message_reactions.map(reaction => ({
            user_uuid: reaction.user_uuid,
            emoji: reaction.emoji,
            created_at: reaction.created_at
          })),

          // Reply parent - now with proper author name
          reply_parent: message.v_chat_messages
            ? {
                message_id: safeBigIntToString(message.v_chat_messages.message_id),
                content: message.v_chat_messages.content,
                author_name: replyParentAuthorName || 'Unknown User',
                message_type: message.v_chat_messages.message_type
              }
            : null
        }
      })
    )

    // Check if there are more messages
    const hasMore = messagesWithData.length === limit
    const oldestMessageId = messagesWithData.length > 0 ? safeBigIntToString(messagesWithData[0].message_id) : null

    const newestMessageId =
      messagesWithData.length > 0 ? safeBigIntToString(messagesWithData[messagesWithData.length - 1].message_id) : null

    const response = {
      success: true,
      data: formattedMessages,
      pagination: {
        limit,
        has_more: hasMore,
        oldest_message_id: oldestMessageId,
        newest_message_id: newestMessageId,
        before: before,
        after: after
      }
    }

    return NextResponse.json(convertBigIntToString(response))
  } catch (error) {
    console.error('Error fetching messages:', error)

    return NextResponse.json({ error: 'Failed to fetch messages' }, { status: 500 })
  }
}

// POST /api/internal-chat/rooms/[roomId]/messages
export async function POST(request: NextRequest, { params }: { params: { roomId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { roomId } = params

    // Validate roomId is a valid UUID
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i

    if (!uuidRegex.test(roomId)) {
      console.error('Invalid roomId format:', roomId)

      return NextResponse.json({ error: 'Invalid room ID format' }, { status: 400 })
    }

    const body: SendMessageRequest = await request.json()
    const { content, message_type = MessageType.TEXT, reply_to } = body

    // Validate message content
    if (!content || content.trim().length === 0) {
      return NextResponse.json({ error: 'Message content cannot be empty' }, { status: 400 })
    }

    if (content.length > 4000) {
      return NextResponse.json({ error: 'Message too long (max 4000 characters)' }, { status: 400 })
    }

    // Check if user is participant in the room (including hidden ones)
    const participant = await prisma.v_chat_room_participants.findFirst({
      where: {
        room_uuid: roomId,
        user_uuid: session.user!.id
      }
    })

    if (!participant) {
      return NextResponse.json({ error: 'Room not found or access denied' }, { status: 404 })
    }

    // If room was soft deleted for sender, check if we should restore it
    if (participant.deleted_at) {
      // Check if there are any messages after the deletion timestamp
      const messagesAfterDeletion = await prisma.v_chat_messages.count({
        where: {
          room_uuid: roomId,
          created_at: {
            gt: Math.floor(participant.deleted_at.getTime() / 1000)
          }
        }
      })

      // Only restore if this is a new conversation (no messages after deletion)
      // This prevents race conditions where deleted conversations reappear
      if (messagesAfterDeletion === 0) {
        await prisma.v_chat_room_participants.update({
          where: { participant_uuid: participant.participant_uuid },
          data: {
            deleted_at: null,
            update_date: new Date()
          }
        })
      } else {
        // Don't restore - user explicitly deleted this conversation and there are already messages
        return NextResponse.json(
          {
            error: 'Cannot send message to deleted conversation. Please restore the conversation first.'
          },
          { status: 403 }
        )
      }
    }

    // Validate reply_to message exists if provided
    if (reply_to) {
      const parentMessage = await prisma.v_chat_messages.findFirst({
        where: {
          message_id: BigInt(reply_to),
          room_uuid: roomId
        }
      })

      if (!parentMessage) {
        return NextResponse.json({ error: 'Reply target message not found' }, { status: 400 })
      }
    }

    // Check if this is the first message in the room BEFORE creating the message
    const messageCount = await prisma.v_chat_messages.count({
      where: { room_uuid: roomId }
    })

    const isFirstMessage = messageCount === 0

    // Create message in transaction
    const result = await prisma.$transaction(async tx => {
      // Create the message first
      const message = await tx.v_chat_messages.create({
        data: {
          room_uuid: roomId,
          author_uuid: session.user!.id,
          content: content.trim(),
          message_type,
          reply_to: reply_to ? BigInt(reply_to) : null,
          created_at: Math.floor(Date.now() / 1000),
          flags: 0
        },
        include: {
          v_chat_messages: true // Reply parent messages (without user info for now)
        }
      })

      // Get other participants for notifications (exclude sender)
      // Note: deleted_at restoration was already handled above for all participants
      const otherParticipants = await tx.v_chat_room_participants.findMany({
        where: {
          room_uuid: roomId,
          user_uuid: { not: session.user!.id }
        },
        select: {
          user_uuid: true,
          is_muted: true,
          notification_settings: true
        }
      })

      // Create notifications for other participants
      const notificationsToCreate = otherParticipants
        .filter(participant => {
          // Skip if user has muted the room
          if (participant.is_muted) return false

          // Check notification settings
          const settings = participant.notification_settings as any

          return settings?.all_messages !== false
        })
        .map(participant => ({
          user_uuid: participant.user_uuid,
          room_uuid: roomId,
          message_id: message.message_id,
          notification_type: 'message' as const,
          is_read: false,
          is_sent: false,
          insert_date: new Date(),
          expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
        }))

      if (notificationsToCreate.length > 0) {
        await tx.v_chat_notifications.createMany({
          data: notificationsToCreate
        })
      }

      // Ensure all participants have unread count records, then update counts
      // This uses the SINGLE SOURCE OF TRUTH for unread counts
      const otherParticipantUuids = otherParticipants.map(p => p.user_uuid)

      // Initialize missing unread count records and update counts
      // First, get existing unread count records for all participants
      const existingCounts = await tx.v_chat_unread_counts.findMany({
        where: {
          room_uuid: roomId,
          user_uuid: { in: [...otherParticipantUuids, session.user!.id] }
        }
      })

      const existingUserIds = new Set(existingCounts.map(c => c.user_uuid))

      // Create missing records for participants who don't have them
      const missingParticipants = [...otherParticipantUuids, session.user!.id].filter(
        uuid => !existingUserIds.has(uuid)
      )

      if (missingParticipants.length > 0) {
        const newCountRecords = missingParticipants.map(uuid => ({
          user_uuid: uuid,
          room_uuid: roomId,
          unread_count: uuid === session.user!.id ? 0 : 1, // Sender gets 0, others get 1
          last_updated: new Date()
        }))

        await tx.v_chat_unread_counts.createMany({
          data: newCountRecords
        })
      }

      // Update existing records for other participants (increment by 1)
      if (otherParticipantUuids.length > 0) {
        await tx.v_chat_unread_counts.updateMany({
          where: {
            room_uuid: roomId,
            user_uuid: { in: otherParticipantUuids }
          },
          data: {
            unread_count: { increment: 1 },
            last_updated: new Date()
          }
        })
      }

      // Update room's last activity
      await tx.v_chat_rooms.update({
        where: { room_uuid: roomId },
        data: {
          update_date: new Date(),
          update_user: session.user!.id
        }
      })

      return message
    })

    // Format response - get proper author info
    const authorInfo = await getMessageAuthorInfo(result, roomId)

    // Get reply parent author info if exists
    let replyParentAuthorName = null

    if (result.v_chat_messages) {
      const replyAuthorInfo = await getMessageAuthorInfo(result.v_chat_messages, roomId)

      replyParentAuthorName = replyAuthorInfo.author_name
    }

    const formattedMessage = {
      message_id: safeBigIntToString(result.message_id),
      room_uuid: result.room_uuid,
      author_uuid: result.author_uuid,
      content: result.content,
      message_type: result.message_type,
      reply_to: safeBigIntToString(result.reply_to),
      edited_at: result.edited_at,
      created_at: result.created_at,
      flags: result.flags,

      // Author details - now properly handles both internal and external messages
      author_name: authorInfo.author_name,
      author_email: authorInfo.author_email,

      // Computed properties
      is_deleted: false,
      is_edited: false,
      is_pinned: false,
      is_system: false,
      is_urgent: false,

      // Empty arrays for new message
      attachments: [],
      reactions: [],

      // Reply parent - now with proper author name
      reply_parent: result.v_chat_messages
        ? {
            message_id: safeBigIntToString(result.v_chat_messages.message_id),
            content: result.v_chat_messages.content,
            author_name: replyParentAuthorName || 'Unknown User',
            message_type: result.v_chat_messages.message_type
          }
        : null
    }

    const response = {
      success: true,
      data: formattedMessage,
      message: 'Message sent successfully'
    }

    // Broadcast message to connected clients via Socket.IO
    try {
      if ((global as any).socketBroadcast && (global as any).socketBroadcast.broadcastMessage) {
        // Get room data including type and name
        const roomData = await prisma.v_chat_rooms.findUnique({
          where: { room_uuid: roomId },
          select: {
            domain_uuid: true,
            room_type: true,
            room_name: true
          }
        })

        if (roomData?.domain_uuid) {
          // Convert any remaining BigInt values before broadcasting
          const safeMessage = convertBigIntToString(formattedMessage)

          // Get updated unread counts for all participants after database update
          const unreadCounts = await prisma.v_chat_unread_counts.findMany({
            where: {
              room_uuid: roomId
            },
            select: {
              user_uuid: true,
              unread_count: true
            }
          })

          // Create a map of user_uuid -> unread_count
          const unreadCountMap = unreadCounts.reduce(
            (acc: Record<string, number>, count: any) => {
              acc[count.user_uuid] = count.unread_count || 0

              return acc
            },
            {} as Record<string, number>
          )

          // Add unread count to the message for each recipient
          const messageWithUnreadCount = {
            ...safeMessage,
            unread_counts: unreadCountMap
          }

          // Broadcast message to room participants
          ;(global as any).socketBroadcast.broadcastMessage(roomId, messageWithUnreadCount, roomData.domain_uuid)

          // Get ALL participants (including sender) for room joining
          const allParticipants = await prisma.v_chat_room_participants.findMany({
            where: {
              room_uuid: roomId
            },
            select: {
              user_uuid: true,
              is_muted: true,
              notification_settings: true
            }
          })

          // Get other participants (excluding sender) for notifications
          const otherParticipants = allParticipants.filter(p => p.user_uuid !== session.user!.id)

          // Ensure ALL participants (including sender) are joined to the socket room for every message
          // This fixes the issue where users don't receive messages if they weren't properly joined initially
          const roomName = `${roomData.domain_uuid}_room_${roomId}`

          // Track room participants and ensure they're joined to socket room
          if (!(global as any).socketBroadcast.roomParticipants.has(roomId)) {
            ;(global as any).socketBroadcast.roomParticipants.set(roomId, new Set())
          }

          // Ensure ALL participants (including sender) are joined to the socket room
          allParticipants.forEach(participant => {
            // Track room participants
            ;(global as any).socketBroadcast.roomParticipants.get(roomId).add(participant.user_uuid)

            // Join connected users to the socket room
            if ((global as any).socketBroadcast.userSockets?.has(participant.user_uuid)) {
              const userSocketIds = (global as any).socketBroadcast.userSockets.get(participant.user_uuid)

              userSocketIds.forEach((socketId: string) => {
                const socketConnection = (global as any).socketBroadcast.connectedUsers.get(socketId)

                if (socketConnection) {
                  // Join the participant to the room
                  socketConnection.socket.join(roomName)
                  console.log(`Ensured user ${participant.user_uuid} is joined to room ${roomName}`)
                }
              })
            }
          })

          // Get complete room data for new room broadcast if this is the first message
          let completeRoomData = null

          if (isFirstMessage) {
            try {
              completeRoomData = await prisma.v_chat_rooms.findUnique({
                where: { room_uuid: roomId },
                include: {
                  v_chat_room_participants: {
                    include: {
                      v_users: {
                        select: {
                          user_uuid: true,
                          username: true,
                          user_email: true
                        }
                      }
                    }
                  }
                }
              })

              // Get unread counts for all participants in this room
              if (completeRoomData) {
                const participantUuids = completeRoomData.v_chat_room_participants.map(p => p.user_uuid)

                const unreadCounts = await prisma.v_chat_unread_counts.findMany({
                  where: {
                    room_uuid: roomId,
                    user_uuid: { in: participantUuids }
                  }
                })

                // Create a map of user_uuid -> unread_count
                const unreadCountMap = unreadCounts.reduce(
                  (acc, count) => {
                    acc[count.user_uuid] = count.unread_count || 0

                    return acc
                  },
                  {} as Record<string, number>
                )

                // Transform room data to match frontend expectations
                completeRoomData = {
                  room_uuid: completeRoomData.room_uuid,
                  domain_uuid: completeRoomData.domain_uuid,
                  room_name: completeRoomData.room_name,
                  room_description: completeRoomData.room_description,
                  room_type: completeRoomData.room_type,
                  room_avatar: completeRoomData.room_avatar,
                  created_by_user_uuid: completeRoomData.created_by_user_uuid,
                  is_active: completeRoomData.is_active,
                  is_archived: completeRoomData.is_archived,
                  max_participants: completeRoomData.max_participants,
                  room_settings: completeRoomData.room_settings,
                  insert_date: completeRoomData.insert_date?.toISOString() || null,
                  update_date: completeRoomData.update_date?.toISOString() || null,

                  // Transform participants to match frontend format
                  participants: completeRoomData.v_chat_room_participants.map(p => ({
                    participant_uuid: p.participant_uuid,
                    user_uuid: p.user_uuid,
                    participant_role: p.participant_role,
                    user_name: p.v_users.username,
                    user_email: p.v_users.user_email,
                    is_muted: p.is_muted,
                    joined_date: p.joined_date?.toISOString() || null,
                    last_read_message_id: p.last_read_message_id?.toString() || null
                  })),

                  // Add unread counts
                  unread_counts: unreadCountMap
                }
              }

              // For new rooms, automatically join participants to the socket room
              if (completeRoomData && (global as any).socketBroadcast) {
                console.log(`Processing first message for room ${roomId}, broadcasting new room to participants`)

                // Broadcast new room to all participants (both online and offline will get it when they connect)
                if (completeRoomData) {
                  const safeRoomData = convertBigIntToString(completeRoomData)

                  // Emit new room event to all participants
                  otherParticipants.forEach(participant => {
                    if ((global as any).socketBroadcast.userSockets?.has(participant.user_uuid)) {
                      const userSocketIds = (global as any).socketBroadcast.userSockets.get(participant.user_uuid)

                      userSocketIds.forEach((socketId: string) => {
                        const socketConnection = (global as any).socketBroadcast.connectedUsers.get(socketId)

                        if (socketConnection) {
                          socketConnection.socket.emit('new_room', safeRoomData)
                          console.log(`Sent new room notification to user ${participant.user_uuid}`)
                        }
                      })
                    }
                  })
                }
              }
            } catch (err) {
              console.error('Error fetching room data for new room broadcast:', err)
            }
          }

          // Send notification events to each participant
          console.log(
            `Sending notifications to ${otherParticipants.length} participants for ${isFirstMessage ? 'new room' : 'message'}`
          )

          otherParticipants.forEach(participant => {
            // Skip if user has muted the room
            if (participant.is_muted) {
              console.log(`Skipping muted user: ${participant.user_uuid}`)

              return
            }

            // Check notification settings
            const settings = participant.notification_settings as any

            if (settings?.all_messages === false) {
              console.log(`Skipping user with disabled notifications: ${participant.user_uuid}`)

              return
            }

            // Determine notification type and content based on whether this is first message
            const notificationType = isFirstMessage ? 'room_invite' : 'message'

            const notificationContent = isFirstMessage
              ? `You've been added to ${roomData.room_type === 'direct' ? 'a conversation' : `room "${roomData.room_name}"`}`
              : formattedMessage.content

            // Create notification object
            const notification = {
              notification_uuid: `temp_${Date.now()}_${participant.user_uuid}`,
              user_uuid: participant.user_uuid,
              room_uuid: roomId,
              message_id: formattedMessage.message_id,
              notification_type: notificationType,
              is_read: false,
              is_sent: true,
              insert_date: new Date().toISOString(),

              // Additional data for frontend
              message_content: notificationContent,
              author_name: formattedMessage.author_name,
              room_name: roomData.room_name || 'Chat'
            }

            // Log notification preparation
            console.log(`Preparing notification for user ${participant.user_uuid}:`, {
              type: notificationType,
              content: notificationContent,
              isFirstMessage
            })

            // Send notification to specific user via socket
            if ((global as any).socketBroadcast.userSockets?.has(participant.user_uuid)) {
              const userSocketIds = (global as any).socketBroadcast.userSockets.get(participant.user_uuid)

              console.log(`Found ${userSocketIds.size} socket connections for user ${participant.user_uuid}`)

              userSocketIds.forEach((socketId: string) => {
                const socketConnection = (global as any).socketBroadcast.connectedUsers.get(socketId)

                if (socketConnection) {
                  console.log(`Sending notification to socket ${socketId} for user ${participant.user_uuid}`)
                  socketConnection.socket.emit('notification', notification)

                  // If this is the first message, also broadcast the new room so it appears in room list
                  if (isFirstMessage && completeRoomData) {
                    console.log(`Sending new_room event to socket ${socketId} for user ${participant.user_uuid}`)
                    socketConnection.socket.emit('new_room', completeRoomData)
                  }
                } else {
                  console.log(`No socket connection found for socket ID ${socketId}`)
                }
              })
            } else {
              console.log(`No socket connections found for user ${participant.user_uuid}`)
            }
          })

          // Create database notifications for room invites if this is the first message
          if (isFirstMessage && otherParticipants.length > 0 && formattedMessage.message_id) {
            try {
              const roomNotifications = otherParticipants
                .filter(p => !p.is_muted) // Don't create notifications for muted users
                .map(participant => ({
                  user_uuid: participant.user_uuid,
                  room_uuid: roomId,
                  message_id: BigInt(formattedMessage.message_id!),
                  notification_type: 'room_invite' as const,
                  is_read: false,
                  is_sent: false,
                  insert_date: new Date(),
                  expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
                }))

              // Create notifications in database if any exist
              if (roomNotifications.length > 0) {
                await prisma.v_chat_notifications.createMany({
                  data: roomNotifications
                })
              }
            } catch (notificationError) {
              console.error('Error creating room invite notifications:', notificationError)

              // Don't fail the message send if notification creation fails
            }
          }
        }
      }
    } catch (broadcastError) {
      console.error('Failed to broadcast message via socket:', broadcastError)

      // Don't fail the API call if broadcast fails
    }

    // Note: Platform-specific message forwarding (Telegram, Facebook, Zalo) is handled
    // by their respective SmartMessageInput components using dedicated API endpoints.
    // This prevents duplicate message creation and ensures proper platform integration.

    return NextResponse.json(convertBigIntToString(response), { status: 201 })
  } catch (error) {
    console.error('Error sending message:', error)

    return NextResponse.json({ error: 'Failed to send message' }, { status: 500 })
  }
}
