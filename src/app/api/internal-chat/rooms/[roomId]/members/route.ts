// Group Member Management API
// Facebook Messenger-inspired member management with role-based permissions

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'
import type { ParticipantRole } from '@/types/apps/internal-chat/chatTypes'

// Recursively convert all BigInt values in an object to strings
const convertBigIntToString = (obj: any): any => {
  if (obj === null || obj === undefined) return obj
  if (typeof obj === 'bigint') return obj.toString()
  if (Array.isArray(obj)) return obj.map(convertBigIntToString)

  if (typeof obj === 'object') {
    const converted: any = {}

    for (const [key, value] of Object.entries(obj)) {
      converted[key] = convertBigIntToString(value)
    }

    return converted
  }

  return obj
}

// GET /api/internal-chat/rooms/[roomId]/members
export async function GET(_request: NextRequest, { params }: { params: { roomId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { roomId } = params

    // Verify user is a participant in the room
    const userParticipation = await prisma.v_chat_room_participants.findFirst({
      where: {
        room_uuid: roomId,
        user_uuid: session.user.id,
        deleted_at: null
      }
    })

    if (!userParticipation) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // Get all room members with their details
    const members = await prisma.v_chat_room_participants.findMany({
      where: {
        room_uuid: roomId,
        deleted_at: null
      },
      include: {
        v_users: {
          select: {
            user_uuid: true,
            username: true,
            user_email: true

            // Add other user fields as needed
          }
        }
      },
      orderBy: [
        { participant_role: 'asc' }, // Owners first, then admins, etc.
        { joined_date: 'asc' }
      ]
    })

    // Get user presence information
    const userUuids = members.map(m => m.user_uuid)

    const presenceData = await prisma.v_chat_user_presence.findMany({
      where: {
        user_uuid: { in: userUuids }
      },
      select: {
        user_uuid: true,
        status: true,
        last_seen: true,
        current_activity: true
      }
    })

    const presenceMap = presenceData.reduce(
      (acc, p) => {
        acc[p.user_uuid] = p

        return acc
      },
      {} as Record<string, any>
    )

    // Format response
    const formattedMembers = members.map(member => ({
      participant_uuid: member.participant_uuid,
      user_uuid: member.user_uuid,
      username: member.v_users.username,
      user_email: member.v_users.user_email,
      participant_role: member.participant_role,
      joined_date: member.joined_date,
      is_muted: member.is_muted,
      notification_settings: member.notification_settings,
      presence: presenceMap[member.user_uuid] || {
        status: 'offline',
        last_seen: null,
        current_activity: null
      }
    }))

    return NextResponse.json(
      convertBigIntToString({
        success: true,
        data: {
          members: formattedMembers,
          total_count: formattedMembers.length,
          current_user_role: userParticipation.participant_role
        }
      })
    )
  } catch (error) {
    console.error('Error fetching group members:', error)

    return NextResponse.json({ error: 'Failed to fetch group members' }, { status: 500 })
  }
}

// POST /api/internal-chat/rooms/[roomId]/members - Add members to group
export async function POST(request: NextRequest, { params }: { params: { roomId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { roomId } = params
    const body = await request.json()
    const { user_uuids, role = 'member', send_notification = true } = body

    // Validate input
    if (!Array.isArray(user_uuids) || user_uuids.length === 0) {
      return NextResponse.json({ error: 'user_uuids must be a non-empty array' }, { status: 400 })
    }

    if (user_uuids.length > 50) {
      return NextResponse.json({ error: 'Cannot add more than 50 members at once' }, { status: 400 })
    }

    // Get room and verify permissions
    const room = await prisma.v_chat_rooms.findUnique({
      where: { room_uuid: roomId },
      include: {
        v_chat_room_participants: {
          where: { deleted_at: null }
        }
      }
    })

    if (!room) {
      return NextResponse.json({ error: 'Room not found' }, { status: 404 })
    }

    // Check if user has permission to add members
    const currentUserParticipation = room.v_chat_room_participants.find(p => p.user_uuid === session.user!.id)

    if (!currentUserParticipation) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // Check permissions based on room settings and user role
    const roomSettings = (room.room_settings as any) || {}

    const canAddMembers = checkAddMemberPermission(
      currentUserParticipation.participant_role as ParticipantRole,
      roomSettings
    )

    if (!canAddMembers) {
      return NextResponse.json({ error: 'Insufficient permissions to add members' }, { status: 403 })
    }

    // Check room member limit
    const currentMemberCount = room.v_chat_room_participants.length
    const maxParticipants = room.max_participants || 250

    if (currentMemberCount + user_uuids.length > maxParticipants) {
      return NextResponse.json(
        {
          error: `Cannot exceed maximum of ${maxParticipants} members`
        },
        { status: 400 }
      )
    }

    // Verify all users exist and are in the same domain
    const usersToAdd = await prisma.v_users.findMany({
      where: {
        user_uuid: { in: user_uuids },
        domain_uuid: room.domain_uuid
      },
      select: {
        user_uuid: true,
        username: true,
        user_email: true
      }
    })

    if (usersToAdd.length !== user_uuids.length) {
      return NextResponse.json({ error: 'Some users not found or not in same domain' }, { status: 400 })
    }

    // Check for existing participants (both active and soft-deleted)
    const allParticipants = await prisma.v_chat_room_participants.findMany({
      where: {
        room_uuid: roomId,
        user_uuid: { in: user_uuids }
      }
    })

    const activeParticipants = allParticipants.filter(p => p.deleted_at === null)
    const deletedParticipants = allParticipants.filter(p => p.deleted_at !== null)

    const activeUserUuids = activeParticipants.map(p => p.user_uuid)
    const deletedUserUuids = deletedParticipants.map(p => p.user_uuid)
    const newUserUuids = user_uuids.filter(uuid => !activeUserUuids.includes(uuid) && !deletedUserUuids.includes(uuid))

    if (newUserUuids.length === 0 && deletedUserUuids.length === 0) {
      return NextResponse.json(
        {
          success: true,
          data: {
            added_members: [],
            system_message: null,
            skipped_existing: activeUserUuids
          },
          message: 'All specified users are already members'
        },
        { status: 200 }
      )
    }

    // Add new members in transaction
    const result = await prisma.$transaction(async tx => {
      // Add participants
      const newParticipants = await Promise.all(
        newUserUuids.map(user_uuid =>
          tx.v_chat_room_participants.create({
            data: {
              room_uuid: roomId,
              user_uuid,
              participant_role: role,
              insert_user: session.user!.id,
              update_user: session.user!.id,
              notification_settings: {
                mentions: true,
                all_messages: true,
                member_additions: true,
                member_removals: true,
                role_changes: true,
                group_info_changes: true
              }
            },
            include: {
              v_users: {
                select: {
                  user_uuid: true,
                  username: true,
                  user_email: true
                }
              }
            }
          })
        )
      )

      // Create system message for member additions
      const addedUsernames = newParticipants.map(p => p.v_users.username).join(', ')

      const systemMessage = await tx.v_chat_messages.create({
        data: {
          room_uuid: roomId,
          author_uuid: session.user!.id,
          content: `Added ${addedUsernames} to the group`,
          message_type: 3, // System message
          created_at: Math.floor(Date.now() / 1000),
          flags: 4 // System flag
        }
      })

      // Initialize unread counts for new members
      await Promise.all(
        newUserUuids.map(user_uuid =>
          tx.v_chat_unread_counts.create({
            data: {
              user_uuid,
              room_uuid: roomId,
              unread_count: 0,
              last_read_message_id: systemMessage.message_id
            }
          })
        )
      )

      // Create notifications for new members if enabled
      if (send_notification) {
        await Promise.all(
          newUserUuids.map(user_uuid =>
            tx.v_chat_notifications.create({
              data: {
                user_uuid,
                room_uuid: roomId,
                message_id: systemMessage.message_id,
                notification_type: 'room_invite'
              }
            })
          )
        )
      }

      return { newParticipants, systemMessage }
    })

    return NextResponse.json(
      convertBigIntToString({
        success: true,
        data: {
          added_members: result.newParticipants.map(p => ({
            participant_uuid: p.participant_uuid,
            user_uuid: p.user_uuid,
            username: p.v_users.username,
            user_email: p.v_users.user_email,
            participant_role: p.participant_role,
            joined_date: p.joined_date
          })),
          system_message: result.systemMessage

          // skipped_existing: existingUserUuids
        }
      })
    )
  } catch (error) {
    console.error('Error adding group members:', error)

    return NextResponse.json({ error: 'Failed to add group members' }, { status: 500 })
  }
}

// Helper function to check add member permissions
function checkAddMemberPermission(userRole: ParticipantRole, roomSettings: any): boolean {
  // Owners and admins can always add members
  if (userRole === 'owner' || userRole === 'admin') {
    return true
  }

  // Check room settings for member permissions
  const allowMembersToAdd = roomSettings.allow_members_to_add_others !== false // Default true

  if (userRole === 'moderator') {
    return allowMembersToAdd
  }

  if (userRole === 'member') {
    return allowMembersToAdd
  }

  return false
}
