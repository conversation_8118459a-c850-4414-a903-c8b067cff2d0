// Individual Group Member Management API
// Handle member removal and role changes

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'
import { ParticipantRole } from '@/types/apps/internal-chat/chatTypes'

// Recursively convert all BigInt values in an object to strings
const convertBigIntToString = (obj: any): any => {
  if (obj === null || obj === undefined) return obj
  if (typeof obj === 'bigint') return obj.toString()
  if (Array.isArray(obj)) return obj.map(convertBigIntToString)

  if (typeof obj === 'object') {
    const converted: any = {}

    for (const [key, value] of Object.entries(obj)) {
      converted[key] = convertBigIntToString(value)
    }

    return converted
  }

  return obj
}

// DELETE /api/internal-chat/rooms/[roomId]/members/[userId] - Remove member from group
export async function DELETE(request: NextRequest, { params }: { params: { roomId: string; userId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { roomId, userId } = params
    const isRemovingSelf = userId === session.user.id

    // Get room and current user's participation
    const room = await prisma.v_chat_rooms.findUnique({
      where: { room_uuid: roomId },
      include: {
        v_chat_room_participants: {
          where: { deleted_at: null }
        }
      }
    })

    if (!room) {
      return NextResponse.json({ error: 'Room not found' }, { status: 404 })
    }

    // Find current user and target user participations
    const currentUserParticipation = room.v_chat_room_participants.find(p => p.user_uuid === session.user?.id)

    const targetUserParticipation = room.v_chat_room_participants.find(p => p.user_uuid === userId)

    if (!currentUserParticipation) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    if (!targetUserParticipation) {
      return NextResponse.json({ error: 'User is not a member of this group' }, { status: 404 })
    }

    // Check permissions
    if (!isRemovingSelf) {
      const canRemove = checkRemoveMemberPermission(
        currentUserParticipation.participant_role as ParticipantRole,
        targetUserParticipation.participant_role as ParticipantRole
      )

      if (!canRemove) {
        return NextResponse.json({ error: 'Insufficient permissions to remove this member' }, { status: 403 })
      }
    }

    // Prevent removing the last owner
    if (targetUserParticipation.participant_role === 'owner') {
      const ownerCount = room.v_chat_room_participants.filter(p => p.participant_role === 'owner').length

      if (ownerCount <= 1) {
        return NextResponse.json(
          {
            error: 'Cannot remove the last owner. Transfer ownership first.'
          },
          { status: 400 }
        )
      }
    }

    // Get target user info for system message
    const targetUser = await prisma.v_users.findUnique({
      where: { user_uuid: userId },
      select: { username: true, user_email: true }
    })

    // Remove member in transaction
    const result = await prisma.$transaction(async tx => {
      // Hard delete the participation (completely remove from group)
      await tx.v_chat_room_participants.delete({
        where: { participant_uuid: targetUserParticipation.participant_uuid }
      })

      // Create system message
      const actionText = isRemovingSelf ? 'left the group' : `was removed from the group`

      const systemMessage = await tx.v_chat_messages.create({
        data: {
          room_uuid: roomId,
          author_uuid: session.user!.id,
          content: `${targetUser?.username || 'User'} ${actionText}`,
          message_type: 3, // System message
          created_at: Math.floor(Date.now() / 1000),
          flags: 4 // System flag
        }
      })

      // Clean up user's unread counts for this room
      await tx.v_chat_unread_counts.deleteMany({
        where: {
          user_uuid: userId,
          room_uuid: roomId
        }
      })

      // Clean up user's notifications for this room
      await tx.v_chat_notifications.deleteMany({
        where: {
          user_uuid: userId,
          room_uuid: roomId
        }
      })

      return { systemMessage }
    })

    return NextResponse.json(
      convertBigIntToString({
        success: true,
        data: {
          removed_user: {
            user_uuid: userId,
            username: targetUser?.username,
            was_self_removal: isRemovingSelf
          },
          system_message: result.systemMessage
        }
      })
    )
  } catch (error) {
    console.error('Error removing group member:', error)

    return NextResponse.json({ error: 'Failed to remove group member' }, { status: 500 })
  }
}

// PUT /api/internal-chat/rooms/[roomId]/members/[userId] - Update member role
export async function PUT(request: NextRequest, { params }: { params: { roomId: string; userId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { roomId, userId } = params
    const body = await request.json()
    const { new_role, notification_settings } = body

    // Validate new role
    const validRoles: ParticipantRole[] = [
      ParticipantRole.MEMBER,
      ParticipantRole.MODERATOR,
      ParticipantRole.ADMIN,
      ParticipantRole.OWNER
    ]

    if (new_role && !validRoles.includes(new_role)) {
      return NextResponse.json({ error: 'Invalid role' }, { status: 400 })
    }

    // Get room and participations
    const room = await prisma.v_chat_rooms.findUnique({
      where: { room_uuid: roomId },
      include: {
        v_chat_room_participants: {
          where: { deleted_at: null }
        }
      }
    })

    if (!room) {
      return NextResponse.json({ error: 'Room not found' }, { status: 404 })
    }

    const currentUserParticipation = room.v_chat_room_participants.find(p => p.user_uuid === session.user?.id)

    const targetUserParticipation = room.v_chat_room_participants.find(p => p.user_uuid === userId)

    if (!currentUserParticipation || !targetUserParticipation) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // Check permissions for role changes
    if (new_role) {
      const canChangeRole = checkChangeRolePermission(
        currentUserParticipation.participant_role as ParticipantRole,
        targetUserParticipation.participant_role as ParticipantRole,
        new_role as ParticipantRole
      )

      if (!canChangeRole) {
        return NextResponse.json({ error: 'Insufficient permissions to change this role' }, { status: 403 })
      }

      // Special handling for ownership transfer
      if (new_role === 'owner') {
        if (currentUserParticipation.participant_role !== 'owner') {
          return NextResponse.json({ error: 'Only owners can transfer ownership' }, { status: 403 })
        }
      }
    }

    // Get target user info
    const targetUser = await prisma.v_users.findUnique({
      where: { user_uuid: userId },
      select: { username: true, user_email: true }
    })

    // Update member in transaction
    const result = await prisma.$transaction(async tx => {
      const updateData: any = {
        update_user: session.user!.id,
        update_date: new Date()
      }

      if (new_role) {
        updateData.participant_role = new_role
      }

      if (notification_settings) {
        updateData.notification_settings = notification_settings
      }

      // Update the participation
      const updatedParticipation = await tx.v_chat_room_participants.update({
        where: { participant_uuid: targetUserParticipation.participant_uuid },
        data: updateData,
        include: {
          v_users: {
            select: {
              user_uuid: true,
              username: true,
              user_email: true
            }
          }
        }
      })

      // Create system message for role changes
      let systemMessage = null

      if (new_role && new_role !== targetUserParticipation.participant_role) {
        const roleChangeText = getRoleChangeText(
          targetUserParticipation.participant_role as ParticipantRole,
          new_role as ParticipantRole
        )

        systemMessage = await tx.v_chat_messages.create({
          data: {
            room_uuid: roomId,
            author_uuid: session.user!.id,
            content: `${targetUser?.username || 'User'} ${roleChangeText}`,
            message_type: 3, // System message
            created_at: Math.floor(Date.now() / 1000),
            flags: 4 // System flag
          }
        })

        // If transferring ownership, demote current owner to admin
        if (new_role === 'owner' && currentUserParticipation.participant_role === 'owner') {
          await tx.v_chat_room_participants.update({
            where: { participant_uuid: currentUserParticipation.participant_uuid },
            data: {
              participant_role: 'admin',
              update_user: session.user!.id,
              update_date: new Date()
            }
          })
        }
      }

      return { updatedParticipation, systemMessage }
    })

    return NextResponse.json(
      convertBigIntToString({
        success: true,
        data: {
          updated_member: {
            participant_uuid: result.updatedParticipation.participant_uuid,
            user_uuid: result.updatedParticipation.user_uuid,
            username: result.updatedParticipation.v_users.username,
            user_email: result.updatedParticipation.v_users.user_email,
            participant_role: result.updatedParticipation.participant_role,
            notification_settings: result.updatedParticipation.notification_settings
          },
          system_message: result.systemMessage
        }
      })
    )
  } catch (error) {
    console.error('Error updating group member:', error)

    return NextResponse.json({ error: 'Failed to update group member' }, { status: 500 })
  }
}

// Helper functions
function checkRemoveMemberPermission(currentRole: ParticipantRole, targetRole: ParticipantRole): boolean {
  // Owners can remove anyone except other owners
  if (currentRole === 'owner') {
    return targetRole !== 'owner'
  }

  // Admins can remove moderators and members
  if (currentRole === 'admin') {
    return targetRole === 'moderator' || targetRole === 'member'
  }

  // Moderators can remove members only
  if (currentRole === 'moderator') {
    return targetRole === 'member'
  }

  return false
}

function checkChangeRolePermission(
  currentRole: ParticipantRole,
  targetCurrentRole: ParticipantRole,
  newRole: ParticipantRole
): boolean {
  // Only owners can change roles to/from owner
  if (newRole === 'owner' || targetCurrentRole === 'owner') {
    return currentRole === 'owner'
  }

  // Owners can change any role (except owner, handled above)
  if (currentRole === 'owner') {
    return true
  }

  // Admins can promote/demote moderators and members
  if (currentRole === 'admin') {
    return (
      (targetCurrentRole === 'moderator' || targetCurrentRole === 'member') &&
      (newRole === 'moderator' || newRole === 'member')
    )
  }

  return false
}

function getRoleChangeText(oldRole: ParticipantRole, newRole: ParticipantRole): string {
  const roleNames = {
    owner: 'owner',
    admin: 'admin',
    moderator: 'moderator',
    member: 'member'
  }

  if (newRole === 'owner') {
    return 'is now the group owner'
  }

  return `is now a group ${roleNames[newRole]}`
}
