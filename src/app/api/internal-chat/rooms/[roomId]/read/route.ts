// Internal Chat - Mark Messages as Read API
// PUT /api/internal-chat/rooms/[roomId]/read - Mark messages as read

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'

// PUT /api/internal-chat/rooms/[roomId]/read
export async function PUT(request: NextRequest, { params }: { params: { roomId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { roomId } = params
    const body = await request.json()
    const { last_read_message_id } = body

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 403 })
    }

    // Verify user is participant in the room
    const participant = await prisma.v_chat_room_participants.findFirst({
      where: {
        room_uuid: roomId,
        user_uuid: session.user.id,
        v_chat_rooms: {
          domain_uuid: user.domain_uuid
        }
      }
    })

    if (!participant) {
      return NextResponse.json({ error: 'Not a participant in this room' }, { status: 403 })
    }

    // Update participant's last read message
    if (last_read_message_id) {
      await prisma.v_chat_room_participants.update({
        where: {
          participant_uuid: participant.participant_uuid
        },
        data: {
          last_read_message_id: BigInt(last_read_message_id)
        }
      })
    }

    // Update unread count for this user and room using SINGLE SOURCE OF TRUTH
    // First check if record exists, then update or create
    const existingCount = await prisma.v_chat_unread_counts.findFirst({
      where: {
        user_uuid: session.user.id,
        room_uuid: roomId
      }
    })

    if (existingCount) {
      await prisma.v_chat_unread_counts.update({
        where: {
          count_uuid: existingCount.count_uuid
        },
        data: {
          unread_count: 0,
          last_read_message_id: last_read_message_id ? BigInt(last_read_message_id) : null,
          last_updated: new Date()
        }
      })
    } else {
      await prisma.v_chat_unread_counts.create({
        data: {
          user_uuid: session.user.id,
          room_uuid: roomId,
          unread_count: 0,
          last_read_message_id: last_read_message_id ? BigInt(last_read_message_id) : null,
          last_updated: new Date()
        }
      })
    }

    return NextResponse.json({
      success: true,
      message: 'Messages marked as read'
    })
  } catch (error) {
    console.error('Error marking messages as read:', error)

    return NextResponse.json({ error: 'Failed to mark messages as read' }, { status: 500 })
  }
}
