// Internal Chat - Individual Message Management API
// PUT /api/internal-chat/messages/[messageId] - Edit message
// DELETE /api/internal-chat/messages/[messageId] - Delete message

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'
import type { EditMessageRequest } from '@/types/apps/internal-chat/chatTypes'
import { MESSAGE_FLAGS, ParticipantRole } from '@/types/apps/internal-chat/chatTypes'

// PUT /api/internal-chat/messages/[messageId]
export async function PUT(request: NextRequest, { params }: { params: { messageId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { messageId } = params
    const body: EditMessageRequest = await request.json()
    const { content } = body

    // Validate content
    if (!content || content.trim().length === 0) {
      return NextResponse.json({ error: 'Message content cannot be empty' }, { status: 400 })
    }

    if (content.length > 4000) {
      return NextResponse.json({ error: 'Message too long (max 4000 characters)' }, { status: 400 })
    }

    // Get the message with room info
    const message = await prisma.v_chat_messages.findUnique({
      where: { message_id: BigInt(messageId) },
      include: {
        v_chat_rooms: true
      }
    })

    if (!message) {
      return NextResponse.json({ error: 'Message not found' }, { status: 404 })
    }

    // Check if message is already deleted
    if (message.flags && message.flags & MESSAGE_FLAGS.DELETED) {
      return NextResponse.json({ error: 'Cannot edit deleted message' }, { status: 400 })
    }

    // Check permissions - user must be message author or room admin/owner
    let canEdit = message.author_uuid === session.user.id

    if (!canEdit) {
      // Check if user is room admin or owner
      const participant = await prisma.v_chat_room_participants.findFirst({
        where: {
          room_uuid: message.room_uuid,
          user_uuid: session.user.id,
          participant_role: { in: [ParticipantRole.OWNER, ParticipantRole.ADMIN, ParticipantRole.MODERATOR] }
        }
      })

      canEdit = !!participant
    }

    if (!canEdit) {
      return NextResponse.json(
        { error: 'Permission denied. You can only edit your own messages or you must be a room moderator.' },
        { status: 403 }
      )
    }

    // Check if message is too old to edit (24 hours)
    const messageAge = Date.now() / 1000 - Number(message.created_at)
    const maxEditAge = 24 * 60 * 60 // 24 hours in seconds

    if (messageAge > maxEditAge && message.author_uuid === session.user.id) {
      return NextResponse.json({ error: 'Message is too old to edit (24 hour limit)' }, { status: 400 })
    }

    // Update the message
    const updatedMessage = await prisma.v_chat_messages.update({
      where: { message_id: BigInt(messageId) },
      data: {
        content: content.trim(),
        edited_at: Math.floor(Date.now() / 1000),
        flags: (message.flags || 0) | MESSAGE_FLAGS.EDITED
      },
      include: {
        v_chat_message_attachments: true,
        v_chat_message_reactions: {
          include: {
            v_users: {
              select: {
                user_uuid: true,
                username: true
              }
            }
          }
        },
        v_chat_messages: true
      }
    })

    // Format response
    const formattedMessage = {
      message_id: updatedMessage.message_id.toString(),
      room_uuid: updatedMessage.room_uuid,
      author_uuid: updatedMessage.author_uuid,
      content: updatedMessage.content,
      message_type: updatedMessage.message_type,
      reply_to: updatedMessage.reply_to?.toString() || null,
      edited_at: updatedMessage.edited_at,
      created_at: updatedMessage.created_at,
      flags: updatedMessage.flags,

      // Computed properties
      is_deleted: updatedMessage.flags ? (updatedMessage.flags & MESSAGE_FLAGS.DELETED) !== 0 : false,
      is_edited: updatedMessage.edited_at !== null,
      is_pinned: updatedMessage.flags ? (updatedMessage.flags & MESSAGE_FLAGS.PINNED) !== 0 : false,
      is_system: updatedMessage.flags ? (updatedMessage.flags & MESSAGE_FLAGS.SYSTEM) !== 0 : false,
      is_urgent: updatedMessage.flags ? (updatedMessage.flags & MESSAGE_FLAGS.URGENT) !== 0 : false,

      // Attachments
      attachments: updatedMessage.v_chat_message_attachments.map((att: any) => ({
        attachment_id: att.attachment_id.toString(),
        filename: att.filename,
        file_size: att.file_size,
        content_type: att.content_type,
        width: att.width,
        height: att.height
      })),

      // Reactions
      reactions: updatedMessage.v_chat_message_reactions.map((reaction: any) => ({
        message_id: reaction.message_id.toString(),
        user_uuid: reaction.user_uuid,
        emoji: reaction.emoji,
        created_at: reaction.created_at,
        user_name: reaction.v_users.username
      })),

      // Reply parent
      reply_parent: updatedMessage.v_chat_messages
        ? {
            message_id: updatedMessage.v_chat_messages.message_id.toString(),
            content: updatedMessage.v_chat_messages.content,
            message_type: updatedMessage.v_chat_messages.message_type
          }
        : null
    }

    return NextResponse.json({
      success: true,
      data: formattedMessage,
      message: 'Message updated successfully'
    })
  } catch (error) {
    console.error('Error updating message:', error)

    return NextResponse.json({ error: 'Failed to update message' }, { status: 500 })
  }
}

// DELETE /api/internal-chat/messages/[messageId]
export async function DELETE(request: NextRequest, { params }: { params: { messageId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { messageId } = params

    // Get the message with room info
    const message = await prisma.v_chat_messages.findUnique({
      where: { message_id: BigInt(messageId) },
      include: {
        v_chat_rooms: true
      }
    })

    if (!message) {
      return NextResponse.json({ error: 'Message not found' }, { status: 404 })
    }

    // Check if message is already deleted
    if (message.flags && message.flags & MESSAGE_FLAGS.DELETED) {
      return NextResponse.json({ error: 'Message already deleted' }, { status: 400 })
    }

    // Check permissions - user must be message author or room admin/owner
    let canDelete = message.author_uuid === session.user.id

    if (!canDelete) {
      // Check if user is room admin or owner
      const participant = await prisma.v_chat_room_participants.findFirst({
        where: {
          room_uuid: message.room_uuid,
          user_uuid: session.user.id,
          participant_role: { in: [ParticipantRole.OWNER, ParticipantRole.ADMIN, ParticipantRole.MODERATOR] }
        }
      })

      canDelete = !!participant
    }

    if (!canDelete) {
      return NextResponse.json(
        { error: 'Permission denied. You can only delete your own messages or you must be a room moderator.' },
        { status: 403 }
      )
    }

    // Soft delete the message (set deleted flag)
    await prisma.v_chat_messages.update({
      where: { message_id: BigInt(messageId) },
      data: {
        flags: (message.flags || 0) | MESSAGE_FLAGS.DELETED,
        content: null // Clear content for privacy
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Message deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting message:', error)

    return NextResponse.json({ error: 'Failed to delete message' }, { status: 500 })
  }
}
