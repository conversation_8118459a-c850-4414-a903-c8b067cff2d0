// Atomic Operations API
// Handles complex operations that require multiple database operations atomically
// Prevents race conditions in room creation + message sending

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'
import type { CreateRoomRequest, SendMessageRequest } from '@/types/apps/internal-chat/chatTypes'

// =====================================================
// UTILITY FUNCTIONS
// =====================================================

function safeBigIntToString(value: any): string {
  if (value === null || value === undefined) return '0'

  return typeof value === 'bigint' ? value.toString() : String(value)
}

// =====================================================
// POST /api/internal-chat/operations
// =====================================================

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { operation, data } = body

    switch (operation) {
      case 'create_room_and_send_message':
        return await handleCreateRoomAndSendMessage(session.user.id, data)

      case 'send_message_with_room_check':
        return await handleSendMessageWithRoomCheck(session.user.id, data)

      default:
        return NextResponse.json({ error: 'Invalid operation' }, { status: 400 })
    }
  } catch (error) {
    console.error('Atomic operation error:', error)

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    )
  }
}

// =====================================================
// ATOMIC OPERATIONS
// =====================================================

/**
 * Atomically create a room and send the first message
 * Prevents race conditions between room creation and message sending
 */
async function handleCreateRoomAndSendMessage(
  userId: string,
  data: {
    roomData: CreateRoomRequest
    messageData: SendMessageRequest
    tempRoomId?: string
  }
) {
  const { roomData, messageData, tempRoomId } = data

  // Get user's domain
  const user = await prisma.v_users.findUnique({
    where: { user_uuid: userId },
    select: { domain_uuid: true }
  })

  if (!user?.domain_uuid) {
    throw new Error('User domain not found')
  }

  // Execute everything in a single transaction
  const result = await prisma.$transaction(async tx => {
    // Step 1: Create the room
    const room = await tx.v_chat_rooms.create({
      data: {
        domain_uuid: user.domain_uuid!,
        room_name: roomData.room_name,
        room_description: roomData.room_description,
        room_type: roomData.room_type,
        room_settings: roomData.room_settings || {},
        created_by_user_uuid: userId,
        insert_user: userId,
        update_user: userId
      }
    })

    // Step 2: Add creator as owner
    await tx.v_chat_room_participants.create({
      data: {
        room_uuid: room.room_uuid,
        user_uuid: userId,
        participant_role: 'owner',
        insert_user: userId,
        update_user: userId
      }
    })

    // Step 3: Add other participants if specified
    if (roomData.participant_uuids && roomData.participant_uuids.length > 0) {
      const participantData = roomData.participant_uuids.map(participantId => ({
        room_uuid: room.room_uuid,
        user_uuid: participantId,
        participant_role: 'member' as const,
        insert_user: userId,
        update_user: userId
      }))

      await tx.v_chat_room_participants.createMany({
        data: participantData
      })
    }

    // Step 4: Send the first message
    const message = await tx.v_chat_messages.create({
      data: {
        room_uuid: room.room_uuid,
        author_uuid: userId,
        content: messageData.content.trim(),
        message_type: messageData.message_type || 0,
        reply_to: messageData.reply_to ? BigInt(messageData.reply_to) : null,
        created_at: Math.floor(Date.now() / 1000),
        flags: 0
      }
    })

    // Step 5: Update room's last activity
    await tx.v_chat_rooms.update({
      where: { room_uuid: room.room_uuid },
      data: {
        update_date: new Date(),
        update_user: userId
      }
    })

    return { room, message }
  })

  // Format response
  const formattedRoom = {
    room_uuid: result.room.room_uuid,
    domain_uuid: result.room.domain_uuid,
    room_name: result.room.room_name,
    room_description: result.room.room_description,
    room_type: result.room.room_type,
    room_avatar: result.room.room_avatar,
    created_by_user_uuid: result.room.created_by_user_uuid,
    is_active: result.room.is_active,
    is_archived: result.room.is_archived,
    max_participants: result.room.max_participants,
    room_settings: result.room.room_settings,
    insert_date: result.room.insert_date,
    update_date: result.room.update_date
  }

  const formattedMessage = {
    message_id: safeBigIntToString(result.message.message_id),
    room_uuid: result.message.room_uuid,
    author_uuid: result.message.author_uuid,
    content: result.message.content,
    message_type: result.message.message_type,
    reply_to: result.message.reply_to ? safeBigIntToString(result.message.reply_to) : null,
    edited_at: result.message.edited_at ? safeBigIntToString(result.message.edited_at) : null,
    created_at: result.message.created_at,
    flags: result.message.flags,
    attachments: [],
    reactions: [],
    reply_parent: null
  }

  return NextResponse.json({
    success: true,
    data: {
      room: formattedRoom,
      message: formattedMessage,
      tempRoomId
    },
    message: 'Room created and message sent successfully'
  })
}

/**
 * Send message with room existence check
 * Ensures room exists before sending message
 */
async function handleSendMessageWithRoomCheck(
  userId: string,
  data: {
    roomId: string
    messageData: SendMessageRequest
    tempId?: string
  }
) {
  const { roomId, messageData, tempId } = data

  // Validate roomId format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i

  if (!uuidRegex.test(roomId)) {
    throw new Error('Invalid room ID format')
  }

  // Execute in transaction
  const result = await prisma.$transaction(async tx => {
    // Verify room exists and user has access
    const roomAccess = await tx.v_chat_room_participants.findFirst({
      where: {
        room_uuid: roomId,
        user_uuid: userId,
        deleted_at: null
      }
    })

    if (!roomAccess) {
      throw new Error('Room not found or access denied')
    }

    // Send the message
    const message = await tx.v_chat_messages.create({
      data: {
        room_uuid: roomId,
        author_uuid: userId,
        content: messageData.content.trim(),
        message_type: messageData.message_type || 0,
        reply_to: messageData.reply_to ? BigInt(messageData.reply_to) : null,
        created_at: Math.floor(Date.now() / 1000),
        flags: 0
      }
    })

    // Update room's last activity
    await tx.v_chat_rooms.update({
      where: { room_uuid: roomId },
      data: {
        update_date: new Date(),
        update_user: userId
      }
    })

    return message
  })

  // Format response
  const formattedMessage = {
    message_id: safeBigIntToString(result.message_id),
    room_uuid: result.room_uuid,
    author_uuid: result.author_uuid,
    content: result.content,
    message_type: result.message_type,
    reply_to: result.reply_to ? safeBigIntToString(result.reply_to) : null,
    edited_at: result.edited_at ? safeBigIntToString(result.edited_at) : null,
    created_at: result.created_at,
    flags: result.flags,
    attachments: [],
    reactions: [],
    reply_parent: null
  }

  return NextResponse.json({
    success: true,
    data: {
      message: formattedMessage,
      tempId
    },
    message: 'Message sent successfully'
  })
}
