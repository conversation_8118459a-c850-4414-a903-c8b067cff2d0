// Internal Chat - ZALO Integration Statistics API
// GET /api/internal-chat/zalo/stats - Get ZALO integration statistics

import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'
import { ZaloIntegrationService } from '@/services/zalo/zaloIntegrationService'

// GET /api/internal-chat/zalo/stats
export async function GET() {
  const session = await getServerSession(authOptions)

  try {
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user!.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    // Check if ZALO configuration exists and is properly configured
    const oaConfig = await prisma.v_zalo_oa_config.findFirst({
      where: {
        domain_uuid: user.domain_uuid
      }
    })

    if (!oaConfig) {
      return NextResponse.json(
        {
          error: 'ZALO integration not configured',
          configured: false,
          message: 'Please configure your ZALO Official Account integration first'
        },
        { status: 404 }
      )
    }

    if (!oaConfig.access_token || oaConfig.access_token.trim().length === 0) {
      return NextResponse.json(
        {
          error: 'Access token is required',
          configured: false,
          message: 'Please provide a valid access token in the configuration'
        },
        { status: 400 }
      )
    }

    if (!oaConfig.is_active) {
      return NextResponse.json(
        {
          error: 'ZALO integration is not active',
          configured: true,
          message: 'Please activate your ZALO integration configuration'
        },
        { status: 400 }
      )
    }

    // Check if token is expired
    const tokenExpired = oaConfig.token_expires_at ? new Date() > oaConfig.token_expires_at : false

    // Get ZALO integration statistics using the integration service
    const stats = await ZaloIntegrationService.getZaloStats(user.domain_uuid)

    return NextResponse.json({
      stats: {
        ...stats,
        token_expired: tokenExpired
      },
      configured: true,
      integration_active: true,
      token_expired: tokenExpired
    })
  } catch (error) {
    console.error('Error fetching ZALO statistics:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Note: getZaloStats function removed - now using ZaloIntegrationService.getZaloStats()
