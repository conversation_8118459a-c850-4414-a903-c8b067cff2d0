// Internal Chat - ZALO Contacts API
// GET /api/internal-chat/zalo/contacts - Get ZALO contacts for domain

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'
import { ZaloIntegrationService } from '@/services/zalo/zaloIntegrationService'

// GET /api/internal-chat/zalo/contacts
export async function GET(request: NextRequest) {
  try {
    const session = await getSession()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const search = searchParams.get('search') || undefined
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')
    const is_follower = searchParams.get('is_follower')

    const followerFilter = is_follower === 'true' ? true : is_follower === 'false' ? false : undefined

    // Get contacts using the integration service
    const contacts = await ZaloIntegrationService.getZaloContactsForDomain(user.domain_uuid, {
      search,
      limit: Math.min(limit, 100), // Cap at 100
      offset: Math.max(offset, 0), // Ensure non-negative
      is_follower: followerFilter
    })

    return NextResponse.json({
      contacts,
      pagination: {
        limit,
        offset,
        has_more: contacts.length === limit
      }
    })
  } catch (error) {
    console.error('Error fetching ZALO contacts:', error)

    return NextResponse.json({ error: 'Failed to fetch contacts' }, { status: 500 })
  }
}

// POST /api/internal-chat/zalo/contacts - Create room for contact
export async function POST(request: NextRequest) {
  try {
    const session = await getSession()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    const body = await request.json()
    const { contact_uuid } = body

    if (!contact_uuid) {
      return NextResponse.json({ error: 'Contact UUID is required' }, { status: 400 })
    }

    // Verify contact belongs to this domain
    const contact = await prisma.v_zalo_oa_contacts.findFirst({
      where: {
        contact_uuid,
        domain_uuid: user.domain_uuid
      }
    })

    if (!contact) {
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 })
    }

    // Create room for contact
    const roomUuid = await ZaloIntegrationService.createRoomForContact(user.domain_uuid, contact_uuid)

    if (!roomUuid) {
      return NextResponse.json({ error: 'Failed to create room' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      room_uuid: roomUuid,
      message: 'Room created successfully'
    })
  } catch (error) {
    console.error('Error creating ZALO room:', error)

    return NextResponse.json({ error: 'Failed to create room' }, { status: 500 })
  }
}
