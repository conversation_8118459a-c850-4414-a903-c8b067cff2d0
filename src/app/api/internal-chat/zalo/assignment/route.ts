// Internal Chat - ZALO Assignment API
// POST /api/internal-chat/zalo/assignment - Assign/unassign agents to ZALO rooms

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'
import { ZaloAssignmentService } from '@/services/zalo/zaloAssignmentService'

interface AssignmentRequest {
  zalo_room_uuid: string
  agent_uuid: string
  action: 'assign' | 'unassign'
}

// POST /api/internal-chat/zalo/assignment
export async function POST(request: NextRequest) {
  try {
    const session = await getSession()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    const body: AssignmentRequest = await request.json()
    const { zalo_room_uuid, agent_uuid, action } = body

    // Validate required fields
    if (!zalo_room_uuid || !agent_uuid || !action) {
      return NextResponse.json({ error: 'ZALO room UUID, agent UUID, and action are required' }, { status: 400 })
    }

    if (!['assign', 'unassign'].includes(action)) {
      return NextResponse.json({ error: 'Action must be either "assign" or "unassign"' }, { status: 400 })
    }

    // Verify the ZALO room exists and belongs to this domain
    const zaloRoom = await prisma.v_zalo_oa_chat_rooms.findFirst({
      where: {
        zalo_room_uuid,
        domain_uuid: user.domain_uuid
      }
    })

    if (!zaloRoom) {
      return NextResponse.json({ error: 'ZALO room not found' }, { status: 404 })
    }

    // Verify the agent exists and belongs to this domain
    const agent = await prisma.v_users.findFirst({
      where: {
        user_uuid: agent_uuid,
        domain_uuid: user.domain_uuid,
        user_enabled: 'true'
      }
    })

    if (!agent) {
      return NextResponse.json({ error: 'Agent not found or not enabled' }, { status: 404 })
    }

    let result

    if (action === 'assign') {
      result = await ZaloAssignmentService.assignAgent(zalo_room_uuid, agent_uuid, session.user.id)
    } else {
      const success = await ZaloAssignmentService.unassignAgent(zalo_room_uuid, agent_uuid, session.user.id)

      result = {
        success,
        assigned_agent_uuid: success ? null : agent_uuid,
        assignment_method: 'manual',
        message: success ? 'Agent unassigned successfully' : 'Failed to unassign agent'
      }
    }

    if (!result.success) {
      return NextResponse.json({ error: result.message }, { status: 400 })
    }

    // Broadcast assignment change using Socket.IO if available
    try {
      if ((global as any).socketBroadcast?.broadcastMessage) {
        const room_name = `${user.domain_uuid}_zalo_chat`

        ;(global as any).socketBroadcast.broadcastMessage(
          room_name,
          {
            type: 'zalo_assignment_changed',
            data: {
              zalo_room_uuid,
              internal_room_uuid: zaloRoom.internal_room_uuid,
              agent_uuid,
              action,
              assigned_by: session.user.id,
              timestamp: new Date().toISOString()
            }
          },
          user.domain_uuid
        )
      }
    } catch (broadcastError) {
      console.error('Socket broadcast error:', broadcastError)

      // Don't fail the API call if broadcast fails
    }

    return NextResponse.json({
      success: true,
      action,
      agent_uuid,
      zalo_room_uuid,
      message: result.message
    })
  } catch (error) {
    console.error('Error managing ZALO assignment:', error)

    return NextResponse.json({ error: 'Failed to manage assignment' }, { status: 500 })
  }
}

// GET /api/internal-chat/zalo/assignment - Get assignment info for a room
export async function GET(request: NextRequest) {
  try {
    const session = await getSession()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    const searchParams = request.nextUrl.searchParams
    const zalo_room_uuid = searchParams.get('zalo_room_uuid')

    if (!zalo_room_uuid) {
      return NextResponse.json({ error: 'ZALO room UUID is required' }, { status: 400 })
    }

    // Verify the ZALO room exists and belongs to this domain
    const zaloRoom = await prisma.v_zalo_oa_chat_rooms.findFirst({
      where: {
        zalo_room_uuid,
        domain_uuid: user.domain_uuid
      }
    })

    if (!zaloRoom) {
      return NextResponse.json({ error: 'ZALO room not found' }, { status: 404 })
    }

    // Get assignment status and available agents
    const [assignmentStatus, availableAgents] = await Promise.all([
      ZaloAssignmentService.getRoomAssignmentStatus(zalo_room_uuid),
      ZaloAssignmentService.getAvailableAgents(user.domain_uuid)
    ])

    return NextResponse.json({
      zalo_room_uuid,
      assignment_status: assignmentStatus,
      available_agents: availableAgents
    })
  } catch (error) {
    console.error('Error getting ZALO assignment info:', error)

    return NextResponse.json({ error: 'Failed to get assignment info' }, { status: 500 })
  }
}
