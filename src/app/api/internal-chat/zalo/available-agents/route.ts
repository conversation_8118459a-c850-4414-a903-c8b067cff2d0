// Internal Chat - Zalo Available Agents API
// GET /api/internal-chat/zalo/available-agents - Get available agents for assignment

import { NextResponse } from 'next/server'

import { prisma } from '@/libs/db/prisma'
import { ZaloIntegrationService } from '@/services/zalo/zaloIntegrationService'
import getSession from '@/actions/getSession'

// GET /api/internal-chat/zalo/available-agents
export async function GET() {
  const session = await getSession()

  try {

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    // Get available agents for assignment
    const agents = await ZaloIntegrationService.getAvailableAgents(user.domain_uuid)

    return NextResponse.json(
      {
        agents
      },
      { status: 200 }
    )
  } catch (error) {
    console.error('Error fetching available agents:', error)

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
