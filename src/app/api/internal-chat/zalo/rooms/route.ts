// Internal Chat - ZALO Rooms API
// GET /api/internal-chat/zalo/rooms - Get ZALO chat rooms for domain

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'
import { ZaloIntegrationService } from '@/services/zalo/zaloIntegrationService'

// GET /api/internal-chat/zalo/rooms
export async function GET(request: NextRequest) {
  const session = await getSession()

  const domain_uuid = session?.user?.domain

  try {

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }


    if (!domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const search = searchParams.get('search')
    const status = searchParams.get('status') || 'active'

    // Get rooms using the integration service
    const rooms = await ZaloIntegrationService.getZaloRoomsForDomain(domain_uuid)

    // Apply search filter if provided
    let filteredRooms = rooms

    if (search) {
      const searchLower = search.toLowerCase()

      filteredRooms = rooms.filter(
        room =>
          room.contact.display_name.toLowerCase().includes(searchLower) ||
          room.contact.zalo_user_id.toLowerCase().includes(searchLower) ||
          (room.contact.username && room.contact.username.toLowerCase().includes(searchLower))
      )
    }

    // Apply status filter
    if (status !== 'all') {
      filteredRooms = filteredRooms.filter(room => room.room_status === status)
    }

    // Get agent information for assigned rooms
    const assignedAgentUuids = filteredRooms
      .map(room => room.assigned_agent_uuid)
      .filter(Boolean) as string[]

    const agents = assignedAgentUuids.length > 0 ? await prisma.v_users.findMany({
      where: {
        user_uuid: { in: assignedAgentUuids }
      },
      select: {
        user_uuid: true,
        username: true,
        user_email: true,
        contact_uuid: true
      }
    }) : []

    // Create agent map for quick lookup
    const agentMap = new Map(agents.map(agent => [
      agent.user_uuid,
      {
        user_uuid: agent.user_uuid,
        username: agent.username,
        display_name: agent.username || agent.user_uuid,
        first_name: null,
        last_name: null
      }
    ]))

    // Get participant information for each room
    const roomsWithParticipants = await Promise.all(
      filteredRooms.map(async room => {
        const participants = await prisma.v_chat_room_participants.findMany({
          where: {
            room_uuid: room.internal_room_uuid,
            deleted_at: null
          },
          include: {
            v_users: {
              select: {
                user_uuid: true,
                username: true,
                user_email: true
              }
            }
          }
        })

        return {
          ...room,
          assigned_agent: room.assigned_agent_uuid ? agentMap.get(room.assigned_agent_uuid) || null : null,
          participants: participants.map(p => ({
            user_uuid: p.v_users.user_uuid,
            username: p.v_users.username,
            user_email: p.v_users.user_email,
            participant_role: p.participant_role,
            joined_date: p.joined_date
          })),
          is_assigned: !!room.assigned_agent_uuid
        }
      })
    )

    return NextResponse.json({
      rooms: roomsWithParticipants,
      total: roomsWithParticipants.length
    })
  } catch (error) {
    console.error('Error fetching ZALO rooms:', error)

    return NextResponse.json({ error: 'Failed to fetch rooms' }, { status: 500 })
  }
}
