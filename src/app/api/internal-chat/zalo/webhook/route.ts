// Enhanced ZALO Webhook Integration
// Unified webhook endpoint replacing legacy implementations

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { prisma } from '@/libs/db/prisma'
import { ZaloMessageService } from '@/services/zalo/zaloMessageService'
import type { ZaloWebhookPayload } from '@/types/social/zaloTypes'
import { ZaloEventType } from '@/types/social/zaloTypes'
import { sanitizeWebhookPayload, isValidDomainUuid } from '@/utils/social/platformUtils'
import { calculateUnreadCounts } from '@/utils/social/messageUtils'

/**
 * POST - Process ZALO webhook events
 * Unified endpoint for all ZALO OA webhook events
 */
export async function POST(request: NextRequest) {
  try {
    const domainUuid = request.nextUrl.searchParams.get('domain')

    if (!domainUuid) {
      return NextResponse.json({ error: 'Domain UUID is required' }, { status: 400 })
    }

    // Validate domain UUID format
    if (!isValidDomainUuid(domainUuid)) {
      return NextResponse.json({ error: 'Invalid domain UUID format' }, { status: 400 })
    }

    // Get request body and signature
    const body = await request.text()
    const signature = request.headers.get('x-zevent-signature')

    console.log('=== ZALO WEBHOOK RECEIVED ===')
    console.log('Domain UUID:', domainUuid)
    console.log('Signature provided:', !!signature)
    console.log('Body length:', body.length)

    // TODO: Re-enable signature verification once algorithm is confirmed
    // Verify webhook signature for security
    // const signatureValid = await ZaloMessageService.verifyWebhookSignature(domainUuid, body, signature)

    // if (!signatureValid) {
    //   console.warn('ZALO webhook signature verification failed')

    //   return NextResponse.json({ error: 'Invalid signature' }, { status: 401 })
    // }

    console.log('ZALO webhook signature verification temporarily disabled for testing')

    // Parse and sanitize webhook payload
    let webhookEvent: ZaloWebhookPayload

    try {
      const rawPayload = JSON.parse(body)

      // Transform the payload to match expected structure
      webhookEvent = sanitizeWebhookPayload(rawPayload)

      // Fix sender structure - webhook sends sender.id but we expect sender.user_id
      if (webhookEvent.sender && 'id' in webhookEvent.sender && !webhookEvent.sender.user_id) {
        webhookEvent.sender.user_id = (webhookEvent.sender as any).id
      }
    } catch (error) {
      console.error('Failed to parse ZALO webhook payload:', error)

      return NextResponse.json({ error: 'Invalid payload format' }, { status: 400 })
    }

    console.log('Webhook Event Name:', webhookEvent.event_name)
    console.log('Sender ID:', webhookEvent.sender?.user_id)
    console.log('App ID:', webhookEvent.app_id)
    console.log('Timestamp:', webhookEvent.timestamp)

    // Verify ZALO configuration exists and is active
    const zaloConfig = await prisma.v_zalo_oa_config.findFirst({
      where: {
        domain_uuid: domainUuid,
        is_active: true
      },
      select: {
        config_uuid: true,
        app_id: true,
        oa_id: true,
        is_active: true
      }
    })

    if (!zaloConfig) {
      console.warn('ZALO configuration not found or inactive for domain:', domainUuid)

      return NextResponse.json({ error: 'ZALO integration not configured' }, { status: 400 })
    }

    // Validate app_id matches configuration
    if (webhookEvent.app_id && webhookEvent.app_id !== zaloConfig.app_id) {
      console.warn('App ID mismatch:', webhookEvent.app_id, 'vs', zaloConfig.app_id)

      return NextResponse.json({ error: 'App ID mismatch' }, { status: 400 })
    }

    // Log webhook event for audit
    await prisma.v_zalo_webhook_event.create({
      data: {
        domain_uuid: domainUuid,
        zalo_userid: webhookEvent.sender?.user_id,
        event_name: webhookEvent.event_name,
        msg: webhookEvent as any,
        insert_date: new Date(),
        update_date: new Date()
      }
    })

    // Process different event types
    let processedMessage: any = null

    try {
      switch (webhookEvent.event_name) {
        case ZaloEventType.USER_SEND_TEXT:
        case ZaloEventType.USER_SEND_IMAGE:
        case ZaloEventType.USER_SEND_FILE:
        case ZaloEventType.USER_SEND_AUDIO:
        case ZaloEventType.USER_SEND_VIDEO:
        case ZaloEventType.USER_SEND_STICKER:
        case ZaloEventType.USER_SEND_LOCATION:
          console.log('Processing inbound message event')
          processedMessage = await ZaloMessageService.processInboundMessage(domainUuid, webhookEvent)

          // Broadcast message using main chat system
          await broadcastZaloMessage(domainUuid, processedMessage)
          break

        case ZaloEventType.FOLLOW:
          console.log('Processing follow event')
          await handleFollowEvent(domainUuid, webhookEvent)
          break

        case ZaloEventType.UNFOLLOW:
          console.log('Processing unfollow event')
          await handleUnfollowEvent(domainUuid, webhookEvent)
          break

        case ZaloEventType.USER_SUBMIT_INFO:
          console.log('Processing user info submission')
          await handleUserInfoSubmission(domainUuid, webhookEvent)
          break

        case ZaloEventType.USER_CLICK_CHABOT:
          console.log('Processing chatbot interaction')
          await handleChatbotInteraction(domainUuid, webhookEvent)
          break

        default:
          console.log('Unknown or unsupported event type:', webhookEvent.event_name)
          break
      }
    } catch (eventError) {
      console.error('Error processing ZALO webhook event:', eventError)

      // Continue to return success to prevent webhook retries
    }

    console.log('=== ZALO WEBHOOK SUCCESS ===')
    console.log('Event processed:', webhookEvent.event_name)
    console.log('Message processed:', !!processedMessage)
    console.log('===========================')

    return NextResponse.json(
      {
        status: 'processed',
        event_name: webhookEvent.event_name,
        message_id: processedMessage?.internal_message_id || null,
        room_uuid: processedMessage?.room_uuid || null
      },
      { status: 200 }
    )
  } catch (error) {
    console.error('=== ZALO WEBHOOK ERROR ===')
    console.error('Error processing ZALO webhook:', error)
    console.error('Domain UUID:', request.nextUrl.searchParams.get('domain'))
    console.error('Error details:', {
      name: (error as Error)?.name,
      message: (error as Error)?.message,
      stack: (error as Error)?.stack
    })
    console.error('=========================')

    return NextResponse.json({ error: 'Webhook processing failed' }, { status: 500 })
  }



  /**
   * Broadcast ZALO message via Socket.IO
   */
  async function broadcastZaloMessage(domainUuid: string, result: any): Promise<void> {
    try {
      if (!(global as any).socketBroadcast?.broadcastMessage) {
        console.warn('Socket broadcast not available')

        return
      }

      // Get the full message data for socket emission
      const fullMessage = await prisma.v_chat_messages.findUnique({
        where: { message_id: BigInt(result.internal_message_id) }
      })

      if (!fullMessage) {
        console.warn('Message not found for broadcasting:', result.internal_message_id)

        return
      }

      // Get room data for domain validation
      const roomData = await prisma.v_chat_rooms.findUnique({
        where: { room_uuid: result.room_uuid },
        select: { domain_uuid: true }
      })

      if (!roomData || roomData.domain_uuid !== domainUuid) {
        console.warn('Room domain mismatch or room not found')

        return
      }

      // Calculate unread counts
      const unreadCounts = await calculateUnreadCounts(result.room_uuid)

      // Use unified broadcast system for consistent message delivery
      const { broadcastZaloMessage } = await import('@/utils/messaging/broadcastUtils')

      await broadcastZaloMessage(
        domainUuid,
        result.room_uuid,
        {
          message_id: result.internal_message_id,
          content: result.content,
          author_uuid: result.external_user_id || 'zalo_user',
          author_name: 'ZALO User',
          zalo_message_id: result.platform_message_id,
          delivery_status: 'delivered',
          unread_counts: unreadCounts
        }
      )

      // Check if conversation is unassigned and create notifications
      const { unifiedNotificationService } = await import('@/services/notifications/unifiedNotificationService')

      // Check if room has assigned agents
      const assignedAgents = await prisma.v_chat_room_participants.findMany({
        where: {
          room_uuid: result.room_uuid,
          deleted_at: null,
          participant_role: { in: ['admin', 'moderator'] } // Agents typically have these roles
        }
      })

      if (assignedAgents.length === 0) {
        // No agents assigned, create unassigned conversation notification
        await unifiedNotificationService.createUnassignedConversationNotification(
          domainUuid,
          result.room_uuid,
          {
            message_id: result.internal_message_id.toString(),
            content: result.content,
            author_name: 'ZALO User',
            platform: 'zalo'
          }
        )
      }

      console.log('ZALO webhook message broadcasted successfully via unified system')
    } catch (socketError) {
      console.error('Failed to broadcast ZALO message:', socketError)

      // Don't fail the webhook if socket emission fails
    }
  }

  /**
   * Handle follow event
   */
  async function handleFollowEvent(domainUuid: string, webhookEvent: ZaloWebhookPayload): Promise<void> {
    try {
      // Update or create contact as follower
      await prisma.v_zalo_oa_contacts.upsert({
        where: {
          domain_uuid_zalo_user_id: {
            domain_uuid: domainUuid,
            zalo_user_id: webhookEvent.sender.user_id
          }
        },
        update: {
          is_follower: true,
          is_active: true,
          last_interaction_date: new Date(),
          update_date: new Date()
        },
        create: {
          domain_uuid: domainUuid,
          zalo_user_id: webhookEvent.sender.user_id,
          display_name: webhookEvent.sender.display_name || `ZALO User ${webhookEvent.sender.user_id}`,
          avatar_url: webhookEvent.sender.avatar,
          is_follower: true,
          is_active: true,
          last_interaction_date: new Date(),
          contact_info: webhookEvent.sender as any,
          contact_metadata: {
            followed_at: new Date().toISOString(),
            follow_source: 'webhook'
          },
          insert_date: new Date(),
          update_date: new Date()
        }
      })

      console.log('Follow event processed for user:', webhookEvent.sender.user_id)
    } catch (error) {
      console.error('Error handling follow event:', error)
    }
  }

  /**
   * Handle unfollow event
   */
  async function handleUnfollowEvent(domainUuid: string, webhookEvent: ZaloWebhookPayload): Promise<void> {
    try {
      // Update contact as non-follower
      await prisma.v_zalo_oa_contacts.updateMany({
        where: {
          domain_uuid: domainUuid,
          zalo_user_id: webhookEvent.sender.user_id
        },
        data: {
          is_follower: false,
          last_interaction_date: new Date(),
          contact_metadata: {
            unfollowed_at: new Date().toISOString(),
            unfollow_source: 'webhook'
          },
          update_date: new Date()
        }
      })

      console.log('Unfollow event processed for user:', webhookEvent.sender.user_id)
    } catch (error) {
      console.error('Error handling unfollow event:', error)
    }
  }

  /**
   * Handle user info submission
   */
  async function handleUserInfoSubmission(domainUuid: string, webhookEvent: ZaloWebhookPayload): Promise<void> {
    try {
      // Update contact with submitted information
      await prisma.v_zalo_oa_contacts.updateMany({
        where: {
          domain_uuid: domainUuid,
          zalo_user_id: webhookEvent.sender.user_id
        },
        data: {
          contact_info: {
            ...webhookEvent.sender,
            submitted_info: webhookEvent.message || {}
          } as any,
          last_interaction_date: new Date(),
          update_date: new Date()
        }
      })

      console.log('User info submission processed for user:', webhookEvent.sender.user_id)
    } catch (error) {
      console.error('Error handling user info submission:', error)
    }
  }

  /**
   * Handle chatbot interaction
   */
  async function handleChatbotInteraction(domainUuid: string, webhookEvent: ZaloWebhookPayload): Promise<void> {
    try {
      // Log chatbot interaction for analytics
      console.log('Chatbot interaction:', {
        user_id: webhookEvent.sender.user_id,
        interaction_data: webhookEvent.message
      })

      // Update contact last interaction
      await prisma.v_zalo_oa_contacts.updateMany({
        where: {
          domain_uuid: domainUuid,
          zalo_user_id: webhookEvent.sender.user_id
        },
        data: {
          last_interaction_date: new Date(),
          contact_metadata: {
            last_chatbot_interaction: new Date().toISOString(),
            chatbot_data: webhookEvent.message
          } as any,
          update_date: new Date()
        }
      })

      console.log('Chatbot interaction processed for user:', webhookEvent.sender.user_id)
    } catch (error) {
      console.error('Error handling chatbot interaction:', error)
    }
  }
}
