// Internal Chat - Zalo Room Info API
// GET /api/internal-chat/zalo/room-info - Get Zalo room information for a specific room

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'

// GET /api/internal-chat/zalo/room-info
export async function GET(request: NextRequest) {
  const session = await getSession()

  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  const domain_uuid = session.user.domain

  if (!domain_uuid) {
    return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
  }

  try {
    // Get room_uuid from query parameters
    const searchParams = request.nextUrl.searchParams
    const room_uuid = searchParams.get('room_uuid')

    if (!room_uuid) {
      return NextResponse.json({ error: 'room_uuid parameter is required' }, { status: 400 })
    }

    // Check if this is a Zalo room and get its information
    const zaloRoom = await prisma.v_zalo_oa_chat_rooms.findFirst({
      where: {
        internal_room_uuid: room_uuid,
        domain_uuid
      },
      include: {
        v_zalo_oa_contacts: {
          select: {
            contact_uuid: true,
            zalo_user_id: true,
            display_name: true,
            username: true,
            avatar_url: true,
            phone: true,
            is_follower: true,
            last_interaction_date: true
          }
        },
        v_chat_rooms: {
          select: {
            room_uuid: true,
            room_name: true,
            room_type: true,
            is_active: true,
            is_archived: true
          }
        }
      }
    })

    if (!zaloRoom) {
      // Not a Zalo room
      return NextResponse.json({ room_info: null })
    }

    // Format the response
    const roomInfo = {
      zalo_room_uuid: zaloRoom.zalo_room_uuid,
      internal_room_uuid: zaloRoom.internal_room_uuid,
      contact: zaloRoom.v_zalo_oa_contacts,
      room_status: zaloRoom.room_status,
      last_message_at: zaloRoom.last_message_at,
      conversation_metadata: zaloRoom.conversation_metadata,
      assigned_agent_uuid: zaloRoom.assigned_agent_uuid
    }

    return NextResponse.json({
      success: true,
      room_info: roomInfo
    })
  } catch (error) {
    console.error('Error fetching Zalo room info:', error)

    return NextResponse.json({ error: 'Failed to fetch room information' }, { status: 500 })
  }
}
