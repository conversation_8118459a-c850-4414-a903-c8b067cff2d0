// Enhanced ZALO OA Configuration API
// Manages ZALO Official Account settings per domain

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'
import type {
  ZaloOaConfig,
  ZaloOaConfigRequest,
  ZaloOaConfigResponse,
  ZaloTokenValidation
} from '@/types/social/zaloTypes'
import { generateWebhookUrl, isValidUrl } from '@/utils/social/platformUtils'
import getSession from '@/actions/getSession'

/**
 * Validate ZALO access token and get OA information
 */
async function validateZaloAccessToken(accessToken: string, oaId?: string): Promise<ZaloTokenValidation> {
  try {
    // Get OA information to validate token - access token must be in header
    const response = await fetch('https://openapi.zalo.me/v2.0/oa/getoa', {
      method: 'GET',
      headers: {
        'access_token': accessToken
      }
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Invalid access token' }))

      return {
        valid: false,
        error: error.message || 'Invalid access token'
      }
    }

    const result = await response.json()

    if (result.error !== 0) {
      return {
        valid: false,
        error: result.message || 'ZALO API error'
      }
    }

    const oaInfo = result.data

    // Validate OA ID if provided
    if (oaId && oaInfo.oa_id !== oaId) {
      return {
        valid: false,
        error: 'OA ID mismatch with access token'
      }
    }

    return {
      valid: true,
      oaInfo: oaInfo
    }
  } catch (error) {
    return {
      valid: false,
      error: 'Failed to validate access token'
    }
  }
}

/**
 * Refresh ZALO access token
 */
async function refreshZaloToken(
  appId: string,
  refreshToken: string,
  appSecret: string
): Promise<{ success: boolean; accessToken?: string; refreshToken?: string; expiresIn?: number; error?: string }> {
  try {
    const response = await fetch('https://oauth.zaloapp.com/v4/oa/access_token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        secret_key: appSecret
      },
      body: new URLSearchParams({
        refresh_token: refreshToken,
        app_id: appId,
        grant_type: 'refresh_token'
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const result = await response.json()

    if (result.error !== 0) {
      return {
        success: false,
        error: result.message || 'Token refresh failed'
      }
    }

    return {
      success: true,
      accessToken: result.access_token,
      refreshToken: result.refresh_token,
      expiresIn: result.expires_in
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Token refresh failed'
    }
  }
}

// GET /api/internal-chat/zalo/oa-config
export async function GET() {
  try {
    const session = await getSession()

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const domain_uuid = session?.user?.domain ?? ''

    if (!domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    // Get ZALO OA configuration for this domain
    const config = await prisma.v_zalo_oa_config.findFirst({
      where: {
        domain_uuid: domain_uuid
      },
      select: {
        config_uuid: true,
        domain_uuid: true,
        app_id: true,
        oa_id: true,
        webhook_url: true,
        is_active: true,
        token_expires_at: true,
        allowed_events: true,
        oa_settings: true,
        insert_date: true,
        update_date: true

        // Exclude sensitive fields like app_secret, access_token, refresh_token
      }
    })

    if (!config) {
      return NextResponse.json({
        configured: false,
        message: 'ZALO OA configuration not found for this domain'
      })
    }

    return NextResponse.json({
      configured: true,
      config: {
        ...config,
        webhook_url: config.webhook_url || generateWebhookUrl('zalo', domain_uuid),
        token_expired: config.token_expires_at ? new Date() > config.token_expires_at : false
      }
    })
  } catch (error) {
    console.error('Error retrieving ZALO OA configuration:', error)

    return NextResponse.json({ error: 'Failed to retrieve configuration' }, { status: 500 })
  }
}

// POST /api/internal-chat/zalo/oa-config
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    const body: ZaloOaConfigRequest = await request.json()

    const {
      app_id,
      app_secret,
      oa_id,
      access_token,
      refresh_token,
      webhook_url,
      allowed_events = ['user_send_text', 'user_send_image', 'user_send_file'],
      oa_settings = {}
    } = body

    // Check if configuration already exists to determine validation requirements
    const existingConfig = await prisma.v_zalo_oa_config.findFirst({
      where: { domain_uuid: user.domain_uuid }
    })

    // For new configurations, require all essential fields
    if (!existingConfig) {
      if (!app_id || !app_secret || !oa_id || !access_token) {
        return NextResponse.json(
          { error: 'Missing required fields for new configuration: app_id, app_secret, oa_id, access_token' },
          { status: 400 }
        )
      }
    } else {
      // For updates, only require app_id and oa_id as minimum
      if (!app_id || !oa_id) {
        return NextResponse.json(
          { error: 'Missing required fields for update: app_id, oa_id' },
          { status: 400 }
        )
      }
    }

    // Validate webhook URL if provided
    if (webhook_url && !isValidUrl(webhook_url)) {
      return NextResponse.json({ error: 'Invalid webhook URL format' }, { status: 400 })
    }

    // Validate access token if provided
    let tokenValidation: any = { valid: true, oaInfo: null }

    if (access_token) {
      console.log('Validating ZALO access token...')
      tokenValidation = await validateZaloAccessToken(access_token.trim(), oa_id.trim())

      if (!tokenValidation.valid) {
        return NextResponse.json({ error: `Invalid access token: ${tokenValidation.error}` }, { status: 400 })
      }
    }

    // Generate webhook URL if not provided
    const finalWebhookUrl = webhook_url?.trim() || generateWebhookUrl('zalo', user.domain_uuid)

    // Build config data - only include provided fields for updates
    const configData: any = {
      domain_uuid: user.domain_uuid,
      app_id: app_id.trim(),
      oa_id: oa_id.trim(),
      webhook_url: finalWebhookUrl,
      is_active: true,
      allowed_events,
      update_date: new Date(),
      update_user: session.user.id
    }

    // Only include optional fields if provided
    if (app_secret) {
      configData.app_secret = app_secret.trim()
    }

    if (access_token) {
      configData.access_token = access_token.trim()
    }

    if (refresh_token) {
      configData.refresh_token = refresh_token.trim()
    }

    // Update OA settings if we have token validation info
    if (tokenValidation.oaInfo) {
      configData.oa_settings = {
        ...oa_settings,
        oa_name: tokenValidation.oaInfo.name,
        oa_description: tokenValidation.oaInfo.description,
        oa_avatar: tokenValidation.oaInfo.avatar,
        is_verified: tokenValidation.oaInfo.is_verified,
        num_follower: tokenValidation.oaInfo.num_follower
      }
    } else {
      configData.oa_settings = oa_settings
    }

    let config

    if (existingConfig) {
      // Update existing configuration
      config = await prisma.v_zalo_oa_config.update({
        where: { config_uuid: existingConfig.config_uuid },
        data: configData
      })
    } else {
      // Create new configuration
      config = await prisma.v_zalo_oa_config.create({
        data: {
          ...configData,
          insert_date: new Date(),
          insert_user: session.user.id
        }
      })
    }

    // Return configuration without sensitive data
    const responseConfig = {
      config_uuid: config.config_uuid,
      domain_uuid: config.domain_uuid,
      app_id: config.app_id,
      oa_id: config.oa_id,
      webhook_url: config.webhook_url,
      is_active: config.is_active,
      token_expires_at: config.token_expires_at,
      allowed_events: config.allowed_events,
      oa_settings: config.oa_settings,
      insert_date: config.insert_date || new Date(),
      update_date: config.update_date || new Date()
    } as Omit<ZaloOaConfig, 'app_secret' | 'access_token' | 'refresh_token'>

    const response: ZaloOaConfigResponse = {
      success: true,
      config: responseConfig,
      token_valid: tokenValidation.valid,
      message: existingConfig ? 'Configuration updated successfully' : 'Configuration created successfully'
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error saving ZALO OA configuration:', error)

    return NextResponse.json({ error: 'Failed to save configuration' }, { status: 500 })
  }
}

// PUT /api/internal-chat/zalo/oa-config
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    const body = await request.json()
    const { is_active, allowed_events, oa_settings, refresh_token_now } = body

    // Find existing configuration
    const existingConfig = await prisma.v_zalo_oa_config.findFirst({
      where: {
        domain_uuid: user.domain_uuid
      }
    })

    if (!existingConfig) {
      return NextResponse.json({ error: 'ZALO OA configuration not found' }, { status: 404 })
    }

    const updateData: any = {
      update_date: new Date(),
      update_user: session.user.id
    }

    // Update specific fields if provided
    if (is_active !== undefined) {
      updateData.is_active = is_active
    }

    if (allowed_events) {
      updateData.allowed_events = allowed_events
    }

    if (oa_settings) {
      updateData.oa_settings = {
        ...((existingConfig.oa_settings as Record<string, any>) || {}),
        ...oa_settings
      }
    }

    // Handle token refresh if requested
    if (refresh_token_now && existingConfig.refresh_token) {
      console.log('Refreshing ZALO access token...')

      const refreshResult = await refreshZaloToken(
        existingConfig.app_id,
        existingConfig.refresh_token,
        existingConfig.app_secret
      )

      if (refreshResult.success) {
        updateData.access_token = refreshResult.accessToken
        updateData.refresh_token = refreshResult.refreshToken
        updateData.token_expires_at = refreshResult.expiresIn
          ? new Date(Date.now() + refreshResult.expiresIn * 1000)
          : null

        console.log('Token refreshed successfully')
      } else {
        console.warn('Token refresh failed:', refreshResult.error)

        return NextResponse.json({ error: `Token refresh failed: ${refreshResult.error}` }, { status: 400 })
      }
    }

    // Update configuration
    const updatedConfig = await prisma.v_zalo_oa_config.update({
      where: { config_uuid: existingConfig.config_uuid },
      data: updateData
    })

    return NextResponse.json({
      success: true,
      config: {
        config_uuid: updatedConfig.config_uuid,
        domain_uuid: updatedConfig.domain_uuid,
        app_id: updatedConfig.app_id,
        oa_id: updatedConfig.oa_id,
        webhook_url: updatedConfig.webhook_url,
        is_active: updatedConfig.is_active,
        token_expires_at: updatedConfig.token_expires_at,
        allowed_events: updatedConfig.allowed_events,
        oa_settings: updatedConfig.oa_settings,
        update_date: updatedConfig.update_date
      },
      message: 'Configuration updated successfully'
    })
  } catch (error) {
    console.error('Error updating ZALO OA configuration:', error)

    return NextResponse.json({ error: 'Failed to update configuration' }, { status: 500 })
  }
}

// DELETE /api/internal-chat/zalo/oa-config
export async function DELETE() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    // Find and delete configuration
    const deletedConfig = await prisma.v_zalo_oa_config.deleteMany({
      where: {
        domain_uuid: user.domain_uuid
      }
    })

    if (deletedConfig.count === 0) {
      return NextResponse.json({ error: 'ZALO OA configuration not found' }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      message: 'ZALO OA configuration deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting ZALO OA configuration:', error)

    return NextResponse.json({ error: 'Failed to delete configuration' }, { status: 500 })
  }
}
