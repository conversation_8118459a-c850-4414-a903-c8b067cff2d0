/* eslint-disable */
// ZALO OA Individual Contact Management APIs
// Handles individual ZALO OA contact operations with domain isolation

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'

import { prisma } from '@/libs/db/prisma'
import { authOptions } from '@/libs/auth'
import type { UpdateZaloOaContactRequest } from '@/types/apps/zalo-oa/zaloOaTypes'

// GET /api/zalo-oa/contacts/[contactId] - Get specific ZALO OA contact
export async function GET(request: NextRequest, { params }: { params: { contactId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { contactId } = params

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 403 })
    }

    // Find contact with domain isolation
    const contact = await prisma.v_zalo_oa_contacts.findFirst({
      where: {
        contact_uuid: contactId,
        domain_uuid: user.domain_uuid // Critical: Domain isolation
      }
    })

    if (!contact) {
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 })
    }

    return NextResponse.json(contact)
  } catch (error) {
    console.error('Error fetching ZALO OA contact:', error)
    return NextResponse.json(
      {
        error: 'Failed to fetch contact'
      },
      { status: 500 }
    )
  }
}

// PUT /api/zalo-oa/contacts/[contactId] - Update ZALO OA contact
export async function PUT(request: NextRequest, { params }: { params: { contactId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { contactId } = params

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 403 })
    }

    const body: UpdateZaloOaContactRequest = await request.json()

    // Verify contact exists and belongs to user's domain
    const existingContact = await prisma.v_zalo_oa_contacts.findFirst({
      where: {
        contact_uuid: contactId,
        domain_uuid: user.domain_uuid // Critical: Domain isolation
      }
    })

    if (!existingContact) {
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 })
    }

    // Update contact
    const updatedContact = await prisma.v_zalo_oa_contacts.update({
      where: { contact_uuid: contactId },
      data: {
        display_name: body.display_name,
        avatar_url: body.avatar_url,
        phone: body.phone,
        is_follower: body.is_follower,
        contact_info: body.contact_info as any,
        update_user: session.user.id,
        update_date: new Date()
      }
    })

    return NextResponse.json(updatedContact)
  } catch (error) {
    console.error('Error updating ZALO OA contact:', error)
    return NextResponse.json(
      {
        error: 'Failed to update contact'
      },
      { status: 500 }
    )
  }
}

// DELETE /api/zalo-oa/contacts/[contactId] - Delete ZALO OA contact
export async function DELETE(request: NextRequest, { params }: { params: { contactId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { contactId } = params

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 403 })
    }

    // Verify contact exists and belongs to user's domain
    const existingContact = await prisma.v_zalo_oa_contacts.findFirst({
      where: {
        contact_uuid: contactId,
        domain_uuid: user.domain_uuid // Critical: Domain isolation
      }
    })

    if (!existingContact) {
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 })
    }

    // Check if contact has active chat rooms
    const activeChatRooms = await prisma.v_zalo_oa_chat_rooms.count({
      where: {
        zalo_contact_uuid: contactId,
        room_status: 'active'
      }
    })

    if (activeChatRooms > 0) {
      return NextResponse.json(
        {
          error: 'Cannot delete contact with active chat rooms. Close conversations first.'
        },
        { status: 400 }
      )
    }

    // Delete contact (cascade will handle related records)
    await prisma.v_zalo_oa_contacts.delete({
      where: { contact_uuid: contactId }
    })

    return NextResponse.json({
      message: 'Contact deleted successfully',
      contact_uuid: contactId
    })
  } catch (error) {
    console.error('Error deleting ZALO OA contact:', error)
    return NextResponse.json(
      {
        error: 'Failed to delete contact'
      },
      { status: 500 }
    )
  }
}
