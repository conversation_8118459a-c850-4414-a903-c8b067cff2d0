/* eslint-disable */
// ZALO OA Contact Management APIs
// Handles ZALO OA contact operations with strict domain isolation

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'

import { prisma } from '@/libs/db/prisma'
import { authOptions } from '@/libs/auth'
import type { ZaloOaContactListResponse, CreateZaloOaContactRequest } from '@/types/apps/zalo-oa/zaloOaTypes'

// GET /api/zalo-oa/contacts - List ZALO OA contacts with domain filtering
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search') || ''
    const is_follower = searchParams.get('is_follower')

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 403 })
    }

    const domain_uuid = user.domain_uuid

    // Build where clause with domain isolation
    const whereClause: any = {
      domain_uuid // Critical: Always filter by domain
    }

    // Add search filter
    if (search) {
      whereClause.OR = [
        { display_name: { contains: search, mode: 'insensitive' } },
        { zalo_user_id: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search, mode: 'insensitive' } }
      ]
    }

    // Add follower filter
    if (is_follower !== null) {
      whereClause.is_follower = is_follower === 'true'
    }

    // Get total count
    const total = await prisma.v_zalo_oa_contacts.count({
      where: whereClause
    })

    // Get contacts with pagination
    const contacts = await prisma.v_zalo_oa_contacts.findMany({
      where: whereClause,
      orderBy: [{ last_interaction_date: 'desc' }, { insert_date: 'desc' }],
      skip: (page - 1) * limit,
      take: limit,
      select: {
        contact_uuid: true,
        domain_uuid: true,
        zalo_user_id: true,
        display_name: true,
        avatar_url: true,
        phone: true,
        is_follower: true,
        last_interaction_date: true,
        contact_info: true,
        insert_date: true,
        update_date: true
      }
    })

    const response: ZaloOaContactListResponse = {
      contacts: contacts as any,
      total,
      page,
      limit
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error fetching ZALO OA contacts:', error)
    return NextResponse.json(
      {
        error: 'Failed to fetch contacts'
      },
      { status: 500 }
    )
  }
}

// POST /api/zalo-oa/contacts - Create new ZALO OA contact
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 403 })
    }

    const domain_uuid = user.domain_uuid
    const body: CreateZaloOaContactRequest = await request.json()

    // Validate required fields
    if (!body.zalo_user_id) {
      return NextResponse.json(
        {
          error: 'ZALO user ID is required'
        },
        { status: 400 }
      )
    }

    // Check if contact already exists in this domain
    const existingContact = await prisma.v_zalo_oa_contacts.findFirst({
      where: {
        domain_uuid,
        zalo_user_id: body.zalo_user_id
      }
    })

    if (existingContact) {
      return NextResponse.json(
        {
          error: 'Contact already exists in this domain'
        },
        { status: 409 }
      )
    }

    // Create new contact with domain isolation
    const newContact = await prisma.v_zalo_oa_contacts.create({
      data: {
        domain_uuid, // Critical: Always set domain
        zalo_user_id: body.zalo_user_id,
        display_name: body.display_name || `ZALO User ${body.zalo_user_id}`,
        avatar_url: body.avatar_url,
        phone: body.phone,
        is_follower: body.is_follower ?? false,
        contact_info: body.contact_info || {},
        insert_user: session.user.id,
        update_user: session.user.id
      }
    })

    return NextResponse.json(newContact, { status: 201 })
  } catch (error) {
    console.error('Error creating ZALO OA contact:', error)
    return NextResponse.json(
      {
        error: 'Failed to create contact'
      },
      { status: 500 }
    )
  }
}

// PUT /api/zalo-oa/contacts - Sync contacts from ZALO API
export async function PUT(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 403 })
    }

    const domain_uuid = user.domain_uuid

    // Get ZALO OAuth credentials for this domain
    const zaloAuth = await prisma.v_zalo_oauth.findFirst({
      where: { domain_uuid },
      select: { access_token: true, app_id: true }
    })

    if (!zaloAuth?.access_token) {
      return NextResponse.json(
        {
          error: 'ZALO authentication not configured for this domain'
        },
        { status: 400 }
      )
    }

    // TODO: Implement ZALO API sync
    // This would call ZALO API to get follower list and sync with database
    // For now, return success message

    return NextResponse.json({
      message: 'Contact sync initiated',
      domain_uuid,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error syncing ZALO OA contacts:', error)
    return NextResponse.json(
      {
        error: 'Failed to sync contacts'
      },
      { status: 500 }
    )
  }
}
