/* eslint-disable */
// ZALO OA Webhook Adapter for Internal Chat Integration
// Handles ZALO OA webhook events and integrates with internal chat system
// Ensures domain isolation and privacy compliance

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { prisma } from '@/libs/db/prisma'
import type { ZaloOaWebhookEvent, ZaloOaWebhookProcessingResult } from '@/types/apps/zalo-oa/zaloOaTypes'
import { ZaloEvent } from '@/types/apps/zaloTypes'

// Domain validation middleware
async function validateDomainAccess(domain_uuid: string): Promise<boolean> {
  if (!domain_uuid) return false

  try {
    const domain = await prisma.v_domains.findUnique({
      where: { domain_uuid },
      select: { domain_uuid: true, domain_enabled: true }
    })

    return domain?.domain_enabled === true
  } catch (error) {
    console.error('Domain validation error:', error)
    return false
  }
}

// ZALO OA Contact Management
async function findOr<PERSON>reateZaloContact(domain_uuid: string, zalo_user_id: string, user_info?: any) {
  try {
    // First try to find existing contact
    let contact = await prisma.v_zalo_oa_contacts.findFirst({
      where: {
        domain_uuid,
        zalo_user_id
      }
    })

    // If not found, create new contact
    if (!contact) {
      contact = await prisma.v_zalo_oa_contacts.create({
        data: {
          domain_uuid,
          zalo_user_id,
          display_name: user_info?.display_name || `ZALO User ${zalo_user_id}`,
          avatar_url: user_info?.avatar,
          is_follower: true,
          last_interaction_date: new Date(),
          contact_info: user_info || {}
        }
      })
    } else {
      // Update last interaction date
      contact = await prisma.v_zalo_oa_contacts.update({
        where: { contact_uuid: contact.contact_uuid },
        data: {
          last_interaction_date: new Date(),
          is_follower: true // Update follower status
        }
      })
    }

    return contact
  } catch (error) {
    console.error('Error finding/creating ZALO contact:', error)
    throw error
  }
}

// ZALO OA Chat Room Management
async function findOrCreateZaloChatRoom(domain_uuid: string, contact_uuid: string, zalo_user_id: string) {
  try {
    // Check if chat room already exists for this contact
    let zaloRoom = await prisma.v_zalo_oa_chat_rooms.findFirst({
      where: {
        domain_uuid,
        zalo_contact_uuid: contact_uuid
      }
    })

    if (!zaloRoom) {
      // Create internal chat room first
      const internalRoom = await prisma.v_chat_rooms.create({
        data: {
          domain_uuid,
          room_name: `ZALO Chat - ${zalo_user_id}`,
          room_description: `ZALO OA conversation with user ${zalo_user_id}`,
          room_type: 'direct',
          created_by_user_uuid: domain_uuid, // Use domain as creator for system rooms
          room_settings: {
            platform: 'zalo_oa',
            zalo_user_id,
            auto_created: true
          }
        }
      })

      // Create ZALO chat room bridge
      zaloRoom = await prisma.v_zalo_oa_chat_rooms.create({
        data: {
          domain_uuid,
          internal_room_uuid: internalRoom.room_uuid,
          zalo_contact_uuid: contact_uuid,
          room_status: 'active',
          last_message_at: new Date(),
          conversation_metadata: {
            zalo_user_id,
            created_from_webhook: true
          }
        }
      })
    } else {
      // Update last message time
      zaloRoom = await prisma.v_zalo_oa_chat_rooms.update({
        where: { zalo_room_uuid: zaloRoom.zalo_room_uuid },
        data: { last_message_at: new Date() }
      })
    }

    return zaloRoom
  } catch (error) {
    console.error('Error finding/creating ZALO chat room:', error)
    throw error
  }
}

// Process inbound ZALO message
async function processInboundMessage(
  domain_uuid: string,
  webhookEvent: ZaloOaWebhookEvent
): Promise<ZaloOaWebhookProcessingResult> {
  try {
    const { sender, message, event_name, timestamp } = webhookEvent

    if (!message?.text && !message?.attachments) {
      return { success: false, error: 'No message content' }
    }

    // Find or create ZALO contact
    const contact = await findOrCreateZaloContact(domain_uuid, sender.id)

    // Find or create chat room
    const zaloRoom = await findOrCreateZaloChatRoom(domain_uuid, contact.contact_uuid, sender.id)

    // Create internal chat message
    const internalMessage = await prisma.v_chat_messages.create({
      data: {
        room_uuid: zaloRoom.internal_room_uuid,
        author_uuid: contact.contact_uuid, // Use contact as author for external messages
        content: message.text || '[Attachment]',
        message_type: message.attachments ? 2 : 0, // 0=text, 2=file
        created_at: Math.floor(timestamp / 1000), // Convert to Unix timestamp
        flags: 4 // Mark as external/system message
      }
    })

    // Create message mapping
    await prisma.v_zalo_message_mapping.create({
      data: {
        domain_uuid,
        internal_message_id: internalMessage.message_id,
        zalo_message_id: message.msg_id,
        zalo_user_id: sender.id,
        message_direction: 'inbound',
        zalo_event_name: event_name,
        delivery_status: 'delivered'
      }
    })

    return {
      success: true,
      contact_uuid: contact.contact_uuid,
      room_uuid: zaloRoom.internal_room_uuid,
      message_id: internalMessage.message_id
    }
  } catch (error) {
    console.error('Error processing inbound ZALO message:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// GET endpoint (webhook verification)
export async function GET(request: NextRequest) {
  const domain_uuid = request.nextUrl.searchParams.get('domain')

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Domain UUID required' }, { status: 400 })
  }

  const isValidDomain = await validateDomainAccess(domain_uuid)
  if (!isValidDomain) {
    return NextResponse.json({ error: 'Invalid domain' }, { status: 403 })
  }

  return NextResponse.json({
    status: 'ZALO OA webhook active',
    domain: domain_uuid,
    timestamp: new Date().toISOString()
  })
}

// POST endpoint (webhook events)
export async function POST(request: NextRequest) {
  const domain_uuid = request.nextUrl.searchParams.get('domain')

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Domain UUID required' }, { status: 400 })
  }

  // Validate domain access
  const isValidDomain = await validateDomainAccess(domain_uuid)
  if (!isValidDomain) {
    return NextResponse.json({ error: 'Invalid domain' }, { status: 403 })
  }

  try {
    const webhookEvent: ZaloOaWebhookEvent = await request.json()

    // Log webhook event (keep existing logging for audit)
    await prisma.v_zalo_webhook_event.create({
      data: {
        domain_uuid,
        zalo_userid: webhookEvent.sender?.id,
        event_name: webhookEvent.event_name,
        msg: webhookEvent as any,
        insert_date: new Date(),
        update_date: new Date()
      }
    })

    // Process message events
    if (
      webhookEvent.event_name === ZaloEvent.user_send_text ||
      webhookEvent.event_name === ZaloEvent.user_send_image ||
      webhookEvent.event_name === ZaloEvent.user_send_file
    ) {
      const result = await processInboundMessage(domain_uuid, webhookEvent)

      if (result.success) {
        // Broadcast to Socket.IO (domain-isolated)
        try {
          if ((global as any).socketBroadcast?.broadcastMessage) {
            const room = `${domain_uuid}_zalo_chat`
            ;(global as any).socketBroadcast.broadcastMessage(
              room,
              {
                type: 'zalo_message_received',
                data: {
                  room_uuid: result.room_uuid,
                  contact_uuid: result.contact_uuid,
                  message_id: result.message_id,
                  event: webhookEvent
                }
              },
              domain_uuid
            )
          }
        } catch (broadcastError) {
          console.error('Socket broadcast error:', broadcastError)
          // Don't fail the webhook if broadcast fails
        }
      }

      return NextResponse.json({
        success: result.success,
        message: result.success ? 'Message processed' : result.error
      })
    }

    // Handle other events (follow/unfollow, etc.)
    return NextResponse.json({
      success: true,
      message: 'Event logged'
    })
  } catch (error) {
    console.error('ZALO OA webhook error:', error)
    return NextResponse.json(
      {
        error: 'Webhook processing failed'
      },
      { status: 500 }
    )
  }
}
