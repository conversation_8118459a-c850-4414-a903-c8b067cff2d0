/* eslint-disable */
// ZALO OA Individual Chat Room Management APIs
// Handles individual ZALO OA chat room operations with domain isolation

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'

import { prisma } from '@/libs/db/prisma'
import { authOptions } from '@/libs/auth'
import type { UpdateZaloOaChatRoomRequest } from '@/types/apps/zalo-oa/zaloOaTypes'

// GET /api/zalo-oa/chat-rooms/[roomId] - Get specific ZALO OA chat room
export async function GET(request: NextRequest, { params }: { params: { roomId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { roomId } = params

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 403 })
    }

    // Find chat room with domain isolation
    const chatRoom = await prisma.v_zalo_oa_chat_rooms.findFirst({
      where: {
        zalo_room_uuid: roomId,
        domain_uuid: user.domain_uuid // Critical: Domain isolation
      }
    })

    if (!chatRoom) {
      return NextResponse.json({ error: 'Chat room not found' }, { status: 404 })
    }

    return NextResponse.json(chatRoom)
  } catch (error) {
    console.error('Error fetching ZALO OA chat room:', error)
    return NextResponse.json(
      {
        error: 'Failed to fetch chat room'
      },
      { status: 500 }
    )
  }
}

// PUT /api/zalo-oa/chat-rooms/[roomId] - Update ZALO OA chat room
export async function PUT(request: NextRequest, { params }: { params: { roomId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { roomId } = params

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 403 })
    }

    const body: UpdateZaloOaChatRoomRequest = await request.json()

    // Verify chat room exists and belongs to user's domain
    const existingChatRoom = await prisma.v_zalo_oa_chat_rooms.findFirst({
      where: {
        zalo_room_uuid: roomId,
        domain_uuid: user.domain_uuid // Critical: Domain isolation
      }
    })

    if (!existingChatRoom) {
      return NextResponse.json({ error: 'Chat room not found' }, { status: 404 })
    }

    // If assigning to agent, verify agent belongs to same domain
    if (body.assigned_agent_uuid) {
      const agent = await prisma.v_users.findFirst({
        where: {
          user_uuid: body.assigned_agent_uuid,
          domain_uuid: user.domain_uuid // Critical: Domain isolation
        }
      })

      if (!agent) {
        return NextResponse.json(
          {
            error: 'Agent not found in this domain'
          },
          { status: 404 }
        )
      }
    }

    // Update chat room
    const updatedChatRoom = await prisma.v_zalo_oa_chat_rooms.update({
      where: { zalo_room_uuid: roomId },
      data: {
        assigned_agent_uuid: body.assigned_agent_uuid,
        room_status: body.room_status,
        conversation_metadata: body.conversation_metadata as any,
        update_user: session.user.id,
        update_date: new Date()
      }
    })

    // Broadcast status change via Socket.IO
    try {
      if ((global as any).socketBroadcast?.broadcastMessage) {
        const room = `${user.domain_uuid}_zalo_chat`
        ;(global as any).socketBroadcast.broadcastMessage(
          room,
          {
            type: 'zalo_room_status_changed',
            data: {
              room_uuid: roomId,
              status: body.room_status
            }
          },
          user.domain_uuid
        )
      }
    } catch (broadcastError) {
      console.error('Socket broadcast error:', broadcastError)
      // Don't fail the API if broadcast fails
    }

    return NextResponse.json(updatedChatRoom)
  } catch (error) {
    console.error('Error updating ZALO OA chat room:', error)
    return NextResponse.json(
      {
        error: 'Failed to update chat room'
      },
      { status: 500 }
    )
  }
}

// DELETE /api/zalo-oa/chat-rooms/[roomId] - Close/Archive ZALO OA chat room
export async function DELETE(request: NextRequest, { params }: { params: { roomId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { roomId } = params

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 403 })
    }

    // Verify chat room exists and belongs to user's domain
    const existingChatRoom = await prisma.v_zalo_oa_chat_rooms.findFirst({
      where: {
        zalo_room_uuid: roomId,
        domain_uuid: user.domain_uuid // Critical: Domain isolation
      }
    })

    if (!existingChatRoom) {
      return NextResponse.json({ error: 'Chat room not found' }, { status: 404 })
    }

    // Close chat room (don't actually delete, just change status)
    const closedChatRoom = await prisma.v_zalo_oa_chat_rooms.update({
      where: { zalo_room_uuid: roomId },
      data: {
        room_status: 'closed',
        update_user: session.user.id,
        update_date: new Date()
      }
    })

    // Also archive the internal chat room
    await prisma.v_chat_rooms.update({
      where: { room_uuid: existingChatRoom.internal_room_uuid },
      data: {
        is_archived: true,
        update_user: session.user.id,
        update_date: new Date()
      }
    })

    return NextResponse.json({
      message: 'Chat room closed successfully',
      room_uuid: roomId,
      status: 'closed'
    })
  } catch (error) {
    console.error('Error closing ZALO OA chat room:', error)
    return NextResponse.json(
      {
        error: 'Failed to close chat room'
      },
      { status: 500 }
    )
  }
}
