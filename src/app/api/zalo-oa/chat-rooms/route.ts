/* eslint-disable */
// ZALO OA Chat Room Management APIs
// Handles ZALO OA chat room operations with strict domain isolation

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'

import { prisma } from '@/libs/db/prisma'
import { authOptions } from '@/libs/auth'
import { ParticipantRole } from '@/types/apps/internal-chat/chatTypes'
import type {
  ZaloOaChatRoomListResponse,
  AssignZaloOaChatRoomRequest,
  ZaloOaChatRoomStatus
} from '@/types/apps/zalo-oa/zaloOaTypes'

// GET /api/zalo-oa/chat-rooms - List ZALO OA chat rooms with domain filtering
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status') as ZaloOaChatRoomStatus
    const assigned_agent_uuid = searchParams.get('assigned_agent_uuid')

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 403 })
    }

    const domain_uuid = user.domain_uuid

    // Build where clause with domain isolation
    const whereClause: any = {
      domain_uuid // Critical: Always filter by domain
    }

    // Add status filter
    if (status) {
      whereClause.room_status = status
    }

    // Add agent filter
    if (assigned_agent_uuid) {
      whereClause.assigned_agent_uuid = assigned_agent_uuid
    }

    // Get total count
    const total = await prisma.v_zalo_oa_chat_rooms.count({
      where: whereClause
    })

    // Get chat rooms with pagination
    const chatRooms = await prisma.v_zalo_oa_chat_rooms.findMany({
      where: whereClause,
      orderBy: [{ last_message_at: 'desc' }, { insert_date: 'desc' }],
      skip: (page - 1) * limit,
      take: limit
    })

    const response: ZaloOaChatRoomListResponse = {
      chat_rooms: chatRooms as any,
      total,
      page,
      limit
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error fetching ZALO OA chat rooms:', error)
    return NextResponse.json(
      {
        error: 'Failed to fetch chat rooms'
      },
      { status: 500 }
    )
  }
}

// POST /api/zalo-oa/chat-rooms/assign - Assign chat room to agent
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 403 })
    }

    const domain_uuid = user.domain_uuid
    const body: AssignZaloOaChatRoomRequest = await request.json()

    // Validate required fields
    if (!body.zalo_room_uuid || !body.assigned_agent_uuid) {
      return NextResponse.json(
        {
          error: 'Room UUID and agent UUID are required'
        },
        { status: 400 }
      )
    }

    // Verify chat room exists and belongs to user's domain
    const chatRoom = await prisma.v_zalo_oa_chat_rooms.findFirst({
      where: {
        zalo_room_uuid: body.zalo_room_uuid,
        domain_uuid // Critical: Domain isolation
      }
    })

    if (!chatRoom) {
      return NextResponse.json({ error: 'Chat room not found' }, { status: 404 })
    }

    // Verify agent exists and belongs to user's domain
    const agent = await prisma.v_users.findFirst({
      where: {
        user_uuid: body.assigned_agent_uuid,
        domain_uuid // Critical: Domain isolation
      }
    })

    if (!agent) {
      return NextResponse.json({ error: 'Agent not found in this domain' }, { status: 404 })
    }

    // Update chat room assignment and ensure participant exists
    const updatedChatRoom = await prisma.$transaction(async tx => {
      // Update the ZALO room assignment
      const zaloRoom = await tx.v_zalo_oa_chat_rooms.update({
        where: { zalo_room_uuid: body.zalo_room_uuid },
        data: {
          assigned_agent_uuid: body.assigned_agent_uuid,
          room_status: 'active',
          update_user: session.user!.id,
          update_date: new Date()
        }
      })

      // Ensure participant exists for assigned agent in the internal room
      const existingParticipant = await tx.v_chat_room_participants.findFirst({
        where: {
          room_uuid: chatRoom.internal_room_uuid,
          user_uuid: body.assigned_agent_uuid
        }
      })

      if (!existingParticipant) {
        await tx.v_chat_room_participants.create({
          data: {
            room_uuid: chatRoom.internal_room_uuid,
            user_uuid: body.assigned_agent_uuid,
            participant_role: ParticipantRole.MEMBER,
            insert_user: session.user!.id,
            update_user: session.user!.id,
            notification_settings: {
              mentions: true,
              all_messages: true,
              member_additions: true,
              member_removals: true,
              role_changes: true,
              group_info_changes: true
            }
          }
        })
      }

      return zaloRoom
    })

    // Broadcast assignment event via Socket.IO
    try {
      if ((global as any).socketBroadcast?.broadcastMessage) {
        const room = `${domain_uuid}_zalo_chat`
        ;(global as any).socketBroadcast.broadcastMessage(
          room,
          {
            type: 'zalo_room_assigned',
            data: {
              room_uuid: updatedChatRoom.zalo_room_uuid,
              agent_uuid: body.assigned_agent_uuid,
              agent_name: agent.username
            }
          },
          domain_uuid
        )
      }
    } catch (broadcastError) {
      console.error('Socket broadcast error:', broadcastError)
      // Don't fail the API if broadcast fails
    }

    return NextResponse.json(updatedChatRoom)
  } catch (error) {
    console.error('Error assigning ZALO OA chat room:', error)
    return NextResponse.json(
      {
        error: 'Failed to assign chat room'
      },
      { status: 500 }
    )
  }
}
