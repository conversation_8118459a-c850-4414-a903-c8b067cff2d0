/* eslint-disable */
// ZALO OA Message History API
// Retrieves ZALO conversation history with domain isolation

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'

import { prisma } from '@/libs/db/prisma'
import { authOptions } from '@/libs/auth'

// GET /api/zalo-oa/messages/[roomId] - Get ZALO conversation history
export async function GET(request: NextRequest, { params }: { params: { roomId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { roomId } = params
    const { searchParams } = new URL(request.url)

    const limit = parseInt(searchParams.get('limit') || '50')
    const before = searchParams.get('before') // Message ID for pagination
    const after = searchParams.get('after') // Message ID for pagination

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 403 })
    }

    const domain_uuid = user.domain_uuid

    // Verify ZALO chat room exists and belongs to user's domain
    const zaloChatRoom = await prisma.v_zalo_oa_chat_rooms.findFirst({
      where: {
        zalo_room_uuid: roomId,
        domain_uuid // Critical: Domain isolation
      },
      include: {
        v_zalo_oa_contacts: {
          select: {
            contact_uuid: true,
            zalo_user_id: true,
            display_name: true,
            avatar_url: true
          }
        },
        v_chat_rooms: {
          select: {
            room_uuid: true,
            room_name: true,
            room_type: true
          }
        }
      }
    })

    if (!zaloChatRoom) {
      return NextResponse.json({ error: 'ZALO chat room not found' }, { status: 404 })
    }

    // Build message query with pagination
    const messageWhereClause: any = {
      room_uuid: zaloChatRoom.internal_room_uuid
    }

    // Add pagination filters
    if (before) {
      messageWhereClause.message_id = { lt: BigInt(before) }
    }
    if (after) {
      messageWhereClause.message_id = { gt: BigInt(after) }
    }

    // Get messages with ZALO mapping information
    const messages = await prisma.v_chat_messages.findMany({
      where: messageWhereClause,
      include: {
        v_zalo_message_mapping: {
          select: {
            mapping_uuid: true,
            zalo_message_id: true,
            zalo_user_id: true,
            message_direction: true,
            delivery_status: true,
            error_message: true,
            insert_date: true
          }
        },
        v_chat_message_attachments: {
          select: {
            attachment_id: true,
            filename: true,
            file_path: true,
            file_size: true,
            content_type: true,
            width: true,
            height: true
          }
        },
        v_chat_message_reactions: true
      },
      orderBy: {
        created_at: 'desc'
      },
      take: limit
    })

    // Format messages for response
    const formattedMessages = messages.map(message => ({
      message_id: message.message_id.toString(),
      room_uuid: message.room_uuid,
      author_uuid: message.author_uuid,
      content: message.content,
      message_type: message.message_type,
      reply_to: message.reply_to?.toString(),
      edited_at: message.edited_at,
      created_at: message.created_at,
      flags: message.flags,
      attachments: message.v_chat_message_attachments,
      reactions: message.v_chat_message_reactions,
      zalo_mapping: (message.v_zalo_message_mapping as any)?.[0] || null,
      is_zalo_message: (message.v_zalo_message_mapping as any)?.length > 0,
      zalo_direction: (message.v_zalo_message_mapping as any)?.[0]?.message_direction || null,
      zalo_delivery_status: (message.v_zalo_message_mapping as any)?.[0]?.delivery_status || null
    }))

    // Get conversation metadata
    const conversationInfo = {
      zalo_room_uuid: zaloChatRoom.zalo_room_uuid,
      internal_room_uuid: zaloChatRoom.internal_room_uuid,
      room_status: zaloChatRoom.room_status,
      assigned_agent_uuid: zaloChatRoom.assigned_agent_uuid,
      last_message_at: zaloChatRoom.last_message_at,
      contact: zaloChatRoom.v_zalo_oa_contacts,
      room: zaloChatRoom.v_chat_rooms
    }

    return NextResponse.json({
      conversation: conversationInfo,
      messages: formattedMessages,
      pagination: {
        limit,
        has_more: messages.length === limit,
        oldest_message_id: messages.length > 0 ? messages[messages.length - 1].message_id.toString() : null,
        newest_message_id: messages.length > 0 ? messages[0].message_id.toString() : null
      }
    })
  } catch (error) {
    console.error('Error fetching ZALO conversation history:', error)
    return NextResponse.json(
      {
        error: 'Failed to fetch conversation history'
      },
      { status: 500 }
    )
  }
}

// PUT /api/zalo-oa/messages/[roomId]/read - Mark messages as read
export async function PUT(request: NextRequest, { params }: { params: { roomId: string } }) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { roomId } = params
    const body = await request.json()
    const { last_read_message_id } = body

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 403 })
    }

    // Verify ZALO chat room exists and belongs to user's domain
    const zaloChatRoom = await prisma.v_zalo_oa_chat_rooms.findFirst({
      where: {
        zalo_room_uuid: roomId,
        domain_uuid: user.domain_uuid // Critical: Domain isolation
      }
    })

    if (!zaloChatRoom) {
      return NextResponse.json({ error: 'ZALO chat room not found' }, { status: 404 })
    }

    // Update unread count for this user and room using SINGLE SOURCE OF TRUTH
    const existingCount = await prisma.v_chat_unread_counts.findFirst({
      where: {
        user_uuid: session.user.id,
        room_uuid: zaloChatRoom.internal_room_uuid
      }
    })

    if (existingCount) {
      await prisma.v_chat_unread_counts.update({
        where: {
          count_uuid: existingCount.count_uuid
        },
        data: {
          unread_count: 0,
          last_read_message_id: last_read_message_id ? BigInt(last_read_message_id) : null,
          last_updated: new Date()
        }
      })
    } else {
      await prisma.v_chat_unread_counts.create({
        data: {
          user_uuid: session.user.id,
          room_uuid: zaloChatRoom.internal_room_uuid,
          unread_count: 0,
          last_read_message_id: last_read_message_id ? BigInt(last_read_message_id) : null,
          last_updated: new Date()
        }
      })
    }

    return NextResponse.json({
      success: true,
      message: 'Messages marked as read'
    })
  } catch (error) {
    console.error('Error marking ZALO messages as read:', error)
    return NextResponse.json(
      {
        error: 'Failed to mark messages as read'
      },
      { status: 500 }
    )
  }
}
