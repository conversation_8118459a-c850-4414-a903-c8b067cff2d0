// Next Imports

import { hash } from 'bcryptjs'

import { prisma } from '@/libs/db/prisma'

export async function POST(req: Request) {
  // Vars
  const { user_uuid, newPassword, newPasswordConfirm } = await req.json()

  // console.log('user uuid is %s', user?.user_email)
  if (newPassword !== newPasswordConfirm) {
    return new Response(JSON.stringify({ error: 'New passwords do not match' }), {
      status: 401,
      statusText: 'Unauthorized Access'
    })
  }

  const newHashedPassword = await hash(newPassword, 10)
  const modifiedHash = newHashedPassword.replace('$2y$', '$2a$')

  const updatedUser = await prisma.v_users.update({
    where: {
      user_uuid: user_uuid
    },
    data: {
      password: modifiedHash
    }
  })

  return new Response(JSON.stringify({ success: true, message: 'Password successfully changed', updatedUser }), {
    status: 200
  })
}
