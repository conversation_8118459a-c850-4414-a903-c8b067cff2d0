// Next Imports

import { NextResponse } from 'next/server'

import type { User } from 'next-auth'
import { compare } from 'bcryptjs'

import { prisma } from '@/libs/db/prisma'

export async function POST(req: Request) {
  // Vars
  const { email, password } = await req.json()

  // console.log('username is %s', email.split('@')[0])

  // dùng xác thực qua database

  const [username, domain_name] = email.split('@')

  const domain_obj = await prisma.v_domains.findFirst({
    select: { domain_name: true, domain_uuid: true },
    where: { domain_name: String(domain_name) }
  })

  const user = await prisma.v_users.findFirst({
    where: {
      username: username, // lấy username của email usernam@domain
      domain_uuid: domain_obj?.domain_uuid
    }
  })

  // console.log('user uuid is %s', user?.user_email)

  if (!user || !user?.password) {
    // We return 401 status code and error message if user is not found
    return NextResponse.json(
      {
        // We create object here to separate each error message for each field in case of multiple errors
        message: ['Email or Password is invalid']
      },
      {
        status: 401,
        statusText: 'Unauthorized Access'
      }
    )
  } else {
    // tương thích bcrypt giữa php và nodejs
    //https://itecnote.com/tecnote/php-comparing-bcrypt-hash-between-php-and-nodejs/
    const _pass = user.password.replace('$2y$', '$2a$')

    // console.log('hash php is %s', _pass)

    const isCorrectPassword = await compare(password, _pass)

    if (isCorrectPassword) {
      const group_users = await prisma.view_users.findFirst({
        where: {
          user_uuid: user.user_uuid // lấy username của email usernam@domain
        }
      })

      // kiểm tra xem user này có phải là Agent Call Center không để lấy UUID lưu session dùng về sau

      const agent = await prisma.v_call_center_agents.findFirst({
        where: {
          user_uuid: user.user_uuid
        },
        select: {
          call_center_agent_uuid: true
        }
      })

      const call_center_agent_uuid = agent?.call_center_agent_uuid ?? ''

      // console.log('agent tìm được ', agent)

      // kiểm tra xem user này có profile chưa để lấy thông tin profile id và image
      const profile = await prisma.v_employee.findFirst({
        where: {
          user_uuid: user.user_uuid
        },
        select: {
          employee_uuid: true,
          avatar: true
        }
      })

      const domain = await prisma.v_domains.findFirst({
        where: {
          domain_uuid: user.domain_uuid || ''
        }
      })

      const _user: User = {
        id: user.user_uuid,
        domain: user.domain_uuid,
        domain_uuid: user.domain_uuid || '',
        domain_name: domain?.domain_name || '',
        email: user.user_email,
        name: user.username,
        role: group_users?.group_names ?? 'user',
        profileId: profile?.employee_uuid ?? null,
        image: profile?.avatar ?? '/images/avatars/1.png',
        status: 'Online',
        agent: call_center_agent_uuid
      }

      return NextResponse.json(_user)
    } else {
      // We return 401 status code and error message if user is not found
      return NextResponse.json(
        {
          // We create object here to separate each error message for each field in case of multiple errors
          message: ['Email or Password is invalid']
        },
        {
          status: 401,
          statusText: 'Unauthorized Access'
        }
      )
    }
  }
}
