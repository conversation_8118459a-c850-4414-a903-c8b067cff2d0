// Next Imports
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { prisma } from '@/libs/db/prisma'
import getSession from '@/actions/getSession'

export async function GET(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  const session = await getSession()

  if (!session) {
    return NextResponse.json({ warning: 'Please authentication !!!' })
  }

  switch (tab) {
    case 'listChat':
      // console.log(' uuid  = %s', uuid)
      const agent_id = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của extensions cần truy vấn

      if (agent_id == '') return NextResponse.json([])

      const listChat = await prisma.v_call_center_agents_chat.findMany({
        include: {
          v_call_center_agents_chat_message: true
        },
        where: {
          agent_uuid: agent_id // lấy extensions trong domain đăng nhập
        }
      })

      console.log(listChat)

      return NextResponse.json(listChat)
      break

    default:
      return NextResponse.json({ warning: 'Please choose terminal endpoint !!!' })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  switch (tab) {
    case 'updateChat':
      const { agent_uuid, unseenMsgs, messages } = await request.json()

      const newChat = await prisma.v_call_center_agents_chat.create({
        data: {
          agent_uuid,
          unseenMsgs,
          v_call_center_agents_chat_message: {
            create: messages.map((message: { senderId: string; message: string; time: string | Date }) => ({
              senderId: message.senderId,
              message: message.message,
              time: new Date(message.time)
            }))
          }
        },
        include: {
          v_call_center_agents_chat_message: true
        }
      })

      return NextResponse.json(newChat)
      break

    default:
      return NextResponse.json('Please choose terminal endpoint !!!')
  }
}
