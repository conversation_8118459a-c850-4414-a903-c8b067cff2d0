import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'

export async function GET() {
  const session = await getSession()

  if (!session) {
    return NextResponse.json('Please authentication !!!')
  }

  const domain_uuid = session?.user?.domain ?? ''

  console.log(' uuid  = %s', domain_uuid)

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  const users: object[] = await prisma.v_users.findMany({
    where: {
      domain_uuid: domain_uuid
    },
    select: {
      user_uuid: true,
      username: true
    }
  })

  const serializedUsers = users.map(user => {
    return {
      ...user
    }
  })

  return NextResponse.json(serializedUsers)
}
