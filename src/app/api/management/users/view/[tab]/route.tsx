// Next Imports
import fs from 'fs'

import path from 'path'

import type { NextRequest } from 'next/server'

import { NextResponse } from 'next/server'

import { v4 as uuidv4 } from 'uuid'

import getSession from '@/actions/getSession'

import { prisma } from '@/libs/db/prisma'

/**
 * Get company
 *
 * @param request API request
 * @returns List of v_employee
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getSession()

    if (!session) {
      return NextResponse.json('Please authenticate !!!', { status: 401 })
    }

    const domain_uuid = session?.user?.domain ?? ''

    if (!domain_uuid) {
      return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    const assignment_uuid = searchParams.get('assignment_uuid')
    const department_uuids = searchParams.get('department_uuid')?.split(',')

    let employees = []

    // Handle case when department UUIDs are provided
    if (department_uuids && department_uuids.length > 0) {
      employees = await prisma.v_employee.findMany({
        where: {
          department_uuid: department_uuids.length === 1 ? department_uuids[0] : { in: department_uuids },
          domain_uuid
        }
      })
    }

    // Handle case when specific employee ID is provided
    else if (id) {
      const employee = await prisma.v_employee.findFirst({
        where: { employee_uuid: id, domain_uuid },
        include: { v_department: true }
      })

      if (employee) employees.push(employee)
    } else if (assignment_uuid) {
      const assignment = await prisma.v_crm_task_interval_assignment.findFirst({
        where: { assignment_uuid: assignment_uuid, domain_uuid }
      })

      const departments = await prisma.v_department.findMany({
        where: { branch_uuid: assignment?.assigned_uuid || '', domain_uuid },
        include: { v_employee: true }
      })

      employees = departments.flatMap(department => department.v_employee)
    }

    // Handle fetching all employees if no filters are provided
    else {
      employees = await prisma.v_employee.findMany({
        where: { domain_uuid },
        include: { v_department: true }
      })
    }

    // Serialize the employees data
    const serializedEmployees = employees.map(employee => ({
      ...employee,
      fullName: `${employee.last_name} ${employee.middle_name} ${employee.first_name}`,
      avatar: employee.avatar || '/images/avatars/2.png',
      coverImg: '/images/pages/profile-banner.png'
    }))

    return new Response(JSON.stringify(serializedEmployees), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    })
  } catch (error) {
    console.error('Error fetching employee:', error)

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

/**
 * Create v_employee
 *
 * @param request POST REQUEST
 * @returns Newly created copmany
 */

export async function POST(request: NextRequest, { params }: { params: { tab: string } }) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate !!!' }, { status: 401 })
  }

  const domain_uuid = session.user?.domain ?? ''

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  try {
    const { tab } = params

    if (tab === 'upload-avatar') {
      try {
        const formData = await request.formData()
        const file = formData.get('file') as File
        const employee_uuid = formData.get('employee_uuid') as string

        if (!file || !employee_uuid) {
          return NextResponse.json({ error: 'Missing file or employee_uuid' }, { status: 400 })
        }

        const uploadDir = path.join(process.cwd(), 'public', 'images', 'avatars')

        // const uploadDir = path.join('/', 'images', 'avatars')
        if (!fs.existsSync(uploadDir)) {
          fs.mkdirSync(uploadDir, { recursive: true })
        }

        const filePath = path.join(uploadDir, file.name)

        const arrayBuffer = await file.arrayBuffer()
        const uint8Array = new Uint8Array(arrayBuffer)

        await fs.promises.writeFile(filePath, uint8Array)

        const employeeUpdate = await prisma.v_employee.update({
          where: { employee_uuid: employee_uuid },

          // data: { avatar: filePath }
          data: { avatar: path.join('/', 'images', 'avatars', file.name) }
        })

        return new Response(
          JSON.stringify({
            message: 'Employee updated successfully!',
            data: {
              ...employeeUpdate,
              fullName: `${employeeUpdate.last_name} ${employeeUpdate.middle_name} ${employeeUpdate.first_name}`,
              avatar: employeeUpdate.avatar || '/images/avatars/2.png',
              coverImg: '/images/pages/profile-banner.png'
            }
          }),
          {
            status: 200,
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
      } catch (error) {
        console.log(error)

        return NextResponse.json({ message: 'Error upload avatar' }, { status: 400 })
      }
    }

    const data = await request.json()

    const {
      first_name,
      last_name,
      middle_name,
      gender,
      birthday,
      email,
      phone,
      description,
      title,
      avatar,
      employee_uuid,
      user_uuid
    } = data

    const employeeToUpdate = {
      employee_uuid: employee_uuid || uuidv4(),
      domain_uuid,
      first_name,
      user_uuid,
      last_name,
      middle_name,
      birthday,
      gender,
      email,
      phone,
      description,
      title,
      avatar
    }

    const newEmployee = await prisma.v_employee.upsert({
      where: { employee_uuid: employeeToUpdate.employee_uuid },
      update: {
        ...employeeToUpdate,
        domain_uuid,
        update_date: new Date(),
        update_user: user_uuid
      },
      create: {
        ...employeeToUpdate,
        domain_uuid,
        insert_date: new Date(),
        insert_user: user_uuid
      },
      include: {
        v_department: true
      }
    })

    const serializedEmployees = {
      ...newEmployee,
      fullName: `${newEmployee.last_name} ${newEmployee.middle_name} ${newEmployee.first_name}`,
      avatar: newEmployee.avatar || '/images/avatars/2.png',
      coverImg: '/images/pages/profile-banner.png'
    }

    return new Response(JSON.stringify({ message: 'Employee created successfully!', data: serializedEmployees }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error('Error creating employee:', error)

    return NextResponse.json({ message: 'An error occurred', error }, { status: 500 })
  } finally {
    await prisma.$disconnect()
  }
}

/**
 * Update employee
 *
 * @param request PUT request
 * @returns Updated employee
 */

export async function PUT(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const session = await getSession()

  if (!session) {
    return NextResponse.json('Please authenticate !!!', { status: 401 })
  }

  const user_uuid = session?.user?.id ?? ''
  const domain_uuid = session?.user?.domain ?? ''

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  const employee_uuid = searchParams.get('id')

  if (!employee_uuid) {
    return NextResponse.json({ error: 'Missing id' }, { status: 400 })
  }

  const data = await request.json()
  const { first_name, last_name, middle_name, birthday, gender, email, phone, description, title, avatar } = data

  try {
    const updatedemployee = await prisma.v_employee.update({
      where: {
        employee_uuid: employee_uuid,
        domain_uuid: domain_uuid
      },
      data: {
        insert_user: user_uuid,
        update_user: user_uuid,
        first_name,
        last_name,
        middle_name,
        birthday,
        gender,
        email,
        phone,
        description,
        title,
        avatar
      }
    })

    console.log(updatedemployee)

    return NextResponse.json(updatedemployee, { status: 200 })
  } catch (error) {
    console.error('Error updating employee:', error)

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  } finally {
    await prisma.$disconnect()
  }
}

export async function DELETE(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const user_uuid = session?.user?.id ?? ''
  const { searchParams } = new URL(request.url)
  const id = searchParams.get('id')

  console.log('id = %s', id)

  if (!domain_uuid || !id) {
    return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 })
  }

  try {
    await prisma.v_employee.delete({
      where: {
        employee_uuid: id,
        domain_uuid: domain_uuid,
        insert_user: user_uuid
      }
    })

    return NextResponse.json({ message: 'Note deleted successfully', employee_id: id }, { status: 200 })
  } catch (error) {
    console.error('Failed to delete note:', error)

    return NextResponse.json({ error: 'Failed to delete note' }, { status: 500 })
  }
}
