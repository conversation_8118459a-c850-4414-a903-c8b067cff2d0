// Next Imports
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'

/**
 * Get company
 *
 * @param request API request
 * @returns List of v_extensions
 */
export async function GET(request: NextRequest) {
  const session = await getSession()

  try {
    if (!session) {
      return NextResponse.json('Please authentication !!!')
    }

    const domain_uuid = session?.user?.domain ?? ''

    if (!domain_uuid) {
      return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
    }

    const { searchParams } = new URL(request.url)
    const user_uuid = searchParams.get('user_uuid')

    let extensions: any[] = []

    if (user_uuid) {
      const resp = await prisma.v_extension_users.findMany({
        where: {
          user_uuid: user_uuid,
          domain_uuid
        }
      })

      extensions = resp
    } else {
      const resp = await prisma.v_extension_users.findMany({
        where: {
          domain_uuid: domain_uuid
        }
      })

      extensions = resp
    }

    const extension_uuids = extensions.map(i => i.extension_uuid).filter(uuid => uuid !== null)

    const v_extensions = await prisma.v_extensions.findMany({
      where: {
        domain_uuid: domain_uuid,
        extension_uuid: {
          in: extension_uuids
        }
      },
      select: {
        extension_uuid: true,
        extension: true
      }
    })

    const extensionMap = v_extensions.reduce((map: any, ext) => {
      map[ext.extension_uuid] = ext.extension

      return map
    }, {})

    const serializedExtensions = extensions.map(ext => ({
      ...ext,
      extension: extensionMap[ext.extension_uuid]
    }))

    return new Response(JSON.stringify(serializedExtensions), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error('Error fetching v_extensions:', error)

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  } finally {
    await prisma.$disconnect()
  }
}
