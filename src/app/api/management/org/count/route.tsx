// Next Imports
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'

import { prisma } from '@/libs/db/prisma'

/**
 * Get total
 *
 * @param request API request
 * @returns
 */
export async function GET() {
  const session = await getSession()

  try {
    if (!session) {
      return NextResponse.json('Please authenticate !!!', { status: 401 })
    }

    const domain_uuid = session?.user?.domain ?? ''

    if (!domain_uuid) {
      return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
    }

    const [totalBranches, totalDepartments, totalEmployees] = await Promise.all([
      prisma.v_branch.count({
        where: {
          domain_uuid: domain_uuid,
          company_uuid: domain_uuid
        }
      }),
      prisma.v_department.count({
        where: {
          domain_uuid: domain_uuid
        }
      }),
      prisma.v_employee.count({
        where: {
          domain_uuid: domain_uuid
        }
      })
    ])

    return new Response(JSON.stringify({ totalBranches, totalDepartments, totalEmployees }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error('Error fetching company:', error)

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  } finally {
    await prisma.$disconnect()
  }
}
