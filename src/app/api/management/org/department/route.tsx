import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'

export async function GET(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''

  console.log('domain_uuid = %s', domain_uuid)

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  const searchParams = request.nextUrl.searchParams
  const branch_uuid = searchParams.get('branch_uuid')

  console.log('branch_uuid = %s', branch_uuid)

  const department_id = searchParams.get('id')

  console.log('department_id = %s', department_id)

  try {
    if (department_id) {
      /**
       * Get department by uuid
       */
      const department = await prisma.v_department.findUniqueOrThrow({
        where: {
          department_uuid: department_id,
          domain_uuid: domain_uuid
        },
        include: {
          v_employee: true
        }
      })

      return NextResponse.json(department)
    }

    if (branch_uuid) {
      const branchUuids = branch_uuid.split(',')

      if (branchUuids.length === 1) {
        /**
         * Get a single branch by UUID
         */
        const department = await prisma.v_department.findMany({
          where: {
            branch_uuid: branchUuids[0],
            domain_uuid: domain_uuid
          }
        })

        return NextResponse.json(department)
      } else {
        /**
         * Get multiple branches by UUIDs
         */
        const department = await prisma.v_department.findMany({
          where: {
            branch_uuid: {
              in: branchUuids
            },
            domain_uuid: domain_uuid
          }
        })

        return NextResponse.json(department)
      }
    }

    const department = await prisma.v_department.findMany({
      where: {
        branch_uuid: branch_uuid,
        domain_uuid: domain_uuid
      }
    })

    return NextResponse.json(department)
  } catch (error) {
    console.error(error)

    return new Response(JSON.stringify({ message: 'An error occurred', error }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }
}

export async function POST(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const user_uuid = session?.user?.id ?? ''

  console.log('domain_uuid = %s', domain_uuid)

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  try {
    const data = await request.json()
    const { department_uuid, department_name, branch_uuid, contact_uuid, description, employees } = data

    const departmentToUpdate = {
      department_uuid,
      department_name,
      branch_uuid,
      contact_uuid,
      description
    }

    const newDepartment = await prisma.$transaction(async (prisma: any) => {
      // Remove old
      if (!!employees) {
        await prisma.v_employee.updateMany({
          where: {
            department_uuid: department_uuid
          },
          data: {
            department_uuid: null
          }
        })

        // Update employee with department uuid
        await prisma.v_employee.updateMany({
          where: {
            employee_uuid: {
              in: employees
            }
          },
          data: {
            department_uuid: department_uuid
          }
        })
      }

      const newDepartment = await prisma.v_department.upsert({
        where: { department_uuid: department_uuid },
        update: {
          ...departmentToUpdate,
          domain_uuid,
          update_date: new Date(),
          update_user: user_uuid
        },
        create: {
          ...departmentToUpdate,
          domain_uuid,
          insert_date: new Date(),
          insert_user: user_uuid
        },
        include: {
          v_employee: true
        }
      })

      return newDepartment
    })

    return new Response(JSON.stringify({ message: 'Form submitted successfully!', data: newDepartment }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error(error)

    return new Response(JSON.stringify({ message: 'An error occurred', error }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }
}

export async function PUT(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const user_uuid = session?.user?.id ?? ''

  console.log('domain_uuid = %s', domain_uuid)

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  try {
    const data = await request.json()
    const { department_name, description, department_uuid } = data

    if (!department_uuid) {
      return NextResponse.json({ error: 'Missing department_uuid' }, { status: 400 })
    }

    // Execute the raw SQL query to update the existing note
    const updatedDepartment = await prisma.$queryRaw`
      UPDATE v_branch
      SET
        department_name = ${department_name},
        description = ${description},
        update_date = NOW(),
        update_user = ${user_uuid}::uuid
      WHERE department_uuid= ${department_uuid}::uuid
        AND domain_uuid = ${domain_uuid}::uuid
      RETURNING *
    `

    if (!updatedDepartment) {
      return NextResponse.json({ error: 'Note not found or not authorized to update' }, { status: 404 })
    }

    return NextResponse.json({ message: 'Note updated successfully!', updatedDepartment }, { status: 200 })
  } catch (error) {
    console.error(error)

    return NextResponse.json({ message: 'An error occurred', error }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const user_uuid = session?.user?.id ?? ''
  const { searchParams } = new URL(request.url)
  const department_uuid = searchParams.get('id')

  if (!domain_uuid || !department_uuid) {
    return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 })
  }

  try {
    await prisma.v_department.delete({
      where: {
        department_uuid: department_uuid,
        domain_uuid: domain_uuid,
        insert_user: user_uuid
      }
    })

    return NextResponse.json({ message: 'Note deleted successfully' }, { status: 200 })
  } catch (error) {
    console.error('Failed to delete note:', error)

    return NextResponse.json({ error: 'Failed to delete note' }, { status: 500 })
  }
}
