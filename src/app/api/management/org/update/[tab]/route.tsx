// Next Imports
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

// import { prisma } from '@/libs/db/prisma'
// import getSession from '@/actions/getSession'

// import type { OrgType } from '@/types/apps/management/orgTypes'

export async function GET(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab
  const result = 'Not support !!!'

  switch (tab) {
    default:
      return NextResponse.json(result)
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  switch (tab) {
    case 'company':
      return NextResponse.json('Not Support !!!')
      break

    default:
      return NextResponse.json('Please choose terminal endpoint !!!')
  }
}
