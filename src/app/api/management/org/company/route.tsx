// Next Imports
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'

import { prisma } from '@/libs/db/prisma'

/**
 * Get company
 *
 * @param request API request
 * @returns List of v_company
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getSession()

    if (!session) {
      return NextResponse.json('Please authenticate !!!', { status: 401 })
    }

    const domain_uuid = session?.user?.domain ?? ''

    if (!domain_uuid) {
      return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    let company: any[] = []

    if (id) {
      // Get company by uuid
      const resp = await prisma.v_company.findFirst({
        where: {
          company_uuid: id
        }
      })

      company.push(resp)
    } else {
      const resp = await prisma.v_company.findMany({
        where: {
          domain_uuid: domain_uuid
        }
      })

      company = resp
    }

    return new Response(JSON.stringify({ ...company }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error('Error fetching company:', error)

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  } finally {
    await prisma.$disconnect()
  }
}

/**
 * Create v_company
 *
 * @param request POST REQUEST
 * @returns Newly created copmany
 */

export async function POST(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate !!!' }, { status: 401 })
  }

  const domain_uuid = session.user?.domain ?? ''
  const user_uuid = session.user?.id ?? ''

  console.log('domain_uuid = %s', domain_uuid)

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  try {
    const data = await request.json()

    console.log('Received data:', data) // Log the data received

    const { company_name, logo, tax_id, description } = data

    // Create a new company record
    const newCompany = await prisma.v_company.upsert({
      where: { company_uuid: domain_uuid },
      update: {
        company_name,
        logo,
        tax_id,
        description,
        update_date: new Date(),
        update_user: user_uuid
      },
      create: {
        company_uuid: domain_uuid,
        domain_uuid,
        company_name,
        logo,
        tax_id,
        description,
        insert_date: new Date(),
        insert_user: user_uuid
      }
    })

    return new Response(JSON.stringify({ message: 'Form submitted successfully!', data: newCompany }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error(error)

    return new Response(JSON.stringify({ message: 'An error occurred', error }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } finally {
    await prisma.$disconnect() // Close Prisma Client connection
  }
}

/**
 * Update company
 *
 * @param request PUT request
 * @returns Updated company
 */

export async function PUT(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const session = await getSession()

  if (!session) {
    return NextResponse.json('Please authenticate !!!', { status: 401 })
  }

  const user_uuid = session?.user?.id ?? ''
  const domain_uuid = session?.user?.domain ?? ''

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  const company_uuid = searchParams.get('id')

  if (!company_uuid) {
    return NextResponse.json({ error: 'Missing id' }, { status: 400 })
  }

  const data = await request.json()
  const { company_name, logo, tax_id, description } = data

  try {
    const updatedcompany = await prisma.v_company.update({
      where: {
        company_uuid: company_uuid,
        domain_uuid: domain_uuid
      },
      data: {
        insert_user: user_uuid,
        update_user: user_uuid,
        company_name,
        logo,
        tax_id,
        description
      }
    })

    console.log(updatedcompany)

    return NextResponse.json(updatedcompany, { status: 200 })
  } catch (error) {
    console.error('Error updating company:', error)

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  } finally {
    await prisma.$disconnect() // Close Prisma Client connection
  }
}
