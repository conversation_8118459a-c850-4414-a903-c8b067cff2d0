import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'

export async function GET(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''

  console.log('domain_uuid = %s', domain_uuid)

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  const searchParams = request.nextUrl.searchParams
  const branch_uuid = searchParams.get('id')

  console.log('branch_uuid = %s', branch_uuid)

  try {
    if (branch_uuid) {
      /**
       * Get branch by uuid
       */
      const branch = await prisma.v_branch.findUniqueOrThrow({
        where: {
          branch_uuid: branch_uuid,
          company_uuid: domain_uuid,
          domain_uuid: domain_uuid
        },
        include: {
          v_department: true,
          v_branch_media: true,
          v_branch_address: true
        }
      })

      return NextResponse.json(branch)
    }

    const branchsInfomation = await prisma.v_branch.findMany({
      where: {
        company_uuid: domain_uuid,
        domain_uuid: domain_uuid
      },
      include: {
        v_department: true,
        v_branch_media: true,
        v_branch_address: true
      }
    })

    return NextResponse.json(branchsInfomation)
  } catch (error) {
    console.error(error)

    return new Response(JSON.stringify({ message: 'An error occurred', error }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }
}

export async function POST(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const user_uuid = session?.user?.id ?? ''

  console.log('domain_uuid = %s', domain_uuid)

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  try {
    const data = await request.json()
    const { branch, address, phone, email } = data
    const { branch_name, company_uuid, contact_uuid, logo, tax_id, description, is_headquarter } = branch

    const branchToUpdate = {
      branch_name,
      company_uuid,
      contact_uuid,
      logo,
      tax_id,
      description,
      is_headquarter,
      update_date: new Date(),
      update_user: user_uuid
    }

    const newBranchInfomation = await prisma.$transaction(async (prisma: any) => {
      const newBranch = await prisma.v_branch.upsert({
        where: { branch_uuid: branch.branch_uuid },
        update: {
          ...branchToUpdate,
          domain_uuid,
          company_uuid: domain_uuid,
          update_date: new Date(),
          update_user: user_uuid
        },
        create: {
          ...branchToUpdate,
          company_uuid: domain_uuid,
          domain_uuid,
          insert_date: new Date(),
          insert_user: user_uuid
        }
      })

      const newAddress = await prisma.v_branch_address.upsert({
        where: { branch_address_uuid: address.branch_address_uuid },
        update: {
          ...address,
          domain_uuid,
          branch_uuid: newBranch.branch_uuid,
          update_date: new Date(),
          update_user: user_uuid
        },
        create: {
          ...address,
          branch_uuid: newBranch.branch_uuid,
          domain_uuid,
          insert_date: new Date(),
          insert_user: user_uuid
        }
      })

      const phoneMedia = await prisma.v_branch_media.upsert({
        where: { branch_media_uuid: phone.branch_media_uuid },
        update: {
          ...phone,
          domain_uuid,
          company_media_type: 'phone',
          branch_uuid: newBranch.branch_uuid,
          update_date: new Date(),
          update_user: user_uuid
        },
        create: {
          ...phone,
          branch_uuid: newBranch.branch_uuid,
          company_media_type: 'phone',
          domain_uuid,
          insert_date: new Date(),
          insert_user: user_uuid
        }
      })

      const emailMedia = await prisma.v_branch_media.upsert({
        where: { branch_media_uuid: email.branch_media_uuid },
        update: {
          ...email,
          domain_uuid,
          company_media_type: 'email',
          branch_uuid: newBranch.branch_uuid,
          update_date: new Date(),
          update_user: user_uuid
        },
        create: {
          ...email,
          company_media_type: 'email',
          branch_uuid: newBranch.branch_uuid,
          domain_uuid,
          insert_date: new Date(),
          insert_user: user_uuid
        }
      })

      return {
        branch: newBranch,
        address: newAddress,
        phone: phoneMedia,
        email: emailMedia
      }
    })

    return new Response(JSON.stringify({ message: 'Form submitted successfully!', data: newBranchInfomation }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error(error)

    return new Response(JSON.stringify({ message: 'An error occurred', error }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }
}

export async function PUT(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const user_uuid = session?.user?.id ?? ''

  console.log('domain_uuid = %s', domain_uuid)

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  try {
    const data = await request.json()
    const { branch_uuid, branch_name, logo, tax_id, description } = data

    if (!branch_uuid) {
      return NextResponse.json({ error: 'Missing branch_uuid' }, { status: 400 })
    }

    // Execute the raw SQL query to update the existing note
    const updatedBranch = await prisma.$queryRaw`
      UPDATE v_branch
      SET
        logo = ${logo},
        branch_name = ${branch_name},
        tax_id = ${tax_id},
        description = ${description},
        update_date = NOW(),
        update_user = ${user_uuid}::uuid
      WHERE branch_uuid= ${branch_uuid}::uuid
        AND domain_uuid = ${domain_uuid}::uuid
      RETURNING *
    `

    if (!updatedBranch) {
      return NextResponse.json({ error: 'Note not found or not authorized to update' }, { status: 404 })
    }

    return NextResponse.json({ message: 'Note updated successfully!', updatedBranch }, { status: 200 })
  } catch (error) {
    console.error(error)

    return NextResponse.json({ message: 'An error occurred', error }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const user_uuid = session?.user?.id ?? ''
  const { searchParams } = new URL(request.url)
  const branch_uuid = searchParams.get('id')

  console.log('branch_uuid', branch_uuid)

  if (!domain_uuid || !branch_uuid) {
    return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 })
  }

  try {
    await prisma.$transaction(async (prisma: any) => {
      await prisma.v_branch_address.deleteMany({
        where: { domain_uuid: domain_uuid, branch_uuid: branch_uuid }
      })

      await prisma.v_branch_media.deleteMany({
        where: { domain_uuid: domain_uuid, branch_uuid: branch_uuid }
      })

      await prisma.v_department.deleteMany({
        where: { domain_uuid: domain_uuid, branch_uuid: branch_uuid }
      })

      await prisma.v_branch.delete({
        where: {
          branch_uuid: branch_uuid,
          domain_uuid: domain_uuid,
          insert_user: user_uuid
        }
      })
    })

    return NextResponse.json({ message: 'Branch deleted successfully', branch_uuid }, { status: 200 })
  } catch (error) {
    console.error('Failed to delete branch:', error)

    return NextResponse.json({ error: 'Failed to delete branch' }, { status: 500 })
  }
}
