// Next Imports
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { prisma } from '@/libs/db/prisma'
import getSession from '@/actions/getSession'

export async function GET(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab
  const result = ''

  switch (tab) {
    case 'list':
      const session = await getSession()

      if (!session) {
        return NextResponse.json('Please authentication !!!')
      }

      const uuid = session?.user?.domain ?? ''

      console.log(' uuid  = %s', uuid)

      const org = await prisma.v_company.findMany({
        where: {
          domain_uuid: uuid // lấy user trong domain đăng nhập
        }
      })

      return NextResponse.json(org)
      break
    case 'reload':
      return NextResponse.json(result)
      break

    default:
      return NextResponse.json('Please choose terminal endpoint !!!')
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  const result = ''

  switch (tab) {
    case 'status':
      return NextResponse.json(result)
      break
    case 'reload':
      // reload hệ thống Freeswitch

      return NextResponse.json(result)
      break

    default:
      return NextResponse.json('Please choose terminal endpoint !!!')
  }
}
