import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'
import { handleTransaction } from '../transactionHelper'

/**
 * Get tasks by uuid or get a list of tasks
 *
 * @param request API request
 * @returns List of v_crm_task
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getSession()

    if (!session) {
      return NextResponse.json('Please authenticate !!!', { status: 401 })
    }

    const domain_uuid = session?.user?.domain ?? ''

    if (!domain_uuid) {
      return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (id) {
      // Get task by uuid
      const task = await prisma.v_crm_task.findFirst({
        where: {
          task_uuid: id
        }
      })

      return NextResponse.json(task, { status: 200 })
    }

    const taskInterval = searchParams.get('taskInterval')

    if (taskInterval) {
      const taskIntervals = await prisma.v_crm_task.findMany({
        where: {
          assignment_uuid: {
            not: null
          }
        },
        include: {
          v_crm_task_interval_assignment: true
        }
      })

      const taskIntervalIds = taskIntervals
        .map(task => task.v_crm_task_interval_assignment?.user_task_uuid)
        .filter(user_task_uuid => user_task_uuid !== null) as string[]

      const intervals = await prisma.v_crm_task_interval.findMany({
        where: {
          user_task_uuid: {
            in: taskIntervalIds
          }
        }
      })

      const categories = await prisma.v_crm_task_category.findMany({
        where: {
          domain_uuid: domain_uuid
        }
      })

      const categoryMap = new Map()

      categories.forEach(category => {
        categoryMap.set(category.task_category_uuid, category)
      })

      const mappedTaskIntervals = taskIntervals.map(({ v_crm_task_interval_assignment, ...task }) => {
        const intervalAssignment = v_crm_task_interval_assignment

        const matchedInterval = intervals.find(
          interval => interval.user_task_uuid === intervalAssignment?.user_task_uuid
        )

        return {
          ...task,
          interval: matchedInterval?.interval,
          task_category_title: categoryMap.get(matchedInterval?.task_category_uuid).task_category_title
        }
      })

      return NextResponse.json(mappedTaskIntervals, { status: 200 })
    }

    // Get all tasks
    const allTasks = await prisma.v_crm_task.findMany({
      where: {
        assignment_uuid: null
      }
    })

    return NextResponse.json(allTasks, { status: 200 })
  } catch (error) {
    console.error('Error fetching tasks:', error)

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  } finally {
    await prisma.$disconnect() // Close Prisma Client connection
  }
}

/**
 * Create v_crm_task
 *
 * @param request POST REQUEST
 * @returns Newly created task
 */
export async function POST(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate !!!' }, { status: 401 })
  }

  const domain_uuid = session.user?.domain ?? ''
  const user_uuid = session.user?.id ?? ''

  console.log('domain_uuid = %s', domain_uuid)

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  try {
    const data = await request.json()

    console.log('Received data:', data) // Log the data received

    const { task_title, task_description, date_end_task, assigned_task, user_end_task, status, priority } = data

    const newTask = await handleTransaction(
      {
        operation: 'CREATE',
        model: 'v_crm_task',
        user_uuid,
        domain_uuid
      },
      async prismaClient => {
        const newTask = await prismaClient.v_crm_task.create({
          data: {
            domain_uuid,
            user_insert_task: user_uuid,
            insert_user: user_uuid,
            task_title,
            task_description,
            date_end_task,
            assigned_task,
            user_end_task,
            status,
            priority
          }
        })

        return newTask
      }
    )

    return new Response(JSON.stringify({ message: 'Form submitted successfully!', data: newTask }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error(error)

    return new Response(JSON.stringify({ message: 'An error occurred', error }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } finally {
    await prisma.$disconnect() // Close Prisma Client connection
  }
}

/**
 * Update task
 *
 * @param request PUT request
 * @returns Updated task
 */
export async function PUT(request: Request) {
  const { searchParams } = new URL(request.url)
  const task_uuid = searchParams.get('id')
  const session = await getSession()

  if (!session) {
    return NextResponse.json('Please authenticate !!!', { status: 401 })
  }

  const domain_uuid = session.user?.domain ?? ''
  const user_uuid = session.user?.id ?? ''

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  if (!task_uuid) {
    return NextResponse.json({ error: 'Missing task_uuid' }, { status: 400 })
  }

  const data = await request.json()

  const { ...updateData } = data

  try {
    const updatedTask = await handleTransaction(
      {
        operation: 'UPDATE',
        model: 'v_crm_task',
        user_uuid,
        domain_uuid,
        whereField: 'task_uuid',
        objectUUID: task_uuid
      },
      async prismaClient => {
        const updatedTask = await prismaClient.v_crm_task.update({
          where: {
            task_uuid: task_uuid,
            domain_uuid: domain_uuid
          },
          data: {
            ...updateData,
            update_user: user_uuid
          }
        })

        return updatedTask
      }
    )

    return NextResponse.json(updatedTask, { status: 200 })
  } catch (error) {
    console.error('Error updating task:', error)

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

/**
 * Delete a task
 *
 * @param request Request object
 * @returns Deleted task info
 */
export async function DELETE(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const user_uuid = session?.user?.id ?? ''
  const { searchParams } = new URL(request.url)
  const task_uuid = searchParams.get('id')

  if (!domain_uuid || !task_uuid) {
    return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 })
  }

  try {
    await handleTransaction(
      {
        operation: 'DELETE',
        model: 'v_crm_task',
        user_uuid,
        domain_uuid,
        whereField: 'task_uuid',
        objectUUID: task_uuid
      },
      async prismaClient => {
        const deletedTask = await prismaClient.v_crm_task.delete({
          where: {
            task_uuid: task_uuid,
            domain_uuid: domain_uuid
          }
        })

        return deletedTask
      }
    )

    return NextResponse.json({ message: 'Task deleted successfully', data: { task_uuid: task_uuid } }, { status: 200 })
  } catch (error) {
    console.error('Failed to delete task:', error)

    return NextResponse.json({ error: 'Failed to delete task' }, { status: 500 })
  }
}
