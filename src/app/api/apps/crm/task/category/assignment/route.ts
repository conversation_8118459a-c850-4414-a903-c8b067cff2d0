import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'

export async function GET(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  const { searchParams } = new URL(request.url)
  const categoryId = searchParams.get('categoryId')

  if (!categoryId) {
    return NextResponse.json({ error: 'Missing categoryId' }, { status: 400 })
  }

  try {
    const categoryInterval = await prisma.v_crm_task_interval.findFirstOrThrow({
      where: {
        task_category_uuid: categoryId,
        domain_uuid: domain_uuid
      }
    })

    const assignment_objs = await prisma.v_crm_task_interval_assignment.findMany({
      where: {
        user_task_uuid: categoryInterval.user_task_uuid,
        domain_uuid: domain_uuid
      }
    })

    // Thay vì trả về [] trực tiếp, trả về một Response rỗng có status 200
    if (assignment_objs.length === 0) {
      return new Response(JSON.stringify([]), {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      })
    }

    const branches = await prisma.v_branch.findMany({
      where: {
        branch_uuid: {
          in: assignment_objs.filter(obj => obj.assigned_type === 'v_branch').map(obj => obj.assigned_uuid)
        },
        domain_uuid: domain_uuid
      }
    })

    const branchMap = Object.fromEntries(branches.map(branch => [branch.branch_uuid, branch.branch_name]))

    const assignmentsWithBranchNames = assignment_objs.map(obj => ({
      ...obj,
      name: obj.assigned_type === 'v_branch' ? branchMap[obj.assigned_uuid] : null
    }))

    return new Response(
      JSON.stringify({ message: 'Task interval retrieve successfully!', data: assignmentsWithBranchNames }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  } catch (error) {
    console.error(error)

    return new Response(JSON.stringify({ message: 'An error occurred', error }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }
}

export async function PUT(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const user_uuid = session?.user?.id ?? ''

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  try {
    const data = await request.json()
    const { assignment_uuid, applied } = data

    const updatedAssignment = await prisma.v_crm_task_interval_assignment.update({
      where: {
        assignment_uuid: assignment_uuid
      },
      data: {
        applied: applied,
        update_user: user_uuid,
        applied_start_time: applied ? new Date() : null
      }
    })

    return new Response(JSON.stringify({ message: 'Task interval updated successfully!', data: updatedAssignment }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error(error)

    return new Response(JSON.stringify({ message: 'An error occurred', error }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }
}
