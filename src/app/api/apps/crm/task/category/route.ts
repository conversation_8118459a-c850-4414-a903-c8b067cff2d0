import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'

/**
 * Get task categories by uuid or get a list of task categories.
 *
 * @param request API request
 * @returns List of v_crm_task_category
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getSession()

    if (!session) {
      return NextResponse.json('Please authenticate !!!', { status: 401 })
    }

    const domain_uuid = session?.user?.domain ?? ''

    if (!domain_uuid) {
      return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (id) {
      // Get a specific task category with its task lists
      const taskCategoryWithLists = await prisma.v_crm_task_category.findFirst({
        where: {
          task_category_uuid: id,
          domain_uuid: domain_uuid
        },
        include: {
          v_crm_task_list: true // Include related task lists
        }
      })

      return NextResponse.json(taskCategoryWithLists, { status: 200 })
    }

    // Get all task categories along with their task lists
    const allTaskCategoriesWithLists = await prisma.v_crm_task_category.findMany({
      where: {
        domain_uuid: domain_uuid
      },
      include: {
        v_crm_task_list: true // Include related task lists
      }
    })

    return NextResponse.json(allTaskCategoriesWithLists, { status: 200 })
  } catch (error) {
    console.error('Error fetching task category:', error)

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  } finally {
    await prisma.$disconnect() // Close Prisma Client connection
  }
}

/**
 * Create a new v_crm_task_category.
 *
 * @param request POST REQUEST
 * @returns Created task category
 */
export async function POST(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json('Please authenticate !!!', { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const user_uuid = session?.user?.id ?? ''

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  try {
    const data = await request.json()
    const { task_category_title, task_category_description } = data

    // Create a new task category
    const taskcategory = await prisma.v_crm_task_category.create({
      data: {
        domain_uuid,
        insert_user: user_uuid,
        update_user: user_uuid,
        task_category_title,
        task_category_description,
        insert_date: new Date(),
        update_date: new Date()
      }
    })

    return NextResponse.json({ message: 'Task category created successfully!', data: taskcategory }, { status: 201 })
  } catch (error) {
    console.error('Error creating taskcategory:', error)

    return NextResponse.json({ message: 'An error occurred', error }, { status: 500 })
  } finally {
    await prisma.$disconnect()
  }
}

/**
 * Update a task category.
 *
 * @param request PUT request
 * @returns Updated task category
 */
export async function PUT(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const session = await getSession()

  if (!session) {
    return NextResponse.json('Please authenticate !!!', { status: 401 })
  }

  const user_uuid = session?.user?.id ?? ''
  const domain_uuid = session?.user?.domain ?? ''

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  const task_category_uuid = searchParams.get('id')

  if (!task_category_uuid) {
    return NextResponse.json({ error: 'Missing id' }, { status: 400 })
  }

  try {
    const data = await request.json()
    const { task_category_title, task_category_description } = data

    // Update task category
    const updatedtaskcategory = await prisma.v_crm_task_category.update({
      where: {
        task_category_uuid: task_category_uuid
      },
      data: {
        update_user: user_uuid,
        task_category_title,
        task_category_description,
        update_date: new Date()
      }
    })

    return NextResponse.json(updatedtaskcategory, { status: 200 })
  } catch (error) {
    console.error('Error updating taskcategory:', error)

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  } finally {
    await prisma.$disconnect()
  }
}

/**
 * Delete a task category.
 *
 * @param request Request object
 * @returns Success or failure message
 */
export async function DELETE(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const { searchParams } = new URL(request.url)
  const task_category_uuid = searchParams.get('id')

  if (!domain_uuid || !task_category_uuid) {
    return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 })
  }

  try {
    await prisma.v_crm_task_list.deleteMany({
      where: {
        task_category_uuid: task_category_uuid
      }
    })
    await prisma.v_crm_task_category.delete({
      where: {
        task_category_uuid: task_category_uuid
      }
    })

    return NextResponse.json(
      { message: 'Task category deleted successfully', data: { task_category_uuid: task_category_uuid } },
      { status: 200 }
    )
  } catch (error) {
    console.error('Failed to delete taskcategory:', error)

    return NextResponse.json({ error: 'Failed to delete taskcategory' }, { status: 500 })
  } finally {
    await prisma.$disconnect() // Close Prisma Client connection
  }
}
