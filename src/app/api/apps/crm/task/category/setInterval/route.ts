import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import type { v_crm_task_interval } from '@prisma/client'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'

export async function GET(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  const { searchParams } = new URL(request.url)
  const categoryId = searchParams.get('categoryId')

  if (!categoryId) {
    return NextResponse.json({ error: 'Missing categoryId' }, { status: 400 })
  }

  try {
    const categoryInterval = await prisma.v_crm_task_interval.findFirstOrThrow({
      where: {
        task_category_uuid: categoryId,
        domain_uuid: domain_uuid
      }
    })

    return new Response(JSON.stringify({ message: 'Task interval retrieve successfully!', data: categoryInterval }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error(error)

    return new Response(JSON.stringify({ message: 'An error occurred', error }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }
}

export async function POST(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const user_uuid = session?.user?.id ?? ''

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  try {
    const data = await request.json()
    const { task_category_uuid, assigned_objects, interval } = data
    let taskInterval: v_crm_task_interval

    const categoryInterval = await prisma.$transaction(async prisma => {
      // Tim task category
      const currentTaskCategory = await prisma.v_crm_task_category.findUniqueOrThrow({
        where: {
          task_category_uuid: task_category_uuid,
          domain_uuid: domain_uuid
        }
      })

      // Update hoac tao task interval
      const existingRecord = await prisma.v_crm_task_interval.findFirst({
        where: {
          task_category_uuid: currentTaskCategory.task_category_uuid,
          domain_uuid: domain_uuid
        }
      })

      if (existingRecord) {
        taskInterval = await prisma.v_crm_task_interval.update({
          where: {
            user_task_uuid: existingRecord.user_task_uuid
          },
          data: {
            interval,
            update_user: user_uuid
          }
        })
      } else {
        taskInterval = await prisma.v_crm_task_interval.create({
          data: {
            task_category_uuid: currentTaskCategory.task_category_uuid,
            domain_uuid,
            interval,
            insert_user: user_uuid
          }
        })
      }

      // Delete old assignment
      // await prisma.v_crm_task_interval_assignment.deleteMany({
      //   where: {
      //     NOT: {
      //       OR: assigned_objects.map((obj: { assigned_uuid: string; assigned_type: string }) => ({
      //         user_task_uuid: taskInterval.user_task_uuid,
      //         assigned_uuid: obj.assigned_uuid,
      //         assigned_type: obj.assigned_type
      //       }))
      //     }
      //   }
      // })

      // Assign task interval
      for (const assigned of assigned_objects) {
        const existingAssignment = await prisma.v_crm_task_interval_assignment.findFirst({
          where: {
            user_task_uuid: taskInterval.user_task_uuid,
            domain_uuid: domain_uuid,
            assigned_uuid: assigned.assigned_uuid,
            assigned_type: assigned.assigned_type
          }
        })

        if (existingAssignment) {
          await prisma.v_crm_task_interval_assignment.update({
            where: {
              assignment_uuid: existingAssignment.assignment_uuid
            },
            data: {
              update_user: user_uuid
            }
          })
        } else {
          await prisma.v_crm_task_interval_assignment.create({
            data: {
              user_task_uuid: taskInterval.user_task_uuid,
              assigned_uuid: assigned.assigned_uuid,
              assigned_type: assigned.assigned_type,
              insert_user: user_uuid,
              domain_uuid: domain_uuid
            }
          })
        }
      }

      return taskInterval
    })

    return new Response(JSON.stringify({ message: 'Task interval updated successfully!', data: categoryInterval }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error(error)

    return new Response(JSON.stringify({ message: 'An error occurred', error }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }
}
