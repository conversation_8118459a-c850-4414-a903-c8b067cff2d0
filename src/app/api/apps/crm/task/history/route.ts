import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { prisma } from '@/libs/db/prisma'
import getSession from '@/actions/getSession'

const IGNORE_FIELDS = ['update_date', 'update_user']

interface AuditLog {
  audit_log_uuid: string
  table_name: string
  object_uuid: string | null
  operation: string
  old_data: any
  new_data: any
  insert_user: string | null
  update_user: string | null
  domain_uuid: string | null
  changed_at: Date | null
  changed_by: any
}

interface Change {
  field: string
  oldValue: any
  newValue: any
}

const statusMapping: Record<number, string> = {
  1: 'In Progress',
  2: 'In Review',
  3: 'Done'
}

const fieldMapping: Record<string, string> = {
  assigned_task: 'Assigned Tasks',
  status: 'Status',
  task_description: 'Comment',
  task_title: 'Title'
}

function mapValue(field: string, value: any): string {
  if (field === 'status') {
    return statusMapping[value] || value
  }

  return value
}

function formatChanges(changes: Change[]): string[] {
  return changes.map(change => {
    const fieldDescription = fieldMapping[change.field] || change.field

    const oldValueFormatted = mapValue(change.field, change.oldValue)
    const newValueFormatted = mapValue(change.field, change.newValue)

    return `${fieldDescription}: "${oldValueFormatted || ''}" thành "${newValueFormatted}"`
  })
}

function formatLog(log: AuditLog): any {
  const { operation, old_data, new_data, changed_at, changed_by } = log

  let messages: string[] = []

  if (operation === 'CREATE') {
    messages.push(`${changed_by} tạo task`)
  } else if (operation === 'UPDATE') {
    const oldData = old_data ? old_data : {}
    const newData = new_data ? new_data : {}
    const changes = getChanges(oldData, newData)

    messages = formatChanges(changes)
  } else if (operation === 'DELETE') {
    messages.push(`${changed_by} xoá task`)
  } else if (operation === 'CONVERT') {
    messages.push(`${changed_by} convert task từ notes`)
  }

  return {
    audit_log_uuid: log.audit_log_uuid,
    operation: operation,
    logs: messages,
    changed_at: changed_at || null, // Use changed_at if available, otherwise current date
    changed_by: changed_by || null
  }
}

function getChanges(
  oldData: Record<string, any>,
  newData: Record<string, any>,
  ignoreFields: string[] = IGNORE_FIELDS
) {
  const changes: { field: string; oldValue: any; newValue: any }[] = []

  for (const key in oldData) {
    if (ignoreFields.includes(key)) {
      continue
    }

    if (oldData[key] !== newData[key]) {
      changes.push({
        field: key,
        oldValue: oldData[key],
        newValue: newData[key]
      })
    }
  }

  return changes
}

function extractUserIds(data: any): string[] {
  const userIds: Set<string> = new Set()

  if (Array.isArray(data.assigned_task)) {
    data.assigned_task.forEach((id: string) => userIds.add(id))
  }

  return Array.from(userIds)
}

/**
 * Get task history by task UUID
 *
 * @param request API request
 * @returns List of audit logs for a specific task
 */
export async function GET(request: NextRequest) {
  const session = await getSession()

  try {
    if (!session) {
      return NextResponse.json('Please authenticate !!!', { status: 401 })
    }

    const domain_uuid = session?.user?.domain ?? ''

    if (!domain_uuid) {
      return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
    }

    const { searchParams } = new URL(request.url)
    const task_uuid = searchParams.get('id')

    if (!task_uuid) {
      return NextResponse.json({ error: 'Missing task_uuid' }, { status: 400 })
    }

    // Get task history
    const taskHistory = await prisma.v_crm_audit_log.findMany({
      where: {
        object_uuid: task_uuid,
        table_name: 'v_crm_task',
        domain_uuid: domain_uuid
      },
      orderBy: { changed_at: 'desc' }
    })

    // Extract user IDs from old_data and new_data
    const userIds: Set<string> = new Set()

    taskHistory.forEach(log => {
      if (log.old_data) {
        const oldData = JSON.parse(log.old_data as string)

        extractUserIds(oldData).forEach(id => userIds.add(id))
      }

      if (log.new_data) {
        const newData = JSON.parse(log.new_data as string)

        extractUserIds(newData).forEach(id => userIds.add(id))
      }

      if (log.changed_by) {
        userIds.add(log.changed_by)
      }
    })

    // Fetch user details
    const users = await prisma.v_users.findMany({
      where: { user_uuid: { in: Array.from(userIds) } }
    })

    // Create a map for quick user lookup
    const userMap = new Map(users.map(user => [user.user_uuid, user.username]))

    // Enrich task history with user details
    const enrichedTaskHistory = taskHistory.map(log => {
      const oldData = log.old_data ? JSON.parse(log.old_data as string) : {}
      const newData = log.new_data ? JSON.parse(log.new_data as string) : {}

      return {
        ...log,
        old_data: {
          ...oldData,
          assigned_task: oldData.assigned_task
            ? oldData.assigned_task.map((id: string) => userMap.get(id) || null).join(', ')
            : ''
        },
        new_data: {
          ...newData,
          assigned_task: newData.assigned_task
            ? newData.assigned_task.map((id: string) => userMap.get(id) || null).join(', ')
            : ''
        },
        changed_by: log.changed_by ? userMap.get(log.changed_by) : ''
      }
    })

    const formattedLogs = enrichedTaskHistory.map(log => formatLog(log))

    return NextResponse.json(formattedLogs, { status: 200 })
  } catch (error) {
    console.error('Error fetching task history:', error)

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  } finally {
    await prisma.$disconnect() // Close Prisma Client connection
  }
}
