import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'

export async function POST(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const user_uuid = session?.user?.id ?? ''

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  try {
    const data = await request.json()
    const { task_list_uuids, interval } = data

    const movedTasks = await prisma.$transaction(async prisma => {
      const movedTasks = []

      for (const task_list_uuid of task_list_uuids) {
        const currentTasklist = await prisma.v_crm_task_list.findUnique({
          where: {
            task_list_uuid: task_list_uuid,
            domain_uuid: domain_uuid
          }
        })

        if (currentTasklist) {
          // Create a new task in v_crm_task
          const newTask = await prisma.v_crm_task.create({
            data: {
              domain_uuid,
              user_insert_task: user_uuid,
              insert_user: user_uuid,
              task_title: currentTasklist.task_title,
              task_description: currentTasklist.task_description
            }
          })

          // Add an entry to v_crm_task_interval
          await prisma.v_crm_task_interval.create({
            data: {
              user_task_uuid: newTask.task_uuid,
              domain_uuid,
              task_category_uuid: currentTasklist.task_category_uuid,
              interval
            }
          })

          // Optionally create an audit log (if needed)
          const now = new Date().toISOString()

          await prisma.v_crm_audit_log.create({
            data: {
              table_name: 'v_crm_task',
              operation: 'MOVE',
              object_uuid: newTask.task_uuid,
              old_data: JSON.stringify(currentTasklist),
              new_data: JSON.stringify(newTask),
              insert_user: user_uuid,
              insert_date: now,
              update_user: undefined,
              update_date: undefined,
              changed_by: user_uuid,
              domain_uuid,
              changed_at: now
            }
          })

          movedTasks.push(newTask)
        }
      }

      return movedTasks
    })

    return new Response(JSON.stringify({ message: 'Tasks moved successfully!', data: movedTasks }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error(error)

    return new Response(JSON.stringify({ message: 'An error occurred', error }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }
}
