import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'

/**
 * Get task categories by uuid or get a list of task categories.
 *
 * @param request API request
 * @returns List of v_crm_task_list
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getSession()

    if (!session) {
      return NextResponse.json('Please authenticate !!!', { status: 401 })
    }

    const domain_uuid = session?.user?.domain ?? ''

    if (!domain_uuid) {
      return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    const categoryId = searchParams.get('categoryId')

    if (id) {
      // Get task list by uuid
      const tasklist = await prisma.v_crm_task_list.findUnique({
        where: {
          task_list_uuid: id,
          domain_uuid: domain_uuid
        }
      })

      return NextResponse.json(tasklist, { status: 200 })
    }

    if (categoryId) {
      // Get task list by uuid && interval
      const tasklist = await prisma.v_crm_task_list.findMany({
        where: {
          task_category_uuid: categoryId,
          domain_uuid: domain_uuid
        }
      })

      return NextResponse.json(tasklist, { status: 200 })
    }

    // Get all task categories
    const alltasklist = await prisma.v_crm_task_list.findMany({
      where: {
        domain_uuid: domain_uuid
      }
    })

    return NextResponse.json(alltasklist, { status: 200 })
  } catch (error) {
    console.error('Error fetching tasklist:', error)

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  } finally {
    await prisma.$disconnect() // Close Prisma Client connection
  }
}

/**
 * Create a new v_crm_task_list.
 *
 * @param request POST REQUEST
 * @returns Created task list
 */
export async function POST(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json('Please authenticate !!!', { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const user_uuid = session?.user?.id ?? ''

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  try {
    const data = await request.json()
    const { task_title, task_description, task_category_uuid } = data

    // Check if task_category_uuid is provided
    if (!task_category_uuid) {
      return NextResponse.json({ error: 'Missing task_category_uuid' }, { status: 400 })
    }

    // Create a new task list linked to a task category
    const tasklist = await prisma.v_crm_task_list.create({
      data: {
        domain_uuid,
        insert_user: user_uuid,
        update_user: user_uuid,
        task_title,
        task_description,
        task_category_uuid,
        insert_date: new Date(),
        update_date: new Date()
      }
    })

    return NextResponse.json({ message: 'Task list created successfully!', data: tasklist }, { status: 201 })
  } catch (error) {
    console.error('Error creating task list:', error)

    return NextResponse.json({ message: 'An error occurred', error }, { status: 500 })
  } finally {
    await prisma.$disconnect()
  }
}

/**
 * Update a task list linked to a task category.
 *
 * @param request PUT request
 * @returns Updated task list
 */
export async function PUT(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const session = await getSession()

  if (!session) {
    return NextResponse.json('Please authenticate !!!', { status: 401 })
  }

  const user_uuid = session?.user?.id ?? ''
  const domain_uuid = session?.user?.domain ?? ''

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  const task_list_uuid = searchParams.get('id')

  if (!task_list_uuid) {
    return NextResponse.json({ error: 'Missing id' }, { status: 400 })
  }

  try {
    const data = await request.json()
    const { task_title, task_description, task_category_uuid } = data

    if (!task_category_uuid) {
      return NextResponse.json({ error: 'Missing task_category_uuid' }, { status: 400 })
    }

    // Update task list with new category and other details
    const updatedTaskList = await prisma.v_crm_task_list.update({
      where: {
        task_list_uuid: task_list_uuid
      },
      data: {
        update_user: user_uuid,
        task_title,
        task_description,
        task_category_uuid, // Update the link to category
        update_date: new Date()
      }
    })

    return NextResponse.json(updatedTaskList, { status: 200 })
  } catch (error) {
    console.error('Error updating task list:', error)

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  } finally {
    await prisma.$disconnect()
  }
}

/**
 * Delete a task list.
 *
 * @param request Request object
 * @returns Success or failure message
 */
export async function DELETE(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const { searchParams } = new URL(request.url)
  const task_list_uuid = searchParams.get('id')

  if (!domain_uuid || !task_list_uuid) {
    return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 })
  }

  try {
    await prisma.v_crm_task_list.delete({
      where: {
        task_list_uuid: task_list_uuid
      }
    })

    return NextResponse.json(
      { message: 'Task list deleted successfully', data: { task_list_uuid: task_list_uuid } },
      { status: 200 }
    )
  } catch (error) {
    console.error('Failed to delete tasklist:', error)

    return NextResponse.json({ error: 'Failed to delete tasklist' }, { status: 500 })
  } finally {
    await prisma.$disconnect() // Close Prisma Client connection
  }
}
