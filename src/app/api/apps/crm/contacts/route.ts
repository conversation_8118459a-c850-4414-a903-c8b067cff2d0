import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'
import type { ContactFormDataType, ContactType } from '@/types/apps/contactTypes'

export async function GET(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''

  console.log('domain_uuid = %s', domain_uuid)

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  const { searchParams } = new URL(request.url)
  const contact_uuid = searchParams.get('id')

  if (contact_uuid) {
    return getContactByID(domain_uuid, contact_uuid)
  }

  try {
    const contacts: ContactType[] = await prisma.$queryRaw`
      SELECT
        c.contact_uuid AS id,
        'User' AS role,
        c.personal_email AS email,
        'Active' AS status,
        c.avatar AS avatar,
        array_agg(coalesce(cm.company_name, '')) AS company_name,
        '' AS country,
        cm.company_uuid as company_uuid,
        c.phone_number AS phone_number,
        CONCAT_WS(' ', c.last_name, c.middle_name, c.first_name) AS full_name,
        '' AS username,
        '' AS currentPlan
      FROM
        v_crm_contact c
      LEFT JOIN
        v_crm_contact_career cc ON c.contact_uuid = cc.contact_uuid
      LEFT JOIN
        v_crm_company cm ON cc.company_uuid = cm.company_uuid
      WHERE
        c.domain_uuid = ${domain_uuid}::uuid
      GROUP BY
        c.contact_uuid, c.personal_email, c.avatar, c.first_name, c.middle_name, c.last_name, c.phone_number, cm.company_uuid
    `

    const serializedContacts = contacts.map(contact => ({
      ...contact,
      fullName: contact.full_name
    }))

    return NextResponse.json(serializedContacts, { status: 200 })
  } catch (error) {
    console.error('Error fetching contacts:', error)

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

async function getContactByID(domain_uuid: string, contact_uuid: string) {
  try {
    const contacts: ContactType[] = await prisma.$queryRaw`
      SELECT
        c.*,
        u.username as insert_by,
        u1.username as update_by,
        array_agg(coalesce(cm.company_name, '')) AS company_name
      FROM v_crm_contact c
      LEFT JOIN v_users u ON c.insert_user = u.user_uuid
      LEFT JOIN v_users u1 ON c.update_user = u1.user_uuid
      LEFT JOIN v_crm_contact_career cc ON c.contact_uuid = cc.contact_uuid
      LEFT JOIN v_crm_company cm ON cc.company_uuid = cm.company_uuid
      WHERE c.contact_uuid = ${contact_uuid}::uuid AND c.domain_uuid = ${domain_uuid}::uuid
      GROUP BY
        c.contact_uuid, c.personal_email, c.avatar, c.first_name, c.middle_name, c.last_name, c.phone_number, cm.company_uuid, u.username, u1.username
    `

    const contact = contacts[0]

    if (!contact) {
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 })
    }

    return NextResponse.json(
      {
        ...contact,
        full_name: `${contact?.last_name} ${contact?.middle_name} ${contact?.first_name}`
      },
      { status: 200 }
    )
  } catch (error) {
    console.error('Error fetching contact:', error)

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const contact_uuid = searchParams.get('id')

  if (!contact_uuid) {
    return NextResponse.json({ error: 'Missing contact_uuid' }, { status: 400 })
  }

  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const user_uuid = session?.user?.id ?? ''

  console.log('domain_uuid = %s', domain_uuid)

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  try {
    const data: ContactFormDataType = await request.json()

    const {
      last_name,
      middle_name,
      first_name,
      gender,
      personal_email,
      phone_number,
      birthday,
      description,
      address_number,
      address_street,
      address_ward,
      address_city,
      address_region,
      address_country,
      address_postal_code,
      address_district,
      company_name,
      company_personal_email,
      company_phone_number,
      company_description,
      company_address_number,
      company_address_street,
      company_address_ward,
      company_address_city,
      company_address_region,
      company_address_country,
      company_address_postal_code,
      company_address_district
    } = data

    // Execute the raw SQL query
    const result = await prisma.$executeRaw`
      WITH cte_v_crm_contacts AS (
        UPDATE v_crm_contact 
        SET 
          first_name = ${first_name},
          last_name = ${last_name},
          middle_name = ${middle_name},
          gender = ${gender},
          personal_email = ${personal_email},
          phone_number = ${phone_number},
          birthday = ${birthday}::timestamp,
          description = ${description},
          update_user = ${user_uuid}::uuid,
          update_date = NOW()
        WHERE contact_uuid = ${contact_uuid}::uuid AND domain_uuid = ${domain_uuid}::uuid
        RETURNING contact_uuid
      ),
      cte_v_crm_company AS (
        UPDATE v_crm_company 
        SET 
          company_name = ${company_name},
          company_phone_number = ${company_phone_number},
          company_personal_email = ${company_personal_email},
          description_company = ${company_description},
          update_user = ${user_uuid}::uuid,
          update_date = NOW()
        WHERE company_uuid = (SELECT company_uuid FROM v_crm_contact_career WHERE contact_uuid = ${contact_uuid}::uuid) AND domain_uuid = ${domain_uuid}::uuid
        RETURNING company_uuid
      ),
      cte_v_crm_contact_address AS (
        UPDATE v_crm_contact_address 
        SET 
          address_number = ${address_number},
          address_street = ${address_street},
          address_ward = ${address_ward},
          address_city = ${address_city},
          address_region = ${address_region},
          address_country = ${address_country},
          address_postal_code = ${address_postal_code},
          address_district = ${address_district},
          update_user = ${user_uuid}::uuid,
          update_date = NOW()
        WHERE contact_uuid = ${contact_uuid}::uuid AND domain_uuid = ${domain_uuid}::uuid
      )

      UPDATE v_crm_company_address 
      SET 
        company_address_number = ${company_address_number},
        company_address_street = ${company_address_street},
        company_address_ward = ${company_address_ward},
        company_address_city = ${company_address_city},
        company_address_region = ${company_address_region},
        company_address_country = ${company_address_country},
        company_address_postal_code = ${company_address_postal_code},
        company_address_district = ${company_address_district},
        update_user = ${user_uuid}::uuid,
        update_date = NOW()
      WHERE company_uuid = (SELECT company_uuid FROM cte_v_crm_company) AND domain_uuid = ${domain_uuid}::uuid
    `

    return new Response(JSON.stringify({ message: 'Form submitted successfully!', result }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error(error)

    return new Response(JSON.stringify({ message: 'An error occurred', error }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }
}

export async function DELETE(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const user_uuid = session?.user?.id ?? ''
  const { searchParams } = new URL(request.url)
  const id = searchParams.get('id')

  console.log('id = %s', id)

  if (!domain_uuid || !id) {
    return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 })
  }

  try {
    await prisma.v_crm_contact.delete({
      where: {
        contact_uuid: id,
        domain_uuid: domain_uuid,
        insert_user: user_uuid
      }
    })

    return NextResponse.json({ message: 'Note deleted successfully' }, { status: 200 })
  } catch (error) {
    console.error('Failed to delete note:', error)

    return NextResponse.json({ error: 'Failed to delete note' }, { status: 500 })
  }
}
