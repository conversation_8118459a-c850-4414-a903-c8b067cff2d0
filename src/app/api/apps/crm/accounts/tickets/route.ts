import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'

export async function GET(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''

  console.log('domain_uuid = %s', domain_uuid)

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  const searchParams = request.nextUrl.searchParams
  const contact_uuid = searchParams.get('contact_uuid')

  console.log('contact_uuid = %s', contact_uuid)

  try {
    const tickets = await prisma.$queryRaw`
      SELECT tk.*
      FROM v_crm_ticket tk
      WHERE tk.domain_uuid = ${domain_uuid}::uuid AND tk.contact_uuid = ${contact_uuid}::uuid`

    return NextResponse.json(tickets)
  } catch (error) {
    console.error(error)

    return new Response(JSON.stringify({ message: 'An error occurred', error }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }
}
