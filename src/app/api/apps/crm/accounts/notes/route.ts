import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'

export async function GET(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''

  console.log('domain_uuid = %s', domain_uuid)

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  const searchParams = request.nextUrl.searchParams
  const contact_uuid = searchParams.get('contact_uuid')

  console.log('contact_uuid = %s', contact_uuid)

  try {
    const notes = await prisma.v_crm_contact_note.findMany({
      where: {
        contact_uuid: contact_uuid,
        domain_uuid: domain_uuid
      }
    })

    return NextResponse.json(notes)
  } catch (error) {
    console.error(error)

    return new Response(JSON.stringify({ message: 'An error occurred', error }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }
}

export async function POST(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const user_uuid = session?.user?.id ?? ''

  console.log('domain_uuid = %s', domain_uuid)

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  try {
    const data = await request.json()
    const { contact_note_name, contact_note_content, contact_note_type, contact_uuid } = data

    // Execute the raw SQL query
    const contacts = await prisma.$queryRaw`
    INSERT INTO v_crm_contact_note (
      contact_note_uuid, 
      domain_uuid, 
      contact_uuid, 
      contact_note_type, 
      contact_note_name, 
      contact_note_content, 
      insert_date, 
      insert_user, 
      update_date, 
      update_user
  ) VALUES (
      uuid_generate_v4(), 
      ${domain_uuid}::uuid, 
      ${contact_uuid}::uuid, 
      ${contact_note_type}, 
      ${contact_note_name}, 
      ${contact_note_content}, 
      NOW(), 
      ${user_uuid}::uuid, 
      NOW(), 
      ${user_uuid}::uuid
  )
    `

    return new Response(JSON.stringify({ message: 'Form submitted successfully!', contacts }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error(error)

    return new Response(JSON.stringify({ message: 'An error occurred', error }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }
}

export async function PUT(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const user_uuid = session?.user?.id ?? ''

  console.log('domain_uuid = %s', domain_uuid)

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  try {
    const data = await request.json()
    const { contact_note_uuid, contact_note_name, contact_note_content, contact_note_type } = data

    if (!contact_note_uuid) {
      return NextResponse.json({ error: 'Missing contact_note_uuid' }, { status: 400 })
    }

    // Execute the raw SQL query to update the existing note
    const updatedContactNote = await prisma.$queryRaw`
      UPDATE v_crm_contact_note
      SET
        contact_note_type = ${contact_note_type},
        contact_note_name = ${contact_note_name},
        contact_note_content = ${contact_note_content},
        update_date = NOW(),
        update_user = ${user_uuid}::uuid
      WHERE contact_note_uuid = ${contact_note_uuid}::uuid
        AND domain_uuid = ${domain_uuid}::uuid
      RETURNING *
    `

    if (!updatedContactNote) {
      return NextResponse.json({ error: 'Note not found or not authorized to update' }, { status: 404 })
    }

    return NextResponse.json({ message: 'Note updated successfully!', updatedContactNote }, { status: 200 })
  } catch (error) {
    console.error(error)

    return NextResponse.json({ message: 'An error occurred', error }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const user_uuid = session?.user?.id ?? ''
  const { searchParams } = new URL(request.url)
  const contact_note_uuid = searchParams.get('contact_note_uuid')

  if (!domain_uuid || !contact_note_uuid) {
    return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 })
  }

  try {
    await prisma.v_crm_contact_note.delete({
      where: {
        contact_note_uuid: contact_note_uuid,
        domain_uuid: domain_uuid,
        insert_user: user_uuid
      }
    })

    return NextResponse.json({ message: 'Note deleted successfully' }, { status: 200 })
  } catch (error) {
    console.error('Failed to delete note:', error)

    return NextResponse.json({ error: 'Failed to delete note' }, { status: 500 })
  }
}
