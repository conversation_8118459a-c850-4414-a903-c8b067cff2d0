import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'

export async function POST(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const user_uuid = session?.user?.id ?? ''

  console.log('user_uuid = %s', user_uuid)

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  try {
    const data = await request.json()
    const { contact_note_uuid, contact_note_name, contact_note_content } = data

    const convertedTask = prisma.$transaction(async prisma => {
      // Get Note from database
      const currentNote = prisma.v_crm_contact_note.findUnique({
        where: {
          contact_note_uuid: contact_note_uuid,
          domain_uuid: domain_uuid
        }
      })

      // Tao Task
      console.log('task history', user_uuid)

      const newTask = await prisma.v_crm_task.create({
        data: {
          domain_uuid,
          user_insert_task: user_uuid,
          insert_user: user_uuid,
          task_title: contact_note_name,
          task_description: contact_note_content
        }
      })

      // task history
      const now = new Date().toISOString()

      await prisma.v_crm_audit_log.create({
        data: {
          table_name: 'v_crm_task',
          operation: 'CONVERT',
          object_uuid: newTask.task_uuid,
          old_data: JSON.stringify(currentNote),
          new_data: JSON.stringify(newTask),
          insert_user: user_uuid,
          insert_date: now,
          update_user: undefined,
          update_date: undefined,
          changed_by: user_uuid,
          domain_uuid,
          changed_at: now
        }
      })

      // Delete Note
      await prisma.v_crm_contact_note.delete({
        where: {
          contact_note_uuid: contact_note_uuid,
          domain_uuid: domain_uuid
        }
      })

      return newTask
    })

    return new Response(JSON.stringify({ message: 'Convert to Task successfully!', data: convertedTask }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error(error)

    return new Response(JSON.stringify({ message: 'An error occurred', error }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }
}
