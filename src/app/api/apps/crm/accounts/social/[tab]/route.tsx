// Next Imports
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { prisma } from '@/libs/db/prisma'
import getSession from '@/actions/getSession'

import type { ContactSocial } from '@/types/apps/setting/socialTypes'

export async function GET(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  const session = await getSession()

  if (!session) {
    return NextResponse.json({ warning: 'Please authentication !!!' })
  }

  const uuid = session?.user?.domain ?? ''

  switch (tab) {
    case 'find': // tìm xem zalo id, facebook id này đã tồn tại chưa
      // social_type = zalo|facebook|viber|telegram ....
      try {
        const social_id = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của domain cần truy vấn

        const contact_social_id = await prisma.v_crm_contact_social.findFirst({
          where: {
            domain_uuid: uuid, // lấy zalo oauth trong domain đăng nhập ; mỗi record chỉ duy nhất 1 domain
            contact_social_id: social_id
          },
          select: {
            contact_social_id: true
          }
        })

        return NextResponse.json(contact_social_id)
        break
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      }

    default:
      return NextResponse.json({ warning: 'Please choose terminal endpoint !!!' })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ warning: 'Please authentication !!!' })
  }

  const uuid = session?.user?.domain ?? ''
  const user_id = session?.user?.id

  switch (tab) {
    case 'add':
      try {
        const socialData: ContactSocial = await request.json()

        // Check if record exists
        const existingRecord = await prisma.v_crm_contact_social.findFirst({
          where: {
            domain_uuid: uuid, // lấy zalo oauth trong domain đăng nhập ; mỗi record chỉ duy nhất 1 domain
            contact_social_id: socialData.contact_social_id
          },
          select: {
            contact_social_id: true
          }
        })

        if (existingRecord) {
          console.log('Social ID này đã tổn tại ')

          return NextResponse.json({ info: 'Social ID is existing !!!' }, { status: 201 })
        } else {
          // nếu chưa tồn tại record thì tạo mới
          const result = await prisma.v_crm_contact_social.create({
            data: {
              domain_uuid: uuid,
              contact_social_type: socialData.contact_social_type,
              contact_social_name: socialData.contact_social_name,
              contact_social_id: socialData.contact_social_id,
              insert_user: user_id,
              insert_date: new Date()
            }
          })

          console.log('Social Contact  tạo  được là = ', result)

          return NextResponse.json(result, { status: 201 })
        }
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      }

      break

    default:
      return NextResponse.json({ warning: 'Please choose terminal endpoint !!!' })
  }
}
