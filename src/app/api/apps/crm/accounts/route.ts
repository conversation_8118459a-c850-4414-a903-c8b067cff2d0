import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'

export async function GET(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''

  console.log('domain_uuid = %s', domain_uuid)

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  const searchParams = request.nextUrl.searchParams
  const contact_uuid = searchParams.get('contact_uuid')

  console.log('contact_uuid = %s', contact_uuid)

  try {
    const contacts = await prisma.$queryRaw`
      SELECT ct.*
      FROM v_crm_contact ct
      WHERE ct.domain_uuid = ${domain_uuid}::uuid`

    return NextResponse.json(contacts)
  } catch (error) {
    console.error(error)

    return new Response(JSON.stringify({ message: 'An error occurred', error }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }
}

export async function POST(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const user_uuid = session?.user?.id ?? ''

  console.log('domain_uuid = %s', domain_uuid)

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  try {
    const data = await request.json()

    const {
      last_name,
      middle_name,
      first_name,
      gender,
      personal_email,
      phone_number,
      birthday,
      description,
      avatar,
      address_number,
      address_street,
      address_ward,
      address_city,
      address_region,
      address_country,
      address_postal_code,
      address_district,
      company_name,
      company_tax_code,
      company_personal_email,
      company_phone_number,
      company_description,
      logo,
      company_address_number,
      company_address_street,
      company_address_ward,
      company_address_city,
      company_address_region,
      company_address_country,
      company_address_postal_code,
      company_address_district
    } = data

    const formattedBirthday = birthday ? new Date(birthday) : null

    // Execute the raw SQL query
    let contacts: any[]

    if (company_name) {
      contacts = await prisma.$queryRaw`
      WITH cte_v_crm_contacts AS (
          INSERT INTO v_crm_contact (
              domain_uuid, contact_uuid, insert_user, first_name, last_name, middle_name, gender, personal_email, phone_number, birthday, description, avatar, update_user, insert_date, update_date
          )
          VALUES (
              ${domain_uuid}::uuid, uuid_generate_v4(), ${user_uuid}::uuid, ${first_name}, ${last_name}, ${middle_name}, ${gender}, ${personal_email}, ${phone_number}, ${formattedBirthday}, ${description}, ${avatar}, ${user_uuid}::uuid, NOW(), NOW()
          )
          RETURNING contact_uuid
      ),
      cte_existing_company AS (
        SELECT company_uuid
        FROM v_crm_company
        WHERE company_name = ${company_name}
      ),
      cte_v_crm_company AS (
        INSERT INTO v_crm_company (
            domain_uuid, company_uuid, company_name, tax_id, company_phone_number, company_personal_email, description_company, logo, insert_user, update_user, insert_date, update_date
        )
        SELECT
            ${domain_uuid}::uuid,
            COALESCE(
              (SELECT company_uuid FROM cte_existing_company),
              uuid_generate_v4()
            ),
            ${company_name},
            ${company_tax_code},
            ${company_phone_number},
            ${company_personal_email},
            ${company_description},
            ${logo},
            ${user_uuid}::uuid,
            ${user_uuid}::uuid,
            NOW(),
            NOW()
        ON CONFLICT (company_uuid) DO UPDATE
        SET
          tax_id = EXCLUDED.tax_id,
          company_phone_number = EXCLUDED.company_phone_number,
          company_personal_email = EXCLUDED.company_personal_email,
          description_company = EXCLUDED.description_company,
          logo = EXCLUDED.logo,
            update_user = EXCLUDED.update_user,
            update_date = EXCLUDED.update_date
        RETURNING company_uuid
      ),
      cte_v_crm_contact_career AS (
          INSERT INTO v_crm_contact_career (
            career_uuid, domain_uuid, contact_uuid, company_uuid
          )	
          VALUES (
            uuid_generate_v4(), ${domain_uuid}::uuid, (SELECT contact_uuid FROM cte_v_crm_contacts), (SELECT company_uuid FROM cte_v_crm_company)
          )
      ),
      cte_v_crm_contact_address AS (
          INSERT INTO v_crm_contact_address (
            domain_uuid, contact_address_uuid, insert_user, update_user, contact_uuid, address_number, address_street, address_ward, address_city, address_region, address_country, address_postal_code, address_district, insert_date, update_date
        )
        VALUES (
            ${domain_uuid}::uuid, uuid_generate_v4(), ${user_uuid}::uuid, ${user_uuid}::uuid, (SELECT contact_uuid FROM cte_v_crm_contacts), ${address_number}, ${address_street}, ${address_ward}, ${address_city}, ${address_region}, ${address_country}, ${address_postal_code}, ${address_district}, NOW(), NOW()
        )
      ),
      cte_v_crm_company_address AS (
        INSERT INTO v_crm_company_address (
          domain_uuid, company_address_uuid, company_uuid, company_address_number, company_address_street, company_address_ward, company_address_city, company_address_region, company_address_country, company_address_postal_code, company_address_district, insert_user, update_user, insert_date, update_date
        )
        SELECT
            ${domain_uuid}::uuid,
            COALESCE(
              (SELECT company_address_uuid FROM v_crm_company_address WHERE company_uuid = (SELECT company_uuid FROM cte_v_crm_company)),
              uuid_generate_v4()
          ),
            (SELECT company_uuid FROM cte_v_crm_company),
            ${company_address_number},
            ${company_address_street},
            ${company_address_ward},
            ${company_address_city},
            ${company_address_region},
            ${company_address_country},
            ${company_address_postal_code},
            ${company_address_district},
            ${user_uuid}::uuid,
            ${user_uuid}::uuid,
            NOW(),
            NOW()
        ON CONFLICT (company_address_uuid) DO UPDATE
        SET
            company_uuid = EXCLUDED.company_uuid,
            company_address_number = EXCLUDED.company_address_number,
            company_address_street = EXCLUDED.company_address_street,
            company_address_ward = EXCLUDED.company_address_ward,
            company_address_city = EXCLUDED.company_address_city,
            company_address_region = EXCLUDED.company_address_region,
            company_address_country = EXCLUDED.company_address_country,
            company_address_postal_code = EXCLUDED.company_address_postal_code,
            company_address_district = EXCLUDED.company_address_district,
            update_user = EXCLUDED.update_user,
            update_date = EXCLUDED.update_date
      )
      SELECT contact_uuid FROM cte_v_crm_contacts;
    `
    } else {
      contacts = await prisma.$queryRaw`
        WITH cte_v_crm_contacts AS (
          INSERT INTO v_crm_contact (
              domain_uuid, contact_uuid, insert_user, first_name, last_name, middle_name, gender, personal_email, phone_number, birthday, description, avatar, update_user, insert_date, update_date
          )
          VALUES (
              ${domain_uuid}::uuid, uuid_generate_v4(), ${user_uuid}::uuid, ${first_name}, ${last_name}, ${middle_name}, ${gender}, ${personal_email}, ${phone_number}, ${formattedBirthday}, ${description}, ${avatar}, ${user_uuid}::uuid, NOW(), NOW()
          )
          RETURNING contact_uuid
        )
        INSERT INTO v_crm_contact_address (
          domain_uuid, contact_address_uuid, insert_user, update_user, contact_uuid, address_number, address_street, address_ward, address_city, address_region, address_country, address_postal_code, address_district, insert_date, update_date
        )
        VALUES (
            ${domain_uuid}::uuid, uuid_generate_v4(), ${user_uuid}::uuid, ${user_uuid}::uuid, (SELECT contact_uuid FROM cte_v_crm_contacts), ${address_number}, ${address_street}, ${address_ward}, ${address_city}, ${address_region}, ${address_country}, ${address_postal_code}, ${address_district}, NOW(), NOW()
        )
        RETURNING contact_uuid;
      `
    }

    const contact_uuid = contacts[0].contact_uuid

    const newContacts: any[] = await prisma.$queryRaw`
    SELECT
      c.contact_uuid AS id,
      'User' AS role,
      c.personal_email AS email,
      'Active' AS status,
      c.avatar AS avatar,
      array_agg(coalesce(cm.company_name, '')) AS company_name,
      '' AS country,
      cm.company_uuid as company_uuid,
      c.phone_number AS phone_number,
      CONCAT_WS(' ', c.last_name, c.middle_name, c.first_name) AS full_name,
      '' AS username,
      '' AS currentPlan
    FROM
      v_crm_contact c
    LEFT JOIN
      v_crm_contact_career cc ON c.contact_uuid = cc.contact_uuid
    LEFT JOIN
      v_crm_company cm ON cc.company_uuid = cm.company_uuid
    WHERE
      c.domain_uuid = ${domain_uuid}::uuid AND c.contact_uuid = ${contact_uuid}::uuid
    GROUP BY
      c.contact_uuid, c.personal_email, c.avatar, c.first_name, c.middle_name, c.last_name, c.phone_number, cm.company_uuid
  `

    const serializedContact = {
      ...newContacts[0],
      fullName: newContacts[0].full_name
    }

    return new Response(
      JSON.stringify({
        message: 'Form submitted successfully!',
        data: serializedContact
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  } catch (error) {
    console.error(error)

    return new Response(JSON.stringify({ message: 'An error occurred', error }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }
}
