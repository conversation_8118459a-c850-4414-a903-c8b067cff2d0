import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'

/**
 * Lay calendar by uuid hoac lay danh sach calendar
 *
 * @param request api request
 * @returns Tra ve danh sach v_crm_task
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getSession()

    if (!session) {
      return NextResponse.json('Please authenticate !!!', { status: 401 })
    }

    const domain_uuid = session?.user?.domain ?? ''

    if (!domain_uuid) {
      return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (id) {
      // Get task by uuid
      const calendar = await prisma.v_crm_calendar.findFirst({
        where: {
          calendar_uuid: id
        }
      })

      return NextResponse.json(calendar, { status: 200 })
    }

    // Get all tasks
    const allcalendar = await prisma.v_crm_calendar.findMany()

    return NextResponse.json(allcalendar, { status: 200 })
  } catch (error) {
    console.error('Error fetching calendar:', error)

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  } finally {
    await prisma.$disconnect() // Close Prisma Client connection
  }
}

/**
 * Create v_crm_calendar
 *
 * @param request POST REQUEST
 * @returns Tra ve vua tao
 */
export async function POST(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json('Please authenticate !!!', { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const user_uuid = session?.user?.id ?? '' // Assuming 'id' is the correct field

  console.log(' uuid  = %s', domain_uuid)

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  try {
    const data = await request.json()
    const { title_calendar, description_calendar, start_date, end_date, location_calendar, calendar_type } = data

    const newcalendar = await prisma.v_crm_calendar.create({
      data: {
        domain_uuid: domain_uuid,
        insert_user: user_uuid,
        update_user: user_uuid,
        title_calendar,
        description_calendar,
        start_date,
        end_date,
        location_calendar,
        calendar_type,
        v_users: {
          connect: { user_uuid: user_uuid } // Use the correct unique field
        }
      }
    })

    return new Response(JSON.stringify({ message: 'Form submitted successfully!', data: newcalendar }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error(error)

    return new Response(JSON.stringify({ message: 'An error occurred', error }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } finally {
    await prisma.$disconnect() // Close Prisma Client connection
  }
}

/**
 * Update calendar
 *
 * @param request PUT request
 * @returns Tra ve task da update
 */
export async function PUT(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const session = await getSession()

  if (!session) {
    return NextResponse.json('Please authenticate !!!', { status: 401 })
  }

  const user_uuid = session?.user?.id ?? '' // Assuming 'id' is the correct field

  const domain_uuid = session?.user?.domain ?? ''

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  const calendar_uuid = searchParams.get('id')

  if (!calendar_uuid) {
    return NextResponse.json({ error: 'Missing id' }, { status: 400 })
  }

  const data = await request.json()
  const { title_calendar, description_calendar, start_date, end_date, location_calendar, calendar_type } = data

  try {
    const updatedcalendar = await prisma.v_crm_calendar.update({
      where: {
        calendar_uuid: calendar_uuid,
        domain_uuid: domain_uuid
      },
      data: {
        insert_user: user_uuid,
        update_user: user_uuid,
        title_calendar,
        description_calendar,
        start_date,
        end_date,
        location_calendar,
        calendar_type,
        v_users: {
          connect: { user_uuid: user_uuid } // Use the correct unique field
        }
      }
    })

    console.log(updatedcalendar)

    return NextResponse.json(updatedcalendar, { status: 200 })
  } catch (error) {
    console.error('Error updating calendar:', error)

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  } finally {
    await prisma.$disconnect() // Close Prisma Client connection
  }
}

/**
 * Delete a calendar event
 *
 * @param request Request object
 * @returns
 */
export async function DELETE(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const user_uuid = session?.user?.id ?? '' // Assuming 'id' is the correct field
  const { searchParams } = new URL(request.url)
  const calendar_uuid = searchParams.get('id')

  if (!domain_uuid || !calendar_uuid) {
    return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 })
  }

  try {
    await prisma.v_crm_calendar.delete({
      where: {
        calendar_uuid: calendar_uuid,
        domain_uuid: domain_uuid,
        update_user: user_uuid // Adjust this based on your schema and requirement
      }
    })

    return NextResponse.json(
      { message: 'Calendar event deleted successfully', data: { calendar_uuid: calendar_uuid } },
      { status: 200 }
    )
  } catch (error) {
    console.error('Failed to delete calendar event:', error)

    return NextResponse.json({ error: 'Failed to delete calendar event' }, { status: 500 })
  } finally {
    await prisma.$disconnect() // Close Prisma Client connection
  }
}
