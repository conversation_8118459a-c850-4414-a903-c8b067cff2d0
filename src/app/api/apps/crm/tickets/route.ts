// Import getSession from '@/actions/getSession'
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'

// Helper function for error responses
function handleErrorResponse(error: any, status: number = 500) {
  console.error('Error:', error)

  return new Response(JSON.stringify({ error: 'Internal server error' }), {
    status: status,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// Function to retrieve a ticket by its ID
async function getTicketByID(domain_uuid: string, ticket_uuid: string) {
  try {
    const tickets: any = await prisma.$queryRaw`
      SELECT
        tk.*,
        ct.avatar,
        ct.first_name,
        ct.last_name,
        ct.middle_name,
        cp.company_name,
        u.username,
        array_agg(assigned_user_info.username) as assigned_to,
        CASE
          WHEN tk.insert_date < (CURRENT_TIMESTAMP - INTERVAL '7 days') AND tk.ticket_status = 1 THEN 'warning'
          ELSE NULL
        END AS warning_status
      FROM v_crm_ticket tk
      LEFT JOIN v_crm_contact ct ON tk.contact_uuid = ct.contact_uuid
      LEFT JOIN v_crm_company cp ON tk.company_uuid = cp.company_uuid
      LEFT JOIN v_users u ON tk.insert_user = u.user_uuid
      LEFT JOIN v_users assigned_user_info ON assigned_user_info.user_uuid = ANY(tk.ticket_assigned_to)
      WHERE tk.domain_uuid = ${domain_uuid}::uuid AND tk.ticket_uuid = ${ticket_uuid}::uuid
      GROUP BY tk.ticket_uuid, ct.avatar, ct.first_name, ct.last_name, ct.middle_name, cp.company_name, u.username
      ORDER BY
        CASE
          WHEN tk.insert_date < (CURRENT_TIMESTAMP - INTERVAL '7 days') AND tk.ticket_status = 1 THEN 0
          ELSE 1
        END,
        tk.ticket_status,
        tk.ticket_priority DESC
    `

    if (!(tickets && tickets.length > 0)) {
      return NextResponse.json({ error: 'Ticket không tồn tại' }, { status: 404 })
    }

    const ticket = tickets[0]

    return NextResponse.json(
      {
        ...ticket,
        ticket_id: ticket?.ticket_id,
        ticket_title: ticket?.ticket_title,
        ticket_description: ticket?.ticket_description,
        ticket_status: ticket?.ticket_status,
        ticket_priority: ticket?.ticket_priority,
        ticket_assigned_to: ticket?.ticket_assigned_to,
        insert_date: ticket?.insert_date,
        insert_user: ticket?.insert_user,
        update_date: ticket?.update_date,
        update_user: ticket?.update_user,
        avatarIcon: ticket?.avatarIcon,
        avatarColor: ticket?.avatarColor
      },
      { status: 200 }
    )
  } catch (error) {
    console.error('Error fetching ticket:', error)

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  } finally {
    await prisma.$disconnect() // Close Prisma Client connection
  }
}

// GET request handler
export async function GET(request: NextRequest) {
  try {
    const session = await getSession()

    if (!session) {
      return NextResponse.json('Please authenticate !!!')
    }

    const domain_uuid = session?.user?.domain ?? ''

    if (!domain_uuid) {
      return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (id) {
      return getTicketByID(domain_uuid, id)
    }

    const tickets: any[] = await prisma.$queryRaw`
      SELECT tk.*,
             ct.avatar,
             ct.first_name,
             ct.last_name,
             ct.middle_name,
             cp.company_name,
             u.username,
             array_agg(assigned_user_info.username) as assigned_to,
             CASE
                 WHEN tk.insert_date < (CURRENT_TIMESTAMP - INTERVAL '7 days') AND tk.ticket_status = 1 THEN 'warning'
                 ELSE NULL
             END AS warning_status
      FROM v_crm_ticket tk
      LEFT JOIN v_crm_contact ct ON tk.contact_uuid = ct.contact_uuid
      LEFT JOIN v_crm_company cp ON tk.company_uuid = cp.company_uuid
      LEFT JOIN v_users u ON tk.insert_user = u.user_uuid
      LEFT JOIN v_users assigned_user_info ON assigned_user_info.user_uuid = ANY(tk.ticket_assigned_to)
      WHERE tk.domain_uuid = ${domain_uuid}::uuid
      GROUP BY
        tk.ticket_uuid,
        tk.ticket_title,
        tk.ticket_description,
        tk.ticket_status,
        tk.ticket_priority,
        tk.insert_date,
        tk.update_date,
        ct.avatar,
        ct.first_name,
        ct.last_name,
        ct.middle_name,
        cp.company_name,
        u.username
      ORDER BY
          CASE
              WHEN tk.insert_date < (CURRENT_TIMESTAMP - INTERVAL '7 days') AND tk.ticket_status = 1 THEN 0
              ELSE 1
          END,
          tk.ticket_status,
          tk.ticket_priority DESC
    `

    const serializedTickets = tickets.map((ticket: any) => ({
      ...ticket,
      full_name: `${ticket.last_name || ''} ${ticket.middle_name || ''} ${ticket.first_name || ''}`.trim()
    }))

    return NextResponse.json(serializedTickets)
  } catch (error) {
    return handleErrorResponse(error)
  } finally {
    await prisma.$disconnect() // Close Prisma Client connection
  }
}

// POST request handler
export async function POST(request: NextRequest) {
  try {
    const session = await getSession()

    if (!session) {
      return NextResponse.json('Please authenticate !!!')
    }

    const domain_uuid = session?.user?.domain ?? ''
    const user_uuid = session?.user?.id ?? ''

    if (!domain_uuid) {
      return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
    }

    const data = await request.json()

    const {
      contact_uuid,
      company_uuid,
      ticket_title,
      ticket_status,
      ticket_priority,
      ticket_description,
      ticket_assigned_to
    } = data

    if (!Array.isArray(ticket_assigned_to) || ticket_assigned_to.some(id => typeof id !== 'string')) {
      return NextResponse.json({ error: 'ticket_assigned_to must be an array of UUID strings' }, { status: 400 })
    }

    const ticket = await prisma.$queryRaw`
      INSERT INTO v_crm_ticket (
        domain_uuid, contact_uuid, company_uuid, ticket_title, ticket_status, ticket_priority, ticket_description, ticket_assigned_to, insert_user, update_user, insert_date, update_date
      )
      VALUES (
        ${domain_uuid}::uuid, ${contact_uuid}::uuid, ${company_uuid}::uuid, ${ticket_title}, ${ticket_status}, ${ticket_priority}, ${ticket_description}, ${ticket_assigned_to}::uuid[], ${user_uuid}::uuid, ${user_uuid}::uuid, NOW(), NOW()
      )
    `

    // Convert ticket_assigned_to array to PostgreSQL UUID array format

    return new Response(JSON.stringify({ message: 'Form submitted successfully!', ticket }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    return handleErrorResponse(error)
  }
}

// PUT request handler
export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const ticket_uuid = searchParams.get('id')
    const session = await getSession()

    if (!session) {
      return NextResponse.json('Please authenticate !!!', { status: 401 })
    }

    const domain_uuid = session.user?.domain ?? ''

    if (!domain_uuid) {
      return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
    }

    if (!ticket_uuid) {
      return NextResponse.json({ error: 'Missing ticket_uuid' }, { status: 400 })
    }

    const data = await request.json()

    const {
      ticket_status,
      ticket_assigned_to,
      ticket_priority,
      ticket_title,
      ticket_description,
      ticket_contact_uuid,
      ticket_company_uuid
    } = data

    const updateData: any = {}

    if (ticket_status !== undefined) {
      updateData.ticket_status = parseInt(ticket_status)
    }

    if (ticket_assigned_to !== undefined) {
      updateData.ticket_assigned_to = ticket_assigned_to
    }

    if (ticket_priority !== undefined) {
      updateData.ticket_priority = parseInt(ticket_priority)
    }

    if (ticket_title !== undefined) {
      updateData.ticket_title = ticket_title
    }

    if (ticket_description !== undefined) {
      updateData.ticket_description = ticket_description
    }

    if (ticket_contact_uuid !== undefined) {
      updateData.contact_uuid = ticket_contact_uuid
    }

    if (ticket_company_uuid !== undefined) {
      updateData.company_uuid = ticket_company_uuid
    }

    const updatedTicket = await prisma.v_crm_ticket.update({
      where: {
        domain_uuid: domain_uuid,
        ticket_uuid: ticket_uuid
      },
      data: updateData
    })

    return NextResponse.json(updatedTicket, { status: 200 })
  } catch (error) {
    return handleErrorResponse(error)
  }
}
