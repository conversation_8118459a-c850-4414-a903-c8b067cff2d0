import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'

// Get Comments for a specific ticket
export async function GET(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const searchParams = request.nextUrl.searchParams
  const ticket_uuid = searchParams.get('ticket_uuid')

  if (!ticket_uuid) {
    return NextResponse.json({ error: 'Missing ticket_uuid' }, { status: 400 })
  }

  try {
    const comments: any = await prisma.$queryRaw`
      SELECT
        c.*,
        u.username as user_name
      FROM v_crm_ticket_comment c LEFT JOIN v_users u ON c.insert_user = u.user_uuid
      WHERE c.ticket_uuid = ${ticket_uuid}::uuid
    `

    return NextResponse.json(comments, { status: 200 })
  } catch (error) {
    console.error('Error fetching comments:', error)

    return NextResponse.json({ message: 'An error occurred', error }, { status: 500 })
  }
}

// Add a new comment
export async function POST(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const user_uuid = session?.user?.id ?? ''
  const user_name = session?.user?.name ?? ''

  if (!user_uuid) {
    return NextResponse.json({ error: 'Missing user_uuid' }, { status: 400 })
  }

  try {
    const data = await request.json()
    const { ticket_uuid, comment_text } = data

    if (!ticket_uuid || !comment_text) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    const newComment = await prisma.v_crm_ticket_comment.create({
      data: {
        ticket_uuid,
        user_uuid,
        comment_text,
        comment_date: new Date(),
        insert_date: new Date(),
        insert_user: user_uuid,
        update_user: user_uuid
      }
    })

    const responseData: any = {
      ...newComment,
      user_name
    }

    return NextResponse.json({ message: 'Comment added successfully!', newComment: responseData }, { status: 201 })
  } catch (error) {
    console.error('Error adding comment:', error)

    return NextResponse.json({ message: 'An error occurred', error }, { status: 500 })
  }
}

// Delete a comment
export async function DELETE(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const user_uuid = session?.user?.id ?? ''
  const { searchParams } = new URL(request.url)
  const id = searchParams.get('id')

  if (!id || !user_uuid) {
    return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 })
  }

  try {
    await prisma.v_crm_ticket_comment.deleteMany({
      where: {
        id: Number(id),
        user_uuid: user_uuid
      }
    })

    return NextResponse.json({ message: 'Comment deleted successfully' }, { status: 200 })
  } catch (error) {
    console.error('Failed to delete comment:', error)

    return NextResponse.json({ error: 'Failed to delete comment' }, { status: 500 })
  }
}
