import type { PrismaClient } from '@prisma/client'

import { prisma } from '@/libs/db/prisma'

interface TransactionParams {
  operation: 'CREATE' | 'UPDATE' | 'DELETE' | 'CONVERT'
  model: string
  whereField?: string
  objectUUID?: string
  data?: Record<string, any>
  user_uuid: string
  domain_uuid: string
}

type OperationFunc = (prisma: PrismaClient, data?: Record<string, any>, objectUUID?: string) => Promise<any>

export async function handleTransaction(
  { operation, model, whereField, objectUUID, data, user_uuid, domain_uuid }: TransactionParams,
  operationFunc: OperationFunc
) {
  return prisma.$transaction(async prisma => {
    let before: any

    const modelClient = prisma as any

    const where = whereField && objectUUID ? { [whereField]: objectUUID } : undefined

    if ((operation === 'UPDATE' || operation === 'DELETE') && where) {
      before = await modelClient[model].findUnique({ where })
    }

    const result = await operationFunc(modelClient, data, objectUUID)

    const now = new Date().toISOString()

    // const changes = operation === 'UPDATE' ? getChanges(before, result) : []

    // Log changes
    await modelClient.v_crm_audit_log.create({
      data: {
        table_name: model,
        object_uuid: objectUUID || result?.task_uuid,
        operation,
        old_data: before ? JSON.stringify(before) : undefined,
        new_data: result ? JSON.stringify(result) : undefined,
        insert_user: operation === 'CREATE' ? user_uuid : undefined,
        insert_date: operation === 'CREATE' ? now : undefined,
        update_user: operation === 'UPDATE' || operation === 'DELETE' ? user_uuid : undefined,
        update_date: operation === 'UPDATE' || operation === 'DELETE' ? now : undefined,
        changed_by: user_uuid,
        domain_uuid,
        changed_at: now
      }
    })

    // changes.forEach(change => {
    //   console.log(
    //     `${user_uuid} ${operation.toLowerCase()} ${change.field} from ${change.oldValue} to ${change.newValue} for object ID ${objectUUID}`
    //   )
    // })

    return result
  })
}
