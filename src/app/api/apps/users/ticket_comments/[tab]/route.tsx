// Next Imports
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import type { v_users_ticket_comment } from '@prisma/client'

import { prisma } from '@/libs/db/prisma'
import getSession from '@/actions/getSession'
import type { TicketCommentCreate, TicketCommentUpdate } from '@/types/apps/ticketTypes'

export async function GET(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  const session = await getSession()

  if (!session) {
    return NextResponse.json('Please authentication !!!')
  }

  switch (tab) {
    case 'listComments':
      try {
        const ticket_uuid = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của domain cần truy vấn

        const comments: v_users_ticket_comment[] = await prisma.v_users_ticket_comment.findMany({
          where: {
            ticket_uuid: ticket_uuid
          }
        })

        if (!comments) {
          return NextResponse.json({ error: 'Lỗi truy vấn danh sách user' }, { status: 404 })
        }

        return NextResponse.json(comments, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      }

      break

    default:
      return NextResponse.json('Please choose terminal endpoint !!!', { status: 500 })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab
  const id = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của domain cần truy vấn

  switch (tab) {
    case 'add':
      const comment: TicketCommentCreate = await request.json()

      try {
        const ticketComment: v_users_ticket_comment = await prisma.v_users_ticket_comment.create({
          data: {
            ...comment
          }
        })

        return NextResponse.json(ticketComment, { status: 201 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      }

      break
    case 'update':
      const ticketCommentUpdate: TicketCommentUpdate = await request.json()

      try {
        const ticket_comment: v_users_ticket_comment = await prisma.v_users_ticket_comment.update({
          where: { id: Number(id) },
          data: {
            ...ticketCommentUpdate
          }
        })

        return NextResponse.json(ticket_comment, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      }

      break
    case 'delete':
      try {
        await prisma.v_users_ticket_comment.delete({
          where: { id: Number(id) }
        })

        return NextResponse.json({}, { status: 204 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      }

      break
    default:
      return NextResponse.json('Please choose terminal endpoint !!!', { status: 500 })
  }
}
