// Next Imports
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import type { v_users_ticket } from '@prisma/client'

import { prisma } from '@/libs/db/prisma'
import getSession from '@/actions/getSession'
import type { TicketCreateData, TicketProps, TicketUpdateData } from '@/types/apps/ticketTypes'

export async function GET(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  const session = await getSession()

  if (!session) {
    return NextResponse.json('Please authentication !!!')
  }

  const uuid = session?.user?.domain ?? ''

  switch (tab) {
    case 'list':
      try {
        const user_uuid = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của domain cần truy vấn

        const listdata: v_users_ticket[] = await prisma.v_users_ticket.findMany({
          relationLoadStrategy: 'join', // or 'query'
          where: {
            domain_uuid: uuid, // lấy domain chỉ định
            user_uuid: user_uuid
          },
          include: {
            v_users_ticket_comment: true
          }
        })

        if (!listdata) {
          return NextResponse.json({ error: 'Lỗi truy vấn danh sách user' }, { status: 404 })
        }

        return NextResponse.json(listdata, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      }

      break
    case 'view':
      // console.log(' uuid  = %s', uuid)
      const ticket_uuid = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của domain cần truy vấn

      try {
        const res = await prisma.v_users_ticket.findUnique({
          relationLoadStrategy: 'join', // or 'query'
          where: {
            domain_uuid: uuid, // lấy domain chỉ định
            ticket_uuid: ticket_uuid
          },
          include: {
            v_users_ticket_comment: true
          }
        })

        // Kiểm tra nếu không tìm thấy ticket
        if (!res) {
          return NextResponse.json({ error: 'Ticket not found' }, { status: 404 })
        }

        const data: TicketProps = {
          ticket: res,
          ticketComment: res.v_users_ticket_comment
        }

        return NextResponse.json(data, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      }

      break
    default:
      return NextResponse.json('Please choose terminal endpoint !!!', { status: 500 })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  const ticket_uuid = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của domain cần truy vấn

  switch (tab) {
    case 'add':
      const userticket: TicketCreateData = await request.json()

      try {
        const ticket: v_users_ticket = await prisma.v_users_ticket.create({
          data: {
            ...userticket
          }
        })

        console.log('Ticket tạo được là = ', ticket)

        return NextResponse.json(ticket, { status: 201 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      }

      break
    case 'update':
      const ticketUpdate: TicketUpdateData = await request.json()

      try {
        const ticket: v_users_ticket = await prisma.v_users_ticket.update({
          where: { ticket_uuid: String(ticket_uuid) },
          data: {
            ...ticketUpdate
          }
        })

        return NextResponse.json(ticket, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      }

      break
    case 'delete':
      try {
        await prisma.v_users_ticket.delete({
          where: { ticket_uuid: String(ticket_uuid) }
        })

        return NextResponse.json({}, { status: 204 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      }

      break
    default:
      return NextResponse.json('Please choose terminal endpoint !!!', { status: 500 })
  }
}
