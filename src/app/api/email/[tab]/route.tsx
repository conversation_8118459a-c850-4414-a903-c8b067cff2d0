import fs from 'fs'

import path from 'path'

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

// import * as cheerio from 'cheerio'
import { createTransport } from 'nodemailer'
import { connect } from 'imap-simple'
import { simpleParser } from 'mailparser'
import { v4 as uuidv4 } from 'uuid'

import { getSettingValueByName, transformDomainSettings } from '@/libs/setting/DomainSettingUtil'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'
import type { DomainSetting } from '@/types/apps/setting/settingTypes'
import type { Email, emailProps } from '@/types/apps/emailTypes'
import { ReadMail } from '@/libs/email/readMail'

export async function GET(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  const session = await getSession()

  if (!session) {
    return NextResponse.json('Please authentication !!!')
  }

  const uuid = session?.user?.domain ?? ''

  switch (tab) {
    case 'view': // view không phụ thuộc domain
      // console.log(' uuid  = %s', uuid)
      const _id = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của domain cần truy vấn

      try {
        const email = await prisma.v_email.findUnique({
          where: {
            id: _id as string,
            domain_uuid: uuid
          },
          include: {
            attachments: true,
            replies: true
          }
        })

        if (!email) {
          return NextResponse.json({ error: 'Email not found' }, { status: 404 })
        }

        const result = {
          id: email.id,
          from: {
            email: email.from_email,
            name: email.from_name,
            avatar: email.from_avatar
          },
          to: email.to,
          subject: email.subject,
          cc: email.cc,
          bcc: email.bcc,
          message: email.message,
          attachments: email.attachments.map(attachment => ({
            fileName: attachment.fileName,
            thumbnail: attachment.thumbnail,
            url: attachment.url,
            size: attachment.size
          })),
          isStarred: email.is_starred,
          labels: email.labels,
          time: email.time,
          replies: email.replies.map(reply => ({
            id: reply.id,
            from: {
              email: reply.fromEmail,
              name: reply.fromName,
              avatar: reply.fromAvatar
            },
            to: [],
            subject: reply.subject,
            cc: [],
            bcc: [],
            message: reply.message,
            attachments: [],
            isStarred: reply.isStarred,
            labels: [],
            time: reply.time,
            replies: [],
            folder: reply.folder,
            isRead: reply.isRead
          })),
          folder: email.folder,
          isRead: email.is_read
        }

        return NextResponse.json(result, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'list': // view không phụ thuộc domain
      try {
        const emails = await prisma.v_email.findMany({
          where: {
            domain_uuid: uuid
          },
          include: {
            attachments: true,
            replies: true
          }
        })

        const result = emails.map(email => ({
          id: email.id,
          from: {
            email: email.from_email,
            name: email.from_name,
            avatar: email.from_avatar
          },
          to: email.to,
          subject: email.subject,
          cc: email.cc,
          bcc: email.bcc,
          message: email.message,
          attachments: email.attachments.map(attachment => ({
            fileName: attachment.fileName,
            thumbnail: attachment.thumbnail,
            url: attachment.url,
            size: attachment.size
          })),
          isStarred: email.is_starred,
          labels: email.labels,
          time: email.time,
          replies: email.replies.map(reply => ({
            id: reply.id,
            from: {
              email: reply.fromEmail,
              name: reply.fromName,
              avatar: reply.fromAvatar
            },
            to: [],
            subject: reply.subject,
            cc: [],
            bcc: [],
            message: reply.message,
            attachments: [],
            isStarred: reply.isStarred,
            labels: [],
            time: reply.time,
            replies: [],
            folder: reply.folder,
            isRead: reply.isRead
          })),
          folder: email.folder,
          isRead: email.is_read
        }))

        return NextResponse.json(result, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    default:
      return NextResponse.json({ error: 'Not Support' }, { status: 500 })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab
  const session = await getSession()
  const uuid = session?.user?.domain ?? ''
  const user_id = session?.user?.id ?? null

  if (!session) {
    return NextResponse.json({ warning: 'Please authentication !!!' })
  }

  switch (tab) {
    case 'sendmail':
      try {
        const { to, cc, bcc, subject, message, attachments }: emailProps = await request.json()

        // Tìm thông tin cấu hình email

        console.log('to=', to)
        console.log('message =', message)

        const domain_settings = await prisma.v_domain_settings.findMany({
          where: {
            domain_uuid: uuid,
            domain_setting_category: 'email'
          }
        })

        if (domain_settings) {
          // có giá trị
          const category: DomainSetting[] = transformDomainSettings(domain_settings)

          const smtp_from = getSettingValueByName(category, 'smtp_from')
          const smtp_host = getSettingValueByName(category, 'smtp_host')
          const smtp_port = getSettingValueByName(category, 'smtp_port')
          const smtp_username = getSettingValueByName(category, 'smtp_username')
          const smtp_password = getSettingValueByName(category, 'smtp_password')

          console.log('from=', smtp_from)

          const transporter = createTransport({
            host: smtp_host,
            port: Number(smtp_port),
            secure: false, // Set to true for port 465 / 587, otherwise false
            auth: {
              user: smtp_username,
              pass: smtp_password
            }
          })

          const info = await transporter.sendMail({
            from: smtp_from,
            to,
            cc,
            bcc,
            subject: subject || 'No Subject',
            text: message,
            html: message
          })

          // ghi vào database ở đây luôn
          const newEmail = await prisma.v_email.create({
            data: {
              domain_uuid: uuid, // Thay bằng giá trị thực tế
              user_id: user_id,
              from_email: session?.user?.email ?? '',
              from_name: session?.user?.name ?? 'Unknown',
              from_avatar: session?.user?.image ?? '',
              subject: subject || 'No Subject',
              message: message,
              is_starred: false,
              time: new Date(),
              folder: 'sent',
              is_read: true,
              to: {
                name: to,
                email: to
              },
              cc: cc,
              bcc: bcc,
              labels: [],
              insert_user: user_id,
              attachments: attachments?.length // kiểm tra xem có file đính kèm không thì mới ghi vào database
                ? {
                    create: attachments.map(attachment => ({
                      domain_uuid: uuid, // Thay bằng giá trị thực tế
                      fileName: attachment.fileName,
                      thumbnail: attachment.thumbnail,
                      url: attachment.url,
                      size: attachment.size,
                      insert_user: user_id
                    }))
                  }
                : undefined
            }
          })

          console.log('Đã tạo database thành công =', newEmail)

          return NextResponse.json({ message: 'Email sent successfully', info }, { status: 200 })
        } else {
          console.error('Vui lòng khai báo cấu hình email:')

          return NextResponse.json({ message: 'Not yet config email server' }, { status: 500 })
        }
      } catch (error: any) {
        console.error('Error sending email:', error)

        return NextResponse.json({ message: 'Failed to send email', error: error.message }, { status: 500 })
      }

      break
    case 'receive':
      const settings = await prisma.v_domain_settings.findMany({
        where: {
          domain_uuid: uuid,
          domain_setting_category: 'email'
        }
      })

      if (!settings) {
        return NextResponse.json({ error: 'Chưa cấu hình máy chủ IMAP' }, { status: 500 })
      }

      // có giá trị
      const category: DomainSetting[] = transformDomainSettings(settings)
      const imap_host = getSettingValueByName(category, 'imap_host')
      const imap_port = getSettingValueByName(category, 'imap_port')
      const smtp_username = getSettingValueByName(category, 'smtp_username')
      const smtp_password = getSettingValueByName(category, 'smtp_password')

      console.log('imap_host =', imap_host)

      if (!imap_host || !imap_port) {
        console.error('Chưa cấu hình máy chủ IMAP:')

        return NextResponse.json({ error: 'Chưa cấu hình máy chủ IMAP' }, { status: 500 })
      }

      const emailConfig = {
        user: smtp_username ?? '',
        password: smtp_password ?? '',
        host: imap_host ?? '',
        port: Number(imap_port) || 993,
        tls: true
      }

      try {
        const emailEnvelopes = await ReadMail(emailConfig)

        console.log('Emails:', emailEnvelopes)

        console.log('Task vu đọc email hoàn thành')

        return NextResponse.json('Receive email sucessfully', { status: 200 })
      } catch (error: any) {
        console.log('Error = ', error.message)

        return NextResponse.json({ error: error.message }, { status: 500 })
      }

      break
    case 'receivemail':
      const domain_settings = await prisma.v_domain_settings.findMany({
        where: {
          domain_uuid: uuid,
          domain_setting_category: 'email'
        }
      })

      if (domain_settings) {
        // có giá trị
        const category: DomainSetting[] = transformDomainSettings(domain_settings)
        const imap_host = getSettingValueByName(category, 'imap_host')
        const imap_port = getSettingValueByName(category, 'imap_port')
        const smtp_username = getSettingValueByName(category, 'smtp_username')
        const smtp_password = getSettingValueByName(category, 'smtp_password')

        console.log('imap_host =', imap_host)

        if (!imap_host || !imap_port) {
          console.error('Chưa cấu hình máy chủ IMAP:')

          return NextResponse.json({ error: 'Chưa cấu hình máy chủ IMAP' }, { status: 500 })
        }

        const config = {
          imap: {
            user: smtp_username ?? '',
            password: smtp_password ?? '',
            host: imap_host ?? '',
            port: Number(imap_port) || 993,
            tls: true,
            authTimeout: 3000
          }
        }

        try {
          const connection = await connect(config)

          await connection.openBox('INBOX')

          const searchCriteria = ['UNSEEN']

          const fetchOptions = {
            bodies: ['HEADER', 'TEXT'],
            markSeen: true
          }

          const messages = await connection.search(searchCriteria, fetchOptions)

          if (messages.length == 0) {
            console.error('Không có email')

            return NextResponse.json({ info: 'Không có email mới' }, { status: 200 })
          }

          console.log('messages =', messages)

          const emails = await Promise.all(
            messages.map(async message => {
              console.log('message =', message)

              // Tìm phần chứa tiêu đề và văn bản của email
              const headerPart = message.parts.find(part => part.which === 'HEADER')
              const textPart = message.parts.find(part => part.which === 'TEXT')

              console.log('message headerPart =', headerPart)
              console.log('message textPart =', textPart)

              // Chuyển đổi phần tiêu đề và văn bản thành buffer

              // Phân tích phần tiêu đề để lấy thông tin from, to, subject
              const headerParsed = JSON.stringify(headerPart?.body)

              console.log('Dùng simpleParser tạo headerParsed =', headerParsed)

              // Trích xuất các trường từ, to, subject từ chuỗi
              const from = JSON.parse(headerParsed).from[0]
              const to = JSON.parse(headerParsed).to[0]
              const subject = JSON.parse(headerParsed).subject[0]
              const date = JSON.parse(headerParsed).date[0]

              console.log('From:', from)
              console.log('To:', to)
              console.log('Subject:', subject)
              console.log('Date:', date)

              const header = await simpleParser(headerPart?.body)

              console.log('Dùng simpleParser tạo headerParsed =', header)

              console.log('subject = ', header.subject)

              // Phân tích phần văn bản để lấy nội dung và tệp đính kèm
              // https://nodemailer.com/extras/mailparser/#options
              const textParsed = await simpleParser(textPart?.body)

              console.log('Dùng simpleParser tạo textParsed =', textParsed)

              // Lưu trữ file đính kèm
              if (textParsed.attachments.length > 0) {
                textParsed.attachments.forEach(attachment => {
                  const filename = attachment.filename || `${uuidv4()}.bin` // Tạo tên file ngẫu nhiên nếu filename undefined
                  const filePath = path.join(process.cwd(), 'public', 'attachments', filename)

                  fs.writeFileSync(filePath, new Uint8Array(attachment.content))

                  // Tính kích thước file
                  const fileSize = Buffer.byteLength(attachment.content)

                  console.log(`File: ${filename}, Size: ${fileSize} bytes`)
                })
              }

              // Xử lý trường hợp parsed.from có thể là AddressObject hoặc AddressObject[]
              // Loại bỏ các ký tự không phải ASCII từ nội dung text
              // const cleanText = (textParsed?.text || '').replace(/[^\x20-\x7E]/g, '')

              // Loại bỏ các ký tự không phải ASCII từ nội dung HTML
              // let cleanHtml = textParsed?.textAsHtml || ''

              // if (cleanHtml) {
              //   const $ = cheerio.load(cleanHtml)

              //   $('*').each((i, elem) => {
              //     const cleanHtmlText =
              //       $(elem)
              //         .html()
              //         ?.replace(/[^\x20-\x7E]/g, '') || ''

              //     $(elem).html(cleanHtmlText)
              //   })
              //   cleanHtml = $.html()
              // }

              return {
                from_name: from,
                from_value: from,
                from_avatar: '',
                subject: subject || 'No Subject',
                text: textParsed?.text || '',
                html: textParsed?.textAsHtml || '',
                is_starred: false,
                time: date,
                folder: 'inbox',
                is_read: false,
                to: to,
                cc: [],
                bcc: [],
                labels: [],
                attachments: textParsed.attachments.map(attachment => ({
                  filename: attachment.filename,
                  url: path.join(
                    process.cwd(),
                    'public',
                    'attachments',
                    attachment.filename || `${uuidv4()}.undefined`
                  ),
                  contentType: attachment.contentType,
                  size: Buffer.byteLength(attachment.content)
                }))
              }
            })
          )

          connection.end()

          console.log('Email nhận được =', emails)

          // ghi vào database ở đây luôn
          emails.map(async email => {
            await prisma.v_email.create({
              data: {
                domain_uuid: uuid, // Thay bằng giá trị thực tế
                from_email: email.from_value,
                from_name: email.from_name,
                from_avatar: email.from_avatar,
                subject: email.subject || 'No Subject',
                message: email.html,
                is_starred: false,
                time: new Date(email.time),
                folder: 'inbox',
                is_read: false,
                to: {
                  name: email.to,
                  email: email.to
                },
                cc: JSON.stringify(email.cc),
                bcc: JSON.stringify(email.bcc),
                labels: [],
                insert_user: user_id,
                attachments: email.attachments?.length // kiểm tra xem có file đính kèm không thì mới ghi vào database
                  ? {
                      create: email.attachments.map(attachment => ({
                        domain_uuid: uuid, // Thay bằng giá trị thực tế
                        fileName: attachment.filename ?? '',
                        thumbnail: '',
                        url: attachment.url,
                        size: attachment.size.toString(),
                        insert_user: user_id
                      }))
                    }
                  : undefined
              }
            })
          })

          return NextResponse.json('Receive email sucessfully', { status: 200 })
        } catch (error) {
          console.error('Error fetching emails:', error)

          return NextResponse.json({ error: 'Error fetching emails' }, { status: 500 })
        }
      } else {
        return NextResponse.json({ error: 'Chưa cấu hình Email Server' }, { status: 500 })
      }

      break
    case 'add':
      const emailData: Email = await request.json()

      try {
        const newEmail = await prisma.v_email.create({
          data: {
            domain_uuid: uuid, // Thay bằng giá trị thực tế
            user_id: user_id,
            from_email: emailData.from.email,
            from_name: emailData.from.name,
            from_avatar: emailData.from.avatar,
            subject: emailData.subject,
            message: emailData.message,
            is_starred: emailData.isStarred,
            time: new Date(emailData.time),
            folder: emailData.folder,
            is_read: emailData.isRead,
            to: emailData.to ?? [],
            cc: emailData.cc ?? [],
            bcc: emailData.bcc ?? [],
            labels: emailData.labels,
            insert_user: user_id,
            attachments: emailData.attachments?.length // kiểm tra xem có file đính kèm không thì mới ghi vào database
              ? {
                  create: emailData.attachments.map(attachment => ({
                    domain_uuid: uuid, // Thay bằng giá trị thực tế
                    fileName: attachment.fileName,
                    thumbnail: attachment.thumbnail,
                    url: attachment.url,
                    size: attachment.size,
                    insert_user: user_id
                  }))
                }
              : undefined
          }
        })

        NextResponse.json(newEmail, { status: 201 })
      } catch (error) {
        console.error(error)
        NextResponse.json({ error: 'Something went wrong' }, { status: 500 })
      }

      break
    default:
      return NextResponse.json({ warning: 'Please choose terminal endpoint !!!' }, { status: 500 })
  }
}
