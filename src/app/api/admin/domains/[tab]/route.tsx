// Next Imports
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { prisma } from '@/libs/db/prisma'
import getSession from '@/actions/getSession'

export async function GET(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  const session = await getSession()

  if (!session) {
    return NextResponse.json('Please authentication !!!')
  }

  const uuid = session?.user?.domain ?? ''

  switch (tab) {
    case 'getDomainByID':
      // console.log(' uuid  = %s', uuid)
      const _domain_uuid = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của domain cần truy vấn

      try {
        const _domain = await prisma.v_domains.findUnique({
          where: {
            domain_uuid: _domain_uuid // lấy domain chỉ định
          }
        })

        return NextResponse.json(_domain, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'list':
      try {
        const list_domain = await prisma.v_domains.findMany({})

        if (!list_domain) {
          return NextResponse.json({ error: 'Lỗi truy vấn danh sách user' }, { status: 404 })
        }

        return NextResponse.json(list_domain, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'getListDomain':
      // console.log(' uuid  = %s', uuid)
      try {
        const _list_domain = await prisma.v_domains.findMany({
          select: {
            domain_uuid: true,
            domain_name: true
          }
        })

        return NextResponse.json(_list_domain, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'view':
      // console.log(' uuid  = %s', uuid)
      try {
        const domain = await prisma.v_domains.findUnique({
          where: {
            domain_uuid: uuid // lấy user trong domain đăng nhập
          }
        })

        return NextResponse.json(domain, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break

    default:
      return NextResponse.json({ error: 'Not Support' }, { status: 500 })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  switch (tab) {
    default:
      return NextResponse.json({ error: 'Not Support' }, { status: 500 })
  }
}
