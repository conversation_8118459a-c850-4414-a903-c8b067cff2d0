// Next Imports
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { prisma } from '@/libs/db/prisma'
import getSession from '@/actions/getSession'

export async function GET(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  const session = await getSession()

  if (!session) {
    return NextResponse.json('Please authentication !!!')
  }

  const uuid = session?.user?.domain ?? ''

  switch (tab) {
    case 'getUserLogByID': // view không phụ thuộc domain
      // console.log(' uuid  = %s', uuid)
      const _user_uuid = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của domain cần truy vấn

      try {
        const _user = await prisma.v_user_logs.findUnique({
          where: {
            user_log_uuid: _user_uuid // lấy domain chỉ định
          }
        })

        return NextResponse.json(_user, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'list':
      // console.log(' uuid  = %s', uuid)
      try {
        const list_users = await prisma.v_user_logs.findMany({
          where: {
            domain_uuid: uuid // lấy domain chỉ định
          }
        })

        return NextResponse.json(list_users, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'getListUserLog':
      // console.log(' uuid  = %s', uuid)
      try {
        const _list_user = await prisma.v_user_logs.findMany({
          where: {
            domain_uuid: uuid // lấy domain chỉ định
          },
          select: {
            user_log_uuid: true,
            username: true
          }
        })

        return NextResponse.json(_list_user, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'view':
      // console.log(' uuid  = %s', uuid)
      const _log_uuid = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của domain cần truy vấn

      try {
        const user = await prisma.v_user_logs.findUnique({
          where: {
            domain_uuid: uuid, // lấy domain chỉ định
            user_log_uuid: _log_uuid
          }
        })

        return NextResponse.json(user, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break

    default:
      return NextResponse.json({ error: 'Not Support' }, { status: 500 })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  switch (tab) {
    default:
      return NextResponse.json({ error: 'Not Support' }, { status: 500 })
  }
}
