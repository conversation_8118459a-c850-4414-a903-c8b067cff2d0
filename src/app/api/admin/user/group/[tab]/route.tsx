// Next Imports
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import type { v_user_groups } from '@prisma/client'

import { prisma } from '@/libs/db/prisma'
import getSession from '@/actions/getSession'

export async function GET(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  const session = await getSession()

  if (!session) {
    return NextResponse.json('Please authentication !!!')
  }

  const uuid = session?.user?.domain ?? ''

  switch (tab) {
    case 'getUserGroupByID': // view không phụ thuộc domain
      // console.log(' uuid  = %s', uuid)
      const _user_group_uuid = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của domain cần truy vấn

      try {
        const _user = await prisma.v_user_groups.findUnique({
          where: {
            user_group_uuid: _user_group_uuid // lấy domain chỉ định
          }
        })

        return NextResponse.json(_user, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'getGroupByUserID': // view không phụ thuộc domain
      // console.log(' uuid  = %s', uuid)
      const _user_uuid = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của domain cần truy vấn

      try {
        const _userGroup = await prisma.v_user_groups.findMany({
          where: {
            user_uuid: _user_uuid
          },
          select: {
            group_name: true
          }
        })

        // Chuyển đổi kết quả trả về thành một mảng các chuỗi
        const groupNames = _userGroup.map(group => group.group_name)

        return NextResponse.json(groupNames, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break

    case 'getGroupsByUserID':
      const _uuid = request.nextUrl.searchParams.get('id') ?? ''

      try {
        const _userGroups = await prisma.v_user_groups.findMany({
          where: {
            user_uuid: _uuid
          },
          select: {
            group_name: true,
            group_uuid: true,
            user_group_uuid: true
          }
        })

        return NextResponse.json(_userGroups, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break

    case 'list':
      // console.log(' uuid  = %s', uuid)

      try {
        const list_users = await prisma.v_user_groups.findMany({
          where: {
            domain_uuid: uuid // lấy domain chỉ định
          }
        })

        if (!list_users) {
          return NextResponse.json({ error: 'Lỗi truy vấn danh sách user' }, { status: 404 })
        }

        return NextResponse.json(list_users, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'getListUserGroupName':
      // console.log(' uuid  = %s', uuid)
      try {
        const _list_user = await prisma.v_user_groups.findMany({
          where: {
            domain_uuid: uuid // lấy domain chỉ định
          },
          select: {
            user_group_uuid: true,
            group_name: true
          }
        })

        return NextResponse.json(_list_user, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'view':
      // console.log(' uuid  = %s', uuid)
      const _us_uuid = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của domain cần truy vấn

      try {
        const user = await prisma.v_users.findUnique({
          where: {
            domain_uuid: uuid, // lấy domain chỉ định
            user_uuid: _us_uuid
          }
        })

        return NextResponse.json(user, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break

    default:
      return NextResponse.json({ error: 'Not Support' }, { status: 500 })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  const uuid = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của user group cần truy vấn

  switch (tab) {
    case 'add':
      // v_users ,v_user_settings, v_groups, v_user_groups, v_contacts

      console.log('Đã đến đây')

      const userGroupData: v_user_groups = await request.json()

      console.log('Data nhận được =', userGroupData)

      try {
        const userGroup: v_user_groups = await prisma.v_user_groups.create({
          data: {
            ...userGroupData
          }
        })

        console.log('User Group tạo được là = ', userGroup)

        return NextResponse.json(userGroup, { status: 201 })
      } catch (error: any) {
        console.log('Lỗi tạo user Group', error.message)

        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'delete':
      console.log('UUID nhận được ', uuid)

      try {
        // Truy vấn record cần xóa dựa trên user_uuid
        const userGroup = await prisma.v_user_groups.findMany({
          where: { user_uuid: uuid },
          select: {
            user_group_uuid: true
          }
        })

        if (!userGroup) {
          console.log('Không tìm thấy userGroup theo  ', uuid)

          return NextResponse.json({ error: 'Record not found' }, { status: 404 })
        }

        // console.log('user_group_uuid =', userGroup.user_group_uuid)

        // Xóa record dựa trên ID hoặc trường duy nhất khác
        await Promise.all(
          userGroup.map(group => {
            return prisma.v_user_groups.delete({
              where: {
                user_group_uuid: group.user_group_uuid
              }
            })
          })
        )

        return NextResponse.json('Xoá thành công', { status: 200 })
      } catch (error: any) {
        console.log('Lỗi xoá User Groups', error.message)

        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break

    default:
      return NextResponse.json({ error: 'Not Support' }, { status: 500 })
  }
}
