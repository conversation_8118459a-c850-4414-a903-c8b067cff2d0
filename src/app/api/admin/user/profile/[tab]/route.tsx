// Next Imports
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { v4 as uuidv4 } from 'uuid'

import type { v_users } from '@prisma/client'

import { prisma } from '@/libs/db/prisma'
import getSession from '@/actions/getSession'
import type { UserMailType, UsernamesType } from '@/types/apps/admin/userTypes'

export async function GET(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  const session = await getSession()

  if (!session) {
    return NextResponse.json('Please authentication !!!')
  }

  const uuid = session?.user?.domain ?? ''

  switch (tab) {
    case 'getUserByID': // view không phụ thuộc domain
      // console.log(' uuid  = %s', uuid)

      try {
        const _user_uuid = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của domain cần truy vấn

        const _user = await prisma.v_users.findUnique({
          where: {
            user_uuid: _user_uuid // lấy domain chỉ định
          }
        })

        return NextResponse.json(_user, { status: 200 })
      } catch (error: any) {
        console.log(' Lỗi truy vấn database ', error.message)

        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'listview':
      // const cacheListView = `view_users`

      try {
        const list_users = await prisma.view_users.findMany({
          where: {
            domain_uuid: uuid // lấy domain chỉ định
          }
        })

        if (!list_users) {
          return NextResponse.json({ error: 'Lỗi truy vấn danh sách user' }, { status: 404 })
        }

        return NextResponse.json(list_users, { status: 200 })
      } catch (error: any) {
        console.log(' Lỗi truy vấn database ', error.message)

        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'totalCount':
      const totalUser = await prisma.v_users.count({
        where: {
          domain_uuid: uuid
        }
      })

      return NextResponse.json({ total: totalUser }, { status: 200 })
      break
    case 'list':
      console.log(' uuid  = %s', uuid)

      try {
        const listview_users = await prisma.v_users.findMany({
          where: {
            domain_uuid: uuid // lấy domain chỉ định
          }
        })

        if (!listview_users) {
          return NextResponse.json({ error: 'Lỗi truy vấn danh sách user' }, { status: 404 })
        }

        return NextResponse.json(listview_users, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'getListUserName':
      // console.log(' uuid  = %s', uuid)
      try {
        const res = await prisma.v_users.findMany({
          where: {
            domain_uuid: uuid // lấy domain chỉ định
          },
          select: {
            user_uuid: true,
            username: true
          }
        })

        const _list_user: UsernamesType[] = res

        return NextResponse.json(_list_user, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'getListUserMail':
      // console.log(' uuid  = %s', uuid)
      try {
        const usermail = await prisma.v_users.findMany({
          where: {
            domain_uuid: uuid // lấy domain chỉ định
          },
          select: {
            user_uuid: true,
            username: true,
            user_email: true
          }
        })

        const _list_usermail: UserMailType[] = usermail

        return NextResponse.json(_list_usermail, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break

    case 'view':
      // console.log(' uuid  = %s', uuid)
      const _us_uuid = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của domain cần truy vấn

      try {
        const user = await prisma.v_users.findUnique({
          where: {
            domain_uuid: uuid, // lấy domain chỉ định
            user_uuid: _us_uuid
          }
        })

        return NextResponse.json(user, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break

    default:
      return NextResponse.json({ error: 'Not Support' }, { status: 500 })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json('Please authentication !!!')
  }

  const tab = params.tab

  const uuid = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của user cần truy vấn
  const domain_uuid = session?.user?.domain ?? ''
  const user_uuid = session?.user?.id ?? ''

  switch (tab) {
    case 'add':
      // v_users ,v_user_settings, v_groups, v_user_groups, v_contacts

      console.log('Đã đến đây')

      const userData: v_users = await request.json()

      console.log('Data nhận được =', userData)

      try {
        const user: v_users = await prisma.v_users.create({
          data: {
            ...userData
          }
        })

        console.log('User tạo được là = ', user)

        // Trả về kết quả
        return NextResponse.json(user, { status: 201 })
      } catch (error: any) {
        console.log('Lỗi tạo user', error.message)

        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break

    case 'edit':
      // v_users ,v_user_settings, v_groups, v_user_groups, v_contacts

      try {
        const userData = await request.json()

        console.log('Received data:', userData)

        // Check if `user_uuid` is provided
        if (!userData.user_uuid) {
          return NextResponse.json({ error: 'user_uuid is required for updating the user.' }, { status: 400 })
        }

        // Update user data
        const updatedUser = await prisma.v_users.update({
          where: {
            user_uuid: userData.user_uuid // Use `user_uuid` instead of `id`
          },
          data: {
            ...userData // Ensure that `userData` only contains valid fields for `v_users`
          }
        })

        console.log('Updated user:', updatedUser)

        return NextResponse.json(updatedUser, { status: 200 })
      } catch (error: any) {
        console.error('Error updating user:', error.message)

        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        await prisma.$disconnect()
      }

      break

    case 'update':
      try {
        const { userData, groups } = await request.json()

        console.log('Received data:', userData, groups)

        if (!userData.user_uuid) {
          return NextResponse.json({ error: 'user_uuid is required for updating the user.' }, { status: 400 })
        }

        const updatedUser = await prisma.$transaction(async prisma => {
          const updatedUser = await prisma.v_users.update({
            where: { user_uuid: userData.user_uuid },
            data: {
              user_type: userData.user_type,
              user_email: userData.user_email,
              contact_uuid: userData.contact_uuid,
              domain_uuid: userData.domain_uuid
            }
          })

          const existingGroups = await prisma.v_user_groups.findMany({
            where: { user_uuid: userData.user_uuid },
            select: { group_uuid: true, user_group_uuid: true }
          })

          const existingGroupUuids = existingGroups.map(group => group.group_uuid)

          const groupsToAddUuids: string[] = groups.filter((uuid: string) => !existingGroupUuids.includes(uuid))
          const groupsToRemove = existingGroups.filter(group => !groups.includes(group.group_uuid))

          const groupsToAdd = await prisma.v_groups.findMany({
            where: { group_uuid: { in: groupsToAddUuids } },
            select: { group_uuid: true, group_name: true }
          })

          await Promise.all([
            ...groupsToAdd.map(group =>
              prisma.v_user_groups.create({
                data: {
                  domain_uuid: userData.domain_uuid || domain_uuid,
                  user_group_uuid: uuidv4(),
                  user_uuid: userData.user_uuid,
                  group_uuid: group.group_uuid,
                  group_name: group.group_name,
                  insert_date: new Date(),
                  insert_user: user_uuid
                }
              })
            ),
            ...groupsToRemove.map(group =>
              prisma.v_user_groups.delete({
                where: { user_group_uuid: group.user_group_uuid }
              })
            )
          ])

          return updatedUser
        })

        console.log('Updated user:', updatedUser)

        return NextResponse.json(updatedUser, { status: 200 })
      } catch (error: any) {
        console.error('Error updating user:', error.message)

        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        await prisma.$disconnect()
      }

      break

    case 'delete':
      try {
        await prisma.v_users.delete({
          where: { user_uuid: uuid }
        })

        return NextResponse.json('Xoá thành công', { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break

    default:
      return NextResponse.json({ error: 'Not Support' }, { status: 500 })
  }
}
