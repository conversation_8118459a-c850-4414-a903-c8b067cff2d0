// Next Imports
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { prisma } from '@/libs/db/prisma'
import getSession from '@/actions/getSession'

export async function GET(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  const session = await getSession()

  if (!session) {
    return NextResponse.json('Please authentication !!!')
  }

  const uuid = session?.user?.domain ?? ''

  switch (tab) {
    case 'getUserSettingByID': // view không phụ thuộc domain
      // console.log(' uuid  = %s', uuid)
      const _user_uuid = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của domain cần truy vấn

      try {
        const _user = await prisma.v_user_settings.findUnique({
          where: {
            user_setting_uuid: _user_uuid // lấy domain chỉ định
          }
        })

        return NextResponse.json(_user)
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'list':
      // console.log(' uuid  = %s', uuid)

      try {
        const list_users = await prisma.v_user_settings.findMany({
          where: {
            domain_uuid: uuid // lấy domain chỉ định
          }
        })

        if (!list_users) {
          return NextResponse.json({ error: 'Lỗi truy vấn danh sách user setting' }, { status: 404 })
        }

        return NextResponse.json(list_users, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'getListUserSetting':
      // console.log(' uuid  = %s', uuid)
      try {
        const _list_user = await prisma.v_user_settings.findMany({
          where: {
            domain_uuid: uuid // lấy domain chỉ định
          },
          select: {
            user_setting_uuid: true,
            user_setting_name: true
          }
        })

        return NextResponse.json(_list_user, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'view':
      // console.log(' uuid  = %s', uuid)
      const _us_uuid = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của domain cần truy vấn

      try {
        const user = await prisma.v_users.findUnique({
          where: {
            domain_uuid: uuid, // lấy domain chỉ định
            user_uuid: _us_uuid
          }
        })

        return NextResponse.json(user, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break

    default:
      return NextResponse.json({ error: 'Not Support' }, { status: 500 })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  const result = ''

  switch (tab) {
    case 'status':
      return NextResponse.json(result)
      break
    case 'reload':
      // reload hệ thống Freeswitch

      return NextResponse.json(result)
      break

    default:
      return NextResponse.json({ error: 'Not Support' }, { status: 500 })
  }
}
