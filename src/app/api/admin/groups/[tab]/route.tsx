// Next Imports
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { prisma } from '@/libs/db/prisma'
import getSession from '@/actions/getSession'

export async function GET(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  const session = await getSession()

  if (!session) {
    return NextResponse.json('Please authentication !!!')
  }

  const uuid = session?.user?.domain ?? ''

  switch (tab) {
    case 'getGroupByID':
      // console.log(' uuid  = %s', uuid)
      const _group_uuid = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của domain cần truy vấn

      try {
        const _group = await prisma.v_groups.findUnique({
          where: {
            group_uuid: _group_uuid // lấy domain chỉ định
          }
        })

        return NextResponse.json(_group, { status: 200 })
      } catch (error: any) {
        console.log('Error = ', error.message)

        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break

    case 'list':
      // console.log(' uuid  = %s', uuid)

      try {
        const list_group = await prisma.v_groups.findMany({})

        if (!list_group) {
          return NextResponse.json({ error: 'Lỗi truy vấn danh sách groups' }, { status: 404 })
        }

        return NextResponse.json(list_group, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'getListGroup':
      // console.log(' uuid  = %s', uuid)
      try {
        const _list_group = await prisma.v_groups.findMany({
          select: {
            group_uuid: true,
            group_name: true
          }
        })

        return NextResponse.json(_list_group, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'view':
      // console.log(' uuid  = %s', uuid)
      try {
        const group = await prisma.v_groups.findFirst({
          where: {
            domain_uuid: uuid // lấy group
          }
        })

        return NextResponse.json(group, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break

    default:
      return NextResponse.json({ error: 'Not Support' }, { status: 500 })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  switch (tab) {
    default:
      return NextResponse.json({ error: 'Not Support' }, { status: 500 })
  }
}
