// Next Imports
import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import type { v_contacts } from '@prisma/client'

import { prisma } from '@/libs/db/prisma'
import getSession from '@/actions/getSession'

export async function GET(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  const session = await getSession()

  if (!session) {
    return NextResponse.json('Please authentication !!!')
  }

  const uuid = session?.user?.domain ?? ''

  switch (tab) {
    case 'getContactByID': // view không phụ thuộc domain
      // console.log(' uuid  = %s', uuid)
      const _contact_uuid = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của domain cần truy vấn

      try {
        if (!_contact_uuid) throw new Error('contact uuid is null')

        const _contact = await prisma.v_contacts.findUnique({
          where: {
            contact_uuid: _contact_uuid // lấy domain chỉ định
          }
        })

        // console.log('Contact = ', _contact)

        return NextResponse.json(_contact, { status: 200 })
      } catch (error: any) {
        console.log('Error = ', error.message)

        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'list':
      // console.log(' uuid  = %s', uuid)

      try {
        const list_contacts = await prisma.v_contacts.findMany({
          where: {
            domain_uuid: uuid // lấy domain chỉ định
          }
        })

        if (!list_contacts) {
          return NextResponse.json({ error: 'Lỗi truy vấn danh sách Contact' }, { status: 404 })
        }

        return NextResponse.json(list_contacts, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'getListContactName':
      // console.log(' uuid  = %s', uuid)
      try {
        const _list_contactname = await prisma.v_contacts.findMany({
          where: {
            domain_uuid: uuid // lấy domain chỉ định
          },
          select: {
            contact_uuid: true,
            contact_nickname: true
          }
        })

        return NextResponse.json(_list_contactname, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'view':
      // console.log(' uuid  = %s', uuid)
      const _ct_uuid = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của domain cần truy vấn

      try {
        const contact = await prisma.v_contacts.findUnique({
          where: {
            domain_uuid: uuid, // lấy domain chỉ định
            contact_uuid: _ct_uuid
          }
        })

        return NextResponse.json(contact, { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break

    default:
      return NextResponse.json({ error: 'Not Support' }, { status: 500 })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { tab: string } } // phương pháp này dùng lấy tab id trong [tab]
) {
  const tab = params.tab

  const uuid = request.nextUrl.searchParams.get('id') ?? '' // lấy uuid của user cần truy vấn

  switch (tab) {
    case 'add':
      // v_users ,v_user_settings, v_groups, v_user_groups, v_contacts

      console.log('Đã đến đây')

      const userData: v_contacts = await request.json()

      console.log('Data nhận được =', userData)

      try {
        const user: v_contacts = await prisma.v_contacts.create({
          data: {
            ...userData
          }
        })

        console.log('User tạo được là = ', user)

        // Trả về kết quả
        return NextResponse.json(user, { status: 201 })
      } catch (error: any) {
        console.log('Lỗi tạo user', error.message)

        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break
    case 'delete':
      try {
        await prisma.v_contacts.delete({
          where: { contact_uuid: uuid }
        })

        return NextResponse.json('Xoá thành công', { status: 200 })
      } catch (error: any) {
        return NextResponse.json({ error: error.message }, { status: 500 })
      } finally {
        // Đóng kết nối sau khi hoàn thành công việc
        await prisma.$disconnect()
      }

      break

    default:
      return NextResponse.json({ error: 'Not Support' }, { status: 500 })
  }
}
