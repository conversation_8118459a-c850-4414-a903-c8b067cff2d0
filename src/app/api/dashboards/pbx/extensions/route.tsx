import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'

export async function GET(request: NextRequest) {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''
  const sessionUserUuid = session?.user?.id ?? ''
  const user_uuid = request.nextUrl.searchParams.get('user_uuid')

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  const isAdmin = session?.user?.role === 'superadmin' || session?.user?.role === 'admin'
  const filterUserUuid = isAdmin ? user_uuid : sessionUserUuid

  try {
    let extensionUuids: string[] = []

    if (filterUserUuid) {
      // Get extension UUIDs associated with the user
      const userExtensions = await prisma.v_extension_users.findMany({
        where: {
          user_uuid: filterUserUuid,
          domain_uuid: domain_uuid
        },
        select: {
          extension_uuid: true
        }
      })

      extensionUuids = userExtensions
        .map(record => record.extension_uuid)
        .filter((uuid): uuid is string => uuid !== null)
    }

    // Fetch extensions based on domain_uuid and filtered user_uuid
    const extensions = await prisma.v_extensions.findMany({
      where: {
        domain_uuid: domain_uuid,
        ...(filterUserUuid ? { extension_uuid: { in: extensionUuids } } : {})
      },
      orderBy: {
        extension: 'asc'
      },
      select: {
        extension_uuid: true,
        extension: true,
        number_alias: true
      }
    })

    return NextResponse.json({ data: extensions }, { status: 200 })
  } catch (error) {
    console.error('Error fetching extensions:', error)

    return NextResponse.json({ message: 'An error occurred' }, { status: 500 })
  }
}
