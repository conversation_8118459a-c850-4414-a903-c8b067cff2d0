import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'

export async function GET() {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  try {
    const enabledCount = await prisma.v_extensions.aggregate({
      _count: {
        extension_uuid: true
      },
      where: {
        domain_uuid: domain_uuid,
        enabled: 'true'
      }
    })

    return NextResponse.json({ data: { total: enabledCount._count.extension_uuid } }, { status: 200 })
  } catch (error) {
    console.error('Error fetching extensions:', error)

    return NextResponse.json({ message: 'An error occurred' }, { status: 500 })
  }
}
