import { NextResponse, type NextRequest } from 'next/server'

import { prisma } from '@/libs/db/prisma'
import getSession from '@/actions/getSession'
import type { ExtensionType } from '@/types/apps/pbx/extensionTypes'

export async function GET(request: NextRequest) {
  const session = await getSession()

  if (!session || !session.user?.domain) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session.user.domain
  const { searchParams } = new URL(request.url)
  const limit = parseInt(searchParams.get('limit') || '20')
  const period = searchParams.get('period')

  try {
    const today = new Date()

    let startDate: Date, endDate: Date

    switch (period) {
      case 'daily':
        startDate = new Date(today)
        startDate.setDate(today.getDate() - 1)
        startDate.setHours(0, 0, 0, 0)

        endDate = new Date(startDate)
        endDate.setHours(23, 59, 59, 999)

        break
      case 'weekly':
        startDate = new Date(today)
        startDate.setDate(today.getDate() - 6)
        startDate.setHours(0, 0, 0, 0)
        endDate = new Date(today)
        endDate.setDate(today.getDate() + 1)
        break
      default:
        return NextResponse.json({ error: 'Invalid period' }, { status: 400 })
    }

    // First query: Get all extension UUIDs
    const extensions = await prisma.v_extensions.findMany({
      where: { domain_uuid: domain_uuid },
      select: { extension_uuid: true, extension: true, directory_first_name: true, directory_last_name: true }
    })

    const extensionMap = new Map<string, ExtensionType>()

    extensions.forEach(item => {
      extensionMap.set(item.extension_uuid, item as ExtensionType)
    })

    const availableExts = await prisma.v_xml_cdr.findMany({
      select: {
        xml_cdr_uuid: true,
        caller_destination: true
      },
      where: {
        domain_uuid: domain_uuid,
        direction: 'outbound',
        start_stamp: {
          gte: startDate,
          lt: endDate
        }
      }
    })

    const xmlCdrUuids = availableExts
      .filter(
        record =>
          record.caller_destination &&
          typeof record.caller_destination === 'string' &&
          record.caller_destination.length < 24
      )
      .map(record => record.xml_cdr_uuid)

    const topExtensions = await prisma.v_xml_cdr.groupBy({
      by: ['extension_uuid'],
      _count: {
        caller_destination: true
      },
      where: {
        domain_uuid: domain_uuid,
        start_stamp: {
          gte: startDate,
          lt: endDate
        },
        xml_cdr_uuid: {
          in: xmlCdrUuids
        }
      },
      orderBy: {
        _count: {
          caller_destination: 'desc'
        }
      },
      take: limit
    })

    const formattedData = topExtensions.map(item => ({
      extension_uuid: item.extension_uuid,
      extension: item.extension_uuid ? extensionMap.get(item.extension_uuid) : {},
      total: item._count.caller_destination
    }))

    return NextResponse.json({ data: formattedData }, { status: 200 })
  } catch (error) {
    console.error('Error fetching data:', error)

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
