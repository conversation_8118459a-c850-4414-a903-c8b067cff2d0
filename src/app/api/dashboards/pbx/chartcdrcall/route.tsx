import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'

import { prisma } from '@/libs/db/prisma'

export async function GET(request: NextRequest) {
  const session = await getSession()

  if (!session || !session.user?.domain) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session.user.domain

  const { searchParams } = new URL(request.url)
  const user_uuid = searchParams.get('user_uuid')
  const extension_uuid = searchParams.get('extension_uuid')

  const getLast7Days = () => {
    // const today = new Date('2024-07-27')
    const today = new Date()
    const last7Days = []

    for (let i = 0; i < 7; i++) {
      const date = new Date(today)

      date.setDate(today.getDate() - i)
      last7Days.push(date.toISOString().split('T')[0])
    }

    return last7Days.reverse()
  }

  const dates = getLast7Days()

  try {
    const results = await Promise.all(
      dates.map(async date => {
        // Define the base query condition
        const whereCondition: any = {
          domain_uuid: String(domain_uuid),
          start_stamp: {
            gte: new Date(date),
            lt: new Date(new Date(date).setDate(new Date(date).getDate() + 1)) // Up to the end of the day
          },
          caller_destination: {
            not: null
          }
        }

        // Add extension_uuid filtering based on user_uuid
        if (user_uuid) {
          const userExtensions = await prisma.v_extension_users.findMany({
            where: {
              user_uuid: String(user_uuid)
            },
            select: {
              extension_uuid: true
            }
          })

          const extensionUuids = userExtensions.map(e => e.extension_uuid)

          whereCondition.extension_uuid = { in: extensionUuids }
        } else if (extension_uuid) {
          whereCondition.extension_uuid = String(extension_uuid)
        }

        const records = await prisma.v_xml_cdr.findMany({
          where: {
            ...whereCondition,
            direction: 'outbound'
          }
        })

        const filteredRecords = records.filter(item => item.caller_destination && item.caller_destination.length <= 24)
        const successfulCalls = filteredRecords.filter(item => item.status === 'answered').length

        const failedCalls = filteredRecords.filter(
          item =>
            item.status !== 'answered' &&
            item.status !== 'busy' &&
            item.status !== 'failed' &&
            !(item.status === 'failed' && item.sip_hangup_disposition === 'send_refuse')
        ).length

        const noPickupCalls = filteredRecords.filter(
          item => item.status === 'failed' && item.sip_hangup_disposition === 'send_refuse'
        ).length

        const busyCalls = filteredRecords.filter(
          item => item.status === 'busy' && item.sip_hangup_disposition === 'send_refuse'
        ).length

        return {
          date,
          total: filteredRecords.length,
          success: successfulCalls,
          failure: failedCalls,
          noPickup: noPickupCalls,
          busy: busyCalls
        }
      })
    )

    return NextResponse.json({ data: results }, { status: 200 })
  } catch (error) {
    console.error('Error fetching data:', error)

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
