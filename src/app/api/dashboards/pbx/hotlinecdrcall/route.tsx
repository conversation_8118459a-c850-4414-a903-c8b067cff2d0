import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'

export async function GET(request: NextRequest) {
  const session = await getSession()

  if (!session || !session.user?.domain) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session.user.domain

  const { searchParams } = new URL(request.url)
  const limit = parseInt(searchParams.get('limit') || '20')
  const period = searchParams.get('period')

  try {
    // const today = new Date('2024-07-27')
    const today = new Date()

    let startDate: Date, endDate: Date

    switch (period) {
      case 'daily':
        startDate = new Date(today)
        startDate.setDate(today.getDate() - 1)
        startDate.setHours(0, 0, 0, 0)

        endDate = new Date(startDate)
        endDate.setHours(23, 59, 59, 999)
        break
      case 'weekly':
        startDate = new Date(today)
        startDate.setDate(today.getDate() - 6)
        endDate = new Date(today)
        endDate.setDate(today.getDate() + 1)
        break
      default:
        return NextResponse.json({ error: 'Invalid period' }, { status: 400 })
    }

    const topHotline = await prisma.v_xml_cdr.groupBy({
      by: ['caller_destination'],
      _count: {
        caller_destination: true
      },
      where: {
        domain_uuid: domain_uuid,
        direction: 'inbound',
        start_stamp: {
          gte: startDate,
          lt: endDate
        }
      },
      orderBy: {
        _count: {
          caller_destination: 'desc'
        }
      },
      take: limit
    })

    const formattedData = topHotline.map(item => ({
      caller_destination: item.caller_destination,
      total: item._count.caller_destination
    }))

    return NextResponse.json({ data: formattedData }, { status: 200 })
  } catch (error) {
    console.error('Error fetching data:', error)

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
