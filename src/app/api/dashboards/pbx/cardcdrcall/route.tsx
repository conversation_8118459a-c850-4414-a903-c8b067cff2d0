import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'

const fetchAggregateData = async (conditions: any, extraCondition: any = {}) => {
  // Fetch all required data without the length filter
  const [totalCallsData, successfulCallsData, failedCallsData, totalBillsecData, noPickUpCallsData, userBusyCallsData] =
    await Promise.all([
      prisma.v_xml_cdr.findMany({ where: conditions, select: { caller_destination: true, extension_uuid: true } }),
      prisma.v_xml_cdr.findMany({
        where: { ...conditions, status: 'answered', ...extraCondition },
        select: { caller_destination: true }
      }),
      prisma.v_xml_cdr.findMany({
        where: {
          ...conditions,
          NOT: [
            { status: 'answered' },
            { status: 'failed', sip_hangup_disposition: 'send_refuse' },
            { status: 'busy', sip_hangup_disposition: 'send_refuse' }
          ],
          ...extraCondition
        },
        select: { caller_destination: true }
      }),
      prisma.v_xml_cdr.findMany({
        where: { ...conditions, hangup_cause: 'NORMAL_CLEARING', ...extraCondition },
        select: { billsec: true, caller_destination: true }
      }),
      prisma.v_xml_cdr.findMany({
        where: { ...conditions, status: 'failed', sip_hangup_disposition: 'send_refuse', ...extraCondition },
        select: { caller_destination: true }
      }),
      prisma.v_xml_cdr.findMany({
        where: { ...conditions, status: 'busy', sip_hangup_disposition: 'send_refuse', ...extraCondition },
        select: { caller_destination: true }
      })
    ])

  const filterAndCount = (records: any[]) =>
    records.filter(
      record =>
        record.caller_destination &&
        typeof record.caller_destination === 'string' &&
        record.caller_destination.length < 24
    ).length

  const filterAndSumBillsec = (records: any[]) => {
    let totalBillsec = 0

    for (const record of records) {
      if (record.billsec && typeof record.caller_destination === 'string' && record.caller_destination.length < 24) {
        totalBillsec += Number(record.billsec)
      }
    }

    return totalBillsec
  }

  const uniqueExtensions = new Set(totalCallsData.map(record => record.extension_uuid))

  return {
    totalCalls: filterAndCount(totalCallsData),
    successfulCalls: filterAndCount(successfulCallsData),
    failedCalls: filterAndCount(failedCallsData),
    totalBillsec: filterAndSumBillsec(totalBillsecData), // Ensure this uses the filtered records
    noPickUpCalls: filterAndCount(noPickUpCallsData),
    userBusyCalls: filterAndCount(userBusyCallsData),
    totalExtCount: Number(uniqueExtensions.size)
  }
}

export async function GET(request: NextRequest) {
  const session = await getSession()

  if (!session || !session.user?.domain || !session.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
  }

  const domain_uuid = session.user.domain

  try {
    const { searchParams } = new URL(request.url)
    const extension_uuid = searchParams.get('extension_uuid')
    const qry_user_uuid = searchParams.get('user_uuid')

    const today = new Date()
    const sixDaysAgo = new Date(today)

    sixDaysAgo.setDate(today.getDate() - 6)
    sixDaysAgo.setHours(0, 0, 0, 0)
    const yesterdayStart = new Date(today)

    yesterdayStart.setDate(today.getDate() - 1)
    yesterdayStart.setHours(0, 0, 0, 0)

    const yesterdayEnd = new Date(yesterdayStart)

    yesterdayEnd.setHours(23, 59, 59, 999)

    // Define conditions with optional extension_uuid
    const conditions: {
      start_stamp: { gte: Date; lt: Date }
      domain_uuid: string
      extension_uuid?: string | { in: string[] }
      direction?: string
    } = {
      start_stamp: { gte: sixDaysAgo, lt: today },
      domain_uuid: domain_uuid,
      direction: 'outbound'
    }

    const yesterdayConditions = {
      ...conditions,
      start_stamp: { gte: yesterdayStart, lt: yesterdayEnd }
    }

    if (extension_uuid) {
      conditions.extension_uuid = extension_uuid
      yesterdayConditions.extension_uuid = extension_uuid
    } else if (qry_user_uuid) {
      const extensions = await prisma.v_extension_users.findMany({
        where: { user_uuid: qry_user_uuid },
        select: { extension_uuid: true }
      })

      const extensionUuids = extensions.map(e => e.extension_uuid).filter((uuid): uuid is string => uuid !== null) // Filters out null values

      conditions.extension_uuid = { in: extensionUuids }
      yesterdayConditions.extension_uuid = { in: extensionUuids }
    }

    // Fetch weekly and daily statistics
    const {
      totalCalls: weeklyTotal,
      successfulCalls: weeklySuccess,
      failedCalls: weeklyFailure,
      totalBillsec: weeklyBillsec,
      noPickUpCalls: weeklyNoPickup,
      userBusyCalls: weeklyUserBusy
    } = await fetchAggregateData(conditions)

    // Fetch daily statistics
    const {
      totalCalls: yesterdayTotal,
      successfulCalls: yesterdaySuccess,
      failedCalls: yesterdayFailure,
      totalBillsec: yesterdayBillsec,
      noPickUpCalls: yesterdayNoPickup,
      userBusyCalls: yesterdayUserBusy,
      totalExtCount: yesterdayExtCount
    } = await fetchAggregateData(yesterdayConditions)

    // Calculate statistics with type conversion
    const totalCallsWeek = Number(weeklyTotal || 0)
    const successfulCallsWeek = Number(weeklySuccess || 0)
    const failedCallsWeek = Number(weeklyFailure || 0)
    const totalBillsecWeek = Number(weeklyBillsec || 0)
    const percentSuccessWeek = totalCallsWeek ? ((successfulCallsWeek / totalCallsWeek) * 100).toFixed(2) : '0'
    const avgCallTimeWeek = totalCallsWeek ? (totalBillsecWeek / totalCallsWeek).toFixed(2) : '0'

    const totalCallsYesterday = Number(yesterdayTotal || 0)
    const successfulCallsYesterday = Number(yesterdaySuccess || 0)
    const failedCallsYesterday = Number(yesterdayFailure || 0)
    const totalBillsecYesterday = Number(yesterdayBillsec || 0)
    const avgCallTimeYesterday = totalCallsYesterday ? (totalBillsecYesterday / totalCallsYesterday).toFixed(2) : '0'

    // Use the directly returned values for `nopickupCallsYesterday` and `userbusyCallsYesterday`
    const nopickupCallsYesterday = Number(yesterdayNoPickup || 0)
    const userbusyCallsYesterday = Number(yesterdayUserBusy || 0)

    return NextResponse.json(
      {
        data: {
          totalYesterday: totalCallsYesterday,
          successYesterday: successfulCallsYesterday,
          failureYesterday: failedCallsYesterday,
          nopickupYesterday: nopickupCallsYesterday,
          userbusyYesterday: userbusyCallsYesterday,
          avgCallTimeYesterday: avgCallTimeYesterday,
          sumBillsecYesterday: totalBillsecYesterday,

          totalWeek: totalCallsWeek,
          successWeek: successfulCallsWeek,
          failureWeek: failedCallsWeek,
          avgTimeWeek: avgCallTimeWeek,
          noPickupWeek: weeklyNoPickup,
          callBusyWeek: weeklyUserBusy,
          totalBillsecWeek,
          percentSuccessWeek,
          yesterdayExtCount
        }
      },
      { status: 200 }
    )
  } catch (error) {
    console.error('Error fetching call stats:', error)

    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
