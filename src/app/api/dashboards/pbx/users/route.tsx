import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'

export async function GET() {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''

  console.log('domain_uuid = %s', domain_uuid)

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  try {
    const users = await prisma.v_users.findMany({
      select: { user_uuid: true, username: true },
      where: { domain_uuid: String(domain_uuid) },
      orderBy: {
        username: 'asc'
      }
    })

    return NextResponse.json({ data: users }, { status: 200 })
  } catch (error) {
    console.error(error)

    return NextResponse.json({ message: 'An error occurred' }, { status: 500 })
  }
}
