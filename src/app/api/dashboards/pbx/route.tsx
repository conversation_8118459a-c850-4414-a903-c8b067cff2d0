import { NextResponse } from 'next/server'

import getSession from '@/actions/getSession'
import { prisma } from '@/libs/db/prisma'

export async function GET() {
  const session = await getSession()

  if (!session) {
    return NextResponse.json({ error: 'Please authenticate' }, { status: 401 })
  }

  const domain_uuid = session?.user?.domain ?? ''

  console.log('domain_uuid = %s', domain_uuid)

  if (!domain_uuid) {
    return NextResponse.json({ error: 'Missing domain_uuid' }, { status: 400 })
  }

  try {
    const domain_obj = await prisma.v_domains.findUnique({
      select: { domain_name: true, domain_uuid: true },
      where: { domain_uuid: String(domain_uuid) }
    })

    return NextResponse.json({ data: domain_obj }, { status: 200 })
  } catch (error) {
    console.error(error)

    return NextResponse.json({ message: 'An error occurred' }, { status: 500 })
  }
}
