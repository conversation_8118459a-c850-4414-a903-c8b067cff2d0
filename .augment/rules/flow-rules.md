---
type: "always_apply"
---

# Development Flow Rules

## 1. Git Workflow Standards

### 1.1 Branch Strategy
- **Main Branch**: `main` - Production-ready code only
- **Development Branch**: `develop` - Integration branch for features
- **Feature Branches**: `feature/[ticket-id]-[description]`
- **Hotfix Branches**: `hotfix/[ticket-id]-[description]`
- **Release Branches**: `release/[version]`

### 1.2 Commit Standards
- **Conventional Commits**: Use conventional commit format
- **Atomic Commits**: Each commit should represent a single logical change
- **Descriptive Messages**: Clear, concise commit messages
- **No Direct Commits**: No direct commits to main/develop branches

### 1.3 Commit Message Format
```
type(scope): description

[optional body]

[optional footer(s)]
```

Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

## 2. Code Review Process

### 2.1 Pull Request Requirements
- **Template Usage**: Use PR templates for consistency
- **Description**: Clear description of changes and reasoning
- **Testing**: Include test results and coverage information
- **Documentation**: Update relevant documentation
- **Breaking Changes**: Clearly mark and document breaking changes

### 2.2 Review Criteria
- **Code Quality**: Follows coding standards and best practices
- **Security**: No security vulnerabilities introduced
- **Performance**: No performance regressions
- **Testing**: Adequate test coverage
- **Documentation**: Code is well-documented

### 2.3 Approval Process
- **Minimum Reviews**: At least 2 reviewers for critical changes
- **Domain Expertise**: Include domain experts in reviews
- **Security Review**: Security-focused review for sensitive changes
- **Automated Checks**: All CI/CD checks must pass

## 3. Development Environment

### 3.1 Local Development Setup
- **Environment Variables**: Use `.env.local` for local overrides
- **Database**: Local PostgreSQL instance or Docker container
- **Dependencies**: Use exact versions in package-lock.json
- **IDE Configuration**: Consistent ESLint and Prettier settings

### 3.2 Development Commands
```bash
# Development server
npm run dev

# Build and test
npm run build
npm run lint
npm run test

# Database operations
npm run migrate
npx prisma generate
```

### 3.3 Pre-commit Hooks
- **Linting**: ESLint checks
- **Formatting**: Prettier formatting
- **Type Checking**: TypeScript compilation
- **Tests**: Run relevant tests
- **Security Scan**: Basic security checks

## 4. Testing Strategy

### 4.1 Testing Pyramid
- **Unit Tests**: 70% - Test individual functions/components
- **Integration Tests**: 20% - Test component interactions
- **E2E Tests**: 10% - Test complete user workflows

### 4.2 Test Organization
- **Co-location**: Tests near the code they test
- **Naming Convention**: `*.test.ts` or `*.spec.ts`
- **Test Data**: Use factories and fixtures
- **Mocking**: Mock external dependencies

### 4.3 Test Requirements
- **Coverage**: Minimum 80% code coverage
- **Critical Paths**: 100% coverage for critical business logic
- **Edge Cases**: Test error conditions and edge cases
- **Performance**: Include performance tests for critical paths

## 5. Deployment Pipeline

### 5.1 CI/CD Stages
1. **Code Quality**: Linting, formatting, type checking
2. **Testing**: Unit, integration, and E2E tests
3. **Security**: Security scanning and vulnerability checks
4. **Build**: Application build and optimization
5. **Deploy**: Automated deployment to target environment

### 5.2 Environment Promotion
- **Development**: Automatic deployment from develop branch
- **Staging**: Manual promotion from development
- **Production**: Manual promotion from staging with approval

### 5.3 Rollback Strategy
- **Blue-Green Deployment**: Zero-downtime deployments
- **Database Migrations**: Backward-compatible migrations
- **Feature Flags**: Ability to disable features without deployment
- **Monitoring**: Real-time monitoring and alerting

## 6. Issue Management

### 6.1 Issue Lifecycle
1. **Triage**: Categorize and prioritize issues
2. **Assignment**: Assign to appropriate team member
3. **Development**: Implement solution following standards
4. **Review**: Code review and testing
5. **Deployment**: Deploy to appropriate environment
6. **Verification**: Verify fix in target environment
7. **Closure**: Close issue with resolution notes

### 6.2 Issue Categories
- **Bug**: Defects in existing functionality
- **Feature**: New functionality requests
- **Enhancement**: Improvements to existing features
- **Technical Debt**: Code quality improvements
- **Security**: Security-related issues

### 6.3 Priority Levels
- **Critical**: Production down, security vulnerabilities
- **High**: Major functionality broken, performance issues
- **Medium**: Minor functionality issues, usability problems
- **Low**: Nice-to-have improvements, documentation updates

## 7. Documentation Standards

### 7.1 Code Documentation
- **JSDoc**: Document all public APIs
- **README**: Comprehensive setup and usage instructions
- **Architecture**: Document system architecture and decisions
- **API Documentation**: OpenAPI/Swagger for API endpoints

### 7.2 Process Documentation
- **Runbooks**: Operational procedures and troubleshooting
- **Deployment**: Deployment procedures and rollback steps
- **Security**: Security procedures and incident response
- **Onboarding**: New team member onboarding guide

### 7.3 Documentation Maintenance
- **Regular Updates**: Keep documentation current with code changes
- **Review Process**: Include documentation in code reviews
- **Accessibility**: Ensure documentation is accessible to all team members
- **Version Control**: Track documentation changes in version control

## 8. Quality Assurance

### 8.1 Code Quality Metrics
- **Complexity**: Monitor cyclomatic complexity
- **Duplication**: Track code duplication
- **Coverage**: Maintain test coverage metrics
- **Performance**: Monitor performance metrics

### 8.2 Automated Quality Checks
- **Static Analysis**: SonarQube or similar tools
- **Security Scanning**: SAST and DAST tools
- **Dependency Scanning**: Check for vulnerable dependencies
- **License Compliance**: Ensure license compatibility

### 8.3 Manual Quality Assurance
- **Code Reviews**: Peer review of all changes
- **Testing**: Manual testing of critical paths
- **Security Reviews**: Security-focused reviews
- **Performance Testing**: Load and stress testing

## 9. Incident Response

### 9.1 Incident Classification
- **Severity 1**: Complete service outage
- **Severity 2**: Major functionality impaired
- **Severity 3**: Minor functionality impaired
- **Severity 4**: Cosmetic issues or minor bugs

### 9.2 Response Procedures
- **Detection**: Monitoring and alerting systems
- **Assessment**: Rapid assessment of impact and severity
- **Response**: Immediate response and mitigation
- **Communication**: Stakeholder communication and updates
- **Resolution**: Permanent fix and verification
- **Post-mortem**: Analysis and improvement planning

### 9.3 Communication Protocols
- **Internal**: Team communication channels and escalation
- **External**: Customer communication and status pages
- **Documentation**: Incident logs and resolution tracking
- **Follow-up**: Post-incident review and improvements
