---
type: "always_apply"
---

# Security Rules

## 1. Authentication & Authorization

### 1.1 Session Management
- **Always validate session**: Use `getSession()` in all API routes
- **Session timeout**: Implement appropriate session timeouts
- **Secure cookies**: Use secure, httpOnly, sameSite cookies
- **Token rotation**: Implement token refresh mechanisms

### 1.2 Multi-tenant Security
- **Domain isolation**: Always scope data by `domain_uuid`
- **Row-level security**: Implement at database and application level
- **Cross-tenant access**: Prevent unauthorized cross-domain access
- **Resource isolation**: Separate sensitive resources per domain

```typescript
// Example: Always include domain_uuid in queries
const session = await getSession()
if (!session?.user?.domain_uuid) {
  return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
}

const data = await prisma.v_users.findMany({
  where: {
    domain_uuid: session.user.domain_uuid,
    // other conditions
  }
})
```

### 1.3 Role-Based Access Control (RBAC)
- **Principle of least privilege**: Grant minimum necessary permissions
- **Role validation**: Validate user roles before sensitive operations
- **Permission checks**: Implement granular permission checking
- **Admin separation**: Separate admin and user access patterns

## 2. Input Validation & Sanitization

### 2.1 API Input Validation
- **Validate all inputs**: Never trust client-side data
- **Use validation libraries**: Implement Valibot or similar for validation
- **Sanitize inputs**: Clean data before processing
- **Type checking**: Use TypeScript for compile-time validation

### 2.2 SQL Injection Prevention
- **Use Prisma ORM**: Avoid raw SQL queries
- **Parameterized queries**: When raw SQL is necessary, use parameters
- **Input escaping**: Properly escape special characters
- **Query validation**: Validate query parameters

### 2.3 XSS Prevention
- **Output encoding**: Encode all user-generated content
- **Content Security Policy**: Implement strict CSP headers
- **Input sanitization**: Remove or escape dangerous HTML/JS
- **Template security**: Use secure templating practices

## 3. Data Protection

### 3.1 Encryption
- **Data at rest**: Encrypt sensitive data in database
- **Data in transit**: Use HTTPS/TLS for all communications
- **Key management**: Secure storage and rotation of encryption keys
- **Password hashing**: Use bcrypt for password storage

### 3.2 Sensitive Data Handling
- **PII protection**: Implement special handling for personal data
- **Data masking**: Mask sensitive data in logs and responses
- **Secure deletion**: Implement secure data deletion procedures
- **Data retention**: Follow data retention policies

### 3.3 File Upload Security
- **File type validation**: Restrict allowed file types
- **Size limits**: Implement file size restrictions
- **Virus scanning**: Scan uploaded files for malware
- **Storage isolation**: Store uploads outside web root

## 4. API Security

### 4.1 Rate Limiting
- **Request throttling**: Implement rate limiting per user/IP
- **Brute force protection**: Protect against password attacks
- **API quotas**: Set appropriate API usage limits
- **DDoS protection**: Implement DDoS mitigation strategies

### 4.2 CORS Configuration
- **Strict origins**: Only allow trusted origins
- **Credential handling**: Properly configure credential sharing
- **Method restrictions**: Limit allowed HTTP methods
- **Header validation**: Validate allowed headers

### 4.3 API Versioning & Documentation
- **Version control**: Maintain API version compatibility
- **Security documentation**: Document security requirements
- **Deprecation notices**: Provide advance notice for changes
- **Breaking changes**: Handle breaking changes securely

## 5. Infrastructure Security

### 5.1 Environment Configuration
- **Environment separation**: Isolate dev/staging/production
- **Secret management**: Use environment variables for secrets
- **Configuration validation**: Validate all configuration settings
- **Default security**: Secure defaults for all configurations

### 5.2 Database Security
- **Connection security**: Use encrypted database connections
- **User privileges**: Limit database user permissions
- **Backup security**: Encrypt and secure database backups
- **Audit logging**: Log all database access and changes

### 5.3 Network Security
- **Firewall rules**: Implement strict firewall configurations
- **VPN access**: Use VPN for administrative access
- **Network segmentation**: Isolate different network zones
- **Monitoring**: Monitor network traffic for anomalies

## 6. Logging & Monitoring

### 6.1 Security Logging
- **Authentication events**: Log all login attempts
- **Authorization failures**: Log access denials
- **Data access**: Log sensitive data access
- **System changes**: Log configuration changes

### 6.2 Monitoring & Alerting
- **Real-time monitoring**: Monitor for security events
- **Anomaly detection**: Detect unusual patterns
- **Incident response**: Automated response to threats
- **Compliance reporting**: Generate security reports

### 6.3 Log Security
- **Log integrity**: Protect logs from tampering
- **Centralized logging**: Use centralized log management
- **Retention policies**: Implement log retention policies
- **Access control**: Restrict log access to authorized personnel

## 7. Third-party Integration Security

### 7.1 API Integration Security
- **Authentication**: Secure API key management
- **Rate limiting**: Respect third-party rate limits
- **Error handling**: Secure error handling for external APIs
- **Data validation**: Validate all external API responses

### 7.2 Social Platform Security
- **OAuth security**: Implement secure OAuth flows
- **Token management**: Secure storage of access tokens
- **Webhook validation**: Validate webhook signatures
- **Data minimization**: Request only necessary permissions

### 7.3 FreeSWITCH Security
- **Connection security**: Secure FreeSWITCH connections
- **Command validation**: Validate all FreeSWITCH commands
- **Access control**: Limit FreeSWITCH access
- **Audit logging**: Log all FreeSWITCH interactions

## 8. Incident Response

### 8.1 Security Incident Classification
- **Critical**: Data breach, system compromise
- **High**: Unauthorized access, service disruption
- **Medium**: Security policy violations
- **Low**: Minor security issues

### 8.2 Response Procedures
- **Immediate response**: Contain and assess threats
- **Investigation**: Forensic analysis of incidents
- **Communication**: Notify stakeholders appropriately
- **Recovery**: Restore services securely
- **Post-incident**: Learn and improve from incidents

### 8.3 Business Continuity
- **Backup procedures**: Regular secure backups
- **Disaster recovery**: Plan for major incidents
- **Service continuity**: Maintain service during incidents
- **Data recovery**: Secure data recovery procedures

## 9. Compliance & Privacy

### 9.1 Data Privacy
- **GDPR compliance**: Implement GDPR requirements
- **Data subject rights**: Support user data rights
- **Privacy by design**: Build privacy into systems
- **Consent management**: Manage user consent properly

### 9.2 Audit & Compliance
- **Regular audits**: Conduct security audits
- **Compliance monitoring**: Monitor compliance status
- **Documentation**: Maintain compliance documentation
- **Training**: Provide security training to team

### 9.3 Vendor Management
- **Security assessments**: Assess vendor security
- **Contract requirements**: Include security in contracts
- **Monitoring**: Monitor vendor security practices
- **Incident coordination**: Coordinate with vendors on incidents

## 10. Security Development Lifecycle

### 10.1 Secure Coding Practices
- **Code reviews**: Security-focused code reviews
- **Static analysis**: Use security scanning tools
- **Dependency scanning**: Check for vulnerable dependencies
- **Penetration testing**: Regular security testing

### 10.2 Security Training
- **Developer training**: Security awareness for developers
- **Best practices**: Share security best practices
- **Threat modeling**: Conduct threat modeling exercises
- **Incident simulation**: Practice incident response

### 10.3 Continuous Improvement
- **Security metrics**: Track security metrics
- **Vulnerability management**: Manage security vulnerabilities
- **Security updates**: Keep systems updated
- **Process improvement**: Continuously improve security processes
