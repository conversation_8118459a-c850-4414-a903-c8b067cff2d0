---
type: "always_apply"
---

# Architecture Rules

## 1. Project Structure & Organization

### 1.1 Directory Structure
- **Follow established patterns**: Maintain the current Next.js App Router structure
- **Separation by domain**: Group related functionality in domain-specific folders
- **Shared components**: Place reusable components in `src/components/`
- **Business logic**: Keep business logic in `src/libs/` organized by domain
- **API routes**: Structure API routes to mirror business domains in `src/app/api/`

### 1.2 Layer Architecture
```
┌─────────────────────────────────────┐
│           Presentation Layer        │ ← Views, Components, Pages
├─────────────────────────────────────┤
│           Application Layer         │ ← API Routes, Actions, Hooks
├─────────────────────────────────────┤
│            Business Layer           │ ← Services, Libs, Utils
├─────────────────────────────────────┤
│         Data Access Layer           │ ← Prisma, Database Models
└─────────────────────────────────────┘
```

### 1.3 Domain-Driven Design
- **Core Domains**: PBX, Chat, Social Integrations, User Management
- **Shared Kernel**: Authentication, Authorization, Common Utils
- **Bounded Contexts**: Each domain should have clear boundaries
- **Anti-Corruption Layer**: Use adapters for external integrations

## 2. Technology Stack Standards

### 2.1 Core Technologies
- **Frontend**: Next.js 14+ with App Router
- **Backend**: Next.js API Routes with Express.js server
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js with custom providers
- **State Management**: Redux Toolkit + React hooks
- **UI Framework**: Material-UI (MUI) + Tailwind CSS
- **Real-time**: Socket.IO for live updates

### 2.2 External Integrations
- **FreeSWITCH**: For PBX functionality
- **Social Platforms**: Facebook, Telegram, Zalo APIs
- **Email**: IMAP/SMTP with Nodemailer
- **File Storage**: Local storage with future cloud migration support

## 3. Scalability Principles

### 3.1 Horizontal Scaling
- **Stateless services**: Design all services to be stateless
- **Database connection pooling**: Use Prisma connection pooling
- **Caching strategy**: Implement Redis for session and data caching
- **Load balancing**: Prepare for multi-instance deployment

### 3.2 Performance Optimization
- **Code splitting**: Use Next.js dynamic imports
- **Image optimization**: Leverage Next.js Image component
- **Bundle optimization**: Tree-shaking and dead code elimination
- **Database optimization**: Use Prisma query optimization and indexes

## 4. Multi-tenancy Architecture

### 4.1 Domain-Based Isolation
- **Domain UUID**: All data must be scoped by `domain_uuid`
- **Row-level security**: Implement at database and application level
- **Shared infrastructure**: Single codebase, isolated data
- **Resource isolation**: Separate resources per domain where needed

### 4.2 Data Isolation Patterns
```typescript
// Always include domain_uuid in queries
const users = await prisma.v_users.findMany({
  where: {
    domain_uuid: session.user.domain_uuid,
    // other conditions
  }
})
```

## 5. Integration Architecture

### 5.1 External Service Integration
- **Adapter Pattern**: Create adapters for each external service
- **Circuit Breaker**: Implement for external API calls
- **Retry Logic**: Exponential backoff for failed requests
- **Rate Limiting**: Respect external API rate limits

### 5.2 Event-Driven Architecture
- **Domain Events**: Use events for cross-domain communication
- **Event Sourcing**: For audit trails and state reconstruction
- **Message Queues**: Use for asynchronous processing
- **Webhooks**: Handle external service notifications

## 6. Microservices Readiness

### 6.1 Service Boundaries
- **Loose Coupling**: Minimize dependencies between domains
- **High Cohesion**: Keep related functionality together
- **Database per Service**: Prepare for service-specific databases
- **API Contracts**: Define clear interfaces between services

### 6.2 Communication Patterns
- **Synchronous**: REST APIs for immediate responses
- **Asynchronous**: Events for eventual consistency
- **Real-time**: WebSockets for live updates
- **Batch Processing**: For heavy computational tasks

## 7. Deployment Architecture

### 7.1 Environment Strategy
- **Development**: Local development with Docker
- **Staging**: Production-like environment for testing
- **Production**: High-availability deployment
- **Feature Flags**: For gradual feature rollouts

### 7.2 Infrastructure as Code
- **Containerization**: Docker for all services
- **Orchestration**: Kubernetes for production deployment
- **CI/CD Pipeline**: Automated testing and deployment
- **Monitoring**: Comprehensive logging and metrics

## 8. Data Architecture

### 8.1 Database Design
- **Normalized Schema**: Follow 3NF for transactional data
- **Denormalized Views**: For read-heavy operations
- **Partitioning**: By domain_uuid for large tables
- **Archival Strategy**: For historical data management

### 8.2 Data Flow
- **CQRS Pattern**: Separate read and write models where beneficial
- **Event Store**: For audit and compliance requirements
- **Data Synchronization**: Between internal and external systems
- **Backup Strategy**: Regular automated backups with point-in-time recovery

## 9. Security Architecture

### 9.1 Defense in Depth
- **Network Security**: Firewalls, VPNs, secure protocols
- **Application Security**: Input validation, output encoding
- **Data Security**: Encryption at rest and in transit
- **Identity Security**: Strong authentication and authorization

### 9.2 Zero Trust Model
- **Verify Everything**: Never trust, always verify
- **Least Privilege**: Minimum necessary access
- **Continuous Monitoring**: Real-time security monitoring
- **Incident Response**: Automated threat detection and response

## 10. Compliance & Governance

### 10.1 Data Governance
- **Data Classification**: Sensitive, internal, public
- **Data Retention**: Automated lifecycle management
- **Data Privacy**: GDPR, CCPA compliance
- **Audit Trails**: Comprehensive logging of all actions

### 10.2 Technical Governance
- **Code Reviews**: Mandatory peer reviews
- **Architecture Reviews**: Regular architecture assessments
- **Security Reviews**: Security-focused code reviews
- **Performance Reviews**: Regular performance assessments
