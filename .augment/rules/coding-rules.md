---
type: "always_apply"
---

# Clean Code Guidelines

## 1. Separation of Concerns (SOC)

- Break down complex programs into smaller, focused units
- Each component/module should have a single responsibility
- Keep layers (UI, business logic, data) separate
- Use meaningful folder structure to organize code
- Create reusable components and utilities

## 2. Document Your Code (DYC)

- Write clear, descriptive comments explaining complex logic
- Use JSDoc for function/component documentation
- Keep README files up-to-date
- Include examples in documentation
- Document configuration and environment setup
- Add inline comments for non-obvious code sections

## 3. Don't Repeat Yourself (DRY)

- Extract common functionality into reusable functions/components
- Use constants for repeated values
- Create shared utilities and hooks
- Implement design patterns to avoid duplication
- Maintain a component library for common UI elements

## 4. Keep It Simple, Stupid (KISS)

- Write clear, straightforward code
- Avoid over-engineering solutions
- Use descriptive variable and function names
- Break complex functions into smaller, focused ones
- Minimize dependencies and complexity

## 5. Test Driven Development (TDD)

- Write tests before implementing features
- Follow Red-Green-Refactor cycle
- Maintain high test coverage
- Write unit, integration, and e2e tests
- Use testing best practices and patterns
- Keep tests readable and maintainable

## 6. You Ain't Gonna Need It (YAGNI)

- Only implement required features
- Avoid speculative programming
- Remove unused code and dependencies
- Focus on current requirements
- Plan for extensibility without over-engineering

## 7. Problem Prevention (PP)

- Run static code analysis regularly
- Use linting tools and fix warnings
- Follow code review best practices
- Document known issues and solutions
- Maintain error logging and monitoring
- Implement automated testing pipelines
- Set up continuous integration checks
- Use code quality metrics and thresholds
- Conduct regular security audits
- Practice defensive programming



## 8. Single Source of Truth (SSOT)
- Maintain one authoritative source for each piece of data
- Avoid data duplication across components/modules
- Use centralized state management
- Implement proper data synchronization
- Create clear data flow patterns
- Document data ownership and access patterns


## 9. Incremental Development (ID)

- Break features into small, testable increments
- Update documentation with each increment
- Use open-source tools for automated issue detection
- Map code dependencies for impact analysis
- Apply fixes iteratively with validation

Quick Reference:
┌─────────────────┐
│ Plan → Code → Test │
│ ↑ ↓ │
│ └── Document │
└─────────────────┘

Key Steps:

1. Small changes
2. Doc updates
3. Auto-detect issues
4. Impact check
5. Validate fixes

## 10. Definition of Done (DoD) for Coding Implementation:

Code is complete and compiles without errors
- All acceptance criteria are met
- Self-review done (logic, edge cases, performance)
- Unit tests are written and pass
- No critical/high severity bugs
- Code follows coding standards/style guide
- Documentation (if needed) is updated
- Security review completed (input validation, no secrets, auth/authz, data protection)
- Static analysis/linting clean
- Potential issues fixed (e.g. memory, race, error handling)

REMEBER: ALWAY RUN BUILD TO ENSURE COMPLETE WITHOUT ERROR OR WARNING
