# AgentDeskLite Development Rules

This directory contains comprehensive development rules to maintain security, consistency, and quality across the AgentDeskLite project over time.

## 📋 Rules Overview

### 🏗️ [Architecture Rules](./architecture-rules.md)
Defines the overall system architecture, technology stack, and design patterns.

**Key Areas:**
- Project structure & organization
- Technology stack standards
- Scalability principles
- Multi-tenancy architecture
- Integration patterns
- Microservices readiness
- Deployment architecture
- Data architecture
- Security architecture
- Compliance & governance

### 💻 [Coding Rules](./coding-rules.md)
Establishes coding standards, best practices, and quality guidelines.

**Key Areas:**
- Clean code principles (SOC, DRY, KISS, YAGNI)
- Documentation standards
- Test-driven development
- TypeScript standards
- React/Next.js best practices
- API development standards
- Database interaction rules
- Performance guidelines

### 🔄 [Flow Rules](./flow-rules.md)
Defines development workflow, processes, and collaboration standards.

**Key Areas:**
- Git workflow standards
- Code review process
- Development environment setup
- Testing strategy
- Deployment pipeline
- Issue management
- Documentation standards
- Quality assurance
- Incident response

### 🔒 [Security Rules](./security-rules.md)
Comprehensive security guidelines for all aspects of development.

**Key Areas:**
- Authentication & authorization
- Input validation & sanitization
- Data protection
- API security
- Infrastructure security
- Logging & monitoring
- Third-party integration security
- Incident response
- Compliance & privacy
- Security development lifecycle

## 🚀 Quick Start Checklist

### For New Features
- [ ] Follow domain-driven design principles
- [ ] Implement proper session validation
- [ ] Scope all data by `domain_uuid`
- [ ] Use TypeScript with strict typing
- [ ] Write tests before implementation
- [ ] Document public APIs with JSDoc
- [ ] Follow RESTful API conventions
- [ ] Implement proper error handling
- [ ] Use Prisma for database operations
- [ ] Validate all inputs with Valibot

### For Code Reviews
- [ ] Check security implementation
- [ ] Verify multi-tenant isolation
- [ ] Ensure proper error handling
- [ ] Review test coverage
- [ ] Validate documentation updates
- [ ] Check performance implications
- [ ] Verify coding standards compliance
- [ ] Review database query optimization

### For Deployments
- [ ] Run all tests and linting
- [ ] Verify security scans pass
- [ ] Check database migration safety
- [ ] Validate environment configuration
- [ ] Ensure monitoring is in place
- [ ] Prepare rollback procedures
- [ ] Update documentation
- [ ] Notify stakeholders

## 🎯 Core Principles

### 1. Security First
- Always validate sessions in API routes
- Implement domain-based data isolation
- Use principle of least privilege
- Encrypt sensitive data
- Log security events

### 2. Multi-Tenant Architecture
- Scope all data by `domain_uuid`
- Prevent cross-tenant data access
- Implement row-level security
- Isolate resources per domain

### 3. Clean Code
- Single responsibility principle
- Don't repeat yourself
- Keep it simple
- Write self-documenting code
- Test-driven development

### 4. Performance & Scalability
- Optimize database queries
- Use proper caching strategies
- Implement code splitting
- Monitor performance metrics
- Design for horizontal scaling

### 5. Maintainability
- Follow consistent patterns
- Document architectural decisions
- Use meaningful naming conventions
- Implement proper error handling
- Maintain comprehensive tests

## 🛠️ Technology Stack

### Frontend
- **Framework**: Next.js 14+ with App Router
- **UI Library**: Material-UI (MUI) + Tailwind CSS
- **State Management**: Redux Toolkit
- **Language**: TypeScript
- **Testing**: Jest + React Testing Library

### Backend
- **Runtime**: Node.js with Next.js API Routes
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js
- **Real-time**: Socket.IO
- **Validation**: Valibot

### Infrastructure
- **Containerization**: Docker
- **Process Management**: PM2/Nodemon
- **Caching**: Redis (planned)
- **Monitoring**: Custom logging + metrics

## 📚 Additional Resources

### Documentation
- [API Documentation](../docs/)
- [Database Schema](../src/prisma/schema.prisma)
- [Environment Setup](../README.md)

### Tools & Configuration
- [ESLint Config](../.eslintrc.json)
- [TypeScript Config](../tsconfig.json)
- [Prettier Config](../.prettierrc)
- [Tailwind Config](../tailwind.config.ts)

## 🔄 Rule Updates

These rules are living documents that should be updated as the project evolves:

1. **Regular Reviews**: Review rules quarterly or after major changes
2. **Team Input**: Gather feedback from all team members
3. **Version Control**: Track rule changes in git
4. **Communication**: Announce rule updates to the team
5. **Training**: Provide training on new or updated rules

## 📞 Support

For questions about these rules or suggestions for improvements:
- Create an issue in the project repository
- Discuss in team meetings
- Contact the architecture team
- Review during code reviews

---

**Remember**: These rules exist to maintain quality, security, and consistency. When in doubt, err on the side of caution and follow the most restrictive interpretation.
